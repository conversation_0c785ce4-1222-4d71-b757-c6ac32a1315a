const puppeteer = require('puppeteer');

// Test configuration
const APP_URL = 'http://localhost:3000';
const TEST_ROOM_CODE = 'TEST' + Date.now().toString().slice(-4);
const CHROME_PATH = '/usr/bin/chromium-browser';

// Timing constants from the game
const ROUND_DURATION = 7000; // 7 seconds
const TRANSITION_DURATION = 3000; // 3 seconds

// Test scenarios
const scenarios = [
  {
    name: 'All players answer quickly (< 3s)',
    answerDelays: [1000, 1500, 2000] // Player 1, 2, 3 answer times
  },
  {
    name: 'Players answer at different times',
    answerDelays: [1000, 3000, 5000]
  },
  {
    name: 'Some players don\'t answer',
    answerDelays: [2000, 4000, null] // Player 3 doesn't answer
  },
  {
    name: 'All players answer near deadline',
    answerDelays: [6000, 6500, 6800]
  }
];

// Helper to create a player instance
async function createPlayer(playerName, browserContext) {
  const page = await browserContext.newPage();
  await page.setViewport({ width: 1200, height: 800 });
  
  // Set up console logging
  page.on('console', msg => {
    console.log(`[${playerName}] Console:`, msg.text());
  });
  
  await page.goto(APP_URL);
  
  return {
    name: playerName,
    page,
    gameState: {},
    timingLog: []
  };
}

// Helper to join a game room
async function joinGame(player, roomCode, isHost = false) {
  const { page, name } = player;
  
  if (isHost) {
    // Host creates the room
    await page.waitForSelector('button:has-text("Play with Friends")', { timeout: 10000 });
    await page.click('button:has-text("Play with Friends")');
    
    // Wait for room code to be generated
    await page.waitForSelector('.room-code', { timeout: 10000 });
    const generatedCode = await page.$eval('.room-code', el => el.textContent);
    console.log(`[${name}] Created room: ${generatedCode}`);
    
    // Start the game
    await page.waitForSelector('button:has-text("Start Game")', { timeout: 10000 });
    await page.click('button:has-text("Start Game")');
    
    return generatedCode;
  } else {
    // Join existing room
    await page.waitForSelector('button:has-text("Join Game")', { timeout: 10000 });
    await page.click('button:has-text("Join Game")');
    
    // Enter room code
    await page.waitForSelector('input[placeholder*="room code"]', { timeout: 10000 });
    await page.type('input[placeholder*="room code"]', roomCode);
    await page.click('button:has-text("Join")');
    
    console.log(`[${name}] Joined room: ${roomCode}`);
  }
}

// Monitor game state and timing
async function monitorGameState(player) {
  const { page, name } = player;
  
  // Inject monitoring code
  await page.evaluateOnNewDocument(() => {
    window.gameStateLog = [];
    window.transitionLog = [];
    
    // Monitor timer changes
    const observeTimer = () => {
      const timerElement = document.querySelector('.timer-display');
      if (timerElement) {
        const observer = new MutationObserver((mutations) => {
          const timestamp = Date.now();
          const timerText = timerElement.textContent;
          const timerClass = timerElement.className;
          
          window.transitionLog.push({
            timestamp,
            timerText,
            timerClass,
            type: 'timer_change'
          });
        });
        
        observer.observe(timerElement, { 
          childList: true, 
          characterData: true, 
          subtree: true,
          attributes: true 
        });
      }
    };
    
    // Try to observe timer when page loads
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', observeTimer);
    } else {
      observeTimer();
    }
    
    // Monitor game state changes
    const originalSetState = window.gameStore?.setState;
    if (originalSetState) {
      window.gameStore.setState = function(...args) {
        const result = originalSetState.apply(this, args);
        window.gameStateLog.push({
          timestamp: Date.now(),
          state: window.gameStore.getState(),
          type: 'state_change'
        });
        return result;
      };
    }
  });
}

// Submit an answer
async function submitAnswer(player, answerIndex = 0) {
  const { page, name } = player;
  
  try {
    // Wait for answer buttons to appear
    await page.waitForSelector('.answer-button', { timeout: 5000 });
    
    // Click the specified answer
    const buttons = await page.$$('.answer-button');
    if (buttons[answerIndex]) {
      await buttons[answerIndex].click();
      console.log(`[${name}] Submitted answer ${answerIndex} at ${new Date().toISOString()}`);
    }
  } catch (error) {
    console.log(`[${name}] Failed to submit answer:`, error.message);
  }
}

// Capture timing data
async function captureTimingData(player) {
  const { page } = player;
  
  return await page.evaluate(() => {
    return {
      gameStateLog: window.gameStateLog || [],
      transitionLog: window.transitionLog || [],
      currentState: window.gameStore?.getState() || {}
    };
  });
}

// Run a test scenario
async function runScenario(scenario) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`Running scenario: ${scenario.name}`);
  console.log(`${'='.repeat(60)}\n`);
  
  const browser = await puppeteer.launch({
    executablePath: CHROME_PATH,
    headless: false, // Set to true for automated testing
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const players = [];
  
  try {
    // Create browser contexts for isolation
    const contexts = await Promise.all([
      browser.createIncognitoBrowserContext(),
      browser.createIncognitoBrowserContext(),
      browser.createIncognitoBrowserContext()
    ]);
    
    // Create players
    for (let i = 0; i < 3; i++) {
      const player = await createPlayer(`Player${i + 1}`, contexts[i]);
      await monitorGameState(player);
      players.push(player);
    }
    
    // Player 1 creates the room
    const roomCode = await joinGame(players[0], TEST_ROOM_CODE, true);
    
    // Other players join
    await new Promise(resolve => setTimeout(resolve, 2000));
    await joinGame(players[1], roomCode, false);
    await new Promise(resolve => setTimeout(resolve, 1000));
    await joinGame(players[2], roomCode, false);
    
    // Wait for game to start
    console.log('Waiting for game to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Monitor multiple rounds
    for (let round = 1; round <= 3; round++) {
      console.log(`\n--- Round ${round} ---`);
      const roundStartTime = Date.now();
      
      // Submit answers according to scenario
      const answerPromises = scenario.answerDelays.map((delay, index) => {
        if (delay === null) {
          console.log(`[Player${index + 1}] Will not answer this round`);
          return Promise.resolve();
        }
        
        return new Promise(resolve => {
          setTimeout(async () => {
            await submitAnswer(players[index], index % 4);
            resolve();
          }, delay);
        });
      });
      
      // Wait for all answer submissions
      await Promise.all(answerPromises);
      
      // Wait for round transition
      const maxDelay = Math.max(...scenario.answerDelays.filter(d => d !== null));
      const expectedTransitionTime = maxDelay < ROUND_DURATION ? 
        maxDelay + TRANSITION_DURATION : 
        ROUND_DURATION + TRANSITION_DURATION;
      
      // Add buffer for network latency
      await new Promise(resolve => setTimeout(resolve, expectedTransitionTime + 1000));
      
      // Capture timing data
      const roundEndTime = Date.now();
      console.log(`Round ${round} duration: ${roundEndTime - roundStartTime}ms`);
      
      // Capture screenshots
      for (const player of players) {
        await player.page.screenshot({ 
          path: `round${round}_${player.name}_${scenario.name.replace(/\s+/g, '_')}.png` 
        });
      }
    }
    
    // Collect final timing data
    console.log('\n--- Timing Analysis ---');
    for (const player of players) {
      const timingData = await captureTimingData(player);
      console.log(`\n[${player.name}] Timing Data:`, JSON.stringify(timingData, null, 2));
    }
    
  } catch (error) {
    console.error('Test scenario failed:', error);
  } finally {
    // Cleanup
    await browser.close();
  }
}

// Main test runner
async function runAllTests() {
  console.log('Starting Puppeteer Multiplayer Tests');
  console.log(`App URL: ${APP_URL}`);
  console.log(`Chrome Path: ${CHROME_PATH}`);
  
  for (const scenario of scenarios) {
    await runScenario(scenario);
    
    // Wait between scenarios
    await new Promise(resolve => setTimeout(resolve, 5000));
  }
  
  console.log('\n\nAll tests completed!');
}

// Run the tests
runAllTests().catch(console.error);