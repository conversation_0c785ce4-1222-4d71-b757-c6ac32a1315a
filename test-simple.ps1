$uri = "http://127.0.0.1:54321/functions/v1/login-handler"
$body = '{"identifier":"jkim","password":"wrongpassword"}'

Write-Host "Testing login-handler function..."
Write-Host "URL: $uri"
Write-Host ""

# Test OPTIONS request first
Write-Host "=== Testing OPTIONS request ==="
try {
    $optionsResponse = Invoke-WebRequest -Uri $uri -Method OPTIONS -UseBasicParsing
    Write-Host "OPTIONS Status: $($optionsResponse.StatusCode)"
    Write-Host "OPTIONS Headers:"
    foreach ($header in $optionsResponse.Headers.GetEnumerator()) {
        Write-Host "  $($header.Key): $($header.Value)"
    }
} catch {
    Write-Host "OPTIONS failed: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "=== Testing POST request ==="

# Test POST request
try {
    $postResponse = Invoke-WebRequest -Uri $uri -Method POST -Body $body -ContentType "application/json" -UseBasicParsing
    Write-Host "POST Status: $($postResponse.StatusCode)"
    Write-Host "POST Headers:"
    foreach ($header in $postResponse.Headers.GetEnumerator()) {
        Write-Host "  $($header.Key): $($header.Value)"
    }
    Write-Host "POST Body: $($postResponse.Content)"
} catch {
    Write-Host "POST Status: $($_.Exception.Response.StatusCode)"
    Write-Host "POST Error: $($_.Exception.Message)"
    
    if ($_.Exception.Response) {
        Write-Host "POST Response Headers:"
        try {
            $headers = $_.Exception.Response.Headers
            foreach ($header in $headers) {
                Write-Host "  $($header.Key): $($header.Value)"
            }
        } catch {
            Write-Host "  Could not read headers: $($_.Exception.Message)"
        }
    }
} 