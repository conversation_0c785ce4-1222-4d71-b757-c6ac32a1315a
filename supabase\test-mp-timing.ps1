# Multiplayer timing test - simplified version

Clear-Host

Write-Host "MULTIPLAYER ROUND ADVANCE TEST" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan
Write-Host ""

# Create monitor code
$monitorCode = @'
// Timing Monitor
(() => {
    console.log('TIMING MONITOR ACTIVE', 'color: #00ff00; font-weight: bold');
    
    let roundStart = null;
    let roundNum = 1;
    
    const elapsed = () => roundStart ? ((Date.now() - roundStart) / 1000).toFixed(1) : '?';
    
    // Track API calls
    const origFetch = window.fetch;
    window.fetch = async function(...args) {
        const [url] = args;
        
        if (url && url.includes('submit-answer-handler')) {
            console.log(`ANSWER SUBMITTED: ${elapsed()}s`, 'color: #00ffff; font-weight: bold');
        }
        
        if (url && url.includes('start-game-handler')) {
            console.log('GAME STARTING', 'color: #ffff00; font-weight: bold');
            roundStart = Date.now();
        }
        
        const resp = await origFetch.apply(this, args);
        
        if (url && url.includes('submit-answer-handler')) {
            try {
                const data = await resp.clone().json();
                if (data.inTransition) {
                    console.log(`TRANSITION AT: ${elapsed()}s`, 'color: #ff00ff; font-weight: bold');
                }
            } catch (e) {}
        }
        
        return resp;
    };
    
    // Monitor question changes
    let lastQ = '';
    setInterval(() => {
        const q = document.querySelector('h2');
        if (q && q.textContent !== lastQ && lastQ) {
            console.log(`ROUND ${roundNum} ADVANCED AT: ${elapsed()}s`, 'color: #00ff00; font-size: 16px; font-weight: bold');
            roundNum++;
            roundStart = Date.now();
        }
        lastQ = q ? q.textContent : '';
    }, 100);
    
    console.log('Monitor ready! Watch for colored messages.');
})();
'@

# Save code
$codePath = "$env:TEMP\monitor.js"
$monitorCode | Out-File -FilePath $codePath -Encoding UTF8

Write-Host "Opening game windows..." -ForegroundColor Yellow
Start-Process "https://recognition-combine.vercel.app/"
Start-Sleep -Seconds 2
Start-Process "https://recognition-combine.vercel.app/"

Write-Host ""
Write-Host "INSTRUCTIONS:" -ForegroundColor Green
Write-Host "1. Sign in to both windows" -ForegroundColor White
Write-Host "2. Create/join same room" -ForegroundColor White
Write-Host "3. Open F12 console in BOTH" -ForegroundColor White
Write-Host "4. Paste the monitor code (see below)" -ForegroundColor White
Write-Host "5. Start game and test scenarios" -ForegroundColor White
Write-Host ""

Write-Host "TEST SCENARIOS:" -ForegroundColor Yellow
Write-Host ""
Write-Host "A) Both at 1s -> Expect 4.0s" -ForegroundColor Green
Write-Host "B) Both at 2s -> Expect 5.0s" -ForegroundColor Blue
Write-Host "C) Both at 5s -> Expect 7.0s (cap)" -ForegroundColor Red
Write-Host "D) One player -> Expect 7.0s" -ForegroundColor Yellow
Write-Host "E) P1@1s P2@3s -> Expect 6.0s" -ForegroundColor Magenta
Write-Host ""

Write-Host "MONITOR CODE:" -ForegroundColor Cyan
Write-Host "=============" -ForegroundColor Cyan
Write-Host $monitorCode -ForegroundColor Gray
Write-Host ""

Write-Host "Code saved to: $codePath" -ForegroundColor Gray
Write-Host ""
Write-Host "Opening monitor code file..."
notepad $codePath