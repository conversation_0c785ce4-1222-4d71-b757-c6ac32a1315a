const puppeteer = require('puppeteer');
const os = require('os');
const fs = require('fs').promises;

async function checkEdgeFunction() {
  console.log('=== Checking Edge Function Response ===');
  
  // Detect environment
  const platform = os.platform();
  const isWSL = platform === 'linux' && os.release().toLowerCase().includes('microsoft');
  
  let executablePath;
  if (isWSL) {
    executablePath = '/usr/bin/chromium-browser';
  }
  
  const browser = await puppeteer.launch({
    headless: false, // Show browser for debugging
    ...(executablePath && { executablePath }),
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable request interception
    await page.setRequestInterception(true);
    
    // Log edge function requests/responses
    page.on('request', request => {
      if (request.url().includes('start-game-handler')) {
        console.log('\n🔵 [REQUEST] start-game-handler');
        console.log('URL:', request.url());
        console.log('Method:', request.method());
        if (request.method() === 'POST') {
          console.log('Body:', request.postData());
        }
      }
      request.continue();
    });
    
    page.on('response', async response => {
      if (response.url().includes('start-game-handler')) {
        console.log('\n🟢 [RESPONSE] start-game-handler');
        console.log('Status:', response.status());
        try {
          const body = await response.text();
          console.log('Raw Body:', body);
          
          try {
            const parsed = JSON.parse(body);
            console.log('\nParsed Response:');
            console.log(JSON.stringify(parsed, null, 2));
            
            // Check the response structure
            if (parsed.firstQuestion) {
              console.log('\n✅ Response has firstQuestion object');
              console.log('  - questionId:', parsed.firstQuestion.questionId ? '✓' : '✗');
              console.log('  - correctPlayerId:', parsed.firstQuestion.correctPlayerId ? '✓' : '✗');
              console.log('  - imageUrl:', parsed.firstQuestion.imageUrl ? '✓' : '✗');
              console.log('  - choices:', Array.isArray(parsed.firstQuestion.choices) ? `✓ (${parsed.firstQuestion.choices.length} choices)` : '✗');
              console.log('  - correctChoiceName:', parsed.firstQuestion.correctChoiceName ? '✓' : '✗');
            } else {
              console.log('\n❌ Response missing firstQuestion object');
            }
          } catch (e) {
            console.log('(Not JSON)');
          }
        } catch (e) {
          console.log('Could not read body:', e.message);
        }
      }
    });
    
    // Log console messages
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('EDGE_START_GAME') || 
          text.includes('start-game-handler') ||
          text.includes('Start Game')) {
        console.log(`[Browser] ${text}`);
      }
    });
    
    console.log('\nNavigating to app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    console.log('Please manually:');
    console.log('1. Login as fresh/test123');
    console.log('2. Go to Multiplayer Mode');
    console.log('3. Create a room');
    console.log('4. Mark ready');
    console.log('5. Click Start Game');
    console.log('\nWatching for edge function calls...');
    
    // Keep browser open for manual testing
    await new Promise(resolve => setTimeout(resolve, 300000)); // 5 minutes
    
  } finally {
    await browser.close();
  }
}

checkEdgeFunction().catch(console.error);