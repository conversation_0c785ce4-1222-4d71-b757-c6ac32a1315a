#!/bin/bash

# Test Edge Function with curl to see exact error

echo "=== Testing Edge Function Directly ==="
echo ""

# First, we need to get a valid auth token
# This would normally come from a logged-in user
# For testing, we'll use the anon key which won't work but will show us the error

SUPABASE_URL="https://xmyxuvuimebjltnaamox.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjcyOTA2NjUsImV4cCI6MjA0Mjg2NjY2NX0.uI_gQzFkKlKMTb7hQItE6OtivgjLGByTNRi9Wvv18B4"

# Test room ID
ROOM_ID="test-room-$(date +%s)"

echo "Testing with room ID: $ROOM_ID"
echo "URL: $SUPABASE_URL/functions/v1/start-game-handler"
echo ""

# Make the request
curl -i -X POST "$SUPABASE_URL/functions/v1/start-game-handler" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
  -H "apikey: $SUPABASE_ANON_KEY" \
  -d "{\"roomId\": \"$ROOM_ID\"}" 2>&1

echo ""
echo "=== End of test ==="