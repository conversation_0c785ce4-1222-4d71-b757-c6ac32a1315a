# Check recent multiplayer games in database

Write-Host "Checking recent multiplayer games..." -ForegroundColor Cyan

# Note: You'll need to run this from Supabase SQL editor or dashboard
Write-Host @"

To verify games are working correctly, run these queries in Supabase SQL Editor:

1. Check recent game rooms:
----------------------------------------
SELECT 
    id,
    status,
    current_round_number,
    created_at,
    last_activity_timestamp,
    jsonb_array_length(current_round_answers) as answers_count,
    player_scores
FROM game_rooms
WHERE created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC
LIMIT 10;
----------------------------------------

2. Check recent player activity:
----------------------------------------
SELECT 
    gr.id as room_id,
    gp.user_id,
    gp.is_ready,
    gp.connection_status,
    gp.last_heartbeat,
    gr.status as room_status
FROM game_players gp
JOIN game_rooms gr ON gp.room_id = gr.id
WHERE gr.created_at > NOW() - INTERVAL '1 hour'
ORDER BY gr.created_at DESC;
----------------------------------------

3. Check if answers are being recorded:
----------------------------------------
SELECT 
    id,
    current_round_answers,
    player_scores,
    player_bonus_levels,
    transition_deadline
FROM game_rooms
WHERE status = 'active' 
   OR (status = 'finished' AND updated_at > NOW() - INTERVAL '10 minutes')
ORDER BY updated_at DESC
LIMIT 5;
----------------------------------------

Press Enter to open Supabase SQL Editor...
"@ -ForegroundColor Yellow

Read-Host

$dashboardUrl = "https://supabase.com/dashboard/project/$projectRef/sql/new"
Start-Process $dashboardUrl

Write-Host "`nSQL Editor opened. Run the queries above to verify:" -ForegroundColor Green
Write-Host "- Game rooms are being created" -ForegroundColor White
Write-Host "- Players are joining rooms" -ForegroundColor White
Write-Host "- Answers are being recorded in current_round_answers" -ForegroundColor White
Write-Host "- Scores are updating in player_scores" -ForegroundColor White
Write-Host "- Transition deadlines are being set" -ForegroundColor White