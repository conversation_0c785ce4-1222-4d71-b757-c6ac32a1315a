/**
 * Working Automated Multiplayer Test
 * Fixed to handle room names instead of codes
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Test configuration
const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → Round at 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → Round at 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → Round at 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'Only P1 → Round at 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1 at 1s, P2 at 3s → Round at 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

// Create screenshots directory
const screenshotsDir = path.join(__dirname, 'test-screenshots-working');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

let screenshotCounter = 0;
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

async function takeScreenshot(page, name) {
  const filename = `${String(screenshotCounter++).padStart(3, '0')}-${name}.png`;
  const filepath = path.join(screenshotsDir, filename);
  await page.screenshot({ path: filepath, fullPage: true });
  console.log(`  📸 Screenshot: ${filename}`);
}

async function signIn(page, credentials, playerName) {
  console.log(`\n🔐 Signing in ${playerName}...`);
  
  // Click Login button
  await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const loginBtn = buttons.find(b => b.textContent && b.textContent.includes('Login'));
    if (loginBtn) loginBtn.click();
  });
  
  await delay(2000);
  
  // Fill credentials
  await page.type('input[placeholder="Username or Email"]', credentials.email);
  await page.type('input[placeholder="Password"]', credentials.password);
  
  // Submit form
  await page.evaluate(() => {
    const form = document.querySelector('form');
    if (form) form.requestSubmit();
  });
  
  await delay(5000);
  console.log(`  ✅ ${playerName} signed in`);
}

async function navigateToMultiplayer(page, playerName) {
  console.log(`  ${playerName} → Multiplayer Mode`);
  
  await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const mpBtn = buttons.find(b => b.textContent === 'Multiplayer Mode');
    if (mpBtn) mpBtn.click();
  });
  
  await delay(3000);
}

async function createRoom(page) {
  console.log('\n🏠 Creating room...');
  
  // Click Host Game
  await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const hostBtn = buttons.find(b => b.textContent && b.textContent.trim() === 'Host Game');
    if (hostBtn) hostBtn.click();
  });
  
  await delay(3000);
  await takeScreenshot(page, 'room-created');
  
  // Room name will be "fresh's Game"
  return "fresh's Game";
}

async function joinRoom(page, roomName) {
  console.log(`  Player 2 joining "${roomName}"...`);
  
  // Click on the room in the lobby
  const joined = await page.evaluate((name) => {
    // Look for the room in the game lobby
    const roomElements = Array.from(document.querySelectorAll('*'));
    for (const el of roomElements) {
      if (el.textContent && el.textContent.includes(name) && el.textContent.includes('Players:')) {
        el.click();
        return true;
      }
    }
    return false;
  }, roomName);
  
  if (!joined) {
    // Try alternative: look for clickable element with room name
    await page.evaluate((name) => {
      const elements = Array.from(document.querySelectorAll('button, div[role="button"], div[onclick]'));
      const roomEl = elements.find(el => el.textContent && el.textContent.includes(name));
      if (roomEl) roomEl.click();
    }, roomName);
  }
  
  await delay(3000);
  await takeScreenshot(page, 'player2-joined');
}

// Monitoring code
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0,
    submissions: []
  };
  
  console.log('🔍 Monitoring active');
  
  let lastQuestion = null;
  setInterval(() => {
    const questionEl = document.querySelector('h2');
    const question = questionEl?.textContent || '';
    
    if (question && question.includes('?') && question !== lastQuestion) {
      const now = Date.now();
      
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('🎮 Game started');
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({
          round: window.testData.currentRound++,
          time: elapsed
        });
        console.log('📍 Round ' + (window.testData.currentRound) + ' at ' + elapsed + 'ms');
      }
      
      lastQuestion = question;
    }
  }, 100);
  
  window.submitAnswerAt = function(targetMs) {
    return new Promise((resolve) => {
      if (!window.testData.gameStartTime) {
        setTimeout(() => window.submitAnswerAt(targetMs).then(resolve), 500);
        return;
      }
      
      const elapsed = Date.now() - window.testData.gameStartTime;
      const delay = targetMs - elapsed;
      
      const submit = () => {
        const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
          const text = b.textContent || '';
          return text.length > 0 && 
                 text.match(/^[A-Za-z\\s\\.\\-\\']+$/) && 
                 !['Ready', 'Start', 'Mode', 'Sign', 'Create', 'Join', 'Leave'].some(word => text.includes(word)) &&
                 b.offsetParent !== null;
        });
        
        if (buttons.length >= 4) {
          const chosen = buttons[Math.floor(Math.random() * 4)];
          console.log('✅ Submitting: ' + chosen.textContent);
          chosen.click();
          resolve(true);
        } else {
          console.log('⚠️ No answer buttons found, retrying...');
          setTimeout(() => submit(), 500);
        }
      };
      
      if (delay > 0) {
        setTimeout(submit, delay);
      } else {
        submit();
      }
    });
  };
`;

async function runTest() {
  console.log('🎯 Working Automated Multiplayer Test');
  console.log('====================================\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Browser options
    const browserOptions = {
      headless: false,
      executablePath: '/usr/bin/chromium-browser',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--window-size=1000,800'
      ]
    };
    
    // Launch browsers
    console.log('🚀 Launching browsers...');
    browser1 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=0,0']
    });
    
    browser2 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=1000,0']
    });
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    
    // Navigate to game
    console.log('📱 Loading game...');
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2' }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2' })
    ]);
    
    await delay(3000);
    
    // Inject monitoring
    await page1.evaluate(monitoringCode);
    await page2.evaluate(monitoringCode);
    
    // Sign in both players
    await signIn(page1, CONFIG.player1, 'Player 1');
    await signIn(page2, CONFIG.player2, 'Player 2');
    
    // Navigate to multiplayer
    console.log('\n🎮 Switching to Multiplayer Mode...');
    await navigateToMultiplayer(page1, 'Player 1');
    await navigateToMultiplayer(page2, 'Player 2');
    
    // Create room with Player 1
    const roomName = await createRoom(page1);
    console.log(`  ✅ Room created: "${roomName}"`);
    
    // Join room with Player 2
    await joinRoom(page2, roomName);
    console.log('  ✅ Player 2 joined room');
    
    // Ready up
    console.log('\n🎮 Getting ready...');
    await delay(1000);
    
    // Both players ready
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready' || b.textContent === 'Ready Up'
      );
      if (btn) btn.click();
    });
    
    await page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready' || b.textContent === 'Ready Up'
      );
      if (btn) btn.click();
    });
    
    await delay(2000);
    
    // Host starts game
    console.log('  Starting game...');
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Start Game'
      );
      if (btn) btn.click();
    });
    
    await delay(3000);
    await takeScreenshot(page1, 'game-started');
    console.log('  ✅ Game started!\n');
    
    // Run test scenarios
    console.log('📊 Running test scenarios:');
    console.log('========================');
    
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const scenario = CONFIG.scenarios[i];
      console.log(`\nTest ${i + 1}: ${scenario.name}`);
      
      // Schedule submissions
      const submissions = [];
      
      if (scenario.p1 !== null) {
        submissions.push(page1.evaluate(`window.submitAnswerAt(${scenario.p1})`));
        console.log(`  P1 answers at ${scenario.p1}ms`);
      }
      
      if (scenario.p2 !== null) {
        submissions.push(page2.evaluate(`window.submitAnswerAt(${scenario.p2})`));
        console.log(`  P2 answers at ${scenario.p2}ms`);
      }
      
      await Promise.all(submissions);
      
      console.log('  Waiting for round...');
      await delay(8000);
      
      // Get results
      const timing = await page1.evaluate((index) => {
        const changes = window.testData.roundChanges || [];
        return changes[index];
      }, i);
      
      if (timing) {
        const diff = Math.abs(timing.time - scenario.expected);
        const passed = diff <= 500;
        
        console.log(`  Expected: ${scenario.expected}ms`);
        console.log(`  Actual: ${timing.time}ms`);
        console.log(`  Diff: ${diff}ms`);
        console.log(`  ${passed ? '✅ PASS' : '❌ FAIL'}`);
        
        results.push({ 
          ...scenario, 
          actual: timing.time, 
          difference: diff,
          passed 
        });
      } else {
        console.log('  ❌ No round change');
        results.push({ 
          ...scenario, 
          actual: null, 
          passed: false 
        });
      }
    }
    
    // Summary
    console.log('\n\n📈 Final Results');
    console.log('===============');
    
    const passed = results.filter(r => r.passed).length;
    console.log(`Total: ${results.length} tests`);
    console.log(`Passed: ${passed} ✅`);
    console.log(`Failed: ${results.length - passed} ❌`);
    console.log(`Success Rate: ${(passed/results.length * 100).toFixed(0)}%\n`);
    
    results.forEach((r, i) => {
      console.log(`${i + 1}. ${r.name}: ${r.passed ? '✅' : '❌'}`);
      if (r.actual !== null) {
        console.log(`   ${r.expected}ms → ${r.actual}ms (±${r.difference}ms)`);
      }
    });
    
    console.log(`\n📸 Screenshots: ${screenshotsDir}`);
    console.log('\n✅ Test complete! Press Ctrl+C to exit.\n');
    
    // Keep alive
    process.on('SIGINT', async () => {
      if (browser1) await browser1.close();
      if (browser2) await browser2.close();
      process.exit(0);
    });
    
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (page1) await takeScreenshot(page1, 'error-p1');
    if (page2) await takeScreenshot(page2, 'error-p2');
    
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    
    process.exit(1);
  }
}

// Run test
runTest().catch(console.error);