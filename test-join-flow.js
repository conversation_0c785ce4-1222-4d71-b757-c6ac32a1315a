const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };

// Helper to extract console logs
async function extractConsoleLogs(page) {
  const logs = [];
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('JOIN') || 
        text.includes('join') ||
        text.includes('ROOM') ||
        text.includes('room') ||
        text.includes('ERROR') ||
        text.includes('Failed')) {
      logs.push({ type: msg.type(), text });
    }
  });
  return logs;
}

async function testJoinFlow() {
  console.log('=== JOIN FLOW DEBUG TEST ===\n');
  
  const browserOptions = {
    headless: true,
    defaultViewport: { width: 1200, height: 900 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  };

  // Find browser
  let executablePath = '/usr/bin/chromium-browser';
  try {
    await fs.access(executablePath);
    browserOptions.executablePath = executablePath;
  } catch (e) {
    // Use bundled browser
  }

  let hostBrowser, guestBrowser;
  let hostPage, guestPage;

  try {
    // Launch browsers
    hostBrowser = await puppeteer.launch(browserOptions);
    guestBrowser = await puppeteer.launch(browserOptions);
    
    hostPage = await hostBrowser.newPage();
    guestPage = await guestBrowser.newPage();
    
    // Set up console logging
    const hostLogs = await extractConsoleLogs(hostPage);
    const guestLogs = await extractConsoleLogs(guestPage);
    
    // Navigate both
    await Promise.all([
      hostPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' }),
      guestPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' })
    ]);
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === STEP 1: Quick auth for both ===
    console.log('=== STEP 1: Authenticating both users ===');
    
    // Helper to login
    async function quickLogin(page, credentials, name) {
      // Click Multiplayer Mode
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Fill credentials
      await page.type('input[type="email"], input[type="text"]:not([type="password"])', credentials.username);
      await page.type('input[type="password"]', credentials.password);
      await page.keyboard.press('Enter');
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Click Multiplayer Mode again if needed
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log(`[${name}] Authenticated`);
    }
    
    await quickLogin(hostPage, HOST_CREDENTIALS, 'HOST');
    await quickLogin(guestPage, GUEST_CREDENTIALS, 'GUEST');
    
    // === STEP 2: Host creates room ===
    console.log('\n=== STEP 2: Host creating room ===');
    
    const roomCreated = await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Host Game');
      if (btn) {
        btn.click();
        return true;
      }
      return false;
    });
    
    if (!roomCreated) {
      throw new Error('Host could not create room');
    }
    
    console.log('[HOST] Room created');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check host state
    const hostState = await hostPage.evaluate(() => {
      const playerCount = document.body.textContent.match(/Players.*\((\d+)\/\d+\)/);
      return {
        playerCount: playerCount ? playerCount[0] : 'Not found',
        inRoom: document.body.textContent.includes('Room:')
      };
    });
    
    console.log('[HOST] State after create:', hostState);
    
    // === STEP 3: Guest refreshes and looks for room ===
    console.log('\n=== STEP 3: Guest looking for room ===');
    
    // Refresh room list
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Refresh List');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check what guest sees
    const guestLobbyView = await guestPage.evaluate(() => {
      // Find all elements that might be rooms
      const roomElements = Array.from(document.querySelectorAll('*'))
        .filter(el => {
          const text = el.textContent || '';
          return text.includes('fresh') && text.includes('Game');
        })
        .map(el => ({
          tag: el.tagName,
          className: el.className,
          text: el.textContent.substring(0, 100)
        }));
      
      return {
        roomElements,
        hasJoinButton: !!Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Join')
      };
    });
    
    console.log('[GUEST] Lobby view:', JSON.stringify(guestLobbyView, null, 2));
    
    // === STEP 4: Guest attempts to join ===
    console.log('\n=== STEP 4: Guest attempting to join ===');
    
    // Try clicking on room
    const roomClicked = await guestPage.evaluate(() => {
      // Find clickable room element
      const roomElements = Array.from(document.querySelectorAll('div, tr, button'))
        .filter(el => el.textContent.includes('fresh') && el.textContent.includes('Game'));
      
      // Try to find the most specific one
      let roomToClick = roomElements.find(el => 
        el.tagName === 'TR' || 
        el.className.includes('room') ||
        el.onclick !== null
      ) || roomElements[0];
      
      if (roomToClick) {
        console.log('Clicking on room element:', roomToClick.tagName, roomToClick.textContent.substring(0, 50));
        roomToClick.click();
        return true;
      }
      
      return false;
    });
    
    console.log('[GUEST] Room clicked:', roomClicked);
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Now click Join button
    const joinClicked = await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Join' || b.textContent.includes('Join'));
      if (btn) {
        console.log('Clicking Join button');
        btn.click();
        return true;
      }
      return false;
    });
    
    console.log('[GUEST] Join clicked:', joinClicked);
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === STEP 5: Check final states ===
    console.log('\n=== STEP 5: Checking final states ===');
    
    const hostFinalState = await hostPage.evaluate(() => {
      const playerCount = document.body.textContent.match(/Players.*\((\d+)\/\d+\)/);
      const playersList = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('fresh') && 
                     (el.textContent.includes('(You)') || 
                      el.textContent.includes('Host') ||
                      el.textContent.includes('Ready')))
        .map(el => el.textContent.trim());
      
      return {
        playerCount: playerCount ? playerCount[0] : 'Not found',
        playersList,
        inRoom: document.body.textContent.includes('Room:'),
        hasStartButton: !!Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Start Game'))
      };
    });
    
    const guestFinalState = await guestPage.evaluate(() => {
      const inRoom = document.body.textContent.includes('Room:') || 
                    document.body.textContent.includes('Ready') ||
                    document.body.textContent.includes('Leave Game');
      
      const playersList = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('fresh') && 
                     (el.textContent.includes('(You)') || 
                      el.textContent.includes('Host') ||
                      el.textContent.includes('Ready')))
        .map(el => el.textContent.trim());
      
      return {
        inRoom,
        playersList,
        inLobby: document.body.textContent.includes('Game Lobby'),
        pageTitle: document.body.textContent.substring(0, 200)
      };
    });
    
    console.log('\n[HOST] Final state:', JSON.stringify(hostFinalState, null, 2));
    console.log('\n[GUEST] Final state:', JSON.stringify(guestFinalState, null, 2));
    
    // Print console logs
    console.log('\n=== Console Logs ===');
    console.log('[HOST] Logs:', hostLogs.slice(-10));
    console.log('[GUEST] Logs:', guestLogs.slice(-10));
    
    // Determine success
    if (guestFinalState.inRoom && hostFinalState.playerCount.includes('2/')) {
      console.log('\n✅ SUCCESS: Guest joined room successfully!');
    } else {
      console.log('\n❌ FAILED: Guest did not join room');
      
      // Additional debugging
      const guestError = await guestPage.evaluate(() => {
        // Look for any error messages
        const errorElements = Array.from(document.querySelectorAll('*'))
          .filter(el => el.textContent.includes('Error') || 
                       el.textContent.includes('failed') ||
                       el.textContent.includes('Cannot'))
          .map(el => el.textContent.substring(0, 100));
        return errorElements;
      });
      
      if (guestError.length > 0) {
        console.log('\nFound errors:', guestError);
      }
    }
    
  } catch (error) {
    console.error('\n❌ Test error:', error.message);
  } finally {
    if (hostBrowser) await hostBrowser.close();
    if (guestBrowser) await guestBrowser.close();
  }
}

// Run test
testJoinFlow().catch(console.error);