// Test Edge Function with proper authentication
const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://xmyxuvuimebjltnaamox.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY';

async function testEdgeFunction() {
  console.log('=== Testing Edge Function with Authentication ===\n');
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  // Sign in as test user
  console.log('1. Signing in as test user...');
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'password123'
  });
  
  if (authError) {
    console.error('❌ Failed to sign in:', authError);
    return;
  }
  
  console.log('✅ Signed in successfully');
  console.log('   User ID:', authData.user?.id);
  console.log('   Session:', authData.session ? 'Valid' : 'Invalid');
  
  // Create a test room first
  console.log('\n2. Creating test room...');
  const roomCode = Math.floor(1000 + Math.random() * 9000).toString();
  const { data: roomData, error: roomError } = await supabase
    .from('game_rooms')
    .insert({
      code: roomCode,
      host_id: authData.user.id,
      created_by: authData.user.id,
      status: 'waiting',
      current_round_number: 0,
      player_scores: {},
      name: 'Test Room'
    })
    .select()
    .single();
    
  if (roomError) {
    console.error('❌ Failed to create room:', roomError);
    return;
  }
  
  console.log('✅ Room created successfully');
  console.log('   Room ID:', roomData.id);
  console.log('   Room Code:', roomData.code);
  
  // Join the room
  console.log('\n3. Joining room as player...');
  const { error: joinError } = await supabase
    .from('game_players')
    .insert({
      room_id: roomData.id,
      user_id: authData.user.id,
      is_ready: true,
      is_connected: true
    });
    
  if (joinError) {
    console.error('❌ Failed to join room:', joinError);
    return;
  }
  
  console.log('✅ Joined room successfully');
  
  // Call the Edge Function
  console.log('\n4. Calling start-game-handler Edge Function...');
  console.log('   Room ID:', roomData.id);
  console.log('   Auth Token:', authData.session?.access_token ? 'Present' : 'Missing');
  
  const { data, error } = await supabase.functions.invoke('start-game-handler', {
    body: { roomId: roomData.id }
  });
  
  if (error) {
    console.error('\n❌ Edge Function returned error:');
    console.error('   Error:', error);
    console.error('   Message:', error.message);
    console.error('   Status:', error.status);
    console.error('   Details:', JSON.stringify(error, null, 2));
  } else {
    console.log('\n✅ Edge Function called successfully!');
    console.log('   Response:', JSON.stringify(data, null, 2));
  }
  
  // Cleanup
  console.log('\n5. Cleaning up...');
  await supabase.from('game_rooms').delete().eq('id', roomData.id);
  await supabase.auth.signOut();
  console.log('✅ Cleanup complete');
}

testEdgeFunction().catch(console.error);