// Simplified test to capture Edge Function error
const puppeteer = require('puppeteer');

async function testEdge() {
  console.log('=== Testing Edge Function Error ===\n');
  
  const browser = await puppeteer.launch({
    headless: false, // Run in headed mode to see what's happening
    executablePath: process.platform === 'linux' ? '/usr/bin/chromium-browser' : undefined,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Log console messages
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('Error') || text.includes('error') || text.includes('Edge Function')) {
        console.log('[Browser]', text);
      }
    });
    
    // Log network responses
    page.on('response', async response => {
      if (response.url().includes('start-game-handler')) {
        console.log('\n=== Edge Function Response ===');
        console.log('Status:', response.status());
        try {
          const body = await response.text();
          console.log('Body:', body);
        } catch (e) {
          console.log('Could not read body');
        }
      }
    });
    
    await page.goto('http://localhost:3000');
    
    console.log('\nPlease manually:');
    console.log('1. Click "Multiplayer Mode"');
    console.log('2. Login with fresh/test123');
    console.log('3. Create a room');
    console.log('4. Mark ready');
    console.log('5. Click "Start Game"');
    console.log('\nWatch the console output above for the Edge Function response.\n');
    
    // Keep browser open for manual testing
    await new Promise(r => setTimeout(r, 60000));
    
  } finally {
    await browser.close();
  }
}

testEdge().catch(console.error);