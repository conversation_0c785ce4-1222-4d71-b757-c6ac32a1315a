# Deploy using Supabase Dashboard method
param(
    [string]$FunctionName = "start-game-handler"
)

Write-Host @"

Since the API deployment is returning 404, let's use the Supabase Dashboard method:

MANUAL DEPLOYMENT STEPS:
========================

1. Open Supabase Dashboard:
   https://app.supabase.com/project/xmyxuvuimebjltnaamox/functions

2. Click on '$FunctionName' (or create it if it doesn't exist)

3. Click 'Deploy' or 'Edit Function'

4. Copy the entire contents of this file:
   $PSScriptRoot\functions\$FunctionName\index.ts

5. Paste into the editor and click 'Deploy'

"@ -ForegroundColor Cyan

# Open the function file in notepad for easy copying
$functionPath = Join-Path $PSScriptRoot "functions\$FunctionName\index.ts"
if (Test-Path $functionPath) {
    Write-Host "Opening function file in notepad for easy copying..." -ForegroundColor Yellow
    notepad $functionPath
    
    Write-Host @"

The function file is now open in Notepad.
1. Press Ctrl+A to select all
2. Press Ctrl+C to copy
3. <PERSON><PERSON> into the Supabase Dashboard editor
4. Click 'Deploy'

Dashboard URL: https://app.supabase.com/project/xmyxuvuimebjltnaamox/functions/$FunctionName

"@ -ForegroundColor Green
}

# Also create a curl command as alternative
Write-Host @"

ALTERNATIVE: Deploy using curl command
======================================

If you have curl installed, you can also try:

"@ -ForegroundColor Yellow

$functionCode = Get-Content $functionPath -Raw -ErrorAction SilentlyContinue
if ($functionCode) {
    # Escape the function code for JSON
    $escapedCode = $functionCode -replace '"', '\"' -replace "`r`n", "\n"
    
    # Save curl command to file
    $curlCmd = @"
curl -X POST https://xmyxuvuimebjltnaamox.supabase.co/functions/v1/deploy \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "$FunctionName",
    "code": "$escapedCode"
  }'
"@
    
    $curlFile = Join-Path $PSScriptRoot "deploy-$FunctionName-curl.sh"
    $curlCmd | Out-File -FilePath $curlFile -Encoding UTF8
    
    Write-Host "Curl command saved to: $curlFile" -ForegroundColor Gray
    Write-Host "(You'll need to replace YOUR_SERVICE_ROLE_KEY with your actual service role key)" -ForegroundColor Yellow
}