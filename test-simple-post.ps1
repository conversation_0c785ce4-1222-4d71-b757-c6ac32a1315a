# Simple POST test
Write-Host "Testing simple POST request..." -ForegroundColor Green

try {
    $body = @{
        identifier = "<EMAIL>"
        password = "wrongpassword"
    } | ConvertTo-Json

    Write-Host "Request body: $body" -ForegroundColor Yellow

    $response = Invoke-WebRequest -Uri "http://127.0.0.1:54321/functions/v1/login-handler" -Method POST -Body $body -ContentType "application/json" -Headers @{"Origin"="http://localhost:3000"} -UseBasicParsing -ErrorAction SilentlyContinue

    if ($response) {
        Write-Host "Response Status: $($response.StatusCode)" -ForegroundColor Cyan
        Write-Host "Response Headers:" -ForegroundColor Cyan
        $response.Headers | Format-Table -AutoSize
        Write-Host "Response Content:" -ForegroundColor Cyan
        Write-Host $response.Content
    }
} catch {
    Write-Host "Request failed with error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "Error Response Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Yellow
        Write-Host "Error Response Headers:" -ForegroundColor Yellow
        $_.Exception.Response.Headers | Format-Table -AutoSize
        
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorContent = $reader.ReadToEnd()
            Write-Host "Error Response Content: $errorContent" -ForegroundColor Yellow
        } catch {
            Write-Host "Could not read error response content" -ForegroundColor Red
        }
    }
}

Write-Host "Test completed." -ForegroundColor Green 