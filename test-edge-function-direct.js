// Direct test of the start-game-handler Edge Function
const fetch = require('node-fetch');

async function testEdgeFunction() {
  console.log('=== Testing Edge Function Directly ===\n');
  
  const SUPABASE_URL = 'https://uyxxkvjvmxtbuywjqsgl.supabase.co';
  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV5eHhrdmp2bXh0YnV5d2pxc2dsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDg2MTIyNzMsImV4cCI6MjAyNDE4ODI3M30.5fvYgVDN0djCHOVbFvBdcAajMUXxF12UNJsLuKJb8Hc';
  
  // Test room ID (you might need to update this with a real room ID)
  const roomId = 'test-room-' + Date.now();
  
  const url = `${SUPABASE_URL}/functions/v1/start-game-handler`;
  
  console.log('Testing Edge Function at:', url);
  console.log('With room ID:', roomId);
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({ roomId })
    });
    
    console.log('\nResponse status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('\nResponse body:', responseText);
    
    if (!response.ok) {
      console.log('\n❌ Edge Function returned error status:', response.status);
      
      // Try to parse as JSON for error details
      try {
        const errorData = JSON.parse(responseText);
        console.log('Error details:', JSON.stringify(errorData, null, 2));
      } catch (e) {
        console.log('Response is not JSON');
      }
    } else {
      console.log('\n✅ Edge Function called successfully');
    }
    
  } catch (error) {
    console.error('\n❌ Failed to call Edge Function:', error);
  }
}

// Check if node-fetch is installed
try {
  require.resolve('node-fetch');
  testEdgeFunction();
} catch(e) {
  console.log('Installing node-fetch...');
  const { execSync } = require('child_process');
  execSync('npm install node-fetch@2', { stdio: 'inherit' });
  console.log('Please run the script again');
}