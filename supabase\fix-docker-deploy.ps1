# Fix Docker and deploy

Write-Host "Attempting to fix Docker Desktop issue..." -ForegroundColor Cyan

# Option 1: Start Docker Desktop
Write-Host "`nOption 1: Starting Docker Desktop..." -ForegroundColor Yellow
try {
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe" -ErrorAction SilentlyContinue
    Write-Host "Docker Desktop starting. Wait 30 seconds..." -ForegroundColor Yellow
    Start-Sleep -Seconds 30
} catch {
    Write-Host "Could not start Docker Desktop automatically" -ForegroundColor Yellow
}

# Option 2: Deploy without Docker using --legacy-bundle flag
Write-Host "`nOption 2: Deploying with legacy bundle (no Docker needed)..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"

# Use legacy bundle mode which doesn't require Docker
$deployCmd = "npx supabase functions deploy submit-answer-handler --project-ref $projectRef --no-verify-jwt --legacy-bundle"

Write-Host "Running: $deployCmd" -ForegroundColor Gray
Invoke-Expression $deployCmd

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nSuccess! Function deployed to: https://$projectRef.supabase.co/functions/v1/submit-answer-handler" -ForegroundColor Green
} else {
    Write-Host "`nIf deployment still fails, use ./deploy-dashboard.ps1 for manual deployment" -ForegroundColor Yellow
}