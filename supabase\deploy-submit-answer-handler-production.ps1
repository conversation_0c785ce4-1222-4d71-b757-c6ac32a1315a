# Deploy submit-answer-handler Edge Function directly to production
# This bypasses local Docker checks

Write-Host "Direct production deployment of submit-answer-handler..." -ForegroundColor Cyan

# Your project reference
$projectRef = "xmyxuvuimebjltnaamox"

Write-Host "Target project: $projectRef" -ForegroundColor Yellow
Write-Host "Skipping local status checks..." -ForegroundColor Yellow

# Deploy directly to production without local checks
Write-Host "`nDeploying Edge Function..." -ForegroundColor Cyan
npx supabase functions deploy submit-answer-handler --no-verify-jwt --project-ref $projectRef

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nsubmit-answer-handler deployed successfully to production!" -ForegroundColor Green
    Write-Host "Function URL: https://$projectRef.supabase.co/functions/v1/submit-answer-handler" -ForegroundColor Blue
} else {
    Write-Host "`nDeployment failed!" -ForegroundColor Red
    Write-Host "Try running 'npx supabase login' first if authentication failed" -ForegroundColor Yellow
    exit 1
}