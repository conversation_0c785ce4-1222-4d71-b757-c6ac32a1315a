const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;

// Create screenshots directory
const screenshotsDir = path.join(__dirname, 'debug-screenshots');

async function ensureDir(dir) {
  try {
    await fs.mkdir(dir, { recursive: true });
  } catch (err) {
    console.log('Directory already exists or error:', err.message);
  }
}

async function takeScreenshot(page, filename, label) {
  const filepath = path.join(screenshotsDir, filename);
  await page.screenshot({ path: filepath, fullPage: false });
  console.log(`[SCREENSHOT] ${label} saved to: ${filename}`);
}

async function testAnswerSubmission() {
  console.log('[TEST] Starting answer submission state test...');
  
  await ensureDir(screenshotsDir);
  
  const browser = await puppeteer.launch({
    headless: false, // Set to false to see what's happening
    defaultViewport: { width: 1280, height: 800 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Enable console logging from the page
    page.on('console', msg => {
      if (msg.text().includes('SUBMISSION_STATE') || 
          msg.text().includes('CHOICE_BUTTON_CLICK') ||
          msg.text().includes('Answer submitted') ||
          msg.text().includes('hasSubmittedCurrentRound')) {
        console.log(`[PAGE LOG] ${msg.text()}`);
      }
    });

    // Navigate to the app
    console.log('[TEST] Navigating to app...');
    await page.goto('http://localhost:3001', { waitUntil: 'networkidle2' });
    
    // Wait for the page to load
    await page.waitForTimeout(2000);
    
    // Click on Multiplayer Mode
    console.log('[TEST] Clicking Multiplayer Mode...');
    await page.waitForSelector('button:has-text("Multiplayer Mode")', { timeout: 10000 });
    await page.click('button:has-text("Multiplayer Mode")');
    
    await page.waitForTimeout(1000);
    
    // Look for any room or create new room button
    console.log('[TEST] Looking for game room...');
    
    // Try to find an existing room or create new room button
    const roomSelector = 'button:has-text("Join"), button:has-text("Create Room"), button:has-text("Start Game")';
    await page.waitForSelector(roomSelector, { timeout: 10000 });
    
    // Click the first available room or create button
    await page.click(roomSelector);
    
    // Wait for game to start or start it
    await page.waitForTimeout(2000);
    
    // Check if we need to start the game
    const startButton = await page.$('button:has-text("Start Game")');
    if (startButton) {
      console.log('[TEST] Starting game...');
      await startButton.click();
      await page.waitForTimeout(2000);
    }
    
    // Wait for the question and choice buttons to appear
    console.log('[TEST] Waiting for question to load...');
    await page.waitForSelector('.grid.grid-cols-2 button', { timeout: 15000 });
    
    // Get all choice buttons
    const choiceButtons = await page.$$('.grid.grid-cols-2 button');
    console.log(`[TEST] Found ${choiceButtons.length} choice buttons`);
    
    if (choiceButtons.length === 0) {
      throw new Error('No choice buttons found!');
    }
    
    // Take initial screenshot
    await takeScreenshot(page, '0-before-click.png', 'Before clicking answer');
    
    // Click the first choice button
    console.log('[TEST] Clicking first choice button...');
    await choiceButtons[0].click();
    
    // Take screenshots at different time intervals
    const timestamps = [
      { delay: 0, name: '1-immediate.png', label: 'Immediately after click (t=0)' },
      { delay: 230, name: '2-after-230ms.png', label: 'After 230ms' },
      { delay: 490, name: '3-after-720ms.png', label: 'After 720ms total' },
      { delay: 280, name: '4-after-1000ms.png', label: 'After 1000ms total' }
    ];
    
    for (const { delay, name, label } of timestamps) {
      if (delay > 0) {
        await page.waitForTimeout(delay);
      }
      await takeScreenshot(page, name, label);
      
      // Check button states
      const buttonStates = await page.evaluate(() => {
        const buttons = document.querySelectorAll('.grid.grid-cols-2 button');
        return Array.from(buttons).map(btn => ({
          text: btn.textContent,
          disabled: btn.disabled,
          className: btn.className,
          opacity: window.getComputedStyle(btn).opacity,
          pointerEvents: window.getComputedStyle(btn).pointerEvents
        }));
      });
      
      console.log(`[TEST] Button states at ${label}:`, JSON.stringify(buttonStates, null, 2));
      
      // Check for submission message
      const submissionMessage = await page.evaluate(() => {
        const msgEl = document.querySelector('p:has-text("Answer submitted")');
        return msgEl ? msgEl.textContent : null;
      });
      
      console.log(`[TEST] Submission message at ${label}:`, submissionMessage || 'NOT FOUND');
    }
    
    // Wait a bit more and take final screenshot
    await page.waitForTimeout(2000);
    await takeScreenshot(page, '5-after-3000ms.png', 'After 3 seconds');
    
  } catch (error) {
    console.error('[TEST] Error:', error);
    await takeScreenshot(page, 'error-state.png', 'Error state');
  } finally {
    console.log('[TEST] Test complete. Check debug-screenshots directory.');
    await browser.close();
  }
}

// Run the test
testAnswerSubmission().catch(console.error);