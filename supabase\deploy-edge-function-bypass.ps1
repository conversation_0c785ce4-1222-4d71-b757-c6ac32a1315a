# Deploy Edge Function using direct API bypass
Write-Host "Deploying Edge Function using direct API bypass..." -ForegroundColor Yellow

$projectRef = "xmyxuvuimebjltnaamox"
$functionName = "start-game-handler"

# First, let's bundle the function manually
Write-Host "Bundling Edge Function..." -ForegroundColor Cyan

# Create a temporary directory for the bundle
$tempDir = New-TemporaryFile | % { Remove-Item $_; New-Item -ItemType Directory -Path $_ }
$bundlePath = Join-Path $tempDir "bundle.ts"

Write-Host "Reading function files..." -ForegroundColor Cyan

# Read the main function file
$mainContent = Get-Content "functions\start-game-handler\index.ts" -Raw

# Read the shared CORS file
$corsContent = @'
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}
'@

# Create a bundled version with inline CORS
$bundledContent = $mainContent -replace 'import { corsHeaders } from "../_shared/cors.ts"', $corsContent

# Write the bundled content
$bundledContent | Out-File -FilePath $bundlePath -Encoding UTF8

Write-Host "Bundle created at: $bundlePath" -ForegroundColor Green

# Alternative approach: Use the Supabase Management API directly
Write-Host "`nTo deploy manually:" -ForegroundColor Yellow
Write-Host "1. Go to: https://supabase.com/dashboard/project/$projectRef/functions" -ForegroundColor Cyan
Write-Host "2. Click on 'start-game-handler'" -ForegroundColor Cyan
Write-Host "3. Click 'Deploy function'" -ForegroundColor Cyan
Write-Host "4. Or use the Supabase dashboard to upload the function" -ForegroundColor Cyan

Write-Host "`nAlternatively, try this command from a different terminal:" -ForegroundColor Yellow
Write-Host "cd $PWD" -ForegroundColor Green
Write-Host "npx supabase functions deploy start-game-handler --project-ref $projectRef" -ForegroundColor Green

# Try one more approach with a different method
Write-Host "`nTrying alternative deployment method..." -ForegroundColor Yellow
$env:SUPABASE_PROJECT_ID = $projectRef
Start-Process -FilePath "npx" -ArgumentList "supabase", "functions", "deploy", $functionName, "--project-ref", $projectRef -NoNewWindow -Wait

Write-Host "`nDeployment attempt complete." -ForegroundColor Green