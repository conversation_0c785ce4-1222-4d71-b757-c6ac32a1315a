import { test, expect } from '@playwright/test';

test.describe('Multiplayer Next Question Auto-Progression', () => {
  let hostPage: any;
  let player2Page: any;
  let player3Page: any;
  
  test.beforeEach(async ({ browser }) => {
    // Create three browser contexts for three players
    const hostContext = await browser.newContext();
    const player2Context = await browser.newContext();
    const player3Context = await browser.newContext();
    
    hostPage = await hostContext.newPage();
    player2Page = await player2Context.newPage();
    player3Page = await player3Context.newPage();
    
    // Navigate all players to the game
    await hostPage.goto('http://localhost:3000');
    await player2Page.goto('http://localhost:3000');
    await player3Page.goto('http://localhost:3000');
  });

  test.afterEach(async () => {
    await hostPage.close();
    await player2Page.close();
    await player3Page.close();
  });

  test('should automatically progress to next question after all players answer', async () => {
    // Host creates room
    await hostPage.click('button:has-text("Multiplayer Mode")');
    await hostPage.fill('input[placeholder="Enter your name"]', 'Host Player');
    await hostPage.click('button:has-text("Create Room")');
    
    // Get room code
    const roomCode = await hostPage.textContent('.room-code');
    
    // Other players join
    await player2Page.click('button:has-text("Multiplayer Mode")');
    await player2Page.fill('input[placeholder="Enter your name"]', 'Player 2');
    await player2Page.fill('input[placeholder="Room Code"]', roomCode);
    await player2Page.click('button:has-text("Join Room")');
    
    await player3Page.click('button:has-text("Multiplayer Mode")');
    await player3Page.fill('input[placeholder="Enter your name"]', 'Player 3');
    await player3Page.fill('input[placeholder="Room Code"]', roomCode);
    await player3Page.click('button:has-text("Join Room")');
    
    // Wait for all players to be ready
    await hostPage.waitForSelector('text=3/3 players');
    
    // All players mark ready
    await hostPage.click('button:has-text("Ready")');
    await player2Page.click('button:has-text("Ready")');
    await player3Page.click('button:has-text("Ready")');
    
    // Host starts game
    await hostPage.click('button:has-text("Start Game")');
    
    // Wait for first question to appear
    await hostPage.waitForSelector('.player-image');
    await player2Page.waitForSelector('.player-image');
    await player3Page.waitForSelector('.player-image');
    
    // Get initial question ID (to verify it changes)
    const initialQuestionText = await hostPage.textContent('.question-text');
    
    // All players submit answers
    await hostPage.click('.choice-button:nth-child(1)');
    await player2Page.click('.choice-button:nth-child(2)');
    await player3Page.click('.choice-button:nth-child(3)');
    
    // Wait for all submissions to be registered
    await hostPage.waitForSelector('text=All players submitted!');
    
    // Wait for automatic progression (2.3 seconds)
    await hostPage.waitForTimeout(2400);
    
    // Verify next question appeared automatically
    const newQuestionText = await hostPage.textContent('.question-text');
    expect(newQuestionText).not.toBe(initialQuestionText);
    
    // Verify all players see the new question
    await player2Page.waitForSelector('.player-image');
    await player3Page.waitForSelector('.player-image');
    
    const player2QuestionText = await player2Page.textContent('.question-text');
    const player3QuestionText = await player3Page.textContent('.question-text');
    
    expect(player2QuestionText).toBe(newQuestionText);
    expect(player3QuestionText).toBe(newQuestionText);
  });

  test('should handle tab focus/blur during auto-progression', async () => {
    // Setup game with 2 players
    await hostPage.click('button:has-text("Multiplayer Mode")');
    await hostPage.fill('input[placeholder="Enter your name"]', 'Host');
    await hostPage.click('button:has-text("Create Room")');
    
    const roomCode = await hostPage.textContent('.room-code');
    
    await player2Page.click('button:has-text("Multiplayer Mode")');
    await player2Page.fill('input[placeholder="Enter your name"]', 'Player 2');
    await player2Page.fill('input[placeholder="Room Code"]', roomCode);
    await player2Page.click('button:has-text("Join Room")');
    
    // Start game
    await hostPage.click('button:has-text("Ready")');
    await player2Page.click('button:has-text("Ready")');
    await hostPage.click('button:has-text("Start Game")');
    
    // Wait for question
    await hostPage.waitForSelector('.player-image');
    const initialQuestion = await hostPage.textContent('.question-text');
    
    // Both submit answers
    await hostPage.click('.choice-button:nth-child(1)');
    await player2Page.click('.choice-button:nth-child(2)');
    
    // Host loses focus (alt-tab simulation)
    await hostPage.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        writable: true
      });
      document.dispatchEvent(new Event('visibilitychange'));
    });
    
    // Wait 1 second
    await hostPage.waitForTimeout(1000);
    
    // Host regains focus
    await hostPage.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        value: 'visible',
        writable: true
      });
      document.dispatchEvent(new Event('visibilitychange'));
    });
    
    // Wait for remaining time until auto-progression
    await hostPage.waitForTimeout(1400);
    
    // Verify progression happened
    const newQuestion = await hostPage.textContent('.question-text');
    expect(newQuestion).not.toBe(initialQuestion);
  });

  test('should allow manual progression before auto-timer', async () => {
    // Setup game
    await hostPage.click('button:has-text("Multiplayer Mode")');
    await hostPage.fill('input[placeholder="Enter your name"]', 'Host');
    await hostPage.click('button:has-text("Create Room")');
    
    const roomCode = await hostPage.textContent('.room-code');
    
    await player2Page.click('button:has-text("Multiplayer Mode")');
    await player2Page.fill('input[placeholder="Enter your name"]', 'Player 2');
    await player2Page.fill('input[placeholder="Room Code"]', roomCode);
    await player2Page.click('button:has-text("Join Room")');
    
    // Start game
    await hostPage.click('button:has-text("Ready")');
    await player2Page.click('button:has-text("Ready")');
    await hostPage.click('button:has-text("Start Game")');
    
    // Submit answers
    await hostPage.waitForSelector('.player-image');
    await hostPage.click('.choice-button:nth-child(1)');
    await player2Page.click('.choice-button:nth-child(1)');
    
    // Host manually advances after 1 second (before auto-timer)
    await hostPage.waitForTimeout(1000);
    await hostPage.click('button:has-text("Next Question")');
    
    // Verify immediate progression
    await hostPage.waitForSelector('.player-image');
    
    // Wait for what would have been the auto-timer
    await hostPage.waitForTimeout(1500);
    
    // Verify we're still on the second question (no double progression)
    const choiceButtons = await hostPage.$$('.choice-button:not([disabled])');
    expect(choiceButtons.length).toBe(4); // Should have active choice buttons
  });

  test('should handle player disconnect during waiting period', async () => {
    // Setup 3 player game
    await hostPage.click('button:has-text("Multiplayer Mode")');
    await hostPage.fill('input[placeholder="Enter your name"]', 'Host');
    await hostPage.click('button:has-text("Create Room")');
    
    const roomCode = await hostPage.textContent('.room-code');
    
    await player2Page.click('button:has-text("Multiplayer Mode")');
    await player2Page.fill('input[placeholder="Enter your name"]', 'Player 2');
    await player2Page.fill('input[placeholder="Room Code"]', roomCode);
    await player2Page.click('button:has-text("Join Room")');
    
    await player3Page.click('button:has-text("Multiplayer Mode")');
    await player3Page.fill('input[placeholder="Enter your name"]', 'Player 3');
    await player3Page.fill('input[placeholder="Room Code"]', roomCode);
    await player3Page.click('button:has-text("Join Room")');
    
    // Start game
    await hostPage.click('button:has-text("Ready")');
    await player2Page.click('button:has-text("Ready")');
    await player3Page.click('button:has-text("Ready")');
    await hostPage.click('button:has-text("Start Game")');
    
    // All submit answers
    await hostPage.waitForSelector('.player-image');
    await hostPage.click('.choice-button:nth-child(1)');
    await player2Page.click('.choice-button:nth-child(2)');
    await player3Page.click('.choice-button:nth-child(3)');
    
    // Player 3 disconnects during waiting period
    await player3Page.close();
    await hostPage.waitForTimeout(500);
    
    // Should still auto-progress after 2.3 seconds
    await hostPage.waitForTimeout(1900);
    
    // Verify progression for remaining players
    await hostPage.waitForSelector('.player-image');
    await player2Page.waitForSelector('.player-image');
  });
});