# PowerShell script to test the stale-room-janitor Edge Function
# This script manually invokes the janitor function to test its functionality

Write-Host "🧪 Testing Stale Room Janitor Edge Function..." -ForegroundColor Cyan

try {
    Write-Host "📞 Invoking stale-room-janitor function..." -ForegroundColor Yellow
    
    # Use curl to invoke the function (you can also use Supabase CLI)
    $response = curl -X POST "https://xmyxuvuimebjltnaamox.supabase.co/functions/v1/stale-room-janitor" `
        -H "Authorization: Bearer $env:SUPABASE_ANON_KEY" `
        -H "Content-Type: application/json" `
        -d "{}" `
        --silent

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Function invoked successfully!" -ForegroundColor Green
        Write-Host "📋 Response:" -ForegroundColor Cyan
        Write-Host $response -ForegroundColor White
    } else {
        Write-Host "❌ Failed to invoke function" -ForegroundColor Red
        Write-Host "Response: $response" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error during test: $_" -ForegroundColor Red
}

Write-Host "🔍 Check the function logs in Supabase Dashboard for detailed execution info." -ForegroundColor Yellow
Write-Host "📊 Monitor: https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/functions/stale-room-janitor" -ForegroundColor Blue
