# Fix Interrupted Migrations Script
# This script resolves database issues from interrupted migration deployment

Write-Host "Database Recovery Script" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This script will fix migration sync issues and apply missing columns" -ForegroundColor Yellow
Write-Host ""

# Step 1: Check current status
Write-Host "Step 1: Checking current database status..." -ForegroundColor Yellow
Write-Host "Please run 'npx supabase db remote list' to see current migrations" -ForegroundColor Gray
Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 2: Fix migration sync issue
Write-Host "`nStep 2: Fixing migration sync issue..." -ForegroundColor Yellow
Write-Host "The remote database has migration 20250129000000 that doesn't exist locally" -ForegroundColor Gray
Write-Host "Running repair command..." -ForegroundColor Cyan

npx supabase migration repair --status reverted 20250129000000

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to repair migration. You may need to provide database password." -ForegroundColor Red
    Write-Host "Try running: npx supabase migration repair --status reverted 20250129000000 --password YOUR_PASSWORD" -ForegroundColor Yellow
    exit 1
}

# Step 3: Apply the recent migrations
Write-Host "`nStep 3: Applying recent migrations..." -ForegroundColor Yellow
Write-Host "These migrations add transition columns and fix RLS policies" -ForegroundColor Gray

$migrations = @(
    "20250629091720 - Add transition columns if not exists",
    "20250629091952 - Safe add missing columns", 
    "20250630000000 - Fix players_data access"
)

foreach ($migration in $migrations) {
    Write-Host "  - $migration" -ForegroundColor Gray
}

Write-Host "`nPushing migrations to remote database..." -ForegroundColor Cyan
npx supabase db push

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to push migrations. You may need to provide database password." -ForegroundColor Red
    Write-Host "Try running: npx supabase db push --password YOUR_PASSWORD" -ForegroundColor Yellow
    exit 1
}

# Step 4: Deploy Edge Functions
Write-Host "`nStep 4: Deploying Edge Functions..." -ForegroundColor Yellow
Write-Host "Deploying updated edge functions with transition support" -ForegroundColor Gray

$functions = @(
    "submit-answer-handler",
    "start-game-handler", 
    "next-question-handler"
)

foreach ($function in $functions) {
    Write-Host "`nDeploying $function..." -ForegroundColor Cyan
    npx supabase functions deploy $function
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to deploy $function" -ForegroundColor Red
        Write-Host "You can try the PowerShell deployment script:" -ForegroundColor Yellow
        Write-Host "  cd supabase" -ForegroundColor Gray
        Write-Host "  .\deploy-$function.ps1" -ForegroundColor Gray
    }
}

# Step 5: Verify deployment
Write-Host "`n`nStep 5: Verification" -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow
Write-Host ""
Write-Host "Database columns that should now exist in game_rooms:" -ForegroundColor Cyan
Write-Host "  - player_bonus_levels (jsonb)" -ForegroundColor Gray
Write-Host "  - transition_until (timestamptz)" -ForegroundColor Gray
Write-Host "  - next_question_data (jsonb)" -ForegroundColor Gray
Write-Host ""
Write-Host "RLS policies that should exist on players_data:" -ForegroundColor Cyan
Write-Host "  - Allow public read access to players_data" -ForegroundColor Gray
Write-Host ""
Write-Host "Edge Functions that should be deployed:" -ForegroundColor Cyan
foreach ($function in $functions) {
    Write-Host "  - $function" -ForegroundColor Gray
}

Write-Host "`n`nRecovery Complete!" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Test multiplayer game functionality" -ForegroundColor Gray
Write-Host "2. Verify 3-second transition between questions works" -ForegroundColor Gray
Write-Host "3. Check that player bonus levels are tracked" -ForegroundColor Gray
Write-Host "4. Monitor for any database connection issues" -ForegroundColor Gray
Write-Host ""
Write-Host "If you encounter any issues, check the troubleshooting guide in CLAUDE.md" -ForegroundColor Cyan