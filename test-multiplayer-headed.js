const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };

// Screenshot directory
const SCREENSHOT_DIR = path.join(__dirname, 'test-screenshots-headed');

// Helper to take screenshots
async function takeScreenshot(page, name, prefix) {
  try {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${prefix}-${name}-${timestamp}.png`;
    const filepath = path.join(SCREENSHOT_DIR, filename);
    await page.screenshot({ path: filepath });
    console.log(`[${prefix}] Screenshot saved: ${filename}`);
  } catch (error) {
    console.error(`[${prefix}] Failed to save screenshot:`, error.message);
  }
}

// Helper to wait and retry
async function waitAndRetry(fn, options = {}) {
  const { timeout = 30000, interval = 1000, description = 'operation' } = options;
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const result = await fn();
      if (result) return result;
    } catch (e) {
      // Ignore errors and retry
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  throw new Error(`Timeout waiting for ${description}`);
}

async function testMultiplayerFlow() {
  console.log('=== MULTIPLAYER GAME FLOW TEST (HEADED MODE) ===\n');
  console.log('This test will open 2 browser windows side by side');
  console.log('Testing at:', BASE_URL);
  console.log('Host:', HOST_CREDENTIALS.username);
  console.log('Guest:', GUEST_CREDENTIALS.username);
  console.log('---\n');

  // Browser launch options
  const browserOptions = {
    headless: false,
    defaultViewport: null,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-web-security',
      '--disable-features=IsolateOrigins,site-per-process'
    ]
  };

  // Platform-specific browser detection
  let executablePath;
  if (process.platform === 'win32') {
    // Windows paths
    const possiblePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
    ];
    
    for (const chromePath of possiblePaths) {
      try {
        await fs.access(chromePath);
        executablePath = chromePath;
        console.log(`Found browser at: ${chromePath}`);
        break;
      } catch (e) {
        // Continue checking other paths
      }
    }
  } else if (process.platform === 'linux') {
    // For WSL or Linux, headed mode is problematic
    console.log('Note: Running headed mode in WSL/Linux may have issues');
    console.log('Consider running from Windows PowerShell instead');
  }

  if (executablePath) {
    browserOptions.executablePath = executablePath;
  } else if (process.platform === 'win32') {
    console.log('No browser found, using Puppeteer bundled browser');
  }

  let hostBrowser, guestBrowser;
  let testPassed = false;

  try {
    // Launch browsers with window positioning
    console.log('Launching HOST browser (left side)...');
    hostBrowser = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-size=900,900', '--window-position=0,0']
    });

    console.log('Launching GUEST browser (right side)...');
    guestBrowser = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-size=900,900', '--window-position=920,0']
    });

    // Create pages
    const hostPage = await hostBrowser.newPage();
    const guestPage = await guestBrowser.newPage();

    // Set up console logging
    hostPage.on('console', msg => {
      if (msg.type() === 'error' || msg.type() === 'warning') {
        console.log(`[HOST ${msg.type()}]`, msg.text());
      }
    });
    
    guestPage.on('console', msg => {
      if (msg.type() === 'error' || msg.type() === 'warning') {
        console.log(`[GUEST ${msg.type()}]`, msg.text());
      }
    });
    
    // Monitor page errors
    hostPage.on('pageerror', err => {
      console.log('[HOST Page Error]', err.message);
    });
    
    guestPage.on('pageerror', err => {
      console.log('[GUEST Page Error]', err.message);
    });
    
    // Set default navigation timeout
    hostPage.setDefaultNavigationTimeout(60000);
    guestPage.setDefaultNavigationTimeout(60000);

    // === PHASE 1: Navigation and Initial Load ===
    console.log('\n=== PHASE 1: Navigation and Initial Load ===');
    
    console.log('[HOST] Navigating to app...');
    try {
      await hostPage.goto(BASE_URL, { 
        waitUntil: 'domcontentloaded',
        timeout: 60000 // Increase timeout to 60 seconds
      });
    } catch (e) {
      console.log('[HOST] Initial navigation had issues, but continuing...');
    }
    await takeScreenshot(hostPage, '01-initial-load', 'host');
    
    console.log('[GUEST] Navigating to app...');
    try {
      await guestPage.goto(BASE_URL, { 
        waitUntil: 'domcontentloaded',
        timeout: 60000 // Increase timeout to 60 seconds  
      });
    } catch (e) {
      console.log('[GUEST] Initial navigation had issues, but continuing...');
    }
    await takeScreenshot(guestPage, '01-initial-load', 'guest');

    // Wait for app to initialize (handle "Loading..." state)
    console.log('\nWaiting for app to initialize...');
    
    // First, give the app some time to start loading
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const startTime = Date.now();
    
    // Wait for app to be ready - check for game elements
    await waitAndRetry(async () => {
      try {
        const hostReady = await hostPage.evaluate(() => {
          // Check multiple indicators that the app is ready
          const hasLoading = document.body.textContent.includes('Loading...');
          const hasButtons = document.querySelectorAll('button').length > 0;
          const hasMultiplayerButton = Array.from(document.querySelectorAll('button'))
            .some(btn => btn.textContent.includes('Multiplayer'));
          const hasGameTitle = document.body.textContent.includes('Recognition Combine') || 
                              document.body.textContent.includes('WHO\'S THIS');
          
          // App is ready if no loading and has game elements
          return !hasLoading && (hasButtons || hasMultiplayerButton || hasGameTitle);
        });
        
        const guestReady = await guestPage.evaluate(() => {
          const hasLoading = document.body.textContent.includes('Loading...');
          const hasButtons = document.querySelectorAll('button').length > 0;
          const hasMultiplayerButton = Array.from(document.querySelectorAll('button'))
            .some(btn => btn.textContent.includes('Multiplayer'));
          const hasGameTitle = document.body.textContent.includes('Recognition Combine') || 
                              document.body.textContent.includes('WHO\'S THIS');
          
          return !hasLoading && (hasButtons || hasMultiplayerButton || hasGameTitle);
        });
        
        if (hostReady && guestReady) {
          return true;
        }
        
        // If still loading after 20 seconds, log the current state
        const elapsed = Date.now() - startTime;
        if (elapsed > 20000 && elapsed < 21000) {
          const hostState = await hostPage.evaluate(() => document.body.innerText.substring(0, 200));
          const guestState = await guestPage.evaluate(() => document.body.innerText.substring(0, 200));
          console.log('[HOST] Current state:', hostState);
          console.log('[GUEST] Current state:', guestState);
        }
        
        return false;
      } catch (e) {
        // Page might be reloading, continue waiting
        return false;
      }
    }, { description: 'app initialization', timeout: 45000, interval: 1000 });
    
    console.log('✓ App initialized');
    
    // Take screenshot after initialization
    await takeScreenshot(hostPage, '02-app-ready', 'host');
    await takeScreenshot(guestPage, '02-app-ready', 'guest');

    // === PHASE 2: Authentication ===
    console.log('\n=== PHASE 2: Authentication ===');

    // Function to handle login for a player
    async function loginPlayer(page, credentials, playerName) {
      console.log(`\n[${playerName}] Starting authentication...`);
      
      // Check if already logged in
      const isLoggedIn = await page.evaluate((username) => {
        return document.body.textContent.includes(username);
      }, credentials.username);
      
      if (isLoggedIn) {
        console.log(`[${playerName}] Already logged in`);
        return true;
      }
      
      // Click Multiplayer Mode to trigger auth modal
      console.log(`[${playerName}] Clicking Multiplayer Mode to open auth modal...`);
      
      const multiplayerClicked = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const mpBtn = buttons.find(btn => 
          btn.textContent.includes('Multiplayer Mode') || 
          btn.textContent.includes('Multiplayer')
        );
        if (mpBtn) {
          mpBtn.click();
          return true;
        }
        return false;
      });
      
      if (!multiplayerClicked) {
        await takeScreenshot(page, '02-no-multiplayer-button', playerName.toLowerCase());
        throw new Error(`[${playerName}] Could not find Multiplayer Mode button`);
      }
      
      // Wait for auth modal
      await page.waitForSelector('input[type="email"], input[type="text"], input[placeholder*="email" i], input[placeholder*="username" i]', 
        { visible: true, timeout: 5000 });
      
      console.log(`[${playerName}] Auth modal opened`);
      await takeScreenshot(page, '02-auth-modal-open', playerName.toLowerCase());
      
      // Fill credentials
      console.log(`[${playerName}] Filling credentials...`);
      
      // Find and fill email/username
      const emailInput = await page.$('input[type="email"], input[type="text"]:not([type="password"]), input[placeholder*="email" i], input[placeholder*="username" i]');
      await emailInput.click();
      await emailInput.type(credentials.username, { delay: 50 });
      
      // Find and fill password
      const passwordInput = await page.$('input[type="password"]');
      await passwordInput.click();
      await passwordInput.type(credentials.password, { delay: 50 });
      
      await takeScreenshot(page, '03-credentials-filled', playerName.toLowerCase());
      
      // Submit form
      console.log(`[${playerName}] Submitting login form...`);
      
      const submitClicked = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const submitBtn = buttons.find(btn => 
          btn.type === 'submit' || 
          btn.textContent.toLowerCase().includes('sign in') ||
          btn.textContent.toLowerCase().includes('login') ||
          btn.textContent.toLowerCase().includes('continue')
        );
        if (submitBtn) {
          submitBtn.click();
          return true;
        }
        return false;
      });
      
      if (!submitClicked) {
        console.log(`[${playerName}] No submit button found, pressing Enter`);
        await passwordInput.press('Enter');
      }
      
      // Wait for login to complete
      await waitAndRetry(async () => {
        const modalGone = await page.evaluate(() => {
          const hasModal = document.querySelector('[role="dialog"], .modal, input[type="password"]');
          return !hasModal;
        });
        return modalGone;
      }, { description: `${playerName} login completion`, timeout: 10000 });
      
      // After login, the app might automatically redirect to multiplayer
      // Give it a moment to complete the transition
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log(`[${playerName}] ✓ Login successful`);
      await takeScreenshot(page, '04-logged-in', playerName.toLowerCase());
      return true;
    }

    // Login both players
    await loginPlayer(hostPage, HOST_CREDENTIALS, 'HOST');
    await loginPlayer(guestPage, GUEST_CREDENTIALS, 'GUEST');

    // === PHASE 3: Navigate to Multiplayer ===
    console.log('\n=== PHASE 3: Navigate to Multiplayer ===');
    
    // After successful login, the app should automatically switch to multiplayer mode
    // Wait for the multiplayer lobby to appear
    console.log('Waiting for multiplayer lobby to load...');
    
    // Check if already in multiplayer mode or need to close auth modal
    const hostNeedsClose = await hostPage.evaluate(() => {
      // Look for Close Auth button or any button containing "Close"
      const buttons = Array.from(document.querySelectorAll('button'));
      const closeBtn = buttons.find(btn => 
        btn.textContent.includes('Close Auth') || 
        (btn.textContent === 'Close' && document.querySelector('[role="dialog"]'))
      );
      if (closeBtn) {
        closeBtn.click();
        return true;
      }
      return false;
    });
    
    if (hostNeedsClose) {
      console.log('[HOST] Closed lingering auth modal');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    const guestNeedsClose = await guestPage.evaluate(() => {
      // Look for Close Auth button or any button containing "Close"
      const buttons = Array.from(document.querySelectorAll('button'));
      const closeBtn = buttons.find(btn => 
        btn.textContent.includes('Close Auth') || 
        (btn.textContent === 'Close' && document.querySelector('[role="dialog"]'))
      );
      if (closeBtn) {
        closeBtn.click();
        return true;
      }
      return false;
    });
    
    if (guestNeedsClose) {
      console.log('[GUEST] Closed lingering auth modal');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Wait for multiplayer elements to appear
    await waitAndRetry(async () => {
      const hostReady = await hostPage.evaluate(() => {
        // Check for multiplayer-specific elements
        const hasHostButton = Array.from(document.querySelectorAll('button'))
          .some(btn => btn.textContent.includes('Host Game'));
        const hasRoomsList = document.body.textContent.includes('Available Games') || 
                           document.body.textContent.includes('Create a Room');
        const inMultiplayerMode = document.querySelector('[class*="multiplayer"]') !== null;
        
        return hasHostButton || hasRoomsList || inMultiplayerMode;
      });
      
      const guestReady = await guestPage.evaluate(() => {
        const hasJoinElements = Array.from(document.querySelectorAll('button'))
          .some(btn => btn.textContent.includes('Join') || btn.textContent.includes('Refresh'));
        const hasRoomsList = document.body.textContent.includes('Available Games') || 
                           document.body.textContent.includes('Join a Room');
        const inMultiplayerMode = document.querySelector('[class*="multiplayer"]') !== null;
        
        return hasJoinElements || hasRoomsList || inMultiplayerMode;
      });
      
      return hostReady && guestReady;
    }, { description: 'multiplayer lobby', timeout: 10000 });
    
    console.log('✓ Multiplayer lobby loaded');
    await takeScreenshot(hostPage, '05-multiplayer-lobby', 'host');
    await takeScreenshot(guestPage, '05-multiplayer-lobby', 'guest');

    // === PHASE 4: Create Room ===
    console.log('\n=== PHASE 4: Create Room ===');
    
    console.log('[HOST] Looking for Host Game button...');
    
    // Debug: log current page state
    const hostPageContent = await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button')).map(btn => btn.textContent.trim());
      return {
        buttonTexts: buttons,
        hasAuthModal: document.querySelector('[role="dialog"]') !== null,
        pageText: document.body.innerText.substring(0, 500)
      };
    });
    
    console.log('[HOST] Available buttons:', hostPageContent.buttonTexts);
    if (hostPageContent.hasAuthModal) {
      console.log('[HOST] Auth modal is still open!');
    }
    
    const roomCreated = await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const hostBtn = buttons.find(btn => 
        btn.textContent.includes('Host Game') || 
        btn.textContent.includes('Create Game') ||
        btn.textContent.includes('Create Room')
      );
      if (hostBtn) {
        hostBtn.click();
        return true;
      }
      return false;
    });
    
    if (!roomCreated) {
      // Take a debug screenshot
      await takeScreenshot(hostPage, 'debug-no-host-button', 'host');
      throw new Error('[HOST] Could not find Host Game button. Current buttons: ' + hostPageContent.buttonTexts.join(', '));
    }
    
    // Wait for room creation
    await new Promise(resolve => setTimeout(resolve, 2000));
    await takeScreenshot(hostPage, '06-room-created', 'host');
    
    // Get room code
    const roomInfo = await hostPage.evaluate(() => {
      const codeElement = document.querySelector('[class*="room-code"], [class*="roomCode"], .text-3xl');
      const roomCode = codeElement ? codeElement.textContent.trim() : null;
      return { roomCode };
    });
    
    console.log('[HOST] Room created with code:', roomInfo.roomCode);

    // === PHASE 5: Join Room ===
    console.log('\n=== PHASE 5: Join Room ===');
    
    // Guest needs to find and join the room
    console.log('[GUEST] Looking for room to join...');
    
    // Refresh room list if needed
    await guestPage.evaluate(() => {
      const refreshBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Refresh'));
      if (refreshBtn) refreshBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Find and click on the room
    const roomJoined = await guestPage.evaluate((hostUsername) => {
      // Look for room created by host
      const roomElements = Array.from(document.querySelectorAll('div, button')).filter(el => 
        el.textContent.includes(hostUsername) && el.textContent.includes('Game')
      );
      
      if (roomElements.length > 0) {
        // Click on the room or its join button
        const clickTarget = roomElements[0];
        clickTarget.click();
        return true;
      }
      return false;
    }, HOST_CREDENTIALS.username);
    
    if (!roomJoined) {
      console.log('[GUEST] Could not find room in list, will look for Join button');
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Click Join button if visible
    await guestPage.evaluate(() => {
      const joinBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Join'));
      if (joinBtn) joinBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    await takeScreenshot(guestPage, '07-joined-room', 'guest');

    // === PHASE 6: Ready Up ===
    console.log('\n=== PHASE 6: Ready Up ===');
    
    // Both players click Ready
    console.log('[HOST] Clicking Ready...');
    await hostPage.evaluate(() => {
      const readyBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Ready'));
      if (readyBtn) readyBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('[GUEST] Clicking Ready...');
    await guestPage.evaluate(() => {
      const readyBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Ready'));
      if (readyBtn) readyBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    await takeScreenshot(hostPage, '08-both-ready', 'host');
    await takeScreenshot(guestPage, '08-both-ready', 'guest');

    // === PHASE 7: Start Game ===
    console.log('\n=== PHASE 7: Start Game ===');
    
    console.log('[HOST] Starting game...');
    const gameStarted = await hostPage.evaluate(() => {
      const startBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Start Game'));
      if (startBtn && !startBtn.disabled) {
        startBtn.click();
        return true;
      }
      return false;
    });
    
    if (!gameStarted) {
      throw new Error('[HOST] Could not start game');
    }
    
    // Wait for game to start
    console.log('Waiting for game to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === PHASE 8: Play Game ===
    console.log('\n=== PHASE 8: Play Game ===');
    
    // Play multiple rounds
    for (let round = 1; round <= 3; round++) {
      console.log(`\n--- Round ${round} ---`);
      
      // Wait for question to appear
      await waitAndRetry(async () => {
        const hostHasQuestion = await hostPage.evaluate(() => {
          return document.querySelector('img[alt*="player"], img[alt*="Player"], .player-image') !== null;
        });
        const guestHasQuestion = await guestPage.evaluate(() => {
          return document.querySelector('img[alt*="player"], img[alt*="Player"], .player-image') !== null;
        });
        return hostHasQuestion && guestHasQuestion;
      }, { description: `round ${round} question`, timeout: 10000 });
      
      await takeScreenshot(hostPage, `09-round${round}-question`, 'host');
      await takeScreenshot(guestPage, `09-round${round}-question`, 'guest');
      
      // Host answers (click random choice)
      console.log('[HOST] Answering...');
      await hostPage.evaluate(() => {
        const choices = Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.className.includes('choice') || btn.textContent.match(/^[A-Z]\./)
        );
        if (choices.length > 0) {
          const randomChoice = choices[Math.floor(Math.random() * choices.length)];
          randomChoice.click();
        }
      });
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Guest answers
      console.log('[GUEST] Answering...');
      await guestPage.evaluate(() => {
        const choices = Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.className.includes('choice') || btn.textContent.match(/^[A-Z]\./)
        );
        if (choices.length > 0) {
          const randomChoice = choices[Math.floor(Math.random() * choices.length)];
          randomChoice.click();
        }
      });
      
      // Wait for round transition
      console.log('Waiting for next round...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    // === PHASE 9: Game Complete ===
    console.log('\n=== PHASE 9: Checking Game Completion ===');
    
    // Check if game finished
    const gameFinished = await waitAndRetry(async () => {
      const hostFinished = await hostPage.evaluate(() => {
        return document.body.textContent.includes('Game Over') || 
               document.body.textContent.includes('Final Score') ||
               document.body.textContent.includes('Winner');
      });
      return hostFinished;
    }, { description: 'game completion', timeout: 20000, interval: 2000 });
    
    if (gameFinished) {
      console.log('✓ Game completed successfully!');
      await takeScreenshot(hostPage, '10-game-complete', 'host');
      await takeScreenshot(guestPage, '10-game-complete', 'guest');
    }
    
    testPassed = true;
    console.log('\n✅ MULTIPLAYER GAME FLOW TEST PASSED!');
    
  } catch (error) {
    console.error('\n❌ TEST FAILED:', error.message);
    console.error(error.stack);
    
    // Take error screenshots
    if (hostBrowser) {
      const pages = await hostBrowser.pages();
      if (pages.length > 1) {
        await takeScreenshot(pages[1], 'error-final-state', 'host');
      }
    }
    if (guestBrowser) {
      const pages = await guestBrowser.pages();
      if (pages.length > 1) {
        await takeScreenshot(pages[1], 'error-final-state', 'guest');
      }
    }
  } finally {
    // Keep browsers open for observation if test failed
    if (!testPassed) {
      console.log('\nKeeping browsers open for debugging...');
      console.log('Press Ctrl+C to close browsers and exit');
      
      // Keep process alive
      await new Promise(() => {});
    } else {
      // Close browsers if test passed
      console.log('\nClosing browsers...');
      if (hostBrowser) await hostBrowser.close();
      if (guestBrowser) await guestBrowser.close();
    }
  }
}

// Run the test
testMultiplayerFlow().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});