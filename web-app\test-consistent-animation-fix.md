# Consistent Animation Fix Test Plan

## What Was Fixed
1. **Removed immediate "landed" marking** for optimistic answers (lines 3040-3047)
   - Previously, the current user's answer was immediately marked as landed
   - This prevented the slide-up animation from playing for the current user
   
2. **Preserved football animation logic** 
   - The football animation already had logic to show immediately for current user (line 4931-4932)
   - No changes needed - it checks `(landedAnswers.has(answerKey) || isCurrentUserAnswer)`

## Expected Behavior
1. **All answers slide up** - Both current user and other players' answers should animate
2. **Consistent timing** - All cards should take the same time to slide up and settle
3. **Football animation** - Should still appear for correct answers:
   - For current user: Appears immediately (doesn't wait for slide to complete)
   - For other players: Appears after slide animation completes

## Test Steps
1. Start a multiplayer game with 2+ players
2. Have the current player submit an answer
   - **Expected**: Answer card slides up from bottom with animation
   - **Previous behavior**: Answer appeared statically without animation
3. Have other players submit answers
   - **Expected**: Their cards also slide up with the same animation
4. For correct answers:
   - **Current user**: Football animation should start immediately
   - **Other players**: Football animation should start after slide completes
5. Alt-tab away and back
   - **Expected**: No animations replay (fixed in previous commit)

## Console Logs to Monitor
- `[ANIMATION_FIX] Adding new answer to animate:` - Should appear for ALL answers including current user
- `[FOOTBALL_ANIMATION_DEBUG] Slide-up animation ended for:` - Should log for all answers
- `[FOOTBALL_ANIMATION_DEBUG] Round Submissions Football Check:` - Check `shouldShowFootball` logic

## UI Constraints Maintained
- No scrollbars in Round Submissions panel
- Cards stack vertically within the fixed-height container
- Overflow is hidden to prevent layout issues