# Deploy all Edge Functions with workdir fix
# This script deploys all Edge Functions in the correct order

Write-Host "`nDeploying all Edge Functions..." -ForegroundColor Cyan

$functions = @(
    "start-game-handler",
    "submit-answer-handler",
    "transition-monitor",
    "leave-room-handler",
    "stale-room-janitor",
    "player-disconnect-janitor"
)

$projectRef = "xmyxuvuimebjltnaamox"
$successCount = 0
$failCount = 0

# Save current location
$originalLocation = Get-Location

foreach ($func in $functions) {
    Write-Host "`n========================================" -ForegroundColor DarkGray
    Write-Host "Deploying: $func" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor DarkGray
    
    # Change to project root (not supabase directory)
    Set-Location "C:\Projects\recognition-combine"
    
    # Deploy with explicit workdir parameter
    $deployCmd = "npx supabase@latest functions deploy $func --project-ref $projectRef --no-verify-jwt --workdir supabase"
    
    Write-Host "Command: $deployCmd" -ForegroundColor Gray
    
    # Execute deployment
    Invoke-Expression $deployCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ $func deployed successfully!" -ForegroundColor Green
        $successCount++
    } else {
        Write-Host "✗ $func deployment failed!" -ForegroundColor Red
        $failCount++
        
        # Try alternative method from supabase directory
        Write-Host "Trying alternative deployment method..." -ForegroundColor Yellow
        Set-Location "$originalLocation"
        npx supabase@latest functions deploy $func --no-verify-jwt
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ $func deployed successfully with alternative method!" -ForegroundColor Green
            $successCount++
            $failCount--
        }
    }
}

# Return to original location
Set-Location $originalLocation

Write-Host "`n========================================" -ForegroundColor DarkGray
Write-Host "Deployment Summary:" -ForegroundColor Cyan
Write-Host "✓ Successful: $successCount" -ForegroundColor Green
Write-Host "✗ Failed: $failCount" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor DarkGray

if ($failCount -eq 0) {
    Write-Host "`nAll Edge Functions deployed successfully!" -ForegroundColor Green
    Write-Host "Run 'node test-multiplayer-game-flow.js' to test the deployment." -ForegroundColor Cyan
} else {
    Write-Host "`nSome deployments failed. Check the errors above." -ForegroundColor Yellow
}