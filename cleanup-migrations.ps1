# Cleanup and organize migrations
Write-Host "Migration Cleanup Script" -ForegroundColor Cyan
Write-Host "=======================" -ForegroundColor Cyan
Write-Host ""

# Remove duplicate migration files
Write-Host "Step 1: Removing duplicate migration files..." -ForegroundColor Yellow

# Remove the duplicate transition migration since columns might already exist
if (Test-Path "supabase/migrations/20250129000000_add_transition_until_column.sql") {
    Remove-Item "supabase/migrations/20250129000000_add_transition_until_column.sql" -Force
    Write-Host "Removed: 20250129000000_add_transition_until_column.sql" -ForegroundColor Red
}

# Keep only the safe migration that checks for existence
Write-Host "Keeping safe migration: 20250629091952_safe_add_missing_columns.sql" -ForegroundColor Green
Write-Host ""

Write-Host "Step 2: Creating deployment script for edge functions..." -ForegroundColor Yellow

# Create edge function deployment script
$deployScript = @'
# Deploy Edge Functions for Multiplayer Transition Feature
Write-Host "Deploying Edge Functions" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""

# Deploy submit-answer-handler (updated for transition support)
Write-Host "Deploying submit-answer-handler..." -ForegroundColor Yellow
Set-Location -Path "supabase"
.\deploy-submit-answer-handler.ps1
Set-Location -Path ".."

Write-Host ""
Write-Host "Edge functions deployed!" -ForegroundColor Green
Write-Host ""
Write-Host "The multiplayer transition feature is now ready to use!" -ForegroundColor Cyan
'@

$deployScript | Out-File -FilePath "deploy-transition-functions.ps1" -Encoding UTF8
Write-Host "Created: deploy-transition-functions.ps1" -ForegroundColor Green
Write-Host ""

Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Run the manual migration SQL in Supabase Dashboard"
Write-Host "2. Run: .\deploy-transition-functions.ps1"
Write-Host "3. Test the multiplayer transition feature"