# Alternative: Deploy using Supabase Dashboard

Write-Host @"

MANUAL DEPLOYMENT INSTRUCTIONS
==============================

Since the CLI is having issues with <PERSON><PERSON>, you can deploy manually:

1. Open your browser to:
   https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/functions

2. Click on 'submit-answer-handler' function (or create it if not exists)

3. Click 'Edit' or 'Create Function'

4. Copy the contents of this file:
   $PWD\functions\submit-answer-handler\index.ts

5. Paste the code into the editor

6. Click 'Deploy'

That's it! The function will be deployed without needing Docker or the CLI.

Press Enter to open the dashboard in your browser...
"@ -ForegroundColor Cyan

Read-Host

Start-Process "https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/functions"

Write-Host "`nDashboard opened. Follow the instructions above." -ForegroundColor Green