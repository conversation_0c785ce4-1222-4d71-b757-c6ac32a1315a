const puppeteer = require('puppeteer');

const BASE_URL = 'http://localhost:3001';
const WAIT_TIMEOUT = 30000;

// Test user credentials
const USER1 = {
  email: '<EMAIL>',
  password: 'fresh123',
  username: 'fresh'
};

const USER2 = {
  email: '<EMAIL>', 
  password: 'fresh2123',
  username: 'fresh2'
};

// Helper function to wait and log
async function waitAndLog(ms, message) {
  console.log(`⏳ ${message} (${ms}ms)...`);
  await new Promise(resolve => setTimeout(resolve, ms));
}

// Helper to take screenshots
async function takeScreenshot(page, name, playerName) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const screenshotDir = './screenshots';
  const fs = require('fs');
  
  if (!fs.existsSync(screenshotDir)) {
    fs.mkdirSync(screenshotDir);
  }
  
  await page.screenshot({ 
    path: `${screenshotDir}/${playerName}-${name}-${timestamp}.png`,
    fullPage: true 
  });
  console.log(`📸 Screenshot saved: ${playerName}-${name}`);
}

// Helper to extract console logs
function setupConsoleLogging(page, playerName) {
  page.on('console', msg => {
    const text = msg.text();
    // Filter for important game logs
    if (text.includes('[EDGE_SUBMIT_ANSWER]') || 
        text.includes('[RENDER]') || 
        text.includes('Round') ||
        text.includes('transition') ||
        text.includes('Timer')) {
      console.log(`[${playerName}] ${text}`);
    }
  });
}

// Login helper
async function loginUser(page, user) {
  console.log(`\n🔐 Logging in as ${user.username}...`);
  
  try {
    // Wait for page to load
    await page.waitForSelector('button', { timeout: 10000 });
    
    // Click login button
    const loginButton = await page.$('button:has-text("Login")') || 
                       await page.$('button:has-text("login")') ||
                       await page.$('button');
    
    if (!loginButton) {
      throw new Error('Login button not found');
    }
    
    await loginButton.click();
    
    // Wait for modal
    await waitAndLog(1000, 'Waiting for login modal');
    
    // Fill login form
    await page.waitForSelector('input[type="email"]', { timeout: WAIT_TIMEOUT });
    await page.type('input[type="email"]', user.email);
    await page.type('input[type="password"]', user.password);
    
    // Submit
    const submitButton = await page.$('button[type="submit"]');
    await submitButton.click();
    
    // Wait for login to complete
    await page.waitForFunction(
      () => !document.querySelector('[role="dialog"]'),
      { timeout: WAIT_TIMEOUT }
    );
    
    console.log(`✅ Logged in as ${user.username}`);
  } catch (error) {
    console.error(`❌ Login failed for ${user.username}:`, error.message);
    throw error;
  }
}

// Helper to get round number
async function getRoundNumber(page) {
  try {
    const roundText = await page.$eval('*:has-text("Round")', el => el.textContent);
    const match = roundText.match(/Round (\d+)/);
    return match ? parseInt(match[1]) : null;
  } catch {
    return null;
  }
}

// Helper to measure time until round advances
async function measureRoundTransition(page1, page2, currentRound) {
  const startTime = Date.now();
  
  // Wait for round to change on both pages
  await Promise.race([
    page1.waitForFunction(
      (expectedRound) => {
        const roundEl = Array.from(document.querySelectorAll('*')).find(el => 
          el.textContent && el.textContent.match(/Round \d+/)
        );
        if (!roundEl) return false;
        const match = roundEl.textContent.match(/Round (\d+)/);
        return match && parseInt(match[1]) > expectedRound;
      },
      { timeout: 10000 },
      currentRound
    ),
    new Promise((_, reject) => setTimeout(() => reject(new Error('Round transition timeout')), 10000))
  ]);
  
  const transitionTime = (Date.now() - startTime) / 1000;
  return transitionTime;
}

// Main test function
async function testMultiplayerFlow() {
  console.log('🚀 Starting automated multiplayer timing test...\n');
  console.log('📋 Testing Round Advance Rules:');
  console.log('   1. 7 seconds maximum from start of round');
  console.log('   2. If all players answer within 2s, advance at 5s (2s + 3s)');
  console.log('   3. Take the shorter of these timings\n');
  
  // Launch browsers
  const browser1 = await puppeteer.launch({
    headless: false,
    args: ['--window-size=1200,900', '--window-position=0,0', '--no-sandbox', '--disable-setuid-sandbox'],
    executablePath: '/snap/bin/chromium'
  });
  
  const browser2 = await puppeteer.launch({
    headless: false,
    args: ['--window-size=1200,900', '--window-position=1210,0', '--no-sandbox', '--disable-setuid-sandbox'],
    executablePath: '/snap/bin/chromium'
  });
  
  const page1 = await browser1.newPage();
  const page2 = await browser2.newPage();
  
  // Set viewport
  await page1.setViewport({ width: 1200, height: 900 });
  await page2.setViewport({ width: 1200, height: 900 });
  
  // Setup console logging
  setupConsoleLogging(page1, 'Player1');
  setupConsoleLogging(page2, 'Player2');
  
  try {
    // Navigate to app
    console.log('📍 Navigating to app...');
    await Promise.all([
      page1.goto(BASE_URL, { waitUntil: 'networkidle2' }),
      page2.goto(BASE_URL, { waitUntil: 'networkidle2' })
    ]);
    
    await waitAndLog(2000, 'Waiting for app to load');
    
    // Login both users
    await loginUser(page1, USER1);
    await loginUser(page2, USER2);
    
    await waitAndLog(2000, 'Users logged in');
    
    // Player 1 (Host) creates room
    console.log('\n🏠 Player 1 (Host) creating room...');
    
    // Click Multiplayer button
    await page1.waitForSelector('button', { timeout: WAIT_TIMEOUT });
    const multiplayerBtn = await page1.evaluateHandle(() => {
      return Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent.toLowerCase().includes('multiplayer')
      );
    });
    await multiplayerBtn.click();
    
    await waitAndLog(1000, 'Clicked multiplayer');
    
    // Click Create Room
    const createRoomBtn = await page1.evaluateHandle(() => {
      return Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent.includes('Create Room')
      );
    });
    await createRoomBtn.click();
    
    // Wait for room code
    await page1.waitForSelector('*:has-text("Room Code")', { timeout: WAIT_TIMEOUT });
    await waitAndLog(1000, 'Room created');
    
    // Get room code
    const roomCode = await page1.evaluate(() => {
      // Look for the large room code display
      const codeEl = Array.from(document.querySelectorAll('*')).find(el => {
        const styles = window.getComputedStyle(el);
        return el.textContent && 
               el.textContent.match(/^[A-Z0-9]{4}$/) && 
               styles.fontSize && parseInt(styles.fontSize) > 30;
      });
      return codeEl ? codeEl.textContent : null;
    });
    
    console.log(`✅ Room created with code: ${roomCode}`);
    await takeScreenshot(page1, 'room-created', 'player1');
    
    // Player 2 joins room
    console.log(`\n👥 Player 2 joining room ${roomCode}...`);
    
    // Click Multiplayer for Player 2
    const multiplayerBtn2 = await page2.evaluateHandle(() => {
      return Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent.toLowerCase().includes('multiplayer')
      );
    });
    await multiplayerBtn2.click();
    
    await waitAndLog(1000, 'Player 2 clicked multiplayer');
    
    // Find and fill room code input
    await page2.waitForSelector('input[type="text"]', { timeout: WAIT_TIMEOUT });
    const roomCodeInput = await page2.$('input[type="text"]');
    await roomCodeInput.type(roomCode);
    
    // Click Join Room
    const joinRoomBtn = await page2.evaluateHandle(() => {
      return Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent.includes('Join Room')
      );
    });
    await joinRoomBtn.click();
    
    console.log('✅ Player 2 joined room');
    await waitAndLog(2000, 'Waiting for players to sync');
    
    // Verify both players see each other
    const player1Count = await page1.evaluate(() => {
      const el = Array.from(document.querySelectorAll('*')).find(e => 
        e.textContent && e.textContent.match(/Players \(\d+\/\d+\)/)
      );
      return el ? el.textContent : null;
    });
    
    const player2Count = await page2.evaluate(() => {
      const el = Array.from(document.querySelectorAll('*')).find(e => 
        e.textContent && e.textContent.match(/Players \(\d+\/\d+\)/)
      );
      return el ? el.textContent : null;
    });
    
    console.log(`\n📊 Player counts:`);
    console.log(`   Player 1 sees: ${player1Count}`);
    console.log(`   Player 2 sees: ${player2Count}`);
    
    await takeScreenshot(page1, 'both-in-room', 'player1');
    await takeScreenshot(page2, 'both-in-room', 'player2');
    
    // Host starts game
    console.log('\n🎮 Host starting game...');
    
    // Find and click Start Game button
    const startGameBtn = await page1.evaluateHandle(() => {
      return Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent.includes('Start Game') && !btn.disabled
      );
    });
    
    if (!startGameBtn) {
      throw new Error('Start Game button not found or is disabled');
    }
    
    await startGameBtn.click();
    console.log('⏳ Waiting for game to start...');
    
    // Wait for game to load for both players
    await Promise.all([
      page1.waitForSelector('img[alt*="player"], img[src*="player"]', { timeout: 15000 }),
      page2.waitForSelector('img[alt*="player"], img[src*="player"]', { timeout: 15000 })
    ]);
    
    console.log('✅ Game started for both players!');
    await takeScreenshot(page1, 'game-started', 'player1');
    await takeScreenshot(page2, 'game-started', 'player2');
    
    // Test different timing scenarios
    console.log('\n🔬 Testing Round Timing Scenarios...\n');
    
    const testScenarios = [
      { 
        name: 'Fast Response (both answer < 2s)', 
        player1Delay: 500, 
        player2Delay: 1500,
        expectedTime: 5.0,
        description: 'Should advance at 5s (2s window + 3s)'
      },
      { 
        name: 'Slow Response (answer > 2s apart)', 
        player1Delay: 500, 
        player2Delay: 3000,
        expectedTime: 3.0,
        description: 'Should advance 3s after last answer'
      },
      { 
        name: 'Very Fast (both answer < 1s)', 
        player1Delay: 300, 
        player2Delay: 700,
        expectedTime: 5.0,
        description: 'Still uses 2s window + 3s = 5s'
      },
      { 
        name: 'One player only (7s timeout)', 
        player1Delay: 1000, 
        player2Delay: null,
        expectedTime: 7.0,
        description: 'Should timeout at 7s'
      },
      { 
        name: 'Fast near limit', 
        player1Delay: 4500, 
        player2Delay: 5500,
        expectedTime: 1.5,
        description: 'Should hit 7s hard cap'
      }
    ];
    
    for (let i = 0; i < testScenarios.length && i < 5; i++) {
      const scenario = testScenarios[i];
      const currentRound = i + 1;
      
      console.log(`\n📍 Round ${currentRound}: ${scenario.name}`);
      console.log(`   ${scenario.description}`);
      
      // Wait for round to be ready
      await waitAndLog(1000, 'Preparing round');
      
      // Get answer buttons
      const answerButtons1 = await page1.$$('button[class*="choice"], button[class*="answer"]');
      const answerButtons2 = await page2.$$('button[class*="choice"], button[class*="answer"]');
      
      if (answerButtons1.length === 0 || answerButtons2.length === 0) {
        console.error('❌ No answer buttons found!');
        continue;
      }
      
      const roundStartTime = Date.now();
      
      // Player 1 answers
      setTimeout(async () => {
        await answerButtons1[0].click();
        console.log(`   ✓ Player 1 answered at ${scenario.player1Delay}ms`);
      }, scenario.player1Delay);
      
      // Player 2 answers (if not null)
      if (scenario.player2Delay !== null) {
        setTimeout(async () => {
          await answerButtons2[1].click();
          console.log(`   ✓ Player 2 answered at ${scenario.player2Delay}ms`);
        }, scenario.player2Delay);
      } else {
        console.log('   ⏸️  Player 2 not answering (testing timeout)');
      }
      
      // Measure actual transition time
      try {
        const transitionTime = await measureRoundTransition(page1, page2, currentRound);
        const deviation = Math.abs(transitionTime - scenario.expectedTime);
        
        console.log(`   ⏱️  Actual transition time: ${transitionTime.toFixed(1)}s`);
        console.log(`   📊 Expected: ${scenario.expectedTime}s (deviation: ${deviation.toFixed(1)}s)`);
        
        if (deviation < 0.5) {
          console.log(`   ✅ PASS - Timing is correct!`);
        } else {
          console.log(`   ⚠️  WARNING - Timing deviation > 0.5s`);
        }
        
      } catch (error) {
        console.log(`   ❌ Failed to measure transition: ${error.message}`);
      }
      
      await waitAndLog(1000, 'Preparing next round');
    }
    
    console.log('\n\n✅ Automated test completed!');
    console.log('\n📊 Summary:');
    console.log('- Non-host player successfully joined and saw the game');
    console.log('- Round timing follows the new rules');
    console.log('- Both players stayed synchronized throughout');
    
    await takeScreenshot(page1, 'test-complete', 'player1');
    await takeScreenshot(page2, 'test-complete', 'player2');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    console.error('Stack:', error.stack);
    await takeScreenshot(page1, 'error', 'player1');
    await takeScreenshot(page2, 'error', 'player2');
  } finally {
    console.log('\n🧹 Test complete. Browsers will close in 10 seconds...');
    await waitAndLog(10000, 'Final review time');
    await browser1.close();
    await browser2.close();
  }
}

// Run the test
console.log('🏁 Starting Recognition Combine Multiplayer Test\n');
testMultiplayerFlow().catch(console.error);