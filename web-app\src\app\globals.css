@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.625rem;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: #111827; /* Fallback color if image fails to load */
    background-image: url('/images/field_background.png');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
  }

  button, 
  [type='button'], 
  [type='reset'], 
  [type='submit'] {
    -webkit-appearance: button; /* Modern reset */
    background-color: transparent; /* Reset background */
    background-image: none; /* Reset background image */
    outline: none !important; /* FORCE no outline */
    box-shadow: none !important; /* FORCE no box-shadow by default */
  }

  button:focus,
  [type='button']:focus,
  [type='reset']:focus,
  [type='submit']:focus,
  button:focus-visible,
  [type='button']:focus-visible,
  [type='reset']:focus-visible,
  [type='submit']:focus-visible {
    outline: none !important; /* FORCE no outline on focus too */
    box-shadow: none !important; /* FORCE no box-shadow on focus too */
  }
}

@layer utilities {
  .jumbotron-title {
    font-family: system-ui, -apple-system, sans-serif;
    color: white;
    font-size: 3.5rem;
    font-weight: 800;
    -webkit-text-stroke-width: 2.5px;
    -webkit-text-stroke-color: #002800;
    text-shadow:
      4px 4px 0px #001a00,
      0px 0px 18px rgba(173, 255, 47, 0.35);
    letter-spacing: 0.045em;
  }

  /* ANNIHILATE THE RED BOX - NUCLEAR OPTION */
  .no-debug-box {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
  }

  /* Connection Status Indicator Styles */
  .reconnecting-indicator .spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* Layer 3: The Reactive UI - Disconnected Player Styles */
  .player-item.offline {
    opacity: 0.5;
    font-style: italic;
    filter: grayscale(80%);
  }
}

.game-loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 20px;
  box-sizing: border-box;
  text-align: center;
  color: white;
}

.loading-text {
  font-size: 1.8em;
  font-weight: bold;
  margin-bottom: 20px;
  color: #FFFFFF;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.football-spinner {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.football-spinner span {
  font-size: 3em;
  display: inline-block;
  animation: footballBounce 1.5s infinite ease-in-out;
}

.football-spinner span:nth-child(1) { animation-delay: 0s; }
.football-spinner span:nth-child(2) { animation-delay: 0.15s; }
.football-spinner span:nth-child(3) { animation-delay: 0.3s; }
.football-spinner span:nth-child(4) { animation-delay: 0.45s; }
.football-spinner span:nth-child(5) { animation-delay: 0.6s; }

@keyframes footballBounce {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15px) rotate(-15deg);
    opacity: 1;
  }
  50% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  75% {
    transform: translateY(-10px) rotate(15deg);
    opacity: 0.9;
  }
}

.loading-subtext {
  font-size: 1em;
  color: #DDDDDD;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}

/* Slide-up animation for answer submissions */
@keyframes slide-up {
  0% {
    transform: translateY(100vh) translateZ(0) scale(0.8);
    opacity: 0;
    filter: blur(2px);
  }
  60% {
    transform: translateY(-5%) translateZ(0) scale(1.02);
    opacity: 1;
    filter: blur(0);
  }
  100% {
    transform: translateY(0) translateZ(0) scale(1);
    opacity: 1;
    filter: blur(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.75s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  will-change: transform, opacity, filter;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Force animations to play immediately */
.force-animation-play {
  animation-play-state: running !important;
  will-change: transform, opacity;
  transform: translateZ(0);
}