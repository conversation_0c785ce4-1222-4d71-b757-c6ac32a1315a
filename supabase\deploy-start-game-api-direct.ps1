# Deploy start-game-handler using Supabase Management API directly
# This bypasses all local tooling issues

Write-Host "Deploying start-game-handler via Supabase Management API..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"
$functionName = "start-game-handler"

# Get access token from Supabase CLI
Write-Host "Getting Supabase access token..." -ForegroundColor Yellow
$tokenOutput = npx supabase@latest login --json 2>&1 | Out-String
$token = $null

# Try to extract token from various possible outputs
if ($tokenOutput -match '"access_token":\s*"([^"]+)"') {
    $token = $matches[1]
} elseif (Test-Path "$env:USERPROFILE\.supabase\access-token") {
    $token = Get-Content "$env:USERPROFILE\.supabase\access-token" -Raw
    $token = $token.Trim()
}

if (-not $token) {
    Write-Host "Could not get access token. Please run 'npx supabase login' first." -ForegroundColor Red
    exit 1
}

Write-Host "Access token obtained." -ForegroundColor Green

# Read the function code
$functionPath = Join-Path $PSScriptRoot "functions\$functionName\index.ts"
if (-not (Test-Path $functionPath)) {
    Write-Host "Function file not found: $functionPath" -ForegroundColor Red
    exit 1
}

$functionCode = Get-Content $functionPath -Raw

# Create the deployment payload
$payload = @{
    slug = $functionName
    body = $functionCode
    verify_jwt = $false
} | ConvertTo-Json -Depth 10

# Deploy using curl
$apiUrl = "https://api.supabase.com/v1/projects/$projectRef/functions/$functionName"
$headers = @(
    "Authorization: Bearer $token",
    "Content-Type: application/json"
)

Write-Host "Deploying to: $apiUrl" -ForegroundColor Yellow

# Create temporary file for payload
$tempFile = New-TemporaryFile
Set-Content -Path $tempFile.FullName -Value $payload

try {
    # Use curl with proper headers
    $curlCmd = @(
        "curl",
        "-X", "PUT",
        $apiUrl,
        "-H", $headers[0],
        "-H", $headers[1],
        "-d", "@$($tempFile.FullName)",
        "-s",
        "-w", "`nHTTP_STATUS:%{http_code}"
    )

    Write-Host "Executing deployment..." -ForegroundColor Yellow
    $response = & curl @curlCmd 2>&1 | Out-String

    if ($response -match "HTTP_STATUS:(\d+)") {
        $statusCode = $matches[1]
        if ($statusCode -eq "200" -or $statusCode -eq "201" -or $statusCode -eq "204") {
            Write-Host "`nDeployment successful! (Status: $statusCode)" -ForegroundColor Green
        } else {
            Write-Host "`nDeployment failed with status: $statusCode" -ForegroundColor Red
            Write-Host "Response: $response" -ForegroundColor Gray
        }
    } else {
        Write-Host "`nCould not determine deployment status." -ForegroundColor Yellow
        Write-Host "Response: $response" -ForegroundColor Gray
    }
} finally {
    # Clean up temp file
    Remove-Item $tempFile.FullName -Force -ErrorAction SilentlyContinue
}

Write-Host "`nTo test the deployment, run: node test-multiplayer-game-flow.js" -ForegroundColor Cyan