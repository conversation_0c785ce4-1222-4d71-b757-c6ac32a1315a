-- Add question_started_at column to track when each question begins
-- This enables implementing a hard 7-second cap for transitions

DO $$ 
BEGIN
    -- Add question_started_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'question_started_at') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN question_started_at timestamptz DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.question_started_at IS 'Timestamp when the current question was displayed to players. Used to implement hard time caps for transitions.';
    END IF;
END$$;

-- Create index on question_started_at for efficient queries if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE schemaname = 'public' 
                   AND tablename = 'game_rooms' 
                   AND indexname = 'idx_game_rooms_question_started_at') THEN
        CREATE INDEX idx_game_rooms_question_started_at ON public.game_rooms(question_started_at) 
        WHERE question_started_at IS NOT NULL;
    END IF;
END$$;