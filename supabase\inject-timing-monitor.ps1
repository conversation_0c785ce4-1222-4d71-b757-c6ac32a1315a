# Script to inject timing monitor into game console

Write-Host @"

MULTIPLAYER TIMING MONITOR
==========================

After signing in and joining a room, paste this code into BOTH browser consoles (F12):

"@ -ForegroundColor Cyan

$monitorCode = @'
// Multiplayer Round Advance Monitor
(() => {
    console.log('%c[MONITOR] Round advance timing monitor activated', 'color: #00ff00; font-weight: bold');
    
    let roundStartTime = null;
    let roundNumber = 1;
    let answerTimes = [];
    let myAnswerTime = null;
    
    // Monitor for round starts
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(fn, delay, ...args) {
        // Detect countdown/round start
        if (delay === 1000 || delay === 3000) {
            const stack = new Error().stack;
            if (stack.includes('countdown') || stack.includes('startRound')) {
                console.log('%c[MONITOR] Round countdown detected', 'color: yellow');
            }
        }
        return originalSetTimeout.call(this, fn, delay, ...args);
    };
    
    // Monitor fetch for answer submissions
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
        const [url, options] = args;
        
        // Track answer submission
        if (url?.includes('submit-answer-handler')) {
            const submitTime = roundStartTime ? ((Date.now() - roundStartTime) / 1000).toFixed(2) : '?';
            myAnswerTime = submitTime;
            console.log(`%c[MONITOR] MY ANSWER SUBMITTED AT: ${submitTime}s`, 'color: #00ffff; font-size: 16px; font-weight: bold');
        }
        
        // Track game start
        if (url?.includes('start-game-handler')) {
            console.log('%c[MONITOR] Game starting...', 'color: #ffff00; font-weight: bold');
            roundStartTime = Date.now();
            roundNumber = 1;
            answerTimes = [];
        }
        
        const response = await originalFetch.apply(this, args);
        
        // Log response for submit-answer
        if (url?.includes('submit-answer-handler')) {
            try {
                const clone = response.clone();
                const data = await clone.json();
                if (data.inTransition) {
                    console.log('%c[MONITOR] TRANSITION TRIGGERED! 3-second timer starting', 'color: #ff00ff; font-size: 16px; font-weight: bold');
                }
                console.log('[MONITOR] Submit response:', data);
            } catch (e) {}
        }
        
        return response;
    };
    
    // Monitor DOM for game state changes
    let lastQuestionText = '';
    let transitionDetected = false;
    
    setInterval(() => {
        // Detect question changes (round advance)
        const questionEl = document.querySelector('h2, .question-text, [class*="question"]');
        const questionText = questionEl?.textContent || '';
        
        if (questionText && questionText !== lastQuestionText && lastQuestionText !== '') {
            const roundDuration = roundStartTime ? ((Date.now() - roundStartTime) / 1000).toFixed(2) : '?';
            console.log(`%c[MONITOR] ROUND ADVANCED AT: ${roundDuration}s`, 'color: #00ff00; font-size: 20px; font-weight: bold');
            console.log(`%c[MONITOR] Round ${roundNumber} duration: ${roundDuration}s`, 'color: #00ff00');
            if (myAnswerTime) {
                console.log(`%c[MONITOR] Your answer was at: ${myAnswerTime}s`, 'color: #00ffff');
            }
            
            // Reset for next round
            roundNumber++;
            roundStartTime = Date.now();
            answerTimes = [];
            myAnswerTime = null;
            transitionDetected = false;
        }
        lastQuestionText = questionText;
        
        // Detect transition timer
        const transitionEl = document.querySelector('[class*="transition"], [class*="timer"]:has-text("3"), div:has-text("Showing results")');
        if (transitionEl && !transitionDetected) {
            transitionDetected = true;
            const transitionTime = roundStartTime ? ((Date.now() - roundStartTime) / 1000).toFixed(2) : '?';
            console.log(`%c[MONITOR] 3-SECOND TRANSITION STARTED AT: ${transitionTime}s`, 'color: #ff00ff; font-size: 16px; font-weight: bold');
        }
        
        // Detect game start from UI
        if (!roundStartTime) {
            const gameActive = document.querySelector('[class*="score"], [class*="Score"]');
            const inLobby = document.querySelector('[class*="lobby"], button:has-text("Start Game")');
            if (gameActive && !inLobby) {
                roundStartTime = Date.now();
                console.log('%c[MONITOR] Game detected as active, starting timer', 'color: yellow');
            }
        }
    }, 100);
    
    // Summary helper
    window.showTimingSummary = () => {
        console.log('%c=== TIMING SUMMARY ===', 'color: #00ff00; font-size: 16px');
        console.log('Expected behavior:');
        console.log('- Both answer <4s → Round at (last_answer + 3s)');
        console.log('- Both answer ≥4s → Round at 7s (cap)');
        console.log('- One answers → Round at 7s');
        console.log('====================');
    };
    
    console.log('%c[MONITOR] Setup complete! Type showTimingSummary() for rules', 'color: #00ff00');
    console.log('%c[MONITOR] Watch for colored messages during gameplay', 'color: #00ff00');
})();
'@

Write-Host $monitorCode -ForegroundColor White

Write-Host @"

INSTRUCTIONS:
1. Copy the code above
2. Open F12 console in BOTH game windows
3. Paste and press Enter in each
4. Play the game and watch for colored [MONITOR] messages

Key timings to verify:
- CYAN: Your answer submission time
- PURPLE: 3-second transition detection
- GREEN: Round advance time

"@ -ForegroundColor Yellow

# Also save to file for easy access
$monitorCode | Out-File -FilePath "$env:TEMP\monitor-code.js" -Encoding UTF8
Write-Host "Code also saved to: $env:TEMP\monitor-code.js" -ForegroundColor Gray