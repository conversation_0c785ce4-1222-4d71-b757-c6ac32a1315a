# Multiplayer Game Synchronization Fix

## Problem Summary
The multiplayer game mode had critical synchronization issues:
- Clients getting out of sync with different questions displayed
- Alt-tabbing causing state inconsistencies
- Race conditions when multiple clients tried to advance questions
- Client-side timers causing conflicts

## Solution Implementation

### 1. Server-Driven Question Transitions
**Changes Made:**
- Removed ALL client-side timers for question advancement
- Created `question-auto-advance` edge function to handle 7-second auto-advance server-side
- Added database migration with `auto_advance_question()` function
- Only the server decides when to move to next question

**Files Modified:**
- `/web-app/src/app/page.tsx` - Removed client-side transition timers
- `/supabase/functions/question-auto-advance/index.ts` - New edge function
- `/supabase/migrations/20250630_server_side_transitions.sql` - Database changes

### 2. Simplified Tab Visibility Handling
**Changes Made:**
- Removed all `syncFullRoomState` calls on visibility change
- Only reconnect real-time subscription when tab becomes visible
- Trust real-time to handle all state updates

**Code Changes:**
```typescript
// OLD: Complex sync logic with multiple checks
if (document.visibilityState === 'visible') {
  // ... lots of sync logic
  syncFullRoomState(activeRoomId, 'visibility_change');
}

// NEW: Simple reconnection only
if (document.visibilityState === 'visible') {
  console.log('[DISCONNECT_DETECTION] Page became visible - reconnecting real-time');
  supabase.realtime.connect();
}
```

### 3. Question Sequence Numbers
**Changes Made:**
- Added `question_sequence` field to GameRoom interface and database
- Real-time subscription ignores updates with older sequence numbers
- Prevents stale data from overwriting current state

**Implementation:**
```typescript
// Ignore stale updates
if (newSequence < currentSequence) {
  console.log(`[Realtime] Ignoring stale update - sequence ${newSequence} < current ${currentSequence}`);
  return;
}
```

### 4. Removed Manual State Syncs
**Changes Made:**
- Removed sync after answer submission
- Removed sync after question transition
- Rely exclusively on real-time subscriptions

**Removed Calls:**
- `syncFullRoomState(activeRoomId, 'answer-submission-ui-sync')`
- `syncFullRoomState(activeRoomId, 'transition_complete_immediate')`
- `syncFullRoomState(activeRoomId, 'visibility_change')`

## Deployment Steps

1. **Run Database Migration:**
   ```powershell
   ./run-server-transitions-migration.ps1
   ```

2. **Deploy Auto-Advance Function:**
   ```powershell
   cd supabase
   ./deploy-question-auto-advance.ps1
   ```

3. **Set Up Cron Job:**
   - Configure Supabase to call `question-auto-advance` function every second
   - Or integrate into existing scheduled jobs

4. **Deploy Frontend Changes:**
   ```powershell
   cd web-app
   npm run build
   npm run deploy
   ```

## Testing Plan

### Test Scenario 1: Basic Multiplayer Flow
1. Open 2 browser windows
2. Create room in Window 1
3. Join room in Window 2
4. Start game
5. Verify both see same question
6. Wait 7 seconds without answering
7. Verify both advance to next question simultaneously

### Test Scenario 2: Alt-Tab Synchronization
1. Start multiplayer game with 2 clients
2. Answer question in Client 1
3. Alt-tab away from Client 2
4. Wait for auto-advance
5. Alt-tab back to Client 2
6. Verify Client 2 shows correct question (not stale)

### Test Scenario 3: Rapid Transitions
1. Start game with 3+ clients
2. Have all clients answer quickly
3. Verify smooth transitions without conflicts
4. Check no 409 errors in console

### Test Scenario 4: Network Interruption
1. Start multiplayer game
2. Disconnect network briefly
3. Reconnect
4. Verify client catches up to correct state

## Expected Results
- No more client-side race conditions
- Consistent question display across all clients
- Alt-tabbing doesn't cause sync issues
- Server handles all transitions reliably
- Real-time subscriptions maintain sync

## Rollback Plan
If issues arise:
1. Revert page.tsx changes
2. Re-enable client-side timers
3. Disable auto-advance function
4. Restore manual sync calls

## Monitoring
- Check Supabase logs for auto-advance function execution
- Monitor for 409 errors (should be eliminated)
- Track real-time subscription stability
- Verify sequence numbers increment properly