# PowerShell script to test multiplayer functionality manually
Write-Host "🎮 Recognition Combine Multiplayer Test Guide" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "This script will guide you through testing the multiplayer timing features." -ForegroundColor Yellow
Write-Host ""

Write-Host "📋 Round Advance Rules Being Tested:" -ForegroundColor Green
Write-Host "   1. 7 seconds maximum from start of round" -ForegroundColor White
Write-Host "   2. If all players answer within 2s, advance at 5s (2s + 3s)" -ForegroundColor White
Write-Host "   3. Take the shorter of these timings" -ForegroundColor White
Write-Host ""

Write-Host "🔧 Setup Instructions:" -ForegroundColor Green
Write-Host "1. Open TWO browser windows (one normal, one incognito/private)" -ForegroundColor White
Write-Host "2. Navigate both to: http://localhost:3001" -ForegroundColor Yellow
Write-Host "3. Browser 1: <NAME_EMAIL> (password: fresh123)" -ForegroundColor White
Write-Host "4. Browser 2: <NAME_EMAIL> (password: fresh2123)" -ForegroundColor White
Write-Host ""

Write-Host "Press any key when both browsers are logged in..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host ""
Write-Host "🏠 Step 1: Create Room" -ForegroundColor Green
Write-Host "- In Browser 1 (fresh): Click 'Multiplayer'" -ForegroundColor White
Write-Host "- Click 'Create Room'" -ForegroundColor White
Write-Host "- Note the 4-letter room code" -ForegroundColor Yellow
Write-Host ""

Write-Host "Press any key when room is created..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host ""
Write-Host "👥 Step 2: Join Room" -ForegroundColor Green
Write-Host "- In Browser 2 (fresh2): Click 'Multiplayer'" -ForegroundColor White
Write-Host "- Enter the room code" -ForegroundColor White
Write-Host "- Click 'Join Room'" -ForegroundColor White
Write-Host "- Verify both players see 'Players (2/4)'" -ForegroundColor Yellow
Write-Host ""

Write-Host "Press any key when both players are in the room..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host ""
Write-Host "🎮 Step 3: Start Game" -ForegroundColor Green
Write-Host "- In Browser 1: Click 'Start Game'" -ForegroundColor White
Write-Host "- IMPORTANT: Watch Browser 2 - it should show:" -ForegroundColor Yellow
Write-Host "  1. 'Game Starting... Loading game data...' briefly" -ForegroundColor White
Write-Host "  2. Then the actual game with player image" -ForegroundColor White
Write-Host ""

Write-Host "Press any key when game has started for BOTH players..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host ""
Write-Host "🧪 Now Testing Different Timing Scenarios:" -ForegroundColor Cyan
Write-Host ""

# Test scenarios
$scenarios = @(
    @{
        Name = "Test 1: Fast Response (both answer < 2s)"
        Instructions = @(
            "- Both players: Answer within 2 seconds",
            "- Player 1: Click an answer immediately",
            "- Player 2: Click an answer within 1-2 seconds"
        )
        Expected = "Round should advance at 5 seconds total (2s + 3s)"
    },
    @{
        Name = "Test 2: Slow Response (> 2s apart)"
        Instructions = @(
            "- Player 1: Answer immediately",
            "- Player 2: Wait 3-4 seconds, then answer"
        )
        Expected = "Round advances 3 seconds after Player 2 answers"
    },
    @{
        Name = "Test 3: 7-Second Timeout"
        Instructions = @(
            "- Player 1: Answer at 1 second",
            "- Player 2: DON'T answer at all"
        )
        Expected = "Round advances at exactly 7 seconds"
    },
    @{
        Name = "Test 4: Very Fast (< 1s)"
        Instructions = @(
            "- Both players: Click as fast as possible"
        )
        Expected = "Still advances at 5s (uses 2s window + 3s)"
    },
    @{
        Name = "Test 5: Edge Case Near 7s"
        Instructions = @(
            "- Player 1: Wait 4.5 seconds, then answer",
            "- Player 2: Answer right after Player 1"
        )
        Expected = "Should hit 7s cap (not extend beyond)"
    }
)

foreach ($scenario in $scenarios) {
    Write-Host ""
    Write-Host ("=" * 50) -ForegroundColor DarkGray
    Write-Host $scenario.Name -ForegroundColor Yellow
    Write-Host ("=" * 50) -ForegroundColor DarkGray
    
    foreach ($instruction in $scenario.Instructions) {
        Write-Host $instruction -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "Expected: $($scenario.Expected)" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "⏱️  Start your timer when the round begins!" -ForegroundColor Cyan
    Write-Host "Press any key after completing this test..." -ForegroundColor Cyan
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    
    Write-Host ""
    $result = Read-Host "What was the actual transition time? (in seconds)"
    Write-Host "Recorded: $result seconds" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "✅ Test Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Check Console Logs:" -ForegroundColor Cyan
Write-Host "Open browser DevTools (F12) and look for:" -ForegroundColor White
Write-Host "- [EDGE_SUBMIT_ANSWER] First answer recorded at:" -ForegroundColor Gray
Write-Host "- [EDGE_SUBMIT_ANSWER] Timing analysis:" -ForegroundColor Gray
Write-Host "- [EDGE_SUBMIT_ANSWER] Fast response detected!" -ForegroundColor Gray
Write-Host "- [EDGE_SUBMIT_ANSWER] Standard timing (slow response):" -ForegroundColor Gray
Write-Host ""

Write-Host "🎯 Success Criteria:" -ForegroundColor Green
Write-Host "✓ Non-host player (fresh2) sees the game properly" -ForegroundColor White
Write-Host "✓ Rounds advance according to timing rules" -ForegroundColor White
Write-Host "✓ No errors in console" -ForegroundColor White
Write-Host "✓ Both players stay synchronized" -ForegroundColor White

Write-Host ""
Write-Host "Test session complete. Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")