Write-Host "Deploying Simple 3-Second Transition" -ForegroundColor Cyan
Write-Host ""

Set-Location -Path "supabase"

Write-Host "Deploying submit-answer-handler with simple 3-second delay..." -ForegroundColor Yellow
.\deploy-submit-answer-handler.ps1

Set-Location -Path ".."

Write-Host ""
Write-Host "Deployment Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Changes made:" -ForegroundColor Cyan
Write-Host "- Removed 'All players have answered' UI screen"
Write-Host "- Kept invisible 3-second delay between questions"
Write-Host "- Simplified transition checking (500ms intervals)"
Write-Host "- Removed unused forceUpdate state"