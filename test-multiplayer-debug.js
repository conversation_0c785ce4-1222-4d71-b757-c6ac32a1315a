const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };

// Screenshot directory
const SCREENSHOT_DIR = path.join(__dirname, 'test-screenshots-debug');

// Helper to take screenshots
async function takeScreenshot(page, name, prefix) {
  try {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${prefix}-${name}-${timestamp}.png`;
    const filepath = path.join(SCREENSHOT_DIR, filename);
    await page.screenshot({ path: filepath, fullPage: true });
    console.log(`[${prefix}] Screenshot: ${filename}`);
  } catch (error) {
    console.error(`[${prefix}] Screenshot failed:`, error.message);
  }
}

// Debug helper to extract console logs
async function setupConsoleLogging(page, prefix) {
  page.on('console', msg => {
    const text = msg.text();
    // Filter for relevant logs
    if (text.includes('CREATE_ROOM') || 
        text.includes('JOIN') || 
        text.includes('SYNC_STATE') || 
        text.includes('PLAYER') ||
        text.includes('game_players') ||
        text.includes('playersInRoom')) {
      console.log(`[${prefix} CONSOLE]`, text);
    }
  });
}

// Main test function
async function debugMultiplayerFlow() {
  console.log('=== MULTIPLAYER DEBUG TEST ===\n');
  console.log('Focus: Debug why host shows 0 players after creating room');
  console.log('---\n');

  // Browser options
  const browserOptions = {
    headless: true,
    defaultViewport: null,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-web-security',
      '--window-size=1200,900'
    ]
  };

  // Find browser
  let executablePath;
  const possiblePaths = [
    '/usr/bin/chromium-browser',
    '/usr/bin/chromium',
    '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe'
  ];
  
  for (const chromePath of possiblePaths) {
    try {
      await fs.access(chromePath);
      executablePath = chromePath;
      console.log(`Found browser at: ${chromePath}`);
      break;
    } catch (e) {
      // Continue
    }
  }

  if (executablePath) {
    browserOptions.executablePath = executablePath;
  }

  let hostBrowser, hostPage;

  try {
    // Launch browser
    console.log('Launching browser...');
    hostBrowser = await puppeteer.launch(browserOptions);
    hostPage = await hostBrowser.newPage();

    // Setup console logging
    await setupConsoleLogging(hostPage, 'HOST');

    // Navigate
    console.log('\n=== PHASE 1: Navigation ===');
    await hostPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' });
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Authenticate
    console.log('\n=== PHASE 2: Authentication ===');
    
    // Click Multiplayer Mode
    await hostPage.evaluate(() => {
      const mpBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent === 'Multiplayer Mode');
      if (mpBtn) mpBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Fill credentials
    const emailInput = await hostPage.$('input[type="email"], input[type="text"]:not([type="password"])');
    await emailInput.type(HOST_CREDENTIALS.username);
    
    const passwordInput = await hostPage.$('input[type="password"]');
    await passwordInput.type(HOST_CREDENTIALS.password);
    
    // Submit
    await passwordInput.press('Enter');
    
    // Wait for auth to complete
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Click Multiplayer Mode again if needed
    await hostPage.evaluate(() => {
      const mpBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent === 'Multiplayer Mode');
      if (mpBtn) mpBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Create room
    console.log('\n=== PHASE 3: Creating Room ===');
    console.log('Clicking Host Game button...');
    
    await hostPage.evaluate(() => {
      const hostBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent === 'Host Game');
      if (hostBtn) {
        console.log('[CLIENT] About to click Host Game button');
        hostBtn.click();
      }
    });
    
    console.log('Waiting for room creation and player sync...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Check state
    console.log('\n=== PHASE 4: Checking State ===');
    const roomState = await hostPage.evaluate(() => {
      // Try to find player count display
      const playerCountElement = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.match(/Players.*\d+\/\d+/));
      
      const playerCount = playerCountElement ? playerCountElement.textContent : 'Not found';
      
      // Check if in room
      const inRoom = document.body.textContent.includes('Room:') || 
                    document.body.textContent.includes('Ready');
      
      // Look for any player list elements
      const playerListElements = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('Ready') && el.tagName !== 'BUTTON');
      
      return {
        playerCount,
        inRoom,
        playerListCount: playerListElements.length,
        bodyText: document.body.innerText.substring(0, 1000)
      };
    });
    
    console.log('\nRoom State:', JSON.stringify(roomState, null, 2));
    await takeScreenshot(hostPage, 'final-state', 'host');
    
    // Extract more debug info
    console.log('\n=== PHASE 5: Extracting Debug Info ===');
    
    // Try to extract any error messages
    const errors = await hostPage.evaluate(() => {
      const errorElements = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('Error') || el.textContent.includes('error'))
        .map(el => el.textContent.substring(0, 100));
      return errorElements;
    });
    
    if (errors.length > 0) {
      console.log('Found errors:', errors);
    }
    
    console.log('\n=== Test Complete ===');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (hostPage) {
      await takeScreenshot(hostPage, 'error', 'host');
    }
  } finally {
    if (hostBrowser) {
      await hostBrowser.close();
    }
  }
}

// Run the test
debugMultiplayerFlow().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});