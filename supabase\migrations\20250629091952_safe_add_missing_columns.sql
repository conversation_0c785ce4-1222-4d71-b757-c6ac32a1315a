﻿-- Safe migration to add missing columns with existence checks
-- This migration will only add columns that don't already exist

DO $$ 
BEGIN
    -- Add player_bonus_levels column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'player_bonus_levels') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN player_bonus_levels jsonb NOT NULL DEFAULT '{}'::jsonb;
        
        COMMENT ON COLUMN public.game_rooms.player_bonus_levels IS 'Tracks consecutive correct answer bonuses for each player';
    END IF;

    -- Add transition_until column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'transition_until') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN transition_until timestamptz DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.transition_until IS 'Timestamp indicating when the transition period ends and the next question should be shown. NULL when not in transition.';
    END IF;

    -- Add next_question_data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'next_question_data') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN next_question_data jsonb DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.next_question_data IS 'The next question data that will become active after the transition period ends.';
    END IF;
END$$;

-- Create index on transition_until for efficient queries if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE schemaname = 'public' 
                   AND tablename = 'game_rooms' 
                   AND indexname = 'idx_game_rooms_transition_until') THEN
        CREATE INDEX idx_game_rooms_transition_until ON public.game_rooms(transition_until) 
        WHERE transition_until IS NOT NULL;
    END IF;
END$$;
