{"name": "multiplayer-round-advance-tests", "version": "1.0.0", "description": "Automated tests for multiplayer round advance timing", "main": "test-multiplayer-automated.js", "scripts": {"test": "node test-multiplayer-automated.js", "test:headless": "cross-env HEADLESS=true node test-multiplayer-automated.js", "test:debug": "cross-env DEBUG=true node test-multiplayer-automated.js", "setup": "npm install"}, "dependencies": {"puppeteer": "^21.5.0"}, "devDependencies": {"cross-env": "^7.0.3"}}