# Deployment Instructions for Multiplayer Fix

## Summary of Changes
1. Created database migration to enable R<PERSON> on `players_data` table
2. Updated `start-game-handler` to better handle database errors
3. Updated `next-question-handler` to query from database instead of hardcoded data
4. Fixed client-side to call `next-question-handler` when all players answer

## Step 1: Deploy Database Migration

```powershell
cd C:\Projects\recognition-combine\supabase
supabase db push
```

This will apply the migration `20250630000000_fix_players_data_access.sql` which:
- Enables RLS on `players_data` table
- Adds public read access policy
- Creates performance indexes

## Step 2: Deploy Edge Functions

Deploy the updated edge functions using PowerShell scripts:

```powershell
cd C:\Projects\recognition-combine\supabase
.\deploy-start-game-handler.ps1
.\deploy-next-question-handler.ps1
```

## Step 3: Deploy Frontend

The frontend changes are in `web-app/src/app/page.tsx`. Deploy as usual:

```bash
cd C:\Projects\recognition-combine\web-app
npm run build
# Deploy to Vercel or your hosting platform
```

## Testing

After deployment:
1. Create a multiplayer room
2. Join with 2+ players
3. Start the game
4. Verify you see real NFL player names (not "Sample Player Alpha/Bravo/etc")
5. Answer questions and verify the game auto-advances after 3 seconds when all players answer

## Rollback Instructions

If issues occur:
1. Revert the code changes
2. The database migration is safe to keep (it only adds access, doesn't modify data)
3. Redeploy the original edge functions

## Future Improvements
- Remove excessive debug logging (lower priority)
- Add better error handling for edge cases
- Consider caching player data for performance