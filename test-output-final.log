=== SIMPLE MULTIPLAYER SYNC TEST ===

Loading pages...
Player 1 logging in...
Test error: TimeoutError: Waiting for selector `input[name="identifier"]` failed: Waiting failed: 30000ms exceeded
    at new WaitTask (/mnt/c/Projects/recognition-combine/node_modules/puppeteer-core/lib/cjs/puppeteer/common/WaitTask.js:48:34)
    at IsolatedWorld.waitForFunction (/mnt/c/Projects/recognition-combine/node_modules/puppeteer-core/lib/cjs/puppeteer/api/Realm.js:25:26)
    at CSSQueryHandler.waitFor (/mnt/c/Projects/recognition-combine/node_modules/puppeteer-core/lib/cjs/puppeteer/common/QueryHandler.js:178:95)
    at async CdpFrame.waitForSelector (/mnt/c/Projects/recognition-combine/node_modules/puppeteer-core/lib/cjs/puppeteer/api/Frame.js:548:21)
    at async CdpPage.waitForSelector (/mnt/c/Projects/recognition-combine/node_modules/puppeteer-core/lib/cjs/puppeteer/api/Page.js:1377:20)
    at async testMultiplayerSync (/mnt/c/Projects/recognition-combine/test-multiplayer-simple.js:49:7)
