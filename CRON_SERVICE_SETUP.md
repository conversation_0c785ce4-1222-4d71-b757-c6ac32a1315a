# Setting Up External Cron Service for Transition Monitor

## Option 1: Cron-job.org (Recommended - Free)

1. Go to https://cron-job.org and create a free account
2. Create a new cron job with these settings:

**URL:** 
```
https://xmyxuvuimebjltnaamox.supabase.co/functions/v1/transition-monitor
```

**Schedule:** Every minute (their free minimum)
- Or use "Custom" and enter: `*/1 * * * * *`

**Request Method:** POST

**Request Headers:**
```
Authorization: Bearer YOUR_SUPABASE_ANON_KEY
Content-Type: application/json
```

**Request Body:**
```json
{}
```

## Option 2: UptimeRobot (Free tier - 5 min intervals)

1. Sign up at https://uptimerobot.com
2. Add New Monitor → HTTP(s)
3. Monitor Type: HTTP(s)
4. URL: Your transition-monitor URL
5. Monitoring Interval: 5 minutes (free tier limit)

## Option 3: EasyCron (Paid - supports seconds)

1. Sign up at https://www.easycron.com
2. Supports intervals down to 30 seconds on paid plans
3. More reliable for production use

## Option 4: Your Own Server

If you have a server, create a simple cron job:

```bash
# Add to crontab (runs every minute)
* * * * * curl -X POST https://xmyxuvuimebjltnaamox.supabase.co/functions/v1/transition-monitor \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{}'
```

Or use a systemd timer for second-level precision:

```ini
# /etc/systemd/system/transition-monitor.service
[Unit]
Description=Game Transition Monitor

[Service]
Type=oneshot
ExecStart=/usr/bin/curl -X POST https://xmyxuvuimebjltnaamox.supabase.co/functions/v1/transition-monitor -H "Authorization: Bearer YOUR_ANON_KEY" -H "Content-Type: application/json" -d '{}'

# /etc/systemd/system/transition-monitor.timer
[Unit]
Description=Run transition monitor every 2 seconds

[Timer]
OnBootSec=10s
OnUnitActiveSec=2s

[Install]
WantedBy=timers.target
```

## Testing Your Setup

Once configured, verify it's working:

1. Check Supabase Function logs
2. Start a multiplayer game
3. Watch for automatic transitions at the right times