const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;

// Configuration
const BASE_URL = 'http://localhost:3000';
const SCREENSHOTS_DIR = path.join(__dirname, 'football-animation-screenshots');
const SCREENSHOT_DELAY = 100; // ms between screenshots

// Test credentials
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'TestFootball123!';
const TEST_USERNAME = 'FootballTester';

// Helper to ensure screenshots directory exists
async function ensureScreenshotsDir() {
  try {
    await fs.mkdir(SCREENSHOTS_DIR, { recursive: true });
    // Clean up old screenshots
    const files = await fs.readdir(SCREENSHOTS_DIR);
    for (const file of files) {
      if (file.endsWith('.png')) {
        await fs.unlink(path.join(SCREENSHOTS_DIR, file));
      }
    }
  } catch (error) {
    console.error('Error creating screenshots directory:', error);
  }
}

// Helper to take screenshot with timestamp
async function takeScreenshot(page, name) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${timestamp}_${name}.png`;
  await page.screenshot({
    path: path.join(SCREENSHOTS_DIR, filename),
    fullPage: false
  });
  console.log(`📸 Screenshot saved: ${filename}`);
}

// Helper to wait and log
async function waitAndLog(page, ms, message) {
  console.log(`⏳ ${message} (${ms}ms)...`);
  await page.waitForTimeout(ms);
}

async function testFootballAnimation() {
  console.log('🏈 Starting Football Animation Test');
  await ensureScreenshotsDir();

  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {
      width: 1920,
      height: 1080
    },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();

  // Enable console logging
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('FOOTBALL_DEBUG') || text.includes('OPTIMISTIC_ANSWER_DEBUG') || text.includes('animation')) {
      console.log('🖥️ Browser:', text);
    }
  });

  try {
    // Navigate to the app
    console.log('📍 Navigating to app...');
    await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
    await takeScreenshot(page, '01_initial_load');

    // Check if we need to sign up or sign in
    const authModalVisible = await page.evaluate(() => {
      const modal = document.querySelector('[data-testid="auth-modal"]');
      return modal && window.getComputedStyle(modal).display !== 'none';
    });

    if (authModalVisible) {
      console.log('🔐 Auth modal detected, signing up/in...');
      
      // Try to sign in first
      await page.click('button:has-text("Sign In")');
      await waitAndLog(page, 500, 'Switched to sign in');
      
      await page.type('input[type="email"]', TEST_EMAIL);
      await page.type('input[type="password"]', TEST_PASSWORD);
      await takeScreenshot(page, '02_sign_in_filled');
      
      await page.click('button[type="submit"]');
      
      // Wait for either success or need to sign up
      await page.waitForTimeout(2000);
      
      // Check if we need to sign up instead
      const needSignUp = await page.evaluate(() => {
        return document.body.textContent?.includes('Invalid login credentials');
      });
      
      if (needSignUp) {
        console.log('📝 Need to sign up first...');
        await page.click('button:has-text("Sign Up")');
        await waitAndLog(page, 500, 'Switched to sign up');
        
        await page.evaluate(() => {
          document.querySelector('input[type="email"]').value = '';
          document.querySelector('input[type="password"]').value = '';
        });
        
        await page.type('input[placeholder*="username"]', TEST_USERNAME);
        await page.type('input[type="email"]', TEST_EMAIL);
        await page.type('input[type="password"]', TEST_PASSWORD);
        await page.click('button[type="submit"]');
      }
      
      // Wait for auth to complete
      await page.waitForSelector('[data-testid="auth-modal"]', { hidden: true, timeout: 10000 });
      console.log('✅ Authentication successful');
    }

    // Switch to multiplayer mode
    console.log('🎮 Switching to multiplayer mode...');
    await page.click('button:has-text("Multiplayer")');
    await waitAndLog(page, 1000, 'Switched to multiplayer');
    await takeScreenshot(page, '03_multiplayer_lobby');

    // Create a new room
    console.log('🏠 Creating new room...');
    await page.click('button:has-text("Create Room")');
    await waitAndLog(page, 1000, 'Room creation dialog opened');
    
    // Select game settings and create
    await page.click('button:has-text("Create")');
    await waitAndLog(page, 2000, 'Room created');
    await takeScreenshot(page, '04_room_created');

    // Mark as ready
    console.log('✅ Marking as ready...');
    await page.click('button:has-text("Ready")');
    await waitAndLog(page, 1000, 'Marked as ready');

    // Start the game
    console.log('🚀 Starting game...');
    await page.click('button:has-text("Start Game")');
    await waitAndLog(page, 2000, 'Game started');
    await takeScreenshot(page, '05_game_started');

    // Wait for question to load
    await page.waitForSelector('.choice-button', { timeout: 10000 });
    console.log('❓ Question loaded');
    await takeScreenshot(page, '06_question_loaded');

    // Inject debug logging for animation detection
    await page.evaluate(() => {
      console.log('🔍 Injecting animation observers...');
      
      // Watch for football elements
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) { // Element node
              const element = node;
              const text = element.textContent || '';
              if (text.includes('🏈') || element.className?.includes('football')) {
                console.log('🏈 FOOTBALL ELEMENT DETECTED:', {
                  className: element.className,
                  id: element.id,
                  textContent: text.substring(0, 50)
                });
              }
            }
          });
        });
      });
      
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
      
      window.__footballObserver = observer;
    });

    // Get the correct answer for guaranteed animation
    const correctAnswer = await page.evaluate(() => {
      const choices = document.querySelectorAll('.choice-button');
      // In dev mode, the correct answer might be marked
      for (const choice of choices) {
        const text = choice.textContent || '';
        // Check if this might be the correct answer (you may need to adjust this logic)
        return text;
      }
      return choices[0]?.textContent || '';
    });

    console.log(`📝 Submitting answer: ${correctAnswer}`);
    
    // Start rapid screenshot capture before clicking
    const screenshotInterval = setInterval(async () => {
      const timestamp = Date.now();
      await takeScreenshot(page, `during_animation_${timestamp}`);
    }, SCREENSHOT_DELAY);

    // Click the first answer choice
    await page.click('.choice-button:first-child');
    
    // Continue capturing for 3 seconds
    await waitAndLog(page, 3000, 'Capturing animation');
    
    // Stop screenshot capture
    clearInterval(screenshotInterval);

    // Take final screenshots
    await takeScreenshot(page, '07_after_animation');

    // Check for football animations in the DOM
    const animationInfo = await page.evaluate(() => {
      const footballElements = Array.from(document.querySelectorAll('*')).filter(el => 
        el.textContent?.includes('🏈') || 
        el.className?.toString().toLowerCase().includes('football')
      );
      
      return {
        footballElementsCount: footballElements.length,
        elements: footballElements.map(el => ({
          tagName: el.tagName,
          className: el.className?.toString() || '',
          textContent: (el.textContent || '').substring(0, 100),
          isVisible: window.getComputedStyle(el).display !== 'none'
        }))
      };
    });

    console.log('🔍 Animation analysis:', JSON.stringify(animationInfo, null, 2));

    // Wait for next question
    await waitAndLog(page, 2000, 'Waiting for next question');
    await takeScreenshot(page, '08_next_question');

    console.log('✅ Test completed successfully!');
    console.log(`📁 Screenshots saved to: ${SCREENSHOTS_DIR}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    await takeScreenshot(page, 'error_state');
  } finally {
    console.log('🧹 Cleaning up...');
    await page.evaluate(() => {
      if (window.__footballObserver) {
        window.__footballObserver.disconnect();
      }
    });
    
    await waitAndLog(page, 2000, 'Final wait before closing');
    await browser.close();
  }
}

// Run the test
testFootballAnimation().catch(console.error);