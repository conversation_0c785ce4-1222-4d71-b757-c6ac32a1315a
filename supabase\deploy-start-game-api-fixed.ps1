# Deploy start-game-handler Edge Function using Supabase Management API
# This bypasses CLI and Docker requirements entirely

Write-Host "Deploying start-game-handler via Supabase Management API..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"
$functionName = "start-game-handler"

# First, get your access token
Write-Host "`nSTEP 1: Getting access token" -ForegroundColor Yellow
Write-Host "Please go to: https://supabase.com/dashboard/account/tokens" -ForegroundColor Cyan
Write-Host "Create a new access token if you don't have one, and paste it below:" -ForegroundColor Yellow
Write-Host "(The token will be hidden as you type)" -ForegroundColor Gray
$accessToken = Read-Host -Prompt "Access Token" -AsSecureString
$accessTokenPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($accessToken))

if (-not $accessTokenPlain) {
    Write-Host "No token provided. Exiting." -ForegroundColor Red
    exit 1
}

Write-Host "`nSTEP 2: Reading function code" -ForegroundColor Yellow
$functionPath = "functions\$functionName\index.ts"
if (-not (Test-Path $functionPath)) {
    Write-Host "Function file not found at: $functionPath" -ForegroundColor Red
    exit 1
}

$functionCode = Get-Content $functionPath -Raw
Write-Host "Function code loaded: $($functionCode.Length) characters" -ForegroundColor Gray

# Also read the shared CORS file
$corsPath = "functions\_shared\cors.ts"
$corsCode = ""
if (Test-Path $corsPath) {
    $corsCode = Get-Content $corsPath -Raw
    Write-Host "CORS module loaded: $($corsCode.Length) characters" -ForegroundColor Gray
}

Write-Host "`nSTEP 3: Deploying function" -ForegroundColor Yellow

# Create the API request
$headers = @{
    "Authorization" = "Bearer $accessTokenPlain"
    "Content-Type" = "application/json"
}

# For functions with imports from _shared, we need to inline them
# Replace the import statement with the actual code
if ($corsCode) {
    $functionCode = $functionCode -replace "import \{ corsHeaders \} from '\.\.\/_shared\/cors\.ts'", $corsCode
}

$body = @{
    name = $functionName
    slug = $functionName
    verify_jwt = $false
    import_map = @{
        imports = @{
            "jsr:@supabase/functions-js/edge-runtime.d.ts" = "https://esm.sh/jsr/@supabase/functions-js/edge-runtime.d.ts"
            "https://deno.land/std@0.177.0/http/server.ts" = "https://deno.land/std@0.177.0/http/server.ts"
            "https://esm.sh/@supabase/supabase-js@2" = "https://esm.sh/@supabase/supabase-js@2"
        }
    }
    entrypoint_path = "index.ts"
    import_map_path = $null
    body = $functionCode
} | ConvertTo-Json -Depth 10

$uri = "https://api.supabase.com/v1/projects/$projectRef/functions/$functionName"

try {
    Write-Host "Sending deployment request..." -ForegroundColor Cyan
    $response = Invoke-RestMethod -Uri $uri -Method Put -Headers $headers -Body $body
    
    Write-Host "`n✓ Function deployed successfully!" -ForegroundColor Green
    Write-Host "Function URL: https://$projectRef.supabase.co/functions/v1/$functionName" -ForegroundColor Blue
    Write-Host "`nYou can now run the Puppeteer test:" -ForegroundColor Cyan
    Write-Host "node test-multiplayer-game-flow.js" -ForegroundColor Yellow
    
} catch {
    Write-Host "`nDeployment failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Yellow
    }
}

# Clear the token from memory
$accessTokenPlain = $null
[System.GC]::Collect()