# Start Game Handler Fix

## Issue Found
The `start-game-handler` edge function was crashing when trying to start a game because it was attempting to access properties that don't exist on the `firstQuestion` object.

## Root Cause
The code was trying to access:
- `firstQuestion.correctPlayer.player_name` 
- `firstQuestion.correctPlayer.id`

But the `firstQuestion` object (as defined by the `PlayerQuestion` interface) only has:
- `questionId`
- `correctPlayerId`
- `imageUrl`
- `choices`
- `correctChoiceName`

There is no `correctPlayer` property.

## Fix Applied
Changed the following lines in `/supabase/functions/start-game-handler/index.ts`:

1. **Line 358-359**: Changed from:
```typescript
correctPlayerName: firstQuestion.correctPlayer.player_name,
correctPlayerId: firstQuestion.correctPlayer.id,
```
To:
```typescript
correctPlayerName: firstQuestion.correctChoiceName,
correctPlayerId: firstQuestion.correctPlayerId,
```

2. **Line 403**: Changed the validation from:
```typescript
if (!firstQuestion || !firstQuestion.correctPlayer || !firstQuestion.imageUrl || !Array.isArray(firstQuestion.choices)) {
```
To:
```typescript
if (!firstQuestion || !firstQuestion.correctPlayerId || !firstQuestion.imageUrl || !Array.isArray(firstQuestion.choices) || !firstQuestion.correctChoiceName) {
```

3. **Line 409-410**: Changed from:
```typescript
correctPlayerName: firstQuestion.correctPlayer.player_name,
correctPlayerId: firstQuestion.correctPlayer.id,
```
To:
```typescript
correctPlayerName: firstQuestion.correctChoiceName,
correctPlayerId: firstQuestion.correctPlayerId,
```

## Deployment Required
The edge function needs to be deployed to apply this fix. Use one of these methods:

### Method 1: Supabase CLI
```bash
cd supabase
supabase functions deploy start-game-handler
```

### Method 2: PowerShell Script
```powershell
cd supabase
.\deploy-start-game-handler.ps1
```

### Method 3: API Deployment (if CLI not available)
```powershell
cd supabase
.\deploy-start-game-api.ps1
```

## Testing
After deployment, run the multiplayer test again:
```bash
node test-multiplayer-automated.js
```

The game should now properly transition from the waiting state to the playing state when the host clicks "Start Game".