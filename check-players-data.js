const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './web-app/.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

async function checkPlayersData() {
  console.log('Checking players_data table...\n');
  
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  try {
    // Check total count
    const { count: totalCount, error: countError } = await supabase
      .from('players_data')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('Error counting players:', countError);
      return;
    }
    
    console.log(`Total players in table: ${totalCount}`);
    
    // Check players with images
    const { data: playersWithImages, error: imageError } = await supabase
      .from('players_data')
      .select('id, player_name, team_name, local_image_path')
      .not('local_image_path', 'is', null)
      .limit(5);
    
    if (imageError) {
      console.error('Error fetching players with images:', imageError);
      return;
    }
    
    console.log(`\nPlayers with images: ${playersWithImages.length}`);
    console.log('\nSample players:');
    playersWithImages.forEach(player => {
      console.log(`- ${player.player_name} (${player.team_name}) - Image: ${player.local_image_path}`);
    });
    
    // Check if there are enough players for the game
    const { count: imageCount, error: imageCountError } = await supabase
      .from('players_data')
      .select('*', { count: 'exact', head: true })
      .not('local_image_path', 'is', null);
    
    if (!imageCountError) {
      console.log(`\nTotal players with images: ${imageCount}`);
      if (imageCount < 4) {
        console.warn('\n⚠️  WARNING: Not enough players with images for the game to work properly!');
        console.warn('The game needs at least 4 players with images to generate questions.');
      }
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkPlayersData();