// supabase/functions/stale-room-janitor/index.ts

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

console.log('[EDGE_FN_LOAD] stale-room-janitor function script loaded.');

serve(async (req: Request) => {
  console.log(`[EDGE_JANITOR] Request received. Method: ${req.method}, URL: ${req.url}`);
  
  // Handle preflight requests for CORS
  if (req.method === 'OPTIONS') {
    console.log('[EDGE_JANITOR] OPTIONS request, responding with CORS.');
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl || !supabaseServiceRoleKey) {
      console.error('[EDGE_JANITOR] Missing required environment variables');
      throw new Error('Missing required environment variables')
    }

    console.log('[EDGE_JANITOR] Environment variables validated successfully');

    // Create admin client with service role key for full database access
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)
    console.log('[EDGE_JANITOR] Admin client created successfully');

    // Define what "stale" means - rooms in 'waiting' status with no activity for 5 minutes
    const STALE_THRESHOLD_MINUTES = 5;
    const threshold = new Date(Date.now() - STALE_THRESHOLD_MINUTES * 60 * 1000).toISOString();

    console.log(`[EDGE_JANITOR] Looking for 'waiting' rooms with last_activity_timestamp before ${threshold}`);

    // Find all stale rooms
    const { data: staleRooms, error: findError } = await supabaseAdmin
      .from('game_rooms')
      .select('id, title, host_id, created_at, last_activity_timestamp')
      .eq('status', 'waiting')
      .lt('last_activity_timestamp', threshold);

    if (findError) {
      console.error('[EDGE_JANITOR] Error finding stale rooms:', findError);
      throw findError;
    }

    if (!staleRooms || staleRooms.length === 0) {
      console.log('[EDGE_JANITOR] No stale rooms found. Cleanup complete.');
      return new Response(JSON.stringify({ 
        message: 'No stale rooms found.',
        cleaned_rooms: [],
        threshold_used: threshold
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      });
    }

    console.log(`[EDGE_JANITOR] Found ${staleRooms.length} stale rooms to clean up:`, 
      staleRooms.map(room => ({ 
        id: room.id, 
        title: room.title, 
        last_activity: room.last_activity_timestamp 
      }))
    );

    const staleRoomIds = staleRooms.map(room => room.id);

    // CRITICAL: Delete associated players first to respect foreign key constraints
    console.log(`[EDGE_JANITOR] Deleting players from stale rooms...`);
    const { error: deletePlayersError } = await supabaseAdmin
      .from('game_players')
      .delete()
      .in('room_id', staleRoomIds);

    if (deletePlayersError) {
      console.error('[EDGE_JANITOR] Error deleting stale players:', deletePlayersError);
      throw deletePlayersError;
    }
    console.log(`[EDGE_JANITOR] ✅ Successfully deleted players from stale rooms`);

    // Now delete the stale rooms themselves
    console.log(`[EDGE_JANITOR] Deleting stale rooms...`);
    const { error: deleteRoomsError } = await supabaseAdmin
      .from('game_rooms')
      .delete()
      .in('id', staleRoomIds);

    if (deleteRoomsError) {
      console.error('[EDGE_JANITOR] Error deleting stale rooms:', deleteRoomsError);
      throw deleteRoomsError;
    }

    console.log(`[EDGE_JANITOR] ✅ Successfully deleted ${staleRooms.length} stale rooms`);
    console.log(`[EDGE_JANITOR] Cleanup complete. Deleted room IDs: ${staleRoomIds.join(', ')}`);

    return new Response(JSON.stringify({
      message: 'Cleanup successful',
      cleaned_rooms: staleRooms,
      threshold_used: threshold,
      rooms_deleted: staleRooms.length
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('[EDGE_JANITOR] CRITICAL ERROR during stale room cleanup:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      details: 'Failed to clean up stale rooms'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
