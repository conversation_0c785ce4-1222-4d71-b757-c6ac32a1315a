// Test start-game-handler Edge Function directly
const https = require('https');
const fs = require('fs');
const path = require('path');

// Read service role key from .env
const envPath = path.join(__dirname, '.env');
const envContent = fs.readFileSync(envPath, 'utf8');
const serviceRoleKey = envContent.match(/SUPABASE_SERVICE_ROLE_KEY=(.+)/)[1].replace(/"/g, '');

const projectRef = 'xmyxuvuimebjltnaamox';
const functionUrl = `https://${projectRef}.supabase.co/functions/v1/start-game-handler`;

// Create a test room first
const { createClient } = require('@supabase/supabase-js');
const supabaseUrl = `https://${projectRef}.supabase.co`;
const supabase = createClient(supabaseUrl, serviceRoleKey);

async function testStartGame() {
  try {
    console.log('Creating test room...');
    
    // Create a test room
    const { data: room, error: roomError } = await supabase
      .from('game_rooms')
      .insert({
        room_code: 'TEST' + Date.now(),
        host_id: '6b6b4242-a0b8-4f36-8811-14bc815c0ef3', // <EMAIL> user ID
        status: 'waiting',
        current_round: 0,
        current_question_index: 0,
        max_players: 4,
        host_display_name: 'Test Host'
      })
      .select()
      .single();
      
    if (roomError) {
      console.error('Failed to create room:', roomError);
      return;
    }
    
    console.log('Created room:', room.id);
    
    // Add host as player
    const { error: playerError } = await supabase
      .from('game_players')
      .insert({
        room_id: room.id,
        player_id: room.host_id,
        display_name: 'Test Host',
        score: 0,
        is_ready: true,
        is_connected: true
      });
      
    if (playerError) {
      console.error('Failed to add host player:', playerError);
      return;
    }
    
    // Call start-game-handler
    console.log('\nCalling start-game-handler...');
    const payload = JSON.stringify({
      roomId: room.id,
      hostPlayerId: room.host_id
    });
    
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceRoleKey}`,
        'Content-Length': Buffer.byteLength(payload)
      }
    };
    
    const req = https.request(functionUrl, options, (res) => {
      console.log(`\nStatus: ${res.statusCode} ${res.statusMessage}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', async () => {
        console.log('\nResponse:');
        try {
          const parsed = JSON.parse(data);
          console.log(JSON.stringify(parsed, null, 2));
          
          if (res.statusCode === 200) {
            console.log('\n✅ Start game handler executed successfully!');
            
            // Check room status
            const { data: updatedRoom } = await supabase
              .from('game_rooms')
              .select('*')
              .eq('id', room.id)
              .single();
              
            console.log('\nUpdated room status:', updatedRoom?.status);
            console.log('Timer state:', updatedRoom?.timer_state);
          }
        } catch (e) {
          console.log(data);
        }
        
        // Cleanup
        console.log('\nCleaning up test room...');
        await supabase.from('game_players').delete().eq('room_id', room.id);
        await supabase.from('game_rooms').delete().eq('id', room.id);
      });
    });
    
    req.on('error', (error) => {
      console.error('Request error:', error);
    });
    
    req.write(payload);
    req.end();
    
  } catch (err) {
    console.error('Test failed:', err);
  }
}

testStartGame();