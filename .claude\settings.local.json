{"permissions": {"allow": ["Bash(supabase db:*)", "Bash(npx supabase:*)", "Bash(grep:*)", "Bash(npm run lint)", "Bash(rg:*)", "Bash(deno check:*)", "Bash(npm run lint:*)", "Bash(npm run build:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "<PERSON><PERSON>(libnss3 )", "<PERSON><PERSON>(libnspr4 )", "Bash(libatk1.0-0 )", "Bash(libatk-bridge2.0-0 )", "Bash(libcups2 )", "<PERSON><PERSON>(libdrm2 )", "Bash(libxkbcommon0 )", "Bash(libxcomposite1 )", "<PERSON><PERSON>(libxdamage1 )", "Bash(libxfixes3 )", "<PERSON><PERSON>(libxrandr2 )", "Bash(libgbm1 )", "<PERSON><PERSON>(libasound2)", "Bash(apt list:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm ls:*)", "Bash(chromium:*)", "Bash(chromium-browser:*)", "Bash(google-chrome:*)", "Bash(npx @modelcontextprotocol/server-puppeteer:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_fill", "Bash(supabase functions deploy:*)", "Bash(powershell.exe:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm install:*)", "Bash(pwsh:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(cat:*)", "Bash(HEADLESS=false timeout 30 node test-multiplayer-game-flow.js)", "Bash(bash:*)", "Bash(./test-edge-simple-curl.sh:*)", "<PERSON><PERSON>(timeout:*)", "Bash(cp:*)"], "deny": []}}