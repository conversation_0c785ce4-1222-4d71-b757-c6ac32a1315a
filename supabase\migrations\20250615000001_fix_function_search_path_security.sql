-- Fix Function Search Path Security Issues
-- This migration adds explicit search_path settings to all functions to prevent search path attacks
-- Reference: https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable

-- 1. Fix is_player_in_room function
CREATE OR REPLACE FUNCTION public.is_player_in_room(p_room_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
DECLARE
  is_player boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.game_players
    WHERE room_id = p_room_id AND user_id = auth.uid()
  ) INTO is_player;
  
  RETURN is_player;
END;
$$;

-- 2. Fix room_has_space function
CREATE OR REPLACE FUNCTION public.room_has_space(p_room_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  current_players integer;
  max_players integer;
BEGIN
  -- Get current player count and max players for the room
  SELECT 
    (SELECT COUNT(*) FROM public.game_players WHERE room_id = p_room_id),
    gr.max_players
  INTO current_players, max_players
  FROM public.game_rooms gr
  WHERE gr.id = p_room_id;
  
  -- Return true if room has space
  RETURN current_players < max_players;
END;
$$;

-- 3. Fix debug_auth_context function
CREATE OR REPLACE FUNCTION public.debug_auth_context()
RETURNS TABLE(
    auth_uid_val uuid,
    auth_role_val text,
    current_db_user_val text,
    jwt_sub_val text,
    jwt_role_val text,
    session_user_id_val uuid
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
DECLARE
    _sub text;
    _role text;
    _session_user_id uuid;
BEGIN
    _sub := current_setting('request.jwt.claims.sub', true);
    _role := current_setting('request.jwt.claims.role', true);

    BEGIN
        _session_user_id := current_setting('request.jwt.claims', true)::jsonb->>'sub';
    EXCEPTION WHEN OTHERS THEN
        _session_user_id := NULL;
    END;

    RETURN QUERY
    SELECT
        auth.uid() AS auth_uid_val,
        auth.role() AS auth_role_val,
        current_user::text AS current_db_user_val,
        _sub AS jwt_sub_val,
        _role AS jwt_role_val,
        _session_user_id AS session_user_id_val;
END;
$$;

-- 4. Fix handle_updated_at function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS trigger
LANGUAGE plpgsql
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;

-- 5. Fix update_last_seen_at function
CREATE OR REPLACE FUNCTION public.update_last_seen_at()
RETURNS trigger
LANGUAGE plpgsql
SET search_path = public
AS $$
BEGIN
    -- Only update last_seen_at if it's not explicitly being set in the UPDATE
    -- This prevents overriding intentional timestamp updates
    IF TG_OP = 'UPDATE' AND OLD.last_seen_at = NEW.last_seen_at THEN
        NEW.last_seen_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$;

-- 6. Fix update_updated_at_column function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS trigger
LANGUAGE plpgsql
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- 7. Fix test_rls_check function
CREATE OR REPLACE FUNCTION public.test_rls_check(p_user_id uuid)
RETURNS TABLE(
    auth_uid uuid,
    provided_user_id uuid,
    ids_match boolean,
    would_pass_rls boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
BEGIN
    RETURN QUERY
    SELECT
        auth.uid() as auth_uid,
        p_user_id as provided_user_id,
        auth.uid() = p_user_id as ids_match,
        (auth.uid() = p_user_id) as would_pass_rls;
END;
$$;

-- 8. Fix cleanup_stale_rooms function
CREATE OR REPLACE FUNCTION public.cleanup_stale_rooms(
    stale_threshold_hours integer DEFAULT 24,
    inactive_threshold_hours integer DEFAULT 6,
    dry_run boolean DEFAULT true
)
RETURNS TABLE(
    room_id uuid,
    room_title text,
    room_status text,
    created_at timestamptz,
    last_activity timestamptz,
    total_players integer,
    connected_players integer,
    cleanup_reason text,
    action text
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  room_record RECORD;
  stale_threshold TIMESTAMPTZ;
  inactive_threshold TIMESTAMPTZ;
  deleted_count INTEGER := 0;
  analyzed_count INTEGER := 0;
BEGIN
  -- Calculate thresholds
  stale_threshold := NOW() - (stale_threshold_hours || ' hours')::INTERVAL;
  inactive_threshold := NOW() - (inactive_threshold_hours || ' hours')::INTERVAL;

  RAISE NOTICE 'Starting stale room cleanup (dry_run: %, stale_threshold: %, inactive_threshold: %)',
    dry_run, stale_threshold, inactive_threshold;

  -- Analyze all non-completed rooms
  FOR room_record IN
    SELECT
      gr.id,
      gr.title,
      gr.status,
      gr.created_at,
      gr.last_activity_timestamp,
      gr.host_id,
      COUNT(gp.id) as total_players,
      COUNT(CASE WHEN gp.is_connected = true THEN 1 END) as connected_players,
      MAX(gp.last_seen_at) as last_player_activity
    FROM game_rooms gr
    LEFT JOIN game_players gp ON gr.id = gp.room_id
    WHERE gr.status != 'completed'
    GROUP BY gr.id, gr.title, gr.status, gr.created_at, gr.last_activity_timestamp, gr.host_id
  LOOP
    analyzed_count := analyzed_count + 1;

    -- Determine if room should be cleaned up and why
    DECLARE
      should_cleanup BOOLEAN := false;
      cleanup_reason TEXT := '';
      action TEXT := 'KEEP';
    BEGIN

      -- Criteria 1: Empty rooms (no players at all)
      IF room_record.total_players = 0 THEN
        should_cleanup := true;
        cleanup_reason := 'No players in room';

      -- Criteria 2: No connected players and room is old
      ELSIF room_record.connected_players = 0 AND room_record.created_at < stale_threshold THEN
        should_cleanup := true;
        cleanup_reason := 'No connected players and room older than ' || stale_threshold_hours || ' hours';

      -- Criteria 3: Waiting rooms with no activity for extended period
      ELSIF room_record.status = 'waiting' AND
            COALESCE(room_record.last_activity_timestamp, room_record.created_at) < inactive_threshold THEN
        should_cleanup := true;
        cleanup_reason := 'Waiting room inactive for more than ' || inactive_threshold_hours || ' hours';

      -- Criteria 4: Active rooms with no connected players and no recent activity
      ELSIF room_record.status = 'active' AND
            room_record.connected_players = 0 AND
            COALESCE(room_record.last_player_activity, room_record.last_activity_timestamp, room_record.created_at) < stale_threshold THEN
        should_cleanup := true;
        cleanup_reason := 'Active room with no connected players and no activity for ' || stale_threshold_hours || ' hours';

      END IF;

      -- Execute cleanup if needed
      IF should_cleanup THEN
        IF NOT dry_run THEN
          -- Delete the room (CASCADE will handle game_players)
          DELETE FROM game_rooms WHERE id = room_record.id;
          deleted_count := deleted_count + 1;
          action := 'DELETED';
          RAISE NOTICE 'Deleted stale room: % (%) - %', room_record.id, room_record.title, cleanup_reason;
        ELSE
          action := 'WOULD_DELETE';
          RAISE NOTICE 'Would delete room: % (%) - %', room_record.id, room_record.title, cleanup_reason;
        END IF;
      END IF;

      -- Return analysis result
      RETURN QUERY SELECT
        room_record.id,
        room_record.title,
        room_record.status,
        room_record.created_at,
        COALESCE(room_record.last_activity_timestamp, room_record.created_at),
        room_record.total_players::INTEGER,
        room_record.connected_players::INTEGER,
        cleanup_reason,
        action;

    END;
  END LOOP;

  RAISE NOTICE 'Cleanup complete. Analyzed: %, Deleted: %', analyzed_count, deleted_count;

END;
$$;

-- 9. Fix get_room_cleanup_stats function
CREATE OR REPLACE FUNCTION public.get_room_cleanup_stats()
RETURNS TABLE(
    stat_name text,
    count_value integer,
    details jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Total rooms
  RETURN QUERY SELECT
    'total_rooms'::TEXT,
    COUNT(*)::INTEGER,
    jsonb_build_object('breakdown', jsonb_object_agg(status, cnt))
  FROM (
    SELECT status, COUNT(*) as cnt
    FROM game_rooms
    GROUP BY status
  ) t;

  -- Empty rooms (no players)
  RETURN QUERY
  WITH empty_rooms AS (
    SELECT gr.id, gr.title, gr.status, gr.created_at
    FROM game_rooms gr
    LEFT JOIN game_players gp ON gr.id = gp.room_id
    WHERE gr.status != 'completed'
    GROUP BY gr.id, gr.title, gr.status, gr.created_at
    HAVING COUNT(gp.id) = 0
  )
  SELECT
    'empty_rooms'::TEXT,
    COUNT(*)::INTEGER,
    COALESCE(jsonb_agg(jsonb_build_object('id', id, 'title', title, 'status', status, 'created_at', created_at)), '[]'::jsonb)
  FROM empty_rooms;

  -- Rooms with no connected players
  RETURN QUERY
  WITH disconnected_rooms AS (
    SELECT
      gr.id, gr.title, gr.status,
      COUNT(gp.id) as total_players,
      COUNT(CASE WHEN gp.is_connected = true THEN 1 END) as connected_players
    FROM game_rooms gr
    LEFT JOIN game_players gp ON gr.id = gp.room_id
    WHERE gr.status != 'completed'
    GROUP BY gr.id, gr.title, gr.status
    HAVING COUNT(CASE WHEN gp.is_connected = true THEN 1 END) = 0 AND COUNT(gp.id) > 0
  )
  SELECT
    'no_connected_players'::TEXT,
    COUNT(*)::INTEGER,
    COALESCE(jsonb_agg(jsonb_build_object('id', id, 'title', title, 'status', status, 'total_players', total_players)), '[]'::jsonb)
  FROM disconnected_rooms;

  -- Old inactive rooms
  RETURN QUERY
  WITH old_rooms AS (
    SELECT
      gr.id, gr.title, gr.status,
      EXTRACT(EPOCH FROM (NOW() - COALESCE(gr.last_activity_timestamp, gr.created_at))) / 3600 as age_hours
    FROM game_rooms gr
    WHERE gr.status != 'completed'
      AND COALESCE(gr.last_activity_timestamp, gr.created_at) < NOW() - INTERVAL '24 hours'
  )
  SELECT
    'old_inactive_rooms'::TEXT,
    COUNT(*)::INTEGER,
    COALESCE(jsonb_agg(jsonb_build_object('id', id, 'title', title, 'status', status, 'age_hours', age_hours)), '[]'::jsonb)
  FROM old_rooms;

END;
$$;

-- 10. Fix cleanup_empty_rooms_enhanced function
CREATE OR REPLACE FUNCTION public.cleanup_empty_rooms_enhanced()
RETURNS trigger
LANGUAGE plpgsql
SET search_path = public
AS $$
BEGIN
  -- Check if this was a DELETE operation on game_players
  IF TG_OP = 'DELETE' THEN
    DECLARE
      player_count INTEGER;
      room_status TEXT;
      room_title TEXT;
    BEGIN
      -- Count remaining players in the room
      SELECT COUNT(*) INTO player_count
      FROM game_players
      WHERE room_id = OLD.room_id;

      -- Get room details
      SELECT status, title INTO room_status, room_title
      FROM game_rooms
      WHERE id = OLD.room_id;

      -- If no players remain and room is not completed, delete the room
      IF player_count = 0 AND room_status != 'completed' THEN
        DELETE FROM game_rooms WHERE id = OLD.room_id;

        RAISE NOTICE 'Auto-deleted empty room: % (%) - status: %',
          OLD.room_id, COALESCE(room_title, 'Untitled'), room_status;
      END IF;
    END;
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$$;
