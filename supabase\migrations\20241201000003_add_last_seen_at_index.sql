-- Migration: Add index on last_seen_at for efficient stale connection cleanup
-- Created: 2024-12-01
-- Purpose: Optimize heartbeat system and stale connection cleanup queries

-- Add index on last_seen_at for efficient queries when finding stale connections
CREATE INDEX IF NOT EXISTS idx_game_players_last_seen_at 
ON game_players (last_seen_at);

-- Add composite index for is_connected + last_seen_at for heartbeat cleanup queries
CREATE INDEX IF NOT EXISTS idx_game_players_connected_last_seen 
ON game_players (is_connected, last_seen_at);

-- Add index on room_id + is_connected for efficient room player queries
CREATE INDEX IF NOT EXISTS idx_game_players_room_connected 
ON game_players (room_id, is_connected);

-- Add function to automatically update last_seen_at on any game_players update
-- This ensures the timestamp is always current when players interact
CREATE OR REPLACE FUNCTION update_last_seen_at()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update last_seen_at if it's not explicitly being set in the UPDATE
    -- This prevents overriding intentional timestamp updates
    IF TG_OP = 'UPDATE' AND OLD.last_seen_at = NEW.last_seen_at THEN
        NEW.last_seen_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update last_seen_at on game_players updates
-- This trigger will fire before any UPDATE on game_players table
DROP TRIGGER IF EXISTS trigger_update_last_seen_at ON game_players;
CREATE TRIGGER trigger_update_last_seen_at
    BEFORE UPDATE ON game_players
    FOR EACH ROW
    EXECUTE FUNCTION update_last_seen_at();

-- Add comments for documentation
COMMENT ON INDEX idx_game_players_last_seen_at IS 'Index for efficient stale connection cleanup queries';
COMMENT ON INDEX idx_game_players_connected_last_seen IS 'Composite index for heartbeat system queries';
COMMENT ON INDEX idx_game_players_room_connected IS 'Index for room-specific connected player queries';
COMMENT ON FUNCTION update_last_seen_at() IS 'Automatically updates last_seen_at timestamp on game_players updates';
COMMENT ON TRIGGER trigger_update_last_seen_at ON game_players IS 'Trigger to maintain last_seen_at timestamps automatically';
