﻿# Singleplayer Game Files Documentation
Generated on: 2025-05-27 22:10:00

This document contains all the relevant files for the singleplayer game implementation and the football emoji celebration animation system. These files should be used as reference for implementing similar functionality in the multiplayer experience.

## Overview

The singleplayer game consists of:
1. **Core Game Logic** - Game state management, question generation, scoring
2. **UI Components** - Player image display, choice buttons, score panels
3. **Animation System** - Football emoji celebrations, score popups, time change animations
4. **Data Management** - Player data loading, image handling
5. **Game Modes** - Normal mode and Timed mode implementations

## File Categories

### Core Game Files
- Game state management and logic
- Question generation and validation
- Score tracking and persistence

### UI Components
- Player image display
- Choice buttons and interactions
- Score and info panels

### Animation System
- Football emoji celebration effects
- Score change popups
- Time change animations
- Loading animations

### Data Management
- Player data loading and caching
- Image path handling
- Type definitions

---


## Core Game Logic Files

### Core Game Logic: src/stores/gameStore.ts

**Description:** Main game state management using Zustand. Handles game modes (normal/timed), scoring, question flow, timer management, and game lifecycle.

**Key Features:**
- Game mode switching (normal/timed)
- Score tracking and persistence
- Question generation and validation
- Timer management for timed mode
- Streak tracking
- Recent answers management
- Animation triggers

**File Size:** 363 lines

**File Content:**
```typescript
import { create } from 'zustand';
import type { PlayerData, PlayerQuestion, RecentAnswer, GameModeType } from '@/types';
import { loadPlayerData, generateQuestion } from '@/lib/playerData';

// Constants
const INITIAL_TIMED_MODE_DURATION = 60; // seconds
const MAX_TIMER_CAP = 60; // seconds

// Helper for logging
const logging = {
  info: (message: string) => console.log(`[GameStore] INFO: ${message}`),
  warning: (message: string) => console.warn(`[GameStore] WARN: ${message}`),
  error: (message: string) => console.error(`[GameStore] ERROR: ${message}`),
};

interface GameState {
  // Core game state
  players: PlayerData[];
  currentQuestion: PlayerQuestion | null;
  gameStatus: 'idle' | 'loading' | 'countdown' | 'playing' | 'finished';
  activeGameMode: GameModeType;
  
  // Scores and tracking
  score: number;
  timedScore: number;
  streak: number;
  bestStreak: number;
  bestNormalScore: number;
  bestTimedScore: number;
  correctCount: number;
  totalAsked: number;
  
  // Answer state
  isAnswered: boolean;
  lastAnswerCorrect: boolean | null;
  userChoiceName: string | null;
  recentAnswers: RecentAnswer[];
  
  // UI state
  askedPlayerIds: Set<number>;
  lastScoreChange: number | null;
  animationTrigger: number;
  nextQuestionImageToPreload: string | null;
  lastTimeChange: number | null;
  timeChangeAnimationTrigger: number;
  
  // Loading state
  isLoadingInitialGame: boolean;
  loadingMessage: string;
  isInitialQuestionReady: boolean;
  
  // Timed mode state
  timer: number;
  isCountdownActive: boolean;
  countdownValue: number;
  _timerIntervalId: NodeJS.Timeout | null;
  
  // Actions
  loadPlayers: () => Promise<void>;
  setGameMode: (mode: GameModeType) => void;
  startCountdown: () => void;
  _tickCountdown: () => void;
  _initializeTimedModeGame: () => void;
  _initializeNormalModeGame: () => void;
  nextQuestion: () => void;
  submitAnswer: (choiceName: string) => void;
  resetCurrentModeGame: () => void;
  _startTimerInterval: () => void;
  _stopTimerInterval: () => void;
  _tickTimer: () => void;
}

// Persist best scores to localStorage
const getStoredBestScores = () => {
  if (typeof window !== 'undefined') {
    const normal = localStorage.getItem('bestNormalScore');
    const timed = localStorage.getItem('bestTimedScore');
    return {
      bestNormalScore: normal ? parseInt(normal, 10) : 0,
      bestTimedScore: timed ? parseInt(timed, 10) : 0,
    };
  }
  return { bestNormalScore: 0, bestTimedScore: 0 };
};

const storedBestScores = getStoredBestScores();

export const useGameStore = create<GameState>((set, get) => ({
  // Initial state
  players: [],
  currentQuestion: null,
  score: 0,
  timedScore: 0,
  streak: 0,
  bestStreak: 0,
  bestNormalScore: storedBestScores.bestNormalScore,
  bestTimedScore: storedBestScores.bestTimedScore,
  correctCount: 0,
  totalAsked: 0,
  isAnswered: false,
  lastAnswerCorrect: null,
  userChoiceName: null,
  recentAnswers: [],
  gameStatus: 'idle',
  activeGameMode: 'normal',
  askedPlayerIds: new Set(),
  lastScoreChange: null,
  animationTrigger: 0,
  nextQuestionImageToPreload: null,
  lastTimeChange: null,
  timeChangeAnimationTrigger: 0,
  isLoadingInitialGame: true,
  loadingMessage: "Initializing Game...",
  isInitialQuestionReady: false,
  timer: INITIAL_TIMED_MODE_DURATION,
  isCountdownActive: false,
  countdownValue: 3,
  _timerIntervalId: null,

  loadPlayers: async () => {
    set({ isLoadingInitialGame: true, loadingMessage: "Initializing Game...", activeGameMode: 'normal' });
    const players = await loadPlayerData();
    set({ players, loadingMessage: "Preparing Game..." });
    logging.info("Players loaded in store");
    get()._initializeNormalModeGame(); // Start with normal mode
    set({ isLoadingInitialGame: false, isInitialQuestionReady: true });
  },

  setGameMode: (mode) => {
    const currentMode = get().activeGameMode;
    if (currentMode === mode && get().gameStatus === 'playing') {
      logging.info(`Already in ${mode} mode and playing. To restart, reset first or finish game.`);
    }

    logging.info(`Switching game mode to: ${mode}`);
    get()._stopTimerInterval(); // Stop any active timers

    set({ activeGameMode: mode, isCountdownActive: false, gameStatus: 'idle' });

    if (mode === 'timed') {
      get().startCountdown();
    } else { // 'normal'
      get()._initializeNormalModeGame();
    }
  },

  startCountdown: () => {
    logging.info("Starting countdown for Timed Mode...");
    set({ 
      gameStatus: 'countdown', 
      isCountdownActive: true, 
      countdownValue: 3,
      recentAnswers: [],
      timedScore: 0,
      score: 0,
      timer: INITIAL_TIMED_MODE_DURATION,
      lastTimeChange: null,
      timeChangeAnimationTrigger: 0,
    });
    get()._tickCountdown();
  },

  _tickCountdown: () => {
    setTimeout(() => {
      const currentCountdown = get().countdownValue;
      if (currentCountdown > 1) {
        set(state => ({ countdownValue: state.countdownValue - 1 }));
        get()._tickCountdown();
      } else {
        set({ isCountdownActive: false, countdownValue: 0 });
        logging.info("Countdown finished. Initializing Timed Mode game.");
        get()._initializeTimedModeGame();
      }
    }, 1000);
  },
  
  _initializeNormalModeGame: () => {
    logging.info("Initializing Normal Mode game...");
    set(state => ({
      currentQuestion: null,
      score: 0,
      streak: 0,
      correctCount: 0,
      totalAsked: 0,
      isAnswered: false,
      lastAnswerCorrect: null,
      gameStatus: 'idle',
      askedPlayerIds: new Set(),
      activeGameMode: 'normal',
      isCountdownActive: false,
      lastTimeChange: null,
      timeChangeAnimationTrigger: 0,
    }));
    get().nextQuestion();
    logging.info("Normal mode initialized.");
  },

  _initializeTimedModeGame: () => {
    logging.info("Initializing Timed Mode game (after countdown)...");
    set(state => ({
      currentQuestion: null,
      correctCount: 0,
      totalAsked: 0,
      isAnswered: false,
      lastAnswerCorrect: null,
      gameStatus: 'idle',
      askedPlayerIds: new Set(),
      activeGameMode: 'timed',
      timer: INITIAL_TIMED_MODE_DURATION,
      lastTimeChange: null,
      timeChangeAnimationTrigger: 0,
    }));
    get().nextQuestion();
    get()._startTimerInterval();
    logging.info("Timed mode initialized and timer started.");
  },

  nextQuestion: () => {
    const { players, askedPlayerIds, activeGameMode } = get();
    
    set({ 
      isAnswered: false,
      lastAnswerCorrect: null,
      userChoiceName: null,
      lastScoreChange: null,
      lastTimeChange: null,
    });

    const question = generateQuestion(players, askedPlayerIds);

    if (question) {
      set((state) => ({
        currentQuestion: question,
        totalAsked: state.totalAsked + 1,
        gameStatus: 'playing',
        askedPlayerIds: new Set(state.askedPlayerIds).add(question.correctPlayer.id),
      }));
    } else {
      logging.info("No more questions available or error generating.");
      set({ gameStatus: 'finished' }); 
      if (activeGameMode === 'timed') {
        get()._stopTimerInterval();
      }
    }
  },

  submitAnswer: (choiceName: string) => {
    const { 
      currentQuestion, 
      score, 
      streak, 
      bestStreak, 
      correctCount, 
      recentAnswers, 
      animationTrigger, 
      activeGameMode, 
      timer, 
      bestNormalScore, 
      bestTimedScore,
      gameStatus,
      timeChangeAnimationTrigger
    } = get();

    if (!currentQuestion || get().isAnswered || gameStatus !== 'playing') {
      console.warn("[GameStore] submitAnswer called in invalid state. Ignoring.", { 
        isAnswered: get().isAnswered, 
        gameStatus 
      });
      return;
    }

    const correctChoice = currentQuestion.choices.find(c => c.isCorrect);
    const isCorrect = correctChoice?.name === choiceName;
    
    let newScore = score;
    let scoreDiff = 0;
    let newStreak = streak;
    let newBestStreak = bestStreak;
    let newCorrectCount = isCorrect ? correctCount + 1 : correctCount;
    let newTimer = timer;
    let newTimeChange = 0;

    set({ 
      isAnswered: true,
      lastAnswerCorrect: isCorrect,
      userChoiceName: choiceName
    });

    if (activeGameMode === 'normal') {
      newScore = isCorrect ? score + 10 : score;
      newStreak = isCorrect ? streak + 1 : 0;
      newBestStreak = Math.max(bestStreak, newStreak);
      scoreDiff = newScore - score;
      if (newScore > bestNormalScore) {
        set({ bestNormalScore: newScore });
        if (typeof window !== 'undefined') localStorage.setItem('bestNormalScore', newScore.toString());
      }
      const newRecentAnswer: RecentAnswer = {
        player: currentQuestion.correctPlayer,
        isCorrect: isCorrect,
        timestamp: Date.now(),
      };
      set(state => ({ recentAnswers: [newRecentAnswer, ...state.recentAnswers].slice(0, 10) }));
    } else { // Timed Mode
      newScore = isCorrect ? score + 10 : score;
      scoreDiff = newScore - score;
      if (isCorrect) {
        newTimer = Math.min(timer + 1, MAX_TIMER_CAP);
        newTimeChange = 1;
      } else {
        newTimer = Math.max(timer - 1, 0);
        newTimeChange = -1;
      }
      if (newScore > bestTimedScore) {
        set({ bestTimedScore: newScore });
        if (typeof window !== 'undefined') localStorage.setItem('bestTimedScore', newScore.toString());
      }
    }
    
    set(state => ({
      score: newScore,
      streak: newStreak,
      bestStreak: newBestStreak,
      correctCount: newCorrectCount,
      lastScoreChange: scoreDiff > 0 ? scoreDiff : null,
      animationTrigger: scoreDiff > 0 ? state.animationTrigger + 1 : state.animationTrigger,
      timer: newTimer,
      lastTimeChange: activeGameMode === 'timed' ? newTimeChange : null,
      timeChangeAnimationTrigger: activeGameMode === 'timed' && newTimeChange !== 0 ? state.timeChangeAnimationTrigger + 1 : state.timeChangeAnimationTrigger,
    }));

    if (activeGameMode === 'timed') {
      if (newTimer <= 0) {
        logging.info("Timer reached 0 due to penalty. Game Over for Timed Mode.");
        get()._stopTimerInterval();
        set({ gameStatus: 'finished' });
      } else {
        setTimeout(() => {
          if (get().gameStatus === 'playing') {
            get().nextQuestion();
          }
        }, 150);
      }
    }
  },

  resetCurrentModeGame: () => {
    logging.info(`Resetting game for mode: ${get().activeGameMode}`);
    get()._stopTimerInterval();
    if (get().activeGameMode === 'timed') {
      get().startCountdown(); // Restart timed mode with countdown
    } else {
      get()._initializeNormalModeGame(); // Restart normal mode
    }
  },

  _startTimerInterval: () => {
    get()._stopTimerInterval(); // Clear any existing timer
    logging.info("Starting timer interval for Timed Mode.");
    const intervalId = setInterval(() => {
      get()._tickTimer();
    }, 1000);
    set({ _timerIntervalId: intervalId });
  },

  _stopTimerInterval: () => {
    const intervalId = get()._timerIntervalId;
    if (intervalId) {
      logging.info("Stopping timer interval.");
      clearInterval(intervalId);
      set({ _timerIntervalId: null });
    }
  },

  _tickTimer: () => {
    const currentTimer = get().timer;
    if (currentTimer > 0) {
      set(state => ({ timer: state.timer - 1 }));
    } else {
      logging.info("Timer reached 0. Game Over for Timed Mode.");
      get()._stopTimerInterval();
      set({ gameStatus: 'finished' });
      // Update best timed score if current is higher
      if (get().score > get().bestTimedScore) {
        set(state => ({ bestTimedScore: state.score }));
        if (typeof window !== 'undefined') localStorage.setItem('bestTimedScore', get().score.toString());
      }
    }
  },
})); 
```

---

### Data Management: src/lib/playerData.ts

**Description:** Player data loading and question generation logic. Loads from players_game_data.json and generates random questions with choices.

**Key Features:**
- Player data caching
- Question generation with 4 choices
- Image URL construction
- Distractor selection logic
- Data validation

**File Size:** 91 lines

**File Content:**
```typescript
import type { PlayerData, PlayerQuestion, PlayerChoice } from '@/types'; // Use alias @

let allPlayersCache: PlayerData[] | null = null;

export async function loadPlayerData(): Promise<PlayerData[]> {
  // Basic caching to avoid re-fetching on every call
  if (allPlayersCache) {
    return allPlayersCache;
  }
  try {
    // Fetch from the game-ready data file
    const response = await fetch('/data/players_game_data.json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    
    // Basic validation (check if it's an array)
    if (!Array.isArray(data)) {
        throw new Error("Loaded game data is not an array");
    }
    
    // No need to filter by local_image_path anymore as prepare_game_data.py already did it
    allPlayersCache = data;
    
    console.log(`Loaded and cached ${allPlayersCache.length} game-ready players.`);
    return allPlayersCache;
  } catch (error) {
    console.error("Failed to load game-ready player data:", error);
    return []; // Return empty array on error
  }
}

// Helper to shuffle array (Fisher-Yates algorithm)
function shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]]; // Swap elements
    }
    return array;
}

export function generateQuestion(
    allPlayers: PlayerData[],
    excludeIds: Set<number> = new Set() // Set of player IDs already asked in current session
): PlayerQuestion | null {
  if (!allPlayers || allPlayers.length < 4) {
    console.error("Not enough player data loaded or available to generate a question.");
    return null; 
  }

  // Filter out players already asked (no need to check local_image_path anymore)
  const availablePlayers = allPlayers.filter(
      (p) => p.id != null && !excludeIds.has(p.id)
  );

  if (availablePlayers.length < 1) {
    console.warn("No more available players to ask questions about.");
    return null; // Or handle game end state
  }

  // Select the correct player
  const correctPlayerIndex = Math.floor(Math.random() * availablePlayers.length);
  const correctPlayer = availablePlayers[correctPlayerIndex];

  // Select 3 distractors (ensure they are different from correct player and each other)
  const distractors: PlayerData[] = [];
  const potentialDistractors = allPlayers.filter(p => p.id !== correctPlayer.id);
  shuffleArray(potentialDistractors); // Shuffle to get random distractors

  for (const p of potentialDistractors) {
      if (distractors.length < 3) {
          distractors.push(p);
      } else {
          break;
      }
  }
  
  if (distractors.length < 3) {
      console.error("Could not find enough unique distractors.");
      // Handle this case, maybe retry or use placeholders? For now, return null.
      return null;
  }

  // Create choices array
  const choices: PlayerChoice[] = [
    { name: correctPlayer.player_name, isCorrect: true },
    ...distractors.map(p => ({ name: p.player_name, isCorrect: false }))
  ];

  // Shuffle choices
  const shuffledChoices = shuffleArray(choices);

  // Construct the full image URL (assuming base path is /)
  const imageUrl = correctPlayer.local_image_path 
    ? `/players_images/${correctPlayer.local_image_path}` 
    : '/images/placeholder.jpg'; // Fallback image in public/images/

  return {
    correctPlayer: correctPlayer,
    choices: shuffledChoices,
    imageUrl: imageUrl,
  };
} 
```

---

### Data Types: src/types/index.ts

**Description:** TypeScript type definitions for game data structures.

**Key Features:**
- PlayerData interface
- PlayerQuestion interface
- PlayerChoice interface
- RecentAnswer interface
- GameModeType enum

**File Size:** 30 lines

**File Content:**
```typescript
export type GameModeType = 'normal' | 'timed';

export interface PlayerData {
  id: number; // Or string if your IDs are different
  team_name: string;
  player_name: string;
  local_image_path: string | null; // Path relative to /public
  jersey_number: string | number | null; // Can be string like '-' or number
  position: string | null;
  height: string | null;
  weight: string | number | null;
  age_or_dob: string | number | null; // Keeping flexible based on scraped data
  experience: string | number | null; // Could be 'R' or number
  college: string | null;
  // Add any other fields from your JSON if needed
}

export interface PlayerChoice {
  name: string;
  isCorrect: boolean;
}

export interface PlayerQuestion {
  correctPlayer: PlayerData;
  choices: PlayerChoice[];
  imageUrl: string; // Derived absolute path for image component
}

export interface RecentAnswer {
  player: PlayerData;
  isCorrect: boolean;
  timestamp: number;
  // chosenAnswer?: string; // Optional field for ChoiceButton styling
} 
```

---


## UI Component Files

### UI Components: src/components/game/PlayerImageDisplay.tsx

**Description:** Displays player images with loading states and optimized image handling.

**Key Features:**
- Next.js Image optimization
- Loading state management
- Blur placeholder
- Responsive sizing
- Error handling

**File Size:** 49 lines

**File Content:**
```typescript
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';

interface Props {
  imageUrl: string;
  altText: string;
}

// Darker blurDataURL to match bg-gray-700 (#374151)
const DARK_BLUR_DATA_URL = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiMzNzQxNTEiLz48L3N2Zz4=`;

export function PlayerImageDisplay({ imageUrl, altText }: Props) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  useEffect(() => {
    setIsImageLoaded(false); // Reset on new image
  }, [imageUrl]);

  return (
    <div className="relative w-full max-w-lg h-80 bg-gray-700 border border-gray-600 rounded-lg mb-6 overflow-hidden shadow-lg flex items-center justify-center">
      {imageUrl ? (
        <Image
          key={imageUrl}
          src={imageUrl}
          alt={altText}
          fill
          sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, 448px"
          priority
          placeholder="blur"
          blurDataURL={DARK_BLUR_DATA_URL}
          unoptimized={imageUrl.endsWith('.gif')}
          onLoad={() => {
            setIsImageLoaded(true);
            console.log('[PlayerImageDisplay] Image loaded via onLoad:', imageUrl);
          }}
          className={cn(
            "object-contain",
            "transition-opacity duration-100 ease-in-out", // Slightly faster transition
            isImageLoaded ? "opacity-100" : "opacity-0"
          )}
        />
      ) : (
        <div className="flex flex-col items-center justify-center h-full text-gray-400">
          <svg className="animate-spin h-10 w-10 text-gray-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading Player...
        </div>
      )}
    </div>
  );
} 
```

---

### UI Components: src/components/game/ChoiceButton.tsx

**Description:** Interactive choice buttons with correct/incorrect styling and disabled states.

**Key Features:**
- Click handling
- Correct/incorrect visual feedback
- Disabled state styling
- Hover effects
- Accessibility features

**File Size:** 32 lines

**File Content:**
```typescript
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils'; // shadcn utility

interface Props {
  choiceText: string;
  onClick: () => void;
  disabled?: boolean;
  isCorrect?: boolean | null; // null = unanswered, true = correct, false = incorrect
  wasChosen?: boolean; // Was this specific button clicked by the user?
}

export function ChoiceButton({ choiceText, onClick, disabled, isCorrect, wasChosen }: Props) {
  // Get appropriate color classes based on state
  const colorClasses = () => {
    if (disabled && isCorrect === true) return 'bg-green-600 hover:bg-green-700 border-green-700 text-white';
    if (disabled && wasChosen && isCorrect === false) return 'bg-red-600 hover:bg-red-700 border-red-700 text-white';
    // Default or non-chosen incorrect
    if (disabled && isCorrect === false) return 'bg-gray-600 hover:bg-gray-700 border-gray-700 text-gray-300 opacity-70';
    return 'bg-blue-600 hover:bg-blue-700 border-blue-700'; // Default active state
  }

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "w-full text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out text-sm md:text-base h-auto min-h-[50px] whitespace-normal", // Allow text wrapping
        colorClasses(),
        disabled ? 'opacity-80 cursor-not-allowed' : ''
      )}
    >
      {choiceText}
    </Button>
  );
} 
```

---

### UI Components: src/components/game/PlayerInfoPanel.tsx

**Description:** Displays detailed player information in the right panel.

**Key Features:**
- Player stats display
- Team information
- Position and physical stats

**File Size:** 61 lines

**File Content:**
```typescript
import type { PlayerData } from '@/types';

interface Props {
  player: PlayerData | null | undefined;
}

export function PlayerInfoPanel({ player }: Props) {
  if (!player) {
    return (
      <div className="text-center text-gray-400 flex flex-col justify-center h-full">
        <h2 className="text-2xl font-bold mb-4">Player Info</h2>
        <p className="text-lg">Select a player to view details</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-between h-full">
      <div className="mt-10">
        <h2 className="text-3xl font-extrabold mb-2 text-center break-words">{player.player_name}</h2>
        <div className="text-center mb-3">
          <h3 className="text-lg font-bold leading-tight">Team</h3>
          <p className="text-xl font-extrabold leading-tight break-words">{player.team_name}</p>
        </div>
        <div className="text-center mb-3">
          <h3 className="text-lg font-bold leading-tight">College</h3>
          <p className="text-xl font-extrabold leading-tight">{player.college || '-'}</p>
        </div>
      </div>
      <div className="flex-1 flex flex-col justify-center -mt-25">
        <div className="grid grid-cols-2 gap-x-2 gap-y-1 mb-3 justify-center text-center">
          <div>
            <h3 className="text-lg font-bold leading-tight">Pos.</h3>
            <p className="text-xl font-extrabold leading-tight">{player.position}</p>
          </div>
          <div>
            <h3 className="text-lg font-bold leading-tight">No.</h3>
            <p className="text-xl font-extrabold leading-tight">#{player.jersey_number}</p>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-x-2 gap-y-1 mb-3 justify-center text-center">
          <div>
            <h3 className="text-lg font-bold leading-tight">Height</h3>
            <p className="text-xl font-extrabold leading-tight">{player.height || '-'}</p>
          </div>
          <div>
            <h3 className="text-lg font-bold leading-tight">Weight</h3>
            <p className="text-xl font-extrabold leading-tight">{player.weight ? `${player.weight} lbs` : '-'}</p>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-x-2 gap-y-1 mb-3 justify-center text-center">
          <div>
            <h3 className="text-lg font-bold leading-tight">Age</h3>
            <p className="text-xl font-extrabold leading-tight">{player.age_or_dob || '-'}</p>
          </div>
          <div>
            <h3 className="text-lg font-bold leading-tight">Exp.</h3>
            <p className="text-xl font-extrabold leading-tight">{player.experience ? `${player.experience} yrs` : '-'}</p>
          </div>
        </div>
      </div>
    </div>
  );
} 
```

---

### UI Components: src/components/game/RecentAnswersList.tsx

**Description:** Shows list of recent answers with correct/incorrect indicators.

**Key Features:**
- Answer history display
- Click to view player details
- Visual correct/incorrect indicators
- Avatar integration
- Scrollable list

**File Size:** 33 lines

**File Content:**
```typescript
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CheckCircle, XCircle } from 'lucide-react';
import type { RecentAnswer, PlayerData } from '@/types';

interface Props {
  answers: RecentAnswer[];
  onSelect: (player: PlayerData) => void;
}

export function RecentAnswersList({ answers, onSelect }: Props) {
  return (
    <div className="space-y-2 text-base text-center">
      {answers.length === 0 && (
        <p className="text-gray-400 text-center mt-4">No answers yet.</p>
      )}
      {answers.slice().reverse().map((answer) => (
        <div
          key={answer.timestamp}
          className="flex items-center space-x-2 p-1 bg-gray-800 bg-opacity-50 rounded cursor-pointer hover:bg-gray-700 transition-colors justify-center"
          onClick={() => onSelect(answer.player)}
        >
          <Avatar className="h-6 w-6 flex-shrink-0">
            <AvatarImage src={answer.player.local_image_path ? `/players_images/${answer.player.local_image_path}` : undefined} alt={answer.player.player_name} />
            <AvatarFallback className="text-[8px]">?</AvatarFallback>
          </Avatar>
          <span className={`flex-1 truncate text-lg font-extrabold text-center ${answer.isCorrect ? 'text-green-400' : 'text-red-400'}`}>{answer.player.player_name}</span>
          {answer.isCorrect
            ? <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
            : <XCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
          }
        </div>
      ))}
    </div>
  );
} 
```

---

### UI Components: src/components/game/ScorePanel.tsx

**Description:** Displays current score and game statistics.

**Key Features:**
- Score display
- Best score tracking
- Streak information
- Game mode indicators

**File Size:** 30 lines

**File Content:**
```typescript
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
  title: string;
  score: number;
  correctAnswers: number;
  totalQuestions: number;
}

export function ScorePanel({ title, score, correctAnswers, totalQuestions }: Props) {
  return (
    <Card className="bg-transparent border-none text-white shadow-none">
      <CardHeader className="pb-2">
        <CardTitle className="text-3xl font-extrabold text-center text-white mb-2">{title}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center text-center text-white p-0">
        <div className="mb-3">
          <p className="text-lg font-bold leading-tight">Score</p>
          <p className="text-xl font-extrabold leading-tight text-yellow-400">{score}</p>
        </div>
        <div className="mb-3">
          <p className="text-lg font-bold leading-tight">Correct</p>
          <p className="text-xl font-extrabold leading-tight">{correctAnswers}/{totalQuestions}</p>
        </div>
        <div>
          <p className="text-lg font-bold leading-tight">Accuracy</p>
          <p className="text-xl font-extrabold leading-tight">{totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0}%</p>
        </div>
      </CardContent>
    </Card>
  );
} 
```

---


## Animation System Files

### Football Celebration Animation: src/components/game/FootballFx.tsx

**Description:** Main football emoji celebration effect. Creates burst of football emojis based on streak count.

**Key Features:**
- Streak-based football count (max 5)
- Random trajectory generation
- Framer Motion animations
- GPU-optimized transforms
- Configurable timing and physics
- Burst effect on correct answers

**File Size:** 102 lines

**File Content:**
```typescript
// FootballFx.tsx
import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useMemo, useState } from "react";

interface Props {
  /** Every time this number increments (> 0), a new burst plays. */
  trigger: number | null;
  /** Current streak of correct answers */
  streak: number;
}

interface FootballCfg {
  initScale: number;
  initX: number;
  angle: number;
  distance: number;
  duration: number;
  delay: number;
  spin: number;
  stretch: number;
}

const MAX_FOOTBALLS = 5;

export function FootballFx({ trigger, streak }: Props) {
  /* Bump `seed` whenever a positive trigger arrives. Keeps randoms stable per burst. */
  const [seed, setSeed] = useState(0);
  useEffect(() => {
    if (trigger) setSeed(trigger);
  }, [trigger]);

  /** Pre-baked random config for each football in the current burst. */
  const configs = useMemo<FootballCfg[]>(() => {
    if (!trigger) return [];

    // Use streak count for number of footballs, capped at MAX_FOOTBALLS
    const count = Math.min(streak, MAX_FOOTBALLS);

    return Array.from({ length: count }, (_, i) => {
      const initScale = 0.9 + Math.random() * 0.2;          // bigger start
      const initX = (Math.random() - 0.5) * 40;

      const angle =
        -Math.PI / 2 + (Math.random() - 0.5) * (Math.PI / 4); // ±22.5°
      const distance = 150 + Math.random() * 80;

      return {
        initScale,
        initX,
        angle,
        distance,
        duration: 0.8 + Math.random() * 0.4,
        delay: i * 0.06,
        spin: (Math.random() - 0.5) * 120,                    // degrees
        stretch: 1.35 + Math.random() * 0.1,                  // juicy overshoot
      };
    });
  }, [streak, trigger]);

  if (!trigger) return null;

  return (
    <AnimatePresence>
      <div
        className="absolute inset-0 flex items-center justify-center pointer-events-none z-40"
        style={{ transform: "translateY(-150px)", overflow: "visible" }}
      >
        {configs.map((cfg, i) => {
          const targetX = cfg.initX + Math.cos(cfg.angle) * cfg.distance * 0.35;
          const targetY = Math.sin(cfg.angle) * cfg.distance;

          return (
            <motion.span
              key={`football-${seed}-${i}`}
              initial={{
                opacity: 1,
                scale: cfg.initScale,
                x: cfg.initX,
                y: 0,
                rotate: 0,
              }}
              animate={{
                opacity: [1, 1, 0],
                scale: [cfg.initScale, cfg.stretch, cfg.stretch * 0.95],
                x: [cfg.initX, targetX],
                y: [0, targetY],
                rotate: [0, cfg.spin],
              }}
              transition={{
                duration: cfg.duration,
                delay: cfg.delay,
                ease: "easeOut",
                opacity: {
                  duration: cfg.duration * 0.35,
                  delay: cfg.delay + cfg.duration * 0.65,
                },
              }}
              exit={{ opacity: 0 }}
              className="text-4xl md:text-5xl select-none"
              style={{
                textShadow: "1px 1px 3px rgba(0,0,0,0.6)",
                willChange: "transform, opacity",
                transformOrigin: "center",
                transform: "translateZ(0)", // GPU promotion
              }}
            >
              🏈
            </motion.span>
          );
        })}
      </div>
    </AnimatePresence>
  );
}

```

---

### Score Animation: src/components/game/ScorePopup.tsx

**Description:** Animated popup showing score increases like +10, +20, etc.

**Key Features:**
- Fade and scale animation
- Upward movement
- Score change display
- Trigger-based activation
- Positioned above game area

**File Size:** 27 lines

**File Content:**
```typescript
import { motion, AnimatePresence } from 'framer-motion';

interface Props {
  scoreChange: number | null;
  trigger: number;
}

export function ScorePopup({ scoreChange, trigger }: Props) {
  if (!scoreChange || scoreChange <= 0) return null;
  
  const initialYOffset = -23; // Move starting position up by 23px
  const animationTravelY = -60; // It travels 60px upwards during animation

  return (
    <AnimatePresence>
      <motion.div
        key={trigger}
        initial={{ opacity: 1, y: initialYOffset, scale: 0.7 }}
        animate={{ opacity: 0, y: initialYOffset + animationTravelY, scale: 1.2 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 1.2, ease: 'easeOut' }}
        className="absolute left-1/2 top-1/3 -translate-x-1/2 z-50 pointer-events-none"
      >
        <span className="text-yellow-300 text-4xl font-bold" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}>
          +{scoreChange}
        </span>
      </motion.div>
    </AnimatePresence>
  );
} 
```

---

### Time Animation: src/components/game/TimeChangePopup.tsx

**Description:** Shows time bonus/penalty changes in timed mode like +1s, -1s.

**Key Features:**
- Positive/negative time display
- Color-coded feedback (green/red)
- Coordinated with score popup
- Conditional positioning
- Timed mode integration

**File Size:** 40 lines

**File Content:**
```typescript
import { motion, AnimatePresence } from 'framer-motion';

interface Props {
  timeChange: number | null; // e.g., 1 for +1s, -1 for -1s
  trigger: number;
  scoreChanged?: boolean; // New prop: true if score also changed
}

export function TimeChangePopup({ timeChange, trigger, scoreChanged }: Props) {
  if (!timeChange || timeChange === 0) return null;
  
  const isPositive = timeChange > 0;
  const text = isPositive ? `+${timeChange}s` : `${timeChange}s`;
  const colorClass = isPositive ? "text-green-400" : "text-red-400";

  // Base vertical position - similar to ScorePopup
  const initialYOffset = -23;
  const animationTravelY = -60;

  // If score also changed, push this popup further down to appear below the score popup
  const additionalOffsetY = scoreChanged && timeChange > 0 ? 35 : 0;
  
  return (
    <AnimatePresence>
      {trigger > 0 && (
        <motion.div
          key={`time-${trigger}`}
          initial={{ opacity: 1, y: initialYOffset + additionalOffsetY, scale: 0.7 }}
          animate={{ opacity: 0, y: initialYOffset + additionalOffsetY + animationTravelY, scale: 1.2 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1.2, ease: 'easeOut' }}
          className="absolute left-1/2 top-1/3 -translate-x-1/2 z-40 pointer-events-none"
        >
          <span 
            className={`text-3xl md:text-4xl font-bold ${colorClass}`}
            style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}
          >
            {text}
          </span>
        </motion.div>
      )}
    </AnimatePresence>
  );
} 
```

---

### Loading Animation: src/components/game/FootballLoader.tsx

**Description:** Loading screen with animated football emojis.

**Key Features:**
- Bouncing football animation
- Staggered timing
- CSS keyframe animations
- Loading message display
- Game initialization feedback

**File Size:** 19 lines

**File Content:**
```typescript
import React from 'react';

interface FootballLoaderProps {
  message: string;
}

export const FootballLoader: React.FC<FootballLoaderProps> = ({ message }) => {
  return (
    <div className="game-loading-container">
      <p className="loading-text">{message}</p>
      <div className="football-spinner">
        <span>🏈</span>
        <span>🏈</span>
        <span>🏈</span>
        <span>🏈</span>
        <span>🏈</span>
      </div>
      <p className="loading-subtext">Just a moment...</p>
    </div>
  );
}; 
```

---


## Main Application File

### Main Application: src/app/page.tsx

**Description:** Main page component containing both singleplayer and multiplayer game logic. Contains the complete singleplayer game implementation.

**Key Features:**
- Game mode selection (single/multiplayer)
- Singleplayer game flow
- Animation integration
- State management
- UI layout and responsive design
- Game controls and interactions

**File Size:** 2527 lines

**File Content:**
```typescript
'use client'; // This page now needs client-side interactivity

import { useEffect, useState, Suspense } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useGameStore } from '@/stores/gameStore';
import { PlayerImageDisplay } from '@/components/game/PlayerImageDisplay';
import { ChoiceButton } from '@/components/game/ChoiceButton';
import { RecentAnswersList } from '@/components/game/RecentAnswersList';
import { PlayerInfoPanel } from '@/components/game/PlayerInfoPanel';
import { Button } from '@/components/ui/button'; // For Start/Next button
import type { PlayerData, GameModeType } from '@/types';
import { ScorePopup } from '@/components/game/ScorePopup';
import { FootballFx } from '@/components/game/FootballFx';
import { FootballLoader } from '@/components/game/FootballLoader';
import { cn } from '@/lib/utils'; // For conditional class names
import { TimeChangePopup } from '@/components/game/TimeChangePopup';
import AuthModal from '@/components/auth/AuthModal';
import Link from 'next/link';
import { User, AuthChangeEvent, Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabaseClient';
import { useSearchParams, useRouter } from 'next/navigation';
import { RealtimeChannel } from '@supabase/supabase-js';
import { createBrowserClient } from '@supabase/ssr';

type OverallGameType = 'single-player' | 'multiplayer';
type MultiplayerPanelState = 'lobby_list' | 'in_room';
type CenterPanelMpState = 'lobby_list_detail' | 'expanded_leaderboard' | 'mp_game_active';
type MultiplayerGameMode = 'competitive' | 'cooperative';

// Define Leaderboard types
interface LeaderboardEntry {
  rank: number;
  username: string;
  score: number;
  userId?: string;
}

interface RegionalLeaderboard {
  regionName: string;
  entries: LeaderboardEntry[];
}

// Update the game player types to use a single consistent type
type GamePlayer = {
  user_id: string;
  is_connected: boolean;
  is_ready?: boolean;
  profile?: { username: string | null } | null;
};

// Add type for answer
type GameAnswer = {
  userId: string;
  questionId: string;
  choiceName: string;
  timestamp: number;
  isCorrect: boolean;
};

// Update GameRoom interface to use GameAnswer type
interface GameRoom {
  id: string;
  created_at: string;
  status: 'waiting' | 'active' | 'finished';
  host_id: string;
  multiplayer_mode: MultiplayerGameMode | null;
  title: string | null;
  room_code: string | null;
  max_players: number;
  profiles: { username: string | null } | null;
  game_players: GamePlayer[];
  player_count: number;
  connected_players: number;
  current_question_data?: PlayerQuestion;
  player_scores?: Record<string, number>;
  current_round_answers?: GameAnswer[];
  current_round_number?: number;
  game_start_timestamp?: string | null;
  current_round_ends_at?: string | null;
}

// Add PlayerChoice type
interface PlayerChoice {
  name: string;
  isCorrect: boolean;
}

// Add PlayerQuestion type
interface PlayerQuestion {
  questionId: string; // Unique ID for this question instance
  correctPlayerId: number;
  imageUrl: string;
  choices: PlayerChoice[];
  correctChoiceName: string; // Added for answer validation
  correctPlayer?: PlayerData; // Optional, for client-side display if needed
}

// Add type definition at the top level
type UserProfile = {
  id: string;
  username: string | null;
};

// Add new type for players in room
type PlayerInRoom = {
  user_id: string;
  profile: { username: string | null } | null;
  is_ready?: boolean;
  is_connected: boolean; // Add this field
};

// Add new types for game settings
type GameDuration = 30 | 60 | 90 | 120;
type MaxPlayers = 2 | 4 | 6 | 8;

// Add state for tracking animated answers
interface AnimatedAnswersState {
  [questionId: string]: Set<string>; // questionId -> Set of userIds that have been animated
}

// Add type for player with score
type PlayerWithScore = PlayerInRoom & {
  score: number;
  is_connected: boolean;
};

function HomePageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Overall game type state (moved to top)
  const [selectedOverallGameType, setSelectedOverallGameType] = useState<OverallGameType>('single-player');

  // Add debug logging for state changes
  console.log("HomePageContent render - Current selectedOverallGameType:", selectedOverallGameType);

  // Add hasMounted state for hydration handling
  const [hasMounted, setHasMounted] = useState(false);
  // Add showAuthForm state
  const [showAuthForm, setShowAuthForm] = useState(false);

  // Get state and actions from the store
  const {
    loadPlayers,
    setGameMode,
    startCountdown,
    nextQuestion,
    submitAnswer,
    resetCurrentModeGame,
    currentQuestion,
    score,
    streak,
    bestStreak,
    bestNormalScore,
    bestTimedScore,
    isAnswered,
    lastAnswerCorrect,
    recentAnswers,
    gameStatus,
    activeGameMode,
    lastScoreChange,
    animationTrigger,
    isLoadingInitialGame,
    loadingMessage,
    isInitialQuestionReady,
    timer,
    isCountdownActive,
    countdownValue,
    userChoiceName,
    lastTimeChange,
    timeChangeAnimationTrigger,
  } = useGameStore();

  // Existing state
  const [selectedPlayerInfo, setSelectedPlayerInfo] = useState<PlayerData | null>(null);
  const [viewingRecentPlayer, setViewingRecentPlayer] = useState<PlayerData | null>(null);

  // New multiplayer state
  const [multiplayerPanelState, setMultiplayerPanelState] = useState<MultiplayerPanelState>('lobby_list');
  const [centerPanelMpState, setCenterPanelMpState] = useState<CenterPanelMpState>('lobby_list_detail');
  const [activeRoomId, setActiveRoomId] = useState<string | null>(null);
  const [selectedRoomForDetail, setSelectedRoomForDetail] = useState<GameRoom | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loadingUser, setLoadingUser] = useState(true);
  const [loadingProfile, setLoadingProfile] = useState(false);
  const [gameRooms, setGameRooms] = useState<GameRoom[]>([]);
  const [isLoadingRooms, setIsLoadingRooms] = useState(false);
  const [errorMp, setErrorMp] = useState<string | null>(null);
  const [lobbyFetchError, setLobbyFetchError] = useState<boolean>(false);
  const [newRoomMode, setNewRoomMode] = useState<MultiplayerGameMode>('competitive');

  // Leaderboard States
  const [personalRecords, setPersonalRecords] = useState<LeaderboardEntry[]>([]);
  const [globalLeaderboard, setGlobalLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [regionalLeaderboards, setRegionalLeaderboards] = useState<RegionalLeaderboard[]>([]);
  const [userRegion, setUserRegion] = useState<string | null>(null);
  const [expandedLeaderboardData, setExpandedLeaderboardData] = useState<{title: string, entries: LeaderboardEntry[]} | null>(null);

  // Update userProfile state with the new type
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  // Add new state for players in room
  const [playersInRoom, setPlayersInRoom] = useState<PlayerInRoom[]>([]);
  const [isLoadingPlayers, setIsLoadingPlayers] = useState(false);

  // Add new state for current room's game data
  const [currentRoomGameData, setCurrentRoomGameData] = useState<GameRoom | null>(null);

  // Add new state for game settings
  const [selectedGameDuration, setSelectedGameDuration] = useState<GameDuration>(60);
  const [selectedMaxPlayers, setSelectedMaxPlayers] = useState<MaxPlayers>(4);
  const [isCreatingRoom, setIsCreatingRoom] = useState(false);
  const [isStartingGame, setIsStartingGame] = useState(false); // New state for preventing double-start

  // Add state for tracking animated answers
  const [animatedCorrectAnswers, setAnimatedCorrectAnswers] = useState<AnimatedAnswersState>({});
  const [optimisticReady, setOptimisticReady] = useState<boolean | undefined>(undefined);

  // Add state for tracking animated answers
  const [animatedAnswers, setAnimatedAnswers] = useState<AnimatedAnswersState>({});

  // Add state for preventing multiple ready state submissions
  const [isSubmittingReady, setIsSubmittingReady] = useState(false);
  
  // Add state for preventing multiple leave room submissions
  const [isLeavingRoom, setIsLeavingRoom] = useState(false);
  
  // Add state for join/rejoin loading feedback
  const [isJoiningOrRejoiningRoom, setIsJoiningOrRejoiningRoom] = useState(false);

  // Load player data on initial mount
  useEffect(() => {
    // Initial load only if players aren't loaded yet
    if (!useGameStore.getState().players.length) {
      loadPlayers();
    }
  }, [loadPlayers]);

  // Add effect for client-side mounting
  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Effect for handling authentication state changes
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      console.log(`[AuthListener] Event: ${event}, Session User: ${session?.user?.id}`);
      setUser(session?.user ?? null);
      if (!session?.user) { // If user is signed out
        setUserProfile(null); // Clear profile
        if (selectedOverallGameType === 'multiplayer' && event === 'SIGNED_OUT') {
          handleOverallGameTypeChange('single-player');
        }
      }
      
      // Reset room/game state on logout to prevent "already in game" issues
      if (event === 'SIGNED_OUT') {
        console.log('[AuthListener] SIGNED_OUT - Resetting ALL relevant client state.');
        setActiveRoomId(null);
        setCurrentRoomGameData(null);
        setPlayersInRoom([]);
        setSelectedRoomForDetail(null);
        setMultiplayerPanelState('lobby_list');
        setCenterPanelMpState('lobby_list_detail');
        setGameRooms([]); // Clear the list of game rooms
        setIsLoadingRooms(true); // Set to true so it re-fetches on next view
        setErrorMp(null); // Clear any multiplayer errors
        setIsLeavingRoom(false); // Reset leaving state
        setIsSubmittingReady(false); // Reset ready submission state
        setIsCreatingRoom(false); // Reset room creation state
        setIsStartingGame(false); // Reset game starting state
        console.log('[AuthListener] Client state reset complete for SIGNED_OUT.');
      }
      
      // Profile fetching will be handled by the effect below, triggered by 'user' changing
      if (['INITIAL_SESSION', 'SIGNED_IN', 'SIGNED_OUT'].includes(event)) {
          setLoadingUser(false);
      }
    });

    // Initial session check (only sets user, profile fetch is separate)
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setLoadingUser(false); // Loading user is done after initial session check
      console.log("[AuthEffect] Initial getSession complete. User:", session?.user?.id);
    });

    return () => {
      console.log("[AuthListener] Cleaning up auth listener.");
      authListener?.subscription.unsubscribe();
    };
  }, [selectedOverallGameType]); // Re-run if selectedOverallGameType changes (for the multiplayer switch on logout)

  // Effect for fetching user profile when user object changes and is not null
  useEffect(() => {
    const fetchProfile = async () => {
      if (user?.id) {
        setLoadingProfile(true); // Indicate profile loading starts
        console.log(`[ProfileFetcher] Attempting to fetch profile for user ID: ${user.id}`);
        try {
          const { data, error, status } = await supabase
            .from('profiles')
            .select('id, username')
            .eq('id', user.id)
            .single();

          if (error && status !== 406) {
            console.error("[ProfileFetcher] Error fetching user profile:", error.message);
            setUserProfile(null);
          } else if (data) {
            console.log("[ProfileFetcher] User profile fetched:", data);
            setUserProfile(data);
          } else {
            console.warn(`[ProfileFetcher] No profile found for user ID: ${user.id}`);
            setUserProfile(null);
          }
        } catch (e: any) {
          console.error("[ProfileFetcher] Exception fetching user profile:", e.message);
          setUserProfile(null);
        } finally {
          setLoadingProfile(false); // Indicate profile loading ends
        }
      } else {
        // If no user, ensure profile is null and not loading
        setUserProfile(null);
        setLoadingProfile(false);
      }
    };

    fetchProfile();
  }, [user]); // This effect runs whenever the 'user' object changes

  // Add fetchAndSetGameRooms function after the state declarations
  const fetchAndSetGameRooms = async () => {
    console.log('[LOBBY_FETCH] fetchAndSetGameRooms CALLED. Current user session:', user?.id);
    setIsLoadingRooms(true);
    setErrorMp(null);
    setLobbyFetchError(false);
    
    // Base query: select rooms that are 'waiting' or 'active' for rejoining
    let query = supabase
      .from('game_rooms')
      .select(`
        id,
        title,
        status,
        room_code,
        multiplayer_mode,
        host_id,
        created_at,
        max_players,
        profiles:host_id (
          username
        ),
        game_players (
          user_id,
          is_connected
        )
      `)
      .in('status', ['waiting', 'active']) // Show waiting AND active games for rejoining
      .order('created_at', { ascending: false });

    console.log('[LOBBY_FETCH] Executing game_rooms SELECT with current filters/logic.');
    
    try {
      const { data: fetchedRooms, error } = await query;

      if (error) {
        console.error('[LOBBY_FETCH] Error fetching game_rooms:', error);
        setGameRooms([]); // Set to empty on error
        setLobbyFetchError(true);
        setErrorMp(`Failed to fetch room details: ${error.message}`);
      } else {
        console.log('[LOBBY_FETCH] Successfully fetched game_rooms. Count:', fetchedRooms?.length, 'Data:', fetchedRooms);
        const enrichedRooms = fetchedRooms?.map(room => {
          const connectedPlayerCount = room.game_players?.filter(p => p.is_connected).length || 0;
          console.log('[LOBBY_FETCH] Enriching room:', room.id, 'Host ID from DB:', room.host_id, 'Profile data from DB join:', room.profiles);
          
          return {
            id: room.id,
            created_at: room.created_at,
            status: room.status,
            host_id: room.host_id,
            multiplayer_mode: room.multiplayer_mode,
            title: room.title,
            room_code: room.room_code,
            max_players: room.max_players,
            profiles: room.profiles ? { username: room.profiles[0]?.username } : null,
            game_players: room.game_players,
            player_count: room.game_players?.length || 0,
            connected_players: connectedPlayerCount
          };
        }) || [];
        setGameRooms(enrichedRooms as unknown as GameRoom[]); // Ensure GameRoom type matches
        console.log('[LOBBY_FETCH] Updated gameRooms state with enriched rooms. First room enriched:', enrichedRooms[0]);
        
        if (enrichedRooms.length === 0 && fetchedRooms && fetchedRooms.length > 0) {
          console.warn('[LOBBY_FETCH] Enriched rooms is empty, but raw DB data was not!', { 
            rawData: fetchedRooms, 
            enrichedRooms 
          });
        }
        setLobbyFetchError(false); // Clear error state on success
      }
    } catch (e: any) {
      console.error('[LOBBY_FETCH] Exception fetching game_rooms:', e);
      setErrorMp(`Exception fetching rooms: ${e.message}`);
      setLobbyFetchError(true);
      setGameRooms([]);
    } finally {
      setIsLoadingRooms(false);
      console.log('[LOBBY_FETCH] fetchAndSetGameRooms FINISHED.');
    }
  };

  // Update handleOverallGameTypeChange to use fetchAndSetGameRooms
  const handleOverallGameTypeChange = (type: OverallGameType) => {
    console.log("handleOverallGameTypeChange called with:", type, "Current type:", selectedOverallGameType);
    setSelectedOverallGameType(type);
    
    if (type === 'multiplayer') {
      console.log("Switching to multiplayer mode");
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setActiveRoomId(null);
      setSelectedRoomForDetail(null);
      // Fetch rooms when switching to multiplayer
      fetchAndSetGameRooms();
    } else {
      console.log("Switching to single-player mode");
      resetCurrentModeGame();
    }
  };

  // Handler for clicking a recent answer
  const handleRecentSelect = (player: PlayerData) => {
    setSelectedPlayerInfo(player);
    setViewingRecentPlayer(player);
  };

  // Clear selected player info on next question or new game
  const handleNextQuestionClick = () => {
    setSelectedPlayerInfo(null);
    setViewingRecentPlayer(null);
    nextQuestion();
  };

  // Handler for returning to game from recent player view
  const handleReturnToGame = () => {
    setViewingRecentPlayer(null);
    const { isAnswered: currentIsAnswered, currentQuestion: gameCurrentQuestion } = useGameStore.getState();
    if (currentIsAnswered && gameCurrentQuestion && gameCurrentQuestion.correctPlayer) {
        setSelectedPlayerInfo(gameCurrentQuestion.correctPlayer);
    } else {
        setSelectedPlayerInfo(null);
    }
  };

  // Handler for resetting the game
  const handleGameResetClick = () => {
    setSelectedPlayerInfo(null);
    setViewingRecentPlayer(null);
    resetCurrentModeGame();
  };

  // Handler for switching game mode
  const handleModeButtonClick = (mode: GameModeType) => {
    setViewingRecentPlayer(null);
    setSelectedPlayerInfo(null);
    setGameMode(mode);
  };

  const handleJoinRoom = async (roomId: string, autoJoinedAfterCreate = false) => {
    console.log(`[Client] handleJoinRoomAttempt called for room: ${roomId}. Current activeRoomId: ${activeRoomId}, User: ${user?.id}`);
    if (!user?.id) {
      console.error('[Client] Join attempt: No user session found.');
      setErrorMp("You must be logged in to join or rejoin a room.");
      setShowAuthForm(true);
      return;
    }
    
    // Prevent multiple simultaneous join attempts
    if (isJoiningOrRejoiningRoom) {
      console.warn('[Client] Join attempt already in progress, ignoring duplicate request.');
      return;
    }
    
    setIsJoiningOrRejoiningRoom(true);
    setErrorMp(null);
    
    if (activeRoomId && activeRoomId !== roomId) {
      // This implies user is trying to join a NEW room while already in another.
      console.warn(`[Client] Join attempt: User is already in activeRoomId ${activeRoomId}, attempting to join ${roomId}. This might need explicit leave first.`);
      // For now, we'll allow it and let the server-side logic handle it
    }

    // Check if already connected client-side to this exact room
    if (activeRoomId === roomId && playersInRoom.some(p => p.user_id === user.id)) {
        console.log(`[Client] User ${user.id} is ALREADY CONNECTED client-side to room ${roomId}. Proceeding to room view if not already there.`);
        setMultiplayerPanelState('in_room');
        setCenterPanelMpState('mp_game_active');
        return;
    }

    console.log(`[Client] User ${user.id} attempting to join/rejoin room: ${roomId} via server-side logic.`);
    setErrorMp(null);
    
    // Get full room details for enhanced analysis
    const roomToJoin = gameRooms.find(r => r.id === roomId) || selectedRoomForDetail;
    if (!roomToJoin) {
        console.error(`[Client] Join attempt: Could not find room details for ${roomId}`);
        setErrorMp("Error: Room details not found.");
        setIsJoiningOrRejoiningRoom(false);
        return;
    }

    const isHostAttemptingActiveGameReentry = roomToJoin.host_id === user.id && 
                                            ['active', 'in-progress', 'playing'].includes(roomToJoin.status.toLowerCase()) &&
                                            activeRoomId !== roomId;
    
    console.log(`[Client] Room analysis for join/rejoin attempt:`, {
      isHostAttemptingActiveGameReentry,
      roomStatus: roomToJoin.status,
      currentActiveRoomId: activeRoomId,
      isCurrentUserHost: roomToJoin.host_id === user.id,
      autoJoinedAfterCreate
    });

    // CRITICAL: Handle host re-entry immediately after detection to prevent fall-through
    if (isHostAttemptingActiveGameReentry) {
      console.log(`[Client] IMMEDIATE HOST RE-ENTRY detected: Host ${user.id} is attempting to RE-ENTER their own active game: ${roomId}.`);
      console.log(`[Client] For host re-entry to active game, bypassing game_players insert/update and directly setting client state.`);
      console.log(`[Client] Note: Host's score should be preserved in game_rooms.player_scores, even if game_players record was deleted.`);
      
      // Set client active state directly - no database modifications needed
      setActiveRoomId(roomId);
      setMultiplayerPanelState('in_room');
      setCenterPanelMpState('mp_game_active');
      setSelectedRoomForDetail(null);
      
      console.log(`[Client] Host re-entry: Client active state set for room ${roomId}. Awaiting data fetches and UI transition.`);
      
      // Fetch current game state and player list
      const { data: updatedRoomState, error: fetchUpdatedRoomError } = await supabase
          .from('game_rooms')
          .select('*')
          .eq('id', roomId)
          .single();
      
      if (updatedRoomState) {
          setCurrentRoomGameData(updatedRoomState as GameRoom);
          console.log(`[Client] Host re-entry: Updated room game data for active game.`);
      }
      
      await fetchAndSetGameRooms(); // Refresh lobby state
      console.log(`[Client] Host re-entry completed successfully. Exiting handleJoinRoom.`);
      setIsJoiningOrRejoiningRoom(false);
      return; // CRITICAL: Exit early - no further processing needed for host re-entry
    }

    try {
        // 1. Check room status and max_players (still useful for validation)
        const { data: roomDetails, error: roomDetailsError } = await supabase
            .from('game_rooms')
            .select('id, status, max_players, host_id')
            .eq('id', roomId)
            .single();

        if (roomDetailsError || !roomDetails) {
            console.error("[Client] Error fetching room details or room not found:", roomDetailsError);
            setErrorMp(`Room not found or error fetching details: ${roomDetailsError?.message || 'Unknown error'}`);
            setIsJoiningOrRejoiningRoom(false);
            return;
        }

        // 2. Host re-entry is now handled immediately after detection (before try block)
        // This section is kept as a fallback, but should not be reached for host re-entry scenarios

        // 3. For non-host re-entry or regular joins: Check if player is in game_players
        console.log(`[Client] Checking if player ${user.id} is already in game_players for room ${roomId}.`);
        const { data: existingPlayer, error: playerCheckError } = await supabase
            .from('game_players')
            .select('user_id, is_connected')
            .eq('room_id', roomId)
            .eq('user_id', user.id)
            .maybeSingle(); // Returns one row or null

        if (playerCheckError) {
            console.error("[Client] Error checking for existing player in room:", playerCheckError);
            setErrorMp(`Error verifying player status: ${playerCheckError.message}`);
            setIsJoiningOrRejoiningRoom(false);
            return;
        }

        if (existingPlayer) {
            // Player has an entry in the room - STANDARD REJOIN
            console.log(`[Client] User ${user.id} is already in game_players table for room ${roomId} (STANDARD REJOIN). Connected: ${existingPlayer.is_connected}`);
            if (existingPlayer.is_connected) {
                console.log(`[Client] User ${user.id} is ALREADY CONNECTED to room ${roomId}. Proceeding to room view.`);
            } else {
                // Player was disconnected, attempt to REJOIN
                if (roomDetails.status === 'waiting' || roomDetails.status === 'active') {
                    console.log(`[Client] User ${user.id} attempting to REJOIN ${roomDetails.status} room ${roomId}.`);
                    const { error: rejoinError } = await supabase
                        .from('game_players')
                        .update({
                            is_connected: true,
                            last_seen_at: new Date().toISOString()
                        })
                        .match({ room_id: roomId, user_id: user.id });

                    if (rejoinError) {
                        console.error("[Client] Error rejoining room (updating player status):", rejoinError);
                        setErrorMp(`Failed to rejoin room: ${rejoinError.message}`);
                        setIsJoiningOrRejoiningRoom(false);
                        return;
                    }
                    console.log(`[Client] User ${user.id} successfully REJOINED room ${roomId}.`);
                } else {
                    setErrorMp("Cannot rejoin: this game has finished.");
                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }
            }
        } else {
            // Player is NOT in the room - NEW JOIN attempt
            console.log(`[Client] User ${user.id} is NOT in game_players for room ${roomId}. Evaluating NEW JOIN attempt.`);
            
            // Prevent non-host from joining active games
            if (['active', 'in-progress', 'playing'].includes(roomDetails.status.toLowerCase()) && roomDetails.host_id !== user.id) {
                console.warn(`[Client] Non-host trying to NEW JOIN an active game ${roomId}. This is disallowed.`);
                setErrorMp("Cannot join: this game has already started or finished and you were not originally part of it.");
                setIsJoiningOrRejoiningRoom(false);
                return;
            }
            
            // Only allow new joins to waiting games
            if (roomDetails.status !== 'waiting') {
                setErrorMp("Cannot join: this game has already started or finished and you were not originally part of it.");
                setIsJoiningOrRejoiningRoom(false);
                return;
            }

            // Check room capacity
            const { count: connectedPlayerCount, error: countError } = await supabase
                .from('game_players')
                .select('*', { count: 'exact', head: true })
                .eq('room_id', roomId)
                .eq('is_connected', true);

            if (countError) {
                console.error("[Client] Error counting connected players:", countError);
                setErrorMp(`Error checking room capacity: ${countError.message}`);
                setIsJoiningOrRejoiningRoom(false);
                return;
            }
            
            if (roomDetails.max_players != null && (connectedPlayerCount ?? 0) >= roomDetails.max_players) {
                setErrorMp("Cannot join: room is full.");
                setIsJoiningOrRejoiningRoom(false);
                return;
            }

            // Perform new join insert
            const { data: newPlayerInsert, error: insertError } = await supabase
                .from('game_players')
                .insert({
                    room_id: roomId,
                    user_id: user.id,
                    is_connected: true,
                    last_seen_at: new Date().toISOString()
                })
                .select()
                .single();

            if (insertError) {
                console.error("[Client] Error during new game_players insert:", insertError);
                if (insertError.code === '23505') { // Unique violation
                    setErrorMp("Failed to join: You might still be registered in another room. Please try refreshing or ensure you've fully left other games.");
                } else {
                    setErrorMp(`Failed to join room: ${insertError.message}`);
                }
                setIsJoiningOrRejoiningRoom(false);
                return;
            }
            console.log(`[Client] User ${user.id} successfully made NEW JOIN (inserted) to game_players for room ${roomId}. Data:`, newPlayerInsert);
        }

        // Standard join/rejoin successful - set client active state
        console.log(`[Client] Standard Join/Rejoin successful for room ${roomId}. Setting client active state.`);
        setActiveRoomId(roomId);
        setMultiplayerPanelState('in_room');
        setCenterPanelMpState('mp_game_active');
        setSelectedRoomForDetail(null);
        
        console.log(`[Client] Client active state set for room ${roomId}. Awaiting data fetches and UI transition.`);
        
        if (autoJoinedAfterCreate) {
            await fetchAndSetGameRooms(); // Refresh lobby list to show the new room
        }
        
        // Fetch updated room state
        const { data: updatedRoomState, error: fetchUpdatedRoomError } = await supabase
            .from('game_rooms')
            .select('*')
            .eq('id', roomId)
            .single();
        if (updatedRoomState) setCurrentRoomGameData(updatedRoomState as GameRoom);
        
        await fetchAndSetGameRooms(); // Refresh lobby in case player counts changed
        setIsJoiningOrRejoiningRoom(false);

    } catch (error: any) {
        console.error(`[Client] Error in handleJoinRoomAttempt for room ${roomId}:`, error);
        setErrorMp(`Failed to join/rejoin room (exception): ${error.message}`);
        setIsJoiningOrRejoiningRoom(false);
    }
};

  const handleViewLobbyDetail = (room: GameRoom) => {
    console.log("[LobbyDetail] Viewing room:", room.id, "Current gameRooms:", gameRooms);
    setSelectedRoomForDetail(room);
    setCenterPanelMpState('lobby_list_detail');
    setExpandedLeaderboardData(null);
    setActiveRoomId(null);
    setMultiplayerPanelState('lobby_list');
  };

  const handleExpandLeaderboard = (title: string, entries: LeaderboardEntry[]) => {
    setExpandedLeaderboardData({ title, entries });
    setCenterPanelMpState('expanded_leaderboard');
    setSelectedRoomForDetail(null);
    setActiveRoomId(null);
  };

  // Update handleLeaveRoom to use the Edge Function
  const handleLeaveRoom = async () => {
    console.log('[Client] handleLeaveRoom called.');
    if (!activeRoomId || !user?.id) {
      console.error('[Client] handleLeaveRoom: Missing activeRoomId or user session.', { 
        activeRoomId, 
        userId: user?.id 
      });
      return;
    }
    
    setIsLeavingRoom(true); // For UI feedback
    console.log(`[Client] Attempting to leave room via Edge Function:`, { 
      roomId: activeRoomId, 
      userId: user.id 
    });

    try {
      const { data, error } = await supabase.functions.invoke('leave-room-handler', {
        body: { roomId: activeRoomId, userId: user.id },
      });

      if (error) {
        console.error('[Client] Error leaving room via Edge Function:', error);
        // Consider showing an error toast to the user
        let errorMessage = 'Failed to leave room.';
        if (error.context && error.context.json) {
          errorMessage = error.context.json.error || error.message;
        } else {
          errorMessage = error.message || 'Unknown server error';
        }
        setErrorMp(errorMessage);
      } else {
        console.log('[Client] Successfully invoked leave-room-handler. Response data:', data);
        // IMPORTANT: Clear client-side state *after* successful leave
        console.log('[Client] Leave successful. Resetting active room state.');
        setActiveRoomId(null);
        setCurrentRoomGameData(null);
        setPlayersInRoom([]);
        setSelectedRoomForDetail(null);
        setMultiplayerPanelState('lobby_list');
        setCenterPanelMpState('lobby_list_detail');
        // Navigate to lobby or refresh lobby list
        await fetchAndSetGameRooms(); // Refresh the lobby list
        console.log('[Client] Client-side active room state reset.');
      }
    } catch (e: any) {
      console.error('[Client] Exception during leave room invocation:', e);
      setErrorMp(`Exception leaving room: ${e.message}`);
      
      // Attempt to reset UI even on general exception
      setActiveRoomId(null);
      setCurrentRoomGameData(null);
      setPlayersInRoom([]);
      setSelectedRoomForDetail(null);
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      await fetchAndSetGameRooms();
    } finally {
      setIsLeavingRoom(false);
      console.log('[Client] handleLeaveRoom finished.');
    }
  };

  // Determine which player info to show in the right panel
  const playerToShow = viewingRecentPlayer 
                        ? viewingRecentPlayer 
                        : (selectedPlayerInfo ? selectedPlayerInfo : (isAnswered && currentQuestion ? currentQuestion.correctPlayer : null));

  // Format timer for display
  const formattedTimer = `${String(Math.floor(timer / 60)).padStart(1, '0')}:${String(timer % 60).padStart(2, '0')}`;

  // Update the roomCode handling useEffect to use fetchAndSetGameRooms
  useEffect(() => {
    const roomCodeFromUrl = searchParams.get('roomCode');
    console.log("[DEBUG] Detected roomCodeFromUrl:", roomCodeFromUrl);

    if (roomCodeFromUrl && !loadingUser) {
      if (!user) {
        setErrorMp("Please log in to join the room.");
        setShowAuthForm(true);
        return;
      }

      if (selectedOverallGameType !== 'multiplayer') {
        handleOverallGameTypeChange('multiplayer');
      }
      
      const joinRoomByCode = async (code: string) => {
        setIsLoadingRooms(true);
        setErrorMp(null);
        try {
          const { data: roomData, error: roomError } = await supabase
            .from('game_rooms')
            .select('id, status, title, multiplayer_mode, profiles:host_id (username), created_at, host_id')
            .eq('room_code', code)
            .maybeSingle();

          if (roomError) throw roomError;

          if (roomData) {
            if (roomData.status === 'waiting') {
              await handleJoinRoom(roomData.id);
              // Refresh lobby after joining
              await fetchAndSetGameRooms();
            } else {
              setErrorMp(`Room '${roomData.title || code}' is no longer waiting or has already started.`);
              setSelectedRoomForDetail(roomData as unknown as GameRoom);
              setCenterPanelMpState('lobby_list_detail');
            }
          } else {
            setErrorMp(`Room with code "${code}" not found.`);
          }
        } catch (e: any) {
          console.error("[DEBUG] Error joining by room code:", e);
          setErrorMp(`Error finding or joining room: ${e.message}`);
        } finally {
          setIsLoadingRooms(false);
        }
      };

      if (multiplayerPanelState === 'lobby_list' || selectedOverallGameType === 'multiplayer') {
        joinRoomByCode(roomCodeFromUrl);
      }
    }
  }, [searchParams, user, loadingUser]);

  // Update handleCreateRoom to include game settings
  const handleCreateRoom = async () => {
    console.log("[handleCreateRoom] Attempting to create room with settings:", {
      duration: selectedGameDuration,
      maxPlayers: selectedMaxPlayers,
      mode: newRoomMode
    });

    if (loadingUser || loadingProfile) {
      setErrorMp("Still authenticating or loading profile, please wait...");
      console.warn("Create room blocked: User or profile still loading.");
      return;
    }

    if (!user || !userProfile || !userProfile.username) {
      setErrorMp("Login & ensure your username is set to create room.");
      console.warn("Create room blocked: User, userProfile, or userProfile.username missing.", { 
        userExists: !!user, 
        profileExists: !!userProfile, 
        usernameExists: !!userProfile?.username 
      });
      return;
    }

    setErrorMp(null);
    setIsCreatingRoom(true);
    
    const roomTitle = userProfile.username 
      ? `${userProfile.username}'s Game` 
      : `User ${user.id.slice(0,6)}'s Game`;
    
    const roomCode = Math.random().toString(36).substring(2, 8).toUpperCase();

    try {
      const { data, error: createError } = await supabase
        .from('game_rooms')
        .insert({ 
          host_id: user.id, 
          status: 'waiting', 
          multiplayer_mode: newRoomMode,
          title: roomTitle,
          room_code: roomCode,
          game_duration_seconds: selectedGameDuration,
          max_players: selectedMaxPlayers,
          current_round_number: 0,
          game_start_timestamp: null
        })
        .select()
        .single();
      
      if (createError) throw createError;
      
      if (data) {
        console.log('[DEBUG] Room created with settings:', data);
        // Show shareable link
        alert(`Room Created! Share this code: ${roomCode}\nOr link: ${window.location.origin}/lobby?roomCode=${roomCode}`);
        // Automatically join the created room
        await handleJoinRoom(data.id, true);
        // Refresh the lobby list
        await fetchAndSetGameRooms();
      }
    } catch (e: any) {
      setErrorMp(`Failed to create room: ${e.message}`);
      console.error("[DEBUG] Error creating room:", e);
    } finally {
      setIsCreatingRoom(false);
    }
  };

  // Add fetchPlayersInActiveRoom function
  const fetchPlayersInActiveRoom = async (roomId: string) => {
    console.log('[RoomView] fetchPlayersInActiveRoom called:', {
      roomId,
      timestamp: new Date().toISOString(),
      currentPlayersCount: playersInRoom.length
    });

    if (!roomId) {
      console.warn('[RoomView] fetchPlayersInActiveRoom: No roomId provided, returning early');
      return;
    }

    setIsLoadingPlayers(true);
    console.log(`[RoomView] Starting to fetch players for room: ${roomId}`);
    
    try {
      console.log('[RoomView] Executing Supabase query for game_players with profiles join');
      const { data, error } = await supabase
        .from('game_players')
        .select(`
          user_id,
          is_ready,
          is_connected,
          profile:profiles (
            username
          )
        `)
        .eq('room_id', roomId);

      if (error) {
        console.error("[RoomView] Database error fetching players:", {
          error: JSON.stringify(error, null, 2),
          errorMessage: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          roomId
        });
        setPlayersInRoom([]);
        setErrorMp(`Failed to fetch players: ${error.message}`);
      } else {
        console.log("[RoomView] Successfully fetched players from database:", {
          rawData: data,
          playerCount: data?.length || 0,
          roomId
        });

        // Transform the data to match PlayerInRoom type
        const transformedData: PlayerInRoom[] = (data || []).map((dbPlayer, index) => {
          console.log(`[RoomView] Transforming player ${index + 1}:`, {
            rawPlayer: dbPlayer,
            profileData: dbPlayer.profile
          });

          let usernameFromProfile: string | null = null;
          if (dbPlayer.profile) { // dbPlayer.profile is the data from the 'profiles' table
            if (Array.isArray(dbPlayer.profile)) { // If Supabase returns it as an array
              usernameFromProfile = dbPlayer.profile[0]?.username || null;
              console.log(`[RoomView] Profile is array, extracted username:`, usernameFromProfile);
            } else { // If Supabase returns it as an object
              usernameFromProfile = (dbPlayer.profile as { username: string | null }).username || null;
              console.log(`[RoomView] Profile is object, extracted username:`, usernameFromProfile);
            }
          } else {
            console.log(`[RoomView] No profile data for player:`, dbPlayer.user_id);
          }

          const transformedPlayer = {
            user_id: dbPlayer.user_id,
            is_ready: dbPlayer.is_ready,
            is_connected: dbPlayer.is_connected,
            profile: usernameFromProfile ? { username: usernameFromProfile } : null
          };

          console.log(`[RoomView] Transformed player ${index + 1}:`, transformedPlayer);
          return transformedPlayer;
        });

        console.log("[RoomView] Final transformed players data:", {
          transformedData,
          playerCount: transformedData.length,
          playersWithUsernames: transformedData.filter(p => p.profile?.username).length,
          playersReady: transformedData.filter(p => p.is_ready).length,
          playersConnected: transformedData.filter(p => p.is_connected).length
        });

        setPlayersInRoom(transformedData);
        console.log("[RoomView] Updated playersInRoom state successfully");
      }
    } catch (e: any) {
      console.error("[RoomView] Exception during fetchPlayersInActiveRoom:", {
        exception: e,
        exceptionMessage: e.message,
        exceptionStack: e.stack,
        roomId
      });
      setPlayersInRoom([]);
      setErrorMp(`Exception fetching players: ${e.message}`);
    } finally {
      setIsLoadingPlayers(false);
      console.log('[RoomView] fetchPlayersInActiveRoom completed:', {
        roomId,
        timestamp: new Date().toISOString(),
        finalPlayersCount: playersInRoom.length
      });
    }
  };

  // Add handleToggleReady function with optimistic updates and submission state
  const handleToggleReady = async () => {
    console.log('[RoomView] handleToggleReady called - Starting comprehensive logging');
    console.log('[RoomView] Current state check:', {
      hasUser: !!user,
      userId: user?.id,
      hasActiveRoomId: !!activeRoomId,
      activeRoomId,
      isSubmittingReady,
      playersInRoomCount: playersInRoom.length,
      timestamp: new Date().toISOString()
    });

    // Early exit conditions with detailed logging
    if (!user?.id || !activeRoomId || isSubmittingReady) {
      if (isSubmittingReady) {
        console.log('[RoomView] Already submitting ready state, skipping this call to prevent duplicate submissions');
        return;
      }
      if (!user?.id) {
        console.warn('[RoomView] handleToggleReady: User ID missing, cannot proceed');
        return;
      }
      if (!activeRoomId) {
        console.warn('[RoomView] handleToggleReady: Active room ID missing, cannot proceed');
        return;
      }
      return;
    }

    // Find current player and validate
    const currentPlayerInRoom = playersInRoom.find(p => p.user_id === user.id);
    console.log('[RoomView] Current player lookup:', {
      foundPlayer: !!currentPlayerInRoom,
      currentPlayerData: currentPlayerInRoom,
      allPlayersInRoom: playersInRoom.map(p => ({ user_id: p.user_id, is_ready: p.is_ready, username: p.profile?.username }))
    });

    if (!currentPlayerInRoom) {
      console.error('[RoomView] Current player not found in playersInRoom list - this should not happen');
      console.error('[RoomView] Debug info:', {
        searchingForUserId: user.id,
        playersInRoom: playersInRoom,
        playersInRoomUserIds: playersInRoom.map(p => p.user_id)
      });
      return;
    }

    // Determine new ready state
    const currentReadyState = currentPlayerInRoom.is_ready;
    const newReadyState = !currentReadyState;
    
    console.log('[RoomView] Ready state transition:', {
      currentReadyState,
      newReadyState,
      playerUserId: user.id,
      roomId: activeRoomId
    });

    // Set submitting state to prevent multiple calls
    setIsSubmittingReady(true);
    console.log('[RoomView] Set isSubmittingReady to true - blocking further submissions');

    // 1. Optimistic UI Update
    console.log('[RoomView] Applying optimistic UI update');
    setPlayersInRoom(prevPlayers => {
      const updatedPlayers = prevPlayers.map(p =>
        p.user_id === user.id ? { ...p, is_ready: newReadyState } : p
      );
      console.log('[RoomView] Optimistic update applied:', {
        beforeUpdate: prevPlayers.find(p => p.user_id === user.id),
        afterUpdate: updatedPlayers.find(p => p.user_id === user.id),
        allPlayersAfterUpdate: updatedPlayers.map(p => ({ user_id: p.user_id, is_ready: p.is_ready }))
      });
      return updatedPlayers;
    });

    try {
      console.log(`[RoomView] Starting database update - attempting to update ready state to: ${newReadyState} for user ${user.id} in room ${activeRoomId}`);
      
      const { data: updatedGamePlayer, error } = await supabase
        .from('game_players')
        .update({ is_ready: newReadyState })
        .eq('user_id', user.id)
        .eq('room_id', activeRoomId)
        .select()
        .single();

      if (error) {
        console.error('[RoomView] Database update failed:', {
          error: JSON.stringify(error, null, 2),
          errorMessage: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          attemptedUpdate: { user_id: user.id, room_id: activeRoomId, is_ready: newReadyState }
        });
        
        // Revert optimistic update on error
        console.log('[RoomView] Reverting optimistic update due to database error');
        setPlayersInRoom(prevPlayers => {
          const revertedPlayers = prevPlayers.map(p =>
            p.user_id === user.id ? { ...p, is_ready: currentReadyState } : p
          );
          console.log('[RoomView] Optimistic update reverted:', {
            revertedTo: currentReadyState,
            playerAfterRevert: revertedPlayers.find(p => p.user_id === user.id)
          });
          return revertedPlayers;
        });
        
        setErrorMp(`Failed to update ready state: ${error.message}`);
      } else {
        console.log('[RoomView] Database update successful:', {
          updatedGamePlayer,
          confirmedReadyState: updatedGamePlayer?.is_ready,
          updateTimestamp: new Date().toISOString()
        });
        
        // Note: We rely on Realtime subscription to update the playersInRoom state
        // with the confirmed data. The optimistic update handles immediacy.
        console.log('[RoomView] Relying on Realtime subscription to confirm the update');
      }
    } catch (e: any) {
      console.error('[RoomView] Exception during ready state update:', {
        exception: e,
        exceptionMessage: e.message,
        exceptionStack: e.stack,
        attemptedUpdate: { user_id: user.id, room_id: activeRoomId, is_ready: newReadyState }
      });
      
      // Revert optimistic update on exception
      console.log('[RoomView] Reverting optimistic update due to exception');
      setPlayersInRoom(prevPlayers => {
        const revertedPlayers = prevPlayers.map(p =>
          p.user_id === user.id ? { ...p, is_ready: currentReadyState } : p
        );
        console.log('[RoomView] Optimistic update reverted after exception:', {
          revertedTo: currentReadyState,
          playerAfterRevert: revertedPlayers.find(p => p.user_id === user.id)
        });
        return revertedPlayers;
      });
      
      setErrorMp(`Exception updating ready state: ${e.message}`);
    } finally {
      setIsSubmittingReady(false);
      console.log('[RoomView] Set isSubmittingReady to false - ready for next submission');
      console.log('[RoomView] handleToggleReady completed at:', new Date().toISOString());
    }
  };

  // Add useEffect for fetching players and setting up realtime subscription
  useEffect(() => {
    const currentRoomDetails = gameRooms.find(room => room.id === activeRoomId);
    const isCurrentUserHost = currentRoomDetails?.host_id === user?.id;
    const logPrefix = isCurrentUserHost ? '[Realtime HOST]' : '[Realtime]';
    const capturedActiveRoomId = activeRoomId; // Capture activeRoomId for cleanup closure
    
    console.log(`${logPrefix} useEffect triggered for room subscriptions:`, {
      activeRoomId: capturedActiveRoomId,
      multiplayerPanelState,
      timestamp: new Date().toISOString(),
      currentPlayersCount: playersInRoom.length,
      isCurrentUserHost,
      userId: user?.id,
      hostId: currentRoomDetails?.host_id
    });

    let gamePlayersChannel: RealtimeChannel | null = null;
    let gameRoomChannel: RealtimeChannel | null = null; // New channel for game_rooms

    if (activeRoomId && multiplayerPanelState === 'in_room') {
      console.log(`${logPrefix} Conditions met for setting up subscriptions:`, {
        activeRoomId,
        multiplayerPanelState,
        isCurrentUserHost
      });
      // Subscription for game_players (player list, ready status)
      console.log(`${logPrefix} Setting up subscription for game_players in room-${activeRoomId}`);
      gamePlayersChannel = supabase.channel(`room-${activeRoomId}-players`)
        .on('postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'game_players',
            filter: `room_id=eq.${activeRoomId}`
          },
          (payload) => {
            // Determine if current user is host for specialized logging
            const currentRoomDetails = gameRooms.find(room => room.id === activeRoomId);
            const isCurrentUserHost = currentRoomDetails?.host_id === user?.id;
            const logPrefix = isCurrentUserHost ? '[Realtime HOST]' : '[Realtime]';

            console.log(`${logPrefix} game_players change received:`, {
              eventType: payload.eventType,
              table: payload.table,
              schema: payload.schema,
              new: payload.new,
              old: payload.old,
              timestamp: new Date().toISOString(),
              isCurrentUserHost,
              currentUserId: user?.id,
              roomHostId: currentRoomDetails?.host_id
            });

            const { eventType, new: newRecord, old: oldRecord } = payload;

            // Update playersInRoom state directly based on the Realtime event
            // This is more efficient than re-fetching all players
            setPlayersInRoom(currentPlayers => {
              console.log(`${logPrefix} Updating playersInRoom state based on event:`, {
                eventType,
                currentPlayersCount: currentPlayers.length,
                currentPlayers: currentPlayers.map(p => ({ user_id: p.user_id, is_ready: p.is_ready })),
                isCurrentUserHost
              });

              let updatedPlayers = [...currentPlayers];

              if (eventType === 'INSERT') {
                const newPlayer = newRecord as any;
                console.log(`${logPrefix} Processing INSERT event for new player:`, newPlayer);
                
                // Check if player already exists (prevent duplicates)
                if (!updatedPlayers.find(p => p.user_id === newPlayer.user_id)) {
                  // We need to fetch the profile data for the new player
                  // For now, add with minimal data and let the fetch handle the profile
                  const playerToAdd: PlayerInRoom = {
                    user_id: newPlayer.user_id,
                    is_ready: newPlayer.is_ready || false,
                    is_connected: newPlayer.is_connected || false,
                    profile: null // Will be populated by the fetch
                  };
                  updatedPlayers.push(playerToAdd);
                  console.log(`${logPrefix} Added new player to local state:`, playerToAdd);
                  
                  // Host-specific logging for player joins
                  if (isCurrentUserHost) {
                    console.log(`${logPrefix} New player joined room - Host perspective:`, {
                      newPlayerId: newPlayer.user_id,
                      newPlayerReady: newPlayer.is_ready,
                      newPlayerConnected: newPlayer.is_connected,
                      totalPlayersAfterJoin: updatedPlayers.length,
                      roomId: activeRoomId
                    });
                  }
                  
                  // Trigger a fetch to get complete player data including profiles
                  setTimeout(() => fetchPlayersInActiveRoom(activeRoomId), 100);
                } else {
                  console.log(`${logPrefix} Player already exists in local state, skipping INSERT`);
                }
              } else if (eventType === 'UPDATE') {
                const updatedPlayer = newRecord as any;
                console.log(`${logPrefix} Processing UPDATE event for player:`, updatedPlayer);
                
                updatedPlayers = updatedPlayers.map(p => {
                  if (p.user_id === updatedPlayer.user_id) {
                    const updated = {
                      ...p,
                      is_ready: updatedPlayer.is_ready,
                      is_connected: updatedPlayer.is_connected
                    };
                    console.log(`${logPrefix} Updated player in local state:`, {
                      before: p,
                      after: updated
                    });
                    
                    // Host-specific logging for player updates
                    if (isCurrentUserHost) {
                      console.log(`${logPrefix} Player state changed - Host perspective:`, {
                        playerId: updatedPlayer.user_id,
                        readyChanged: p.is_ready !== updatedPlayer.is_ready,
                        connectedChanged: p.is_connected !== updatedPlayer.is_connected,
                        newReadyState: updatedPlayer.is_ready,
                        newConnectedState: updatedPlayer.is_connected,
                        roomId: activeRoomId,
                        allPlayersReady: updatedPlayers.every(player => player.is_ready),
                        totalPlayers: updatedPlayers.length
                      });
                    }
                    
                    return updated;
                  }
                  return p;
                });
              } else if (eventType === 'DELETE') {
                const deletedPlayer = oldRecord as any;
                console.log(`${logPrefix} Processing DELETE event for player:`, deletedPlayer);
                
                const playerIdToRemove = deletedPlayer?.user_id;
                if (playerIdToRemove) {
                  const playerBeforeRemoval = updatedPlayers.find(p => p.user_id === playerIdToRemove);
                  updatedPlayers = updatedPlayers.filter(p => p.user_id !== playerIdToRemove);
                  console.log(`${logPrefix} Removed player from local state:`, playerIdToRemove);
                  
                  // Host-specific logging for player departures
                  if (isCurrentUserHost) {
                    console.log(`${logPrefix} Player left room - Host perspective:`, {
                      departedPlayerId: playerIdToRemove,
                      departedPlayerWasReady: playerBeforeRemoval?.is_ready,
                      remainingPlayersCount: updatedPlayers.length,
                      roomId: activeRoomId,
                      allRemainingPlayersReady: updatedPlayers.every(player => player.is_ready)
                    });
                  }
                }
              }

              console.log(`${logPrefix} Final updated players state:`, {
                updatedPlayersCount: updatedPlayers.length,
                updatedPlayers: updatedPlayers.map(p => ({ user_id: p.user_id, is_ready: p.is_ready })),
                isCurrentUserHost,
                allPlayersReady: updatedPlayers.every(p => p.is_ready),
                connectedPlayersCount: updatedPlayers.filter(p => p.is_connected).length
              });

              return updatedPlayers;
            });
          }
        )
        .subscribe((status, err) => {
          const currentRoomDetails = gameRooms.find(room => room.id === activeRoomId);
          const isCurrentUserHost = currentRoomDetails?.host_id === user?.id;
          const logPrefix = isCurrentUserHost ? '[Realtime HOST]' : '[Realtime]';
          
          if (status === 'SUBSCRIBED') {
            console.log(`${logPrefix} Successfully subscribed to game_players for room-${activeRoomId}`, {
              isCurrentUserHost,
              roomId: activeRoomId,
              userId: user?.id,
              hostId: currentRoomDetails?.host_id
            });
            fetchPlayersInActiveRoom(activeRoomId); // Initial fetch on subscribe
          }
          if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.error(`${logPrefix} game_players channel error/timeout for room-${activeRoomId}:`, err);
            setErrorMp(`Realtime (players) connection error: ${err?.message || 'Unknown error'}`);
          }
        });

      // New Subscription for game_rooms (game state: status, current_question_data, scores, etc.)
      console.log(`${logPrefix} Setting up subscription for game_rooms, specific_room-${activeRoomId}`);
      gameRoomChannel = supabase.channel(`room-${activeRoomId}-state`) // Unique channel name
        .on('postgres_changes',
          {
            event: 'UPDATE', // Only listen to UPDATE events for the specific room
            schema: 'public',
            table: 'game_rooms',
            filter: `id=eq.${activeRoomId}`
          },
          (payload) => {
            // Determine if current user is host for specialized logging
            const isCurrentUserHost = payload.new?.host_id === user?.id;
            const logPrefix = isCurrentUserHost ? '[Realtime HOST]' : '[Realtime]';
            
            console.log(`${logPrefix} game_rooms (specific room) UPDATE received:`, {
              ...payload,
              isCurrentUserHost,
              currentUserId: user?.id,
              roomHostId: payload.new?.host_id
            });
            const updatedRoomData = payload.new as GameRoom; // Type assertion

            // Host-specific logging for room state changes
            if (isCurrentUserHost) {
              console.log(`${logPrefix} Room state changed - Host perspective:`, {
                roomId: updatedRoomData.id,
                newStatus: updatedRoomData.status,
                currentRound: updatedRoomData.current_round_number,
                hasQuestionData: !!updatedRoomData.current_question_data,
                playerScores: updatedRoomData.player_scores,
                roundEndsAt: updatedRoomData.current_round_ends_at
              });
            }

            // Update local state with the new room data
            // This will drive UI changes for question display, scores, status
            setCurrentRoomGameData(updatedRoomData);

            // Also refresh the main gameRooms list in case status changes affect lobby view
            // This ensures the selectedRoomForDetail also gets updated if it's the current room
            setGameRooms(prevRooms =>
              prevRooms.map(room => room.id === updatedRoomData.id ? { ...room, ...updatedRoomData } : room)
            );
            if (selectedRoomForDetail?.id === updatedRoomData.id) {
              setSelectedRoomForDetail(prev => prev ? { ...prev, ...updatedRoomData } : null);
            }
          }
        )
        .subscribe((status, err) => {
          const currentRoomDetails = gameRooms.find(room => room.id === activeRoomId);
          const isCurrentUserHost = currentRoomDetails?.host_id === user?.id;
          const logPrefix = isCurrentUserHost ? '[Realtime HOST]' : '[Realtime]';
          
          if (status === 'SUBSCRIBED') {
            console.log(`${logPrefix} Successfully subscribed to game_rooms (specific room) for room-${activeRoomId}`, {
              isCurrentUserHost,
              roomId: activeRoomId,
              userId: user?.id,
              hostId: currentRoomDetails?.host_id
            });
            // Fetch initial room state once subscribed
            const fetchInitialRoomState = async () => {
              const { data: roomArray, error } = await supabase
                .from('game_rooms')
                .select('id, status, host_id, multiplayer_mode, title, room_code, max_players, current_question_data, player_scores, current_round_answers, current_round_number, game_start_timestamp, current_round_ends_at')
                .eq('id', activeRoomId);

              if (error) {
                console.error(`${logPrefix} Error fetching initial room state:`, error);
              } else if (roomArray && roomArray.length > 0) {
                setCurrentRoomGameData(roomArray[0] as GameRoom);
                if (isCurrentUserHost) {
                  console.log(`${logPrefix} Initial room state loaded - Host perspective:`, {
                    roomId: activeRoomId,
                    status: roomArray[0].status,
                    currentRound: roomArray[0].current_round_number,
                    hasQuestionData: !!roomArray[0].current_question_data
                  });
                }
              } else {
                console.warn(`${logPrefix} Initial room state fetch: No room data found for id:`, activeRoomId);
                // setCurrentRoomGameData(null); // Or handle appropriately
              }
            };
            fetchInitialRoomState();
          }
          if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.error(`${logPrefix} game_rooms channel error/timeout for room-${activeRoomId}:`, err);
            setErrorMp(`Realtime (game state) connection error: ${err?.message || 'Unknown error'}`);
          }
        });
    }

    // Cleanup function
    return () => {
      const cleanupRoomDetails = gameRooms.find(room => room.id === capturedActiveRoomId);
      const isCurrentUserHost = cleanupRoomDetails?.host_id === user?.id;
      const logPrefix = isCurrentUserHost ? '[Realtime HOST]' : '[Realtime]';
      
      console.log(`${logPrefix} Cleanup function called for room subscriptions:`, {
        capturedActiveRoomId,
        hasGamePlayersChannel: !!gamePlayersChannel,
        hasGameRoomChannel: !!gameRoomChannel,
        timestamp: new Date().toISOString(),
        isCurrentUserHost,
        userId: user?.id,
        hostId: cleanupRoomDetails?.host_id
      });

      if (gamePlayersChannel) {
        console.log(`${logPrefix} Unsubscribing from game_players for room-${capturedActiveRoomId}`);
        supabase.removeChannel(gamePlayersChannel)
          .then(status => console.log(`${logPrefix} game_players channel removal status for room-${capturedActiveRoomId}:`, status))
          .catch(err => console.error(`${logPrefix} Error removing game_players channel for room-${capturedActiveRoomId}:`, err));
        gamePlayersChannel = null;
      }
      if (gameRoomChannel) {
        console.log(`${logPrefix} Unsubscribing from game_rooms (specific room) for room-${capturedActiveRoomId}`);
        supabase.removeChannel(gameRoomChannel)
          .then(status => console.log(`${logPrefix} game_rooms channel removal status for room-${capturedActiveRoomId}:`, status))
          .catch(err => console.error(`${logPrefix} Error removing game_rooms channel for room-${capturedActiveRoomId}:`, err));
        gameRoomChannel = null;
      }
    };
  }, [activeRoomId, multiplayerPanelState]); // Dependency array

  // Update handleStartGame function after handleToggleReady
  const handleStartGame = async () => {
    if (isStartingGame) {
      console.warn("[Client] Start game already in progress.");
      return;
    }

    if (!activeRoomId || !user) {
      console.warn("[Client] Start game conditions not met: no room or user.");
      setErrorMp("Cannot start game: conditions not met.");
      return;
    }

    const currentRoomDetails = gameRooms.find(room => room.id === activeRoomId);
    const isCurrentUserHost = currentRoomDetails?.host_id === user.id;
    
    if (!isCurrentUserHost) {
      console.warn("[Client] Start game conditions not met: not host.");
      setErrorMp("Only the host can start the game.");
      return;
    }

    const allPlayersReady = playersInRoom.length > 0 && playersInRoom.every(p => p.is_ready);
    const minPlayersMet = playersInRoom.length >= 2;
    const canStartGame = allPlayersReady && minPlayersMet && currentRoomDetails?.status === 'waiting';

    if (!canStartGame) {
      console.warn("[Client] Start game conditions not met: players not ready or minimum not met.");
      setErrorMp("Cannot start game: all players must be ready and minimum 2 players required.");
      return;
    }

    setIsStartingGame(true); // Set flag before starting
    console.log(`[Client] Host ${user.id} attempting to start game in room ${activeRoomId}`);
    setErrorMp(null);

    try {
      const { data, error: invokeError } = await supabase.functions.invoke('start-game-handler', {
        body: { roomId: activeRoomId },
      });

      if (invokeError) {
        console.error("[Client] Error invoking start-game-handler:", invokeError);
        let errorMessage = 'Failed to start game.';
        if (invokeError.context && invokeError.context.json) {
          errorMessage = invokeError.context.json.error || invokeError.message;
        } else {
          errorMessage = invokeError.message || 'Unknown server error';
        }
        setErrorMp(errorMessage);
      } else {
        console.log("[Client] Successfully invoked start-game-handler:", data);
        // The game state will be updated via Realtime subscription
      }
    } catch (e: any) {
      console.error("[Client] Client-side exception invoking start-game-handler:", e);
      setErrorMp(`Client-side exception: ${e.message}`);
    } finally {
      setIsStartingGame(false); // Clear flag in finally block
    }
  };

  // Add handleMultiplayerAnswerSubmit function after handleStartGame
  const handleMultiplayerAnswerSubmit = async (choiceName: string) => {
    if (!user || !activeRoomId || !currentRoomGameData) {
      console.warn("[Client] Cannot submit answer: missing user, room, or game data");
      return;
    }

    // TODO: Implement answer submission to Edge Function
    console.log(`[Client] User ${user.id} submitting answer "${choiceName}" for room ${activeRoomId}`);
    // This will be implemented in the next step
  };

  // Add effect to handle new answers
  useEffect(() => {
    // Ensure currentRoomGameData and current_round_answers exist
    if (!currentRoomGameData || currentRoomGameData.current_round_answers === null || currentRoomGameData.current_round_answers === undefined) {
      // If null or undefined, nothing to process for answers
      return;
    }

    // Now check if it's an array. If it's an object {} but not an array, treat as empty array.
    let answersToProcess: GameAnswer[];
    if (!Array.isArray(currentRoomGameData.current_round_answers)) {
      console.warn(
        '[Effect_HandleNewAnswers] current_round_answers was defined but not an array, treating as empty array. Value:', 
        currentRoomGameData.current_round_answers
      );
      // Treat as empty array for processing - this handles the {} case gracefully
      answersToProcess = [];
    } else {
      answersToProcess = currentRoomGameData.current_round_answers; // Now guaranteed to be an array
    }
    
    // Your existing logic for processing answersToProcess
    const newAnswers = answersToProcess.filter((answer: GameAnswer) => { // Ensure GameAnswer type is imported/defined
      const questionAnswers = animatedAnswers[answer.questionId] || new Set();
      return !questionAnswers.has(answer.userId);
    });

    if (newAnswers.length > 0) {
      setAnimatedAnswers((prev: AnimatedAnswersState) => { // Ensure AnimatedAnswersState type
        const updated = { ...prev };
        newAnswers.forEach((answer: GameAnswer) => {
          if (!updated[answer.questionId]) {
            updated[answer.questionId] = new Set();
          }
          updated[answer.questionId].add(answer.userId);
        });
        return updated;
      });

      const timeoutId = setTimeout(() => {
        setAnimatedAnswers((prev: AnimatedAnswersState) => {
          const updated = { ...prev };
          newAnswers.forEach((answer: GameAnswer) => {
            if (updated[answer.questionId]) {
              updated[answer.questionId].delete(answer.userId);
              if (updated[answer.questionId].size === 0) {
                delete updated[answer.questionId];
              }
            }
          });
          return updated;
        });
      }, 3000);
      return () => clearTimeout(timeoutId);
    }
  }, [currentRoomGameData?.current_round_answers, animatedAnswers]); // Make sure animatedAnswers is in the dependency array

  // Add new useEffect for debugging player scores panel
  useEffect(() => {
    if (currentRoomGameData?.status === 'active' && multiplayerPanelState === 'in_room') {
      console.log("[PlayerScoresPanel] Rendering with playersInRoom:", playersInRoom);
    }
  }, [playersInRoom, currentRoomGameData?.status, multiplayerPanelState]);

  return (
    <main className="flex min-h-screen flex-col items-center p-4 md:p-12 lg:p-24 pt-12 md:pt-20">
      {/* Auth Modal in top-right corner */}
      <div className="absolute top-4 right-4 z-[100]">
        <AuthModal />
      </div>

      {/* Header Row Container for Title and Flanking SP Mode Buttons */}
      <div className="flex items-center justify-center w-full max-w-5xl mb-1 space-x-4 md:space-x-6">
        {/* Timed Mode Button Container (always takes up space) */}
        <div className="w-[150px] md:w-[180px] flex-shrink-0 flex justify-start">
          {selectedOverallGameType === 'single-player' && (
            <Button
              onClick={() => handleModeButtonClick('timed')}
              className={cn(
                "px-5 py-2.5 md:px-7 md:py-3.5 text-lg md:text-xl font-bold rounded-lg",
                "border-yellow-500 border-[3px]",
                "transition-all shadow-md",
                activeGameMode === 'timed' 
                  ? "bg-red-600 text-white ring-4 ring-yellow-500 ring-offset-2 ring-offset-green-950"
                  : "bg-red-700 text-gray-200 hover:bg-red-500"
              )}
            >
              Timed Mode
            </Button>
          )}
        </div>

        {/* Jumbotron Title - Centered */}
        <div
          className="relative bg-[#0A0A0A] p-3 md:p-4 shadow-2xl inline-block border-2 border-[#050505] rounded-[5px]
                     flex-shrink-0
                     [background-image:repeating-radial-gradient(circle_at_center,rgba(255,255,255,0.10)_0,rgba(255,255,255,0.10)_2px,transparent_2px,transparent_100%)]
                     [background-size:8px_8px]
                     [box-shadow:0_0_10px_rgba(0,0,0,0.5),inset_0_0_8px_rgba(80,80,80,0.2),0_2px_10px_1px_rgba(0,0,0,0.35)]
                     after:content-[''] after:absolute after:inset-0
                     after:[background:linear-gradient(rgba(10,10,10,0.2)_50%,transparent_50%)]
                     after:[background-size:100%_6px] after:opacity-50 after:pointer-events-none after:z-[1]
                     before:content-[''] before:absolute before:inset-0 before:rounded-[5px]
                     before:[box-shadow:inset_0_3px_0_0_rgba(255,255,255,0.1),inset_0_-3px_0_0_rgba(0,0,0,0.3),inset_3px_0_0_0_rgba(255,255,255,0.07),inset_-3px_0_0_0_rgba(0,0,0,0.2)]
                     before:pointer-events-none before:z-[2]"
        >
          <h1 className="jumbotron-title text-4xl md:text-5xl uppercase whitespace-nowrap">
            Recognition Combine
          </h1>
        </div>

        {/* Normal Mode Button Container (always takes up space) */}
        <div className="w-[150px] md:w-[180px] flex-shrink-0 flex justify-end">
          {selectedOverallGameType === 'single-player' && (
            <Button
              onClick={() => handleModeButtonClick('normal')}
              className={cn(
                "px-5 py-2.5 md:px-7 md:py-3.5 text-lg md:text-xl font-bold rounded-lg",
                "border-yellow-500 border-[3px]",
                "transition-all shadow-md",
                activeGameMode === 'normal' 
                  ? "bg-blue-600 text-white ring-4 ring-yellow-500 ring-offset-2 ring-offset-green-950"
                  : "bg-blue-700 text-gray-200 hover:bg-blue-600"
              )}
            >
              Normal Mode
            </Button>
          )}
        </div>
      </div>

      {/* Single-player / Multiplayer Mode Selection Buttons */}
      <div 
        className="flex items-center justify-center w-full max-w-lg mb-4 md:mb-6 space-x-0"
        style={{ marginTop: '-5px' }}
      >
        <Button
          onClick={() => handleOverallGameTypeChange('single-player')}
          className={cn(
            "px-5 py-2.5 md:px-7 md:py-3 text-sm md:text-base font-bold rounded-l-lg rounded-r-none",
            "transition-all duration-200 ease-in-out",
            selectedOverallGameType === 'single-player'
              ? "bg-black text-yellow-300 ring-2 ring-lime-500 ring-offset-2 ring-offset-green-900 brightness-95 shadow-inner shadow-lime-500/30 scale-105 z-10"
              : "bg-slate-800 text-slate-400 hover:bg-slate-700 hover:text-lime-400 border border-slate-700 shadow-md"
          )}
        >
          Single-player Mode
        </Button>
        <Button
          onClick={() => handleOverallGameTypeChange('multiplayer')}
          className={cn(
            "px-5 py-2.5 md:px-7 md:py-3 text-sm md:text-base font-bold rounded-r-lg rounded-l-none",
            "transition-all duration-200 ease-in-out",
            selectedOverallGameType === 'multiplayer'
              ? "bg-black text-yellow-300 ring-2 ring-lime-500 ring-offset-2 ring-offset-green-900 brightness-95 shadow-inner shadow-lime-500/30 scale-105 z-10"
              : "bg-slate-800 text-slate-400 hover:bg-slate-700 hover:text-lime-400 border border-slate-700 shadow-md"
          )}
        >
          Multiplayer Mode
        </Button>
      </div>

      {/* Main Content Container */}
      <div className="w-full flex flex-col items-center">
        {/* Main Grid Container - ALWAYS PRESENT with consistent spacing */}
        <div className="w-full max-w-[77rem] mx-auto grid grid-cols-1 md:grid-cols-4 gap-5" style={{ minHeight: '600px' }}>
          {/* Left Panel */}
          <div className="md:col-span-1 bg-green-900 bg-opacity-75 pt-5 px-3 pb-3 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col">
            {selectedOverallGameType === 'single-player' ? (
              // Single Player Left Panel Content
              <>
                <div className="flex flex-col items-center">
                  <div className="flex items-baseline justify-center gap-x-3 w-full">
                    <div className="text-center">
                      <h2 className="text-2xl md:text-3xl font-bold mb-0">Score</h2>
                      <span className="text-3xl md:text-4xl font-extrabold text-yellow-400 leading-none block">
                        {score}
                      </span>
                    </div>
                    {activeGameMode === 'timed' && gameStatus === 'playing' && (
                      <div className="text-center">
                        <h2 className="text-lg md:text-xl font-bold mb-0">Timer</h2>
                        <span className="text-2xl md:text-3xl font-extrabold text-orange-400 leading-none block">
                          {formattedTimer}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="mt-3 text-sm leading-tight text-center w-full">
                    {activeGameMode === 'normal' && (
                      <>
                        <p>Streak: {streak}</p>
                        <p>Best Streak: {bestStreak}</p>
                      </>
                    )}
                    <p>Best Normal: {hasMounted ? bestNormalScore : 0}</p>
                    <p>Best Timed: {hasMounted ? bestTimedScore : 0}</p>
                  </div>
                </div>
                <hr className="my-3 border-green-700" />
                <div className="flex-1 flex flex-col min-h-0 pb-2.5">
                  <h3 className="text-2xl font-semibold mb-1 text-center">Recent Answers</h3>
                  <div className="flex-1 overflow-y-auto pr-2 bg-gray-800 bg-opacity-50">
                    {activeGameMode === 'normal' ? (
                      <RecentAnswersList answers={recentAnswers} onSelect={handleRecentSelect} />
                    ) : (
                      <p className="text-gray-400 text-center mt-4">Recent answers disabled in Timed Mode.</p>
                    )}
                  </div>
                </div>
              </>
            ) : currentRoomGameData && currentRoomGameData.status === 'active' && activeRoomId && multiplayerPanelState === 'in_room' ? (
              // Multiplayer Active Game - Scores Panel
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-2 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                  Player Scores (Round {currentRoomGameData.current_round_number || 0})
                </h2>
                <div className="flex-1 overflow-y-auto space-y-1.5 pr-1 text-sm">
                  {(() => {
                    console.log('[PlayerScoresPanel] Generating display players from player_scores:', {
                      player_scores: currentRoomGameData.player_scores,
                      playersInRoom: playersInRoom.map(p => ({ user_id: p.user_id, username: p.profile?.username })),
                      currentUserId: user?.id,
                      hostId: currentRoomGameData.host_id
                    });

                    // Use player_scores as the authoritative source for who should be displayed
                    if (!currentRoomGameData.player_scores || Object.keys(currentRoomGameData.player_scores).length === 0) {
                      console.log('[PlayerScoresPanel] No player_scores found, falling back to playersInRoom');
                      if (playersInRoom.length === 0) {
                        return <p className="text-gray-400 italic text-center mt-4">Waiting for players...</p>;
                      }
                      // Fallback to playersInRoom if no scores yet
                      return playersInRoom.map((player, index) => (
                        <div key={player.user_id} className="flex justify-between items-center p-2 rounded bg-slate-800/70">
                          <span className="truncate">
                            {player.profile?.username || `User...${player.user_id.slice(-4)}`}
                            {player.user_id === user?.id && <span className="text-xs text-blue-400 ml-1">(You)</span>}
                            {player.user_id === currentRoomGameData.host_id && <span className="text-xs text-green-400 ml-1">(Host)</span>}
                          </span>
                          <span className="font-semibold text-yellow-400">0 pts</span>
                        </div>
                      ));
                    }

                    // Create display players from player_scores (authoritative source)
                    const displayPlayers = Object.keys(currentRoomGameData.player_scores).map(userId => {
                      const score = currentRoomGameData.player_scores![userId];
                      
                      // Try to get player info from playersInRoom first
                      const playerFromGamePlayers = playersInRoom.find(p => p.user_id === userId);
                      
                      // Determine username with fallback logic
                      let username = playerFromGamePlayers?.profile?.username;
                      if (!username && userId === user?.id && userProfile?.username) {
                        // Current user fallback - use their profile username
                        username = userProfile.username;
                        console.log('[PlayerScoresPanel] Using current user profile username for:', userId, username);
                      }
                      if (!username) {
                        username = `User...${userId.slice(-4)}`;
                      }

                      const isCurrentUser = userId === user?.id;
                      const isHost = userId === currentRoomGameData.host_id;
                      const isConnected = !!playerFromGamePlayers?.is_connected;

                      console.log('[PlayerScoresPanel] Processing player for display:', {
                        userId,
                        username,
                        score,
                        isCurrentUser,
                        isHost,
                        isConnected,
                        foundInPlayersInRoom: !!playerFromGamePlayers
                      });

                      return {
                        user_id: userId,
                        username,
                        score,
                        isCurrentUser,
                        isHost,
                        isConnected
                      };
                    });

                    console.log('[PlayerScoresPanel] Final display players list:', displayPlayers);

                    // Sort by score (descending), then by username (ascending)
                    const sortedPlayers = displayPlayers.sort((a, b) => {
                      if (b.score !== a.score) return b.score - a.score;
                      return a.username.localeCompare(b.username);
                    });

                    return sortedPlayers.map((player, index) => {
                      const rank = index + 1;
                      let trophy = '';
                      if (rank === 1) trophy = '🥇';
                      else if (rank === 2) trophy = '🥈';
                      else if (rank === 3) trophy = '🥉';

                      return (
                        <div
                          key={player.user_id}
                          className={cn(
                            "flex justify-between items-center p-2 rounded",
                            "bg-slate-800/70 hover:bg-slate-700/90 transition-colors",
                            !player.isConnected && "opacity-50 italic",
                            player.isCurrentUser && "ring-1 ring-yellow-500/50" // Highlight current user
                          )}
                        >
                          <span className="truncate flex items-center">
                            <span className="mr-2 w-5 text-center">{trophy || rank}</span>
                            {player.username}
                            {player.isCurrentUser && <span className="text-xs text-blue-400 ml-1">(You)</span>}
                            {player.isHost && <span className="text-xs text-green-400 ml-1">(Host)</span>}
                            {!player.isConnected && <span className="text-xs text-red-500 ml-1">(off)</span>}
                          </span>
                          <span className="font-semibold text-yellow-400">{player.score} pts</span>
                        </div>
                      );
                    });
                  })()}
                </div>
              </div>
            ) : (
              // Multiplayer Lobby - Leaderboards Panel (existing logic)
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">Leaderboards</h2>
                <div className="flex-1 overflow-y-auto space-y-3 pr-1 text-sm">
                  {/* Personal Records */}
                  <div className="mb-2">
                    <h3 className="text-lg font-semibold text-lime-300 mb-1">Personal Bests</h3>
                    {personalRecords.length > 0 ? personalRecords.map(pr => (
                      <div key={pr.username} className="flex justify-between text-xs bg-slate-800 px-2 py-1 rounded">
                        <span>{pr.username}</span>
                        <span className="font-semibold">{pr.score}</span>
                      </div>
                    )) : <p className="text-xs text-gray-400 italic">No records yet.</p>}
                  </div>

                  {/* Global Leaderboard */}
                  <div>
                    <button 
                      onClick={() => handleExpandLeaderboard("Global Top 5", globalLeaderboard)}
                      className="text-lg font-semibold text-lime-300 mb-1 w-full text-left hover:text-yellow-300 transition-colors"
                    >
                      Global Top 5 ❯
                    </button>
                    {globalLeaderboard.slice(0, 3).map(entry => (
                      <div key={entry.rank + entry.username} className="flex justify-between text-xs bg-slate-800/50 px-2 py-0.5 rounded mb-0.5">
                        <span>{entry.rank}. {entry.username}</span>
                        <span className="font-semibold">{entry.score}</span>
                      </div>
                    ))}
                  </div>

                  {/* Regional Leaderboards */}
                  {userRegion && (
                    <div>
                      <h3 className="text-lg font-semibold text-lime-300 mt-2 mb-1">Regional (Your: {userRegion})</h3>
                      {regionalLeaderboards.map(rl => (
                        <div key={rl.regionName} className="mb-1">
                          <button 
                            onClick={() => handleExpandLeaderboard(`${rl.regionName} Top 5`, rl.entries)}
                            className="text-sm font-medium text-sky-300 w-full text-left hover:text-sky-200 transition-colors"
                          >
                            {rl.regionName} {rl.regionName === userRegion ? '(Your Region)' : ''} ❯
                          </button>
                          {rl.entries.slice(0,2).map(entry => (
                            <div key={entry.rank + entry.username} className="flex justify-between text-xs bg-slate-800/30 px-2 py-0.5 rounded mb-0.5">
                              <span>{entry.rank}. {entry.username}</span>
                              <span className="font-semibold">{entry.score}</span>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Center Panel */}
          <div className="relative md:col-span-2 bg-green-800 bg-opacity-80 p-4 md:p-6 rounded-lg border-2 border-green-600 text-white flex flex-col items-center justify-center min-h-[600px] h-[600px] shadow-2xl">
            {/* Animation Components */}
            {selectedOverallGameType === 'single-player' && (
              <>
                <ScorePopup scoreChange={lastScoreChange} trigger={animationTrigger} />
                <FootballFx trigger={animationTrigger} streak={streak} />
                <TimeChangePopup
                  timeChange={lastTimeChange}
                  trigger={timeChangeAnimationTrigger}
                  scoreChanged={!!lastScoreChange && lastScoreChange > 0}
                />
              </>
            )}

            {selectedOverallGameType === 'single-player' ? (
              // Single Player Center Panel Content
              <>
                {isLoadingInitialGame ? (
                  <FootballLoader message={loadingMessage} />
                ) : isCountdownActive ? (
                  <div className="text-8xl font-archivo text-yellow-300" style={{textShadow: '3px 3px 5px rgba(0,0,0,0.7)'}}>
                    {countdownValue}
                  </div>
                ) : viewingRecentPlayer ? (
                  <>
                    <PlayerImageDisplay
                      imageUrl={viewingRecentPlayer?.local_image_path ? `/players_images/${viewingRecentPlayer.local_image_path}` : '/images/placeholder.jpg'}
                      altText={viewingRecentPlayer?.player_name || 'Recent Player'}
                    />
                    <h2 className="text-3xl font-bold my-4 text-center">{viewingRecentPlayer?.player_name}</h2>
                    <Button onClick={handleReturnToGame} className="mt-7 bg-sky-600 hover:bg-sky-700 text-white font-bold shadow-lg text-lg">
                      Return to Game
                    </Button>
                  </>
                ) : gameStatus === 'playing' && currentQuestion ? (
                  <>
                    {!isAnswered && (
                      <div className="mb-2 text-5xl font-archivo font-bold text-white tracking-wider" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.5)' }}>
                        ???
                      </div>
                    )}
                    <PlayerImageDisplay
                      imageUrl={currentQuestion?.imageUrl || '/images/placeholder.jpg'}
                      altText={(isAnswered && activeGameMode === 'normal' && currentQuestion?.correctPlayer) ? currentQuestion.correctPlayer.player_name : 'Guess the player'}
                    />
                    <div className="grid grid-cols-2 gap-5 w-full max-w-[440px] mt-4">
                      {currentQuestion?.choices.map((choice, index) => {
                        const choiceKey = `${currentQuestion?.correctPlayer?.id || 'unknown'}-${choice.name}-${index}`;
                        const wasChosen = isAnswered && userChoiceName === choice.name;

                        return (
                          <ChoiceButton
                            key={choiceKey}
                            choiceText={choice.name}
                            onClick={() => submitAnswer(choice.name)}
                            disabled={isAnswered}
                            isCorrect={(isAnswered && activeGameMode === 'normal') ? choice.isCorrect : null}
                            wasChosen={wasChosen}
                          />
                        );
                      })}
                    </div>
                    {isAnswered && activeGameMode === 'normal' && (
                      <Button onClick={handleNextQuestionClick} className="mt-7 bg-yellow-500 hover:bg-yellow-600 text-black font-bold shadow-lg text-lg">
                        Next Question
                      </Button>
                    )}
                  </>
                ) : gameStatus === 'finished' && (
                  <div className="flex flex-col items-center justify-center h-full">
                    <h2 className="text-4xl font-bold mb-2 text-yellow-300">Game Over!</h2>
                    <p className="text-2xl mb-4">Your {activeGameMode === 'timed' ? 'Timed Mode' : ''} Score: {score}</p>
                    {activeGameMode === 'timed' && <p className="text-lg mb-4">Best Timed: {bestTimedScore}</p>}
                    {activeGameMode === 'normal' && <p className="text-lg mb-4">Best Normal: {bestNormalScore}</p>}
                    <Button onClick={handleGameResetClick} className="bg-lime-600 hover:bg-lime-700 text-black font-bold shadow-lg text-lg px-6 py-3">
                      Play {activeGameMode === 'timed' ? 'Timed Mode' : 'Normal Mode'} Again
                    </Button>
                  </div>
                )}
              </>
            ) : centerPanelMpState === 'expanded_leaderboard' && expandedLeaderboardData ? (
              // Expanded Leaderboard View
              <div className="w-full h-full flex flex-col p-2">
                <div className="flex justify-between items-center mb-3">
                  <h2 className="text-3xl font-bold text-yellow-300">{expandedLeaderboardData?.title || 'Leaderboard'}</h2>
                  <Button 
                    onClick={() => { setExpandedLeaderboardData(null); setCenterPanelMpState('lobby_list_detail');}} 
                    className="bg-slate-700 hover:bg-slate-600 text-xs py-1 px-2"
                  >
                    Back to Lobby
                  </Button>
                </div>
                <div className="flex-1 overflow-y-auto bg-slate-900/50 p-3 rounded-md">
                  {expandedLeaderboardData?.entries.map(entry => (
                    <div key={entry.rank + entry.username} className="flex justify-between items-center p-2 border-b border-slate-700 hover:bg-slate-700/50 rounded">
                      <span className="text-lg">{entry.rank}. {entry.username}</span>
                      <span className="text-xl font-semibold text-yellow-400">{entry.score}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) : centerPanelMpState === 'lobby_list_detail' ? (
              <div className="w-full h-full flex flex-col items-center justify-start p-2">
                <div className="flex justify-between items-center mb-2 w-full">
                  <h2 className="text-3xl font-bold text-yellow-300">
                    {selectedRoomForDetail ? `Room: ${selectedRoomForDetail.title || ('...' + selectedRoomForDetail.id.slice(-6))}` : "Game Lobby"}
                  </h2>
                  {!selectedRoomForDetail && (
                    <Button
                      onClick={async () => {
                        console.log("[Client] Refresh List button clicked.");
                        await fetchAndSetGameRooms();
                      }}
                      disabled={isLoadingRooms}
                      className="px-3 py-1 text-xs bg-sky-600 hover:bg-sky-700 text-white rounded-md shadow-md"
                    >
                      {isLoadingRooms ? 'Refreshing...' : 'Refresh List'}
                    </Button>
                  )}
                </div>
                {errorMp && !selectedRoomForDetail && (
                  <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-3 text-center w-full">{errorMp}</p>
                )}
                
                {lobbyFetchError && !selectedRoomForDetail && !isLoadingRooms && (
                  <div className="text-center text-red-400 bg-red-900/50 p-3 rounded text-xs mb-3 w-full">
                    <p>Could not load games. Please try again.</p>
                    <Button
                      onClick={async () => {
                        console.log("[Client] Retry fetch button clicked after error.");
                        await fetchAndSetGameRooms();
                      }}
                      className="mt-2 px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded-md"
                    >
                      Retry
                    </Button>
                  </div>
                )}

                {selectedRoomForDetail ? (
                  <div className="w-full bg-slate-800/60 p-4 rounded-lg shadow-lg text-sm">
                    <p><span className="font-semibold text-lime-300">Mode:</span> <span className="capitalize">{selectedRoomForDetail.multiplayer_mode?.replace('_',' ')}</span></p>
                    <p><span className="font-semibold text-lime-300">Host:</span> {selectedRoomForDetail.profiles?.username || 'Unknown Host'}</p>
                    <p><span className="font-semibold text-lime-300">Status:</span> <span className="capitalize">{selectedRoomForDetail.status}</span></p>
                    <p>
                      <span className="font-semibold text-lime-300">Players:</span> {selectedRoomForDetail.connected_players ?? 0} / {selectedRoomForDetail.max_players ?? 8}
                    </p>
                    <p className="mt-1"><span className="font-semibold text-lime-300">Code:</span> {selectedRoomForDetail.room_code || 'N/A'}</p>
                    
                    {user ? (() => {
                      // Find if the current logged-in user has an entry in the game_players list *of the room being detailed*.
                      const playerEntry = selectedRoomForDetail.game_players?.find(
                        (gp) => gp.user_id === user.id
                      );
                      
                      console.log('[LobbyDetail] Room detail view state check:', {
                        roomId: selectedRoomForDetail.id,
                        roomStatus: selectedRoomForDetail.status,
                        userId: user.id,
                        activeRoomId,
                        hasPlayerEntry: !!playerEntry,
                        playerEntryConnected: playerEntry?.is_connected,
                        isViewingActiveRoom: activeRoomId === selectedRoomForDetail.id,
                        isCurrentUserHost: selectedRoomForDetail.host_id === user.id,
                        isClientSideConnectedToThisRoom: activeRoomId === selectedRoomForDetail.id,
                        scenarioDetected: selectedRoomForDetail.status === 'active' && playerEntry?.is_connected && activeRoomId !== selectedRoomForDetail.id ? 'HOST_REJOIN_AFTER_LOGOUT' : 'OTHER'
                      });

                      if (selectedRoomForDetail.status === 'waiting') {
                        // Room is WAITING
                        if (playerEntry) {
                          // User has an entry in this waiting room
                          if (playerEntry.is_connected) {
                            // User is already connected to this waiting room (should ideally not happen if they are viewing details, but defensive)
                            return <p className="text-center text-gray-400 mt-3">You are already in this waiting room.</p>;
                          } else {
                            // User has an entry but is disconnected -> "Rejoin Waiting Room"
                            return (
                              <Button
                                onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                                disabled={isJoiningOrRejoiningRoom}
                                className="w-full mt-3 bg-orange-500 hover:bg-orange-600 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed" // Distinct color for rejoin waiting
                              >
                                {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Waiting Room"}
                              </Button>
                            );
                          }
                        } else {
                          // User has no entry in this waiting room -> "Join This Room"
                          const isFull = selectedRoomForDetail.connected_players != null &&
                                        selectedRoomForDetail.max_players != null &&
                                        selectedRoomForDetail.connected_players >= selectedRoomForDetail.max_players;
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              className="w-full mt-3 bg-blue-600 hover:bg-blue-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                              disabled={isFull || isJoiningOrRejoiningRoom}
                            >
                              {isJoiningOrRejoiningRoom ? "Joining..." : isFull ? "Room Full" : "Join This Room"}
                            </Button>
                          );
                        }
                      } else if (selectedRoomForDetail.status === 'active') {
                        // Room is ACTIVE
                        const isCurrentUserHost = selectedRoomForDetail.host_id === user.id;
                        const isClientSideConnectedToThisRoom = activeRoomId === selectedRoomForDetail.id;
                        
                        if (isClientSideConnectedToThisRoom) {
                          // Client THINKS it's in this room. Should usually mean game view is shown, not lobby detail.
                          // This case might be for when navigating back to lobby view while still technically "in" the game.
                          console.log('[LobbyDetail] Client is already actively connected to this room. Showing "You are in this game" or similar.');
                          return <p className="text-center text-gray-400 mt-3">You are currently in this game session.</p>;
                        } else if (playerEntry && !playerEntry.is_connected) {
                          // User was part of this active game and is disconnected -> "Rejoin Active Game"
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-green-600 hover:bg-green-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Active Game"}
                            </Button>
                          );
                        } else if (playerEntry && playerEntry.is_connected && !isClientSideConnectedToThisRoom) {
                          // User is part of this active game, DB shows connected, BUT client is NOT actively connected
                          // This happens after logout/login - need to re-establish client connection
                          const buttonText = isCurrentUserHost ? "Re-enter Your Game" : "Rejoin Active Game";
                          const buttonColor = isCurrentUserHost ? "bg-blue-600 hover:bg-blue-700" : "bg-green-600 hover:bg-green-700";
                          
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className={`w-full mt-3 ${buttonColor} py-1.5 disabled:opacity-50 disabled:cursor-not-allowed`}
                            >
                              {isJoiningOrRejoiningRoom ? (isCurrentUserHost ? "Re-entering..." : "Rejoining...") : buttonText}
                            </Button>
                          );
                        } else if (!playerEntry && isCurrentUserHost) {
                          // CRITICAL FIX: Host is viewing their own active game but has NO player entry
                          // This happens when host used "Leave Game" button and leave-room-handler deleted their record
                          // Host should be able to re-enter their own game
                          console.log('[LobbyDetail] Host is viewing their active game with no player entry. Offering RE-ENTER option.');
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-blue-600 hover:bg-blue-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Re-entering..." : "Re-enter Your Game"}
                            </Button>
                          );
                        } else {
                          // Game is active, and user was NOT part of it (or no entry) AND is not the host
                          console.log('[LobbyDetail] Non-host viewing an active game with no player entry. Showing "Game in progress."');
                          return <p className="text-center text-yellow-400 mt-3">Game in progress. Cannot join.</p>;
                        }
                      } else if (selectedRoomForDetail.status === 'finished') {
                        // Room is FINISHED
                        return <p className="text-center text-gray-400 mt-3">This game has finished.</p>;
                      }

                      return null; // Default: no button if other conditions aren't met
                    })() : null}
                    
                    {/* "Back to List" Button - always shown when selectedRoomForDetail is active */}
                    <Button 
                      onClick={() => {
                        console.log('[BackToList] Navigating back to lobby list from room detail view');
                        setSelectedRoomForDetail(null);
                        setMultiplayerPanelState('lobby_list');
                        setCenterPanelMpState('lobby_list_detail');
                      }} 
                      className="w-full mt-2 bg-gray-600 hover:bg-gray-500 text-xs py-1"
                    >
                      Back to List
                    </Button>
                  </div>
                ) : (
                  <div className="w-full flex-1 overflow-y-auto space-y-2 pr-1">
                    {(() => {
                      console.log("[RoomList Render Attempt] isLoadingRooms:", isLoadingRooms, "gameRooms:", gameRooms, "gameRooms.length:", gameRooms?.length);
                      return null;
                    })()}

                    {isLoadingRooms && (
                      <p className="text-center text-gray-300 pt-5">Loading available games...</p>
                    )}
                    
                    {!isLoadingRooms && gameRooms && gameRooms.length === 0 && (
                      <p className="text-center text-gray-300 pt-5">No public games available. Create one!</p>
                    )}

                    {!isLoadingRooms && gameRooms && gameRooms.length > 0 && (
                      gameRooms.map(room => (
                        <div
                          key={room.id}
                          className="bg-slate-800 p-3 rounded-lg shadow-lg border-2 border-transparent hover:border-yellow-500 cursor-pointer transition-all"
                          onClick={() => handleViewLobbyDetail(room)}
                          onDoubleClick={() => user ? handleJoinRoom(room.id) : null}
                        >
                          <div className="flex justify-between items-center mb-1">
                            <h4 className="text-md font-bold text-yellow-400 truncate">
                              {room.title || `Room ${room.id.slice(-6)}`}
                            </h4>
                            <span className="text-xs bg-sky-600 px-1.5 py-0.5 rounded capitalize">
                              {room.multiplayer_mode === 'competitive' ? (
                                <span className="text-xs text-yellow-400">Competitive</span>
                              ) : (
                                <span className="text-xs text-blue-400">Cooperative</span>
                              )}
                            </span>
                          </div>
                          <p className="text-xs text-gray-300 truncate">
                            Host: {room.profiles?.username || 'Unknown'} • Players: {room.connected_players ?? 0}/{room.max_players ?? 8}
                          </p>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>
            ) : centerPanelMpState === 'mp_game_active' && activeRoomId ? (
              // Active Multiplayer Game
              <div>
                {(() => {
                  // First, ensure all necessary data exists and has the correct type
                  if (
                    currentRoomGameData &&
                    currentRoomGameData.status === 'active' &&
                    currentRoomGameData.current_question_data &&
                    typeof currentRoomGameData.current_question_data.questionId === 'string' &&
                    Array.isArray(currentRoomGameData.current_question_data.choices)
                  ) {
                    // If all checks pass, we can safely access the properties
                    const questionData = currentRoomGameData.current_question_data;
                    const roundNumber = currentRoomGameData.current_round_number || 0;

                    return (
                      <div className="flex flex-col items-center">
                        <h3 className="text-xl mb-2">Round: {roundNumber}</h3>
                        <PlayerImageDisplay
                          imageUrl={questionData.imageUrl || '/images/placeholder.jpg'}
                          altText="Guess the player"
                        />
                        <div className="grid grid-cols-2 gap-5 w-full max-w-[440px] mt-4">
                          {questionData.choices.map((choice: PlayerChoice, index: number) => (
                            <ChoiceButton
                              key={`${questionData.questionId}-${index}`}
                              choiceText={choice.name}
                              onClick={() => handleMultiplayerAnswerSubmit(choice.name)}
                            />
                          ))}
                        </div>
                        <Button
                          onClick={handleLeaveRoom}
                          className="mt-6 bg-red-600 hover:bg-red-700"
                        >
                          Leave Game
                        </Button>
                      </div>
                    );
                  } else if (currentRoomGameData?.status === 'waiting') {
                    return (
                      <div className="text-center">
                        <h2 className="text-3xl font-bold text-yellow-300">Waiting for game to start...</h2>
                        <p className="mt-2">Room: {currentRoomGameData.title || `...${activeRoomId?.slice(-6)}`}</p>
                        <Button
                          onClick={handleLeaveRoom}
                          className="mt-6 bg-red-600 hover:bg-red-700"
                        >
                          Leave Game
                        </Button>
                      </div>
                    );
                  } else {
                    return (
                      <div className="text-center">
                        <h2 className="text-4xl font-bold text-yellow-300">Game Lobby Active</h2>
                        <p className="text-xl mt-4">Waiting for game data or game has ended.</p>
                        <Button
                          onClick={handleLeaveRoom}
                          className="mt-6 bg-red-600 hover:bg-red-700"
                        >
                          Leave Game
                        </Button>
                      </div>
                    );
                  }
                })()}
              </div>
            ) : (
              <div className="text-gray-400">Error: Invalid multiplayer state.</div>
            )}
          </div>

          {/* Right Panel */}
          <div className="md:col-span-1 bg-green-900 bg-opacity-75 pt-5 px-3 pb-3 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col">
            {selectedOverallGameType === 'single-player' ? (
              <PlayerInfoPanel player={playerToShow} />
            ) : multiplayerPanelState === 'lobby_list' ? (
              // Lobby Actions / Create Room
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">Lobby Actions</h2>
                {errorMp && centerPanelMpState === 'lobby_list_detail' && !selectedRoomForDetail && (
                  <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-2 text-center">{errorMp}</p>
                )}

                <div className="p-3 bg-slate-800/50 rounded-md border border-slate-700 mb-4">
                  <h3 className="text-lg font-semibold mb-2 text-center text-lime-300">Create New Game</h3>
                  
                  {/* Game Mode Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Game Mode:</p>
                    <div className="flex justify-around space-x-2">
                      <div>
                        <input 
                          type="radio" 
                          id="competitive_lobby" 
                          name="mpModeLobby" 
                          value="competitive" 
                          checked={newRoomMode === 'competitive'} 
                          onChange={() => setNewRoomMode('competitive')} 
                          className="mr-1 accent-yellow-400"
                        />
                        <label htmlFor="competitive_lobby" className="text-gray-200 text-xs cursor-pointer">Competitive</label>
                      </div>
                      <div>
                        <input 
                          type="radio" 
                          id="cooperative_lobby" 
                          name="mpModeLobby" 
                          value="cooperative" 
                          checked={newRoomMode === 'cooperative'} 
                          onChange={() => setNewRoomMode('cooperative')} 
                          className="mr-1 accent-yellow-400"
                        />
                        <label htmlFor="cooperative_lobby" className="text-gray-200 text-xs cursor-pointer">Cooperative</label>
                      </div>
                    </div>
                  </div>

                  {/* Game Duration Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Game Duration:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {[30, 60, 90, 120].map((duration) => (
                        <div key={duration} className="flex items-center">
                          <input
                            type="radio"
                            id={`duration_${duration}`}
                            name="gameDuration"
                            value={duration}
                            checked={selectedGameDuration === duration}
                            onChange={() => setSelectedGameDuration(duration as GameDuration)}
                            className="mr-1 accent-yellow-400"
                          />
                          <label htmlFor={`duration_${duration}`} className="text-gray-200 text-xs cursor-pointer">
                            {duration} seconds
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Max Players Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Max Players:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {[2, 4, 6, 8].map((max) => (
                        <div key={max} className="flex items-center">
                          <input
                            type="radio"
                            id={`max_players_${max}`}
                            name="maxPlayers"
                            value={max}
                            checked={selectedMaxPlayers === max}
                            onChange={() => setSelectedMaxPlayers(max as MaxPlayers)}
                            className="mr-1 accent-yellow-400"
                          />
                          <label htmlFor={`max_players_${max}`} className="text-gray-200 text-xs cursor-pointer">
                            {max} Players
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Room Title (Optional) */}
                  <div className="mb-3 text-sm">
                    <label htmlFor="roomTitle" className="block text-xs font-medium text-gray-300 mb-0.5">Room Name (optional):</label>
                    <p className="text-xs text-gray-400 mt-0.5">Default: {userProfile?.username || 'Your'}'s Game</p>
                  </div>

                  {/* Create Room Button */}
                  <Button 
                    onClick={handleCreateRoom} 
                    disabled={!user || isLoadingRooms || isCreatingRoom} 
                    className={cn(
                      "w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-1.5 text-sm rounded shadow-md",
                      "transition-all duration-200",
                      isCreatingRoom && "opacity-75 cursor-not-allowed"
                    )}
                  >
                    {isCreatingRoom ? "Creating..." : isLoadingRooms ? "..." : "Host Game"}
                  </Button>
                </div>

                <p className="text-xs text-center text-gray-400 mb-3">
                  View available games in the center panel. Click to see details, double-click to join.
                </p>
                <div className="mt-auto border-t border-slate-700 pt-2">
                  <p className="text-xs text-gray-500 text-center">Tip: Click a room in the center to see details, then join.</p>
                </div>
              </div>
            ) : multiplayerPanelState === 'in_room' && activeRoomId ? (
              // In-Room Info / Player List OR Round Submissions
              <div className="flex flex-col h-full">
                {/* Conditional rendering based on game status */}
                {currentRoomGameData?.status === 'waiting' ? (
                  <>
                    {/* ----- WAITING STATE UI ----- */}
                    <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                      Room: {currentRoomGameData.title || `...${activeRoomId.slice(-6)}`}
                    </h2>
                    {errorMp && (
                      <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-2 text-center">{errorMp}</p>
                    )}
                    <div className="flex-1 bg-slate-800/50 p-2 rounded mb-2 overflow-y-auto">
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm text-lime-300">Players ({playersInRoom.length}/{currentRoomGameData.max_players || 8}):</p>
                        {/* Show "Ready Up" button only if game is waiting */}
                        {user && playersInRoom.some(p => p.user_id === user.id) && (
                          <Button
                            onClick={handleToggleReady}
                            disabled={isSubmittingReady}
                            className={cn(
                              "text-xs px-2 py-1 transition-colors",
                              isSubmittingReady 
                                ? "bg-gray-400 cursor-not-allowed" 
                                : playersInRoom.find(p => p.user_id === user.id)?.is_ready
                                  ? "bg-green-600 hover:bg-green-700"
                                  : "bg-yellow-600 hover:bg-yellow-700"
                            )}
                          >
                            {isSubmittingReady 
                              ? "Processing..." 
                              : playersInRoom.find(p => p.user_id === user.id)?.is_ready 
                                ? "Ready ✓" 
                                : "Ready Up"}
                          </Button>
                        )}
                      </div>
                      {isLoadingPlayers ? (
                        <p className="text-xs text-gray-400 italic">Loading players...</p>
                      ) : playersInRoom.length === 0 ? (
                        <p className="text-xs text-gray-400 italic">Waiting for players...</p>
                      ) : (
                        playersInRoom.map(player => (
                          <div 
                            key={player.user_id} 
                            className={cn(
                              "text-xs p-1.5 rounded mb-1 flex justify-between items-center",
                              "bg-slate-700/50 hover:bg-slate-700/70 transition-colors",
                              player.user_id === user?.id && "bg-slate-600/70" // Highlight current user
                            )}
                          >
                            <span className="truncate">
                              {player.profile?.username || `Player...${player.user_id.slice(-4)}`}
                              {player.user_id === user?.id && " (You)"}
                              {currentRoomGameData?.host_id === player.user_id && " (Host)"}
                            </span>
                            {player.is_ready === true && (
                              <span className="text-green-400 ml-2">✓</span>
                            )}
                          </div>
                        ))
                      )}
                    </div>

                    {/* Show "Start Game" button only if user is host and game is waiting */}
                    {user && currentRoomGameData?.host_id === user.id && (
                      <Button
                        onClick={handleStartGame}
                        disabled={
                          isStartingGame || 
                          !playersInRoom.every(p => p.is_ready) || 
                          playersInRoom.length < 2 // Assuming min 2 players to start
                        }
                        className={cn(
                          "w-full mt-2 mb-2 py-2 text-lg font-bold",
                          "bg-green-600 hover:bg-green-700 text-white",
                          (isStartingGame || !playersInRoom.every(p => p.is_ready) || playersInRoom.length < 2) && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        {isStartingGame 
                          ? "Starting Game..."
                          : playersInRoom.length < 2 
                            ? `Need ${2 - playersInRoom.length} more player(s)`
                            : !playersInRoom.every(p => p.is_ready)
                              ? "Waiting for all to ready..."
                              : "Start Game"}
                      </Button>
                    )}
                    <div className="mt-auto border-t border-slate-700 pt-2">
                      <p className="text-xs text-gray-500 text-center">
                        {user && currentRoomGameData?.host_id === user.id
                          ? (playersInRoom.length < 2 ? `Need ${2 - playersInRoom.length} more players to start.` : !playersInRoom.every(p => p.is_ready) ? "Waiting for players to ready up." : "Ready to start game!")
                          : "Waiting for host to start..."}
                      </p>
                    </div>
                  </>
                ) : currentRoomGameData?.status === 'active' ? (
                  <>
                    {/* ----- ACTIVE GAME STATE UI (Round Submissions Only) ----- */}
                    <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                      Round Submissions
                    </h2>
                    <div className="flex-1 space-y-1.5 max-h-[calc(100%-60px)] overflow-y-auto pr-1">
                      {Array.isArray(currentRoomGameData.current_round_answers) && currentRoomGameData.current_round_answers.length > 0 && (
                        currentRoomGameData.current_round_answers
                          .slice() 
                          .sort((a, b) => a.timestamp - b.timestamp) 
                          .map((answer) => {
                            const player = playersInRoom.find(p => p.user_id === answer.userId);
                            
                            // Improved player name lookup with fallbacks
                            let playerName = player?.profile?.username;
                            if (!playerName && answer.userId === user?.id && userProfile?.username) {
                              // Current user fallback - use their profile username
                              playerName = userProfile.username;
                            }
                            if (!playerName) {
                              playerName = `User...${answer.userId.slice(-4)}`;
                            }
                            
                            console.log('[RoundSubmissions] Processing answer display:', {
                              userId: answer.userId,
                              playerName,
                              foundInPlayersInRoom: !!player,
                              isCurrentUser: answer.userId === user?.id,
                              choiceName: answer.choiceName,
                              isCorrect: answer.isCorrect
                            });
                            
                            const isNewCorrectAnswerForAnimation = 
                              answer.isCorrect && 
                              currentRoomGameData.current_question_data && 
                              animatedAnswers[currentRoomGameData.current_question_data.questionId]?.has(answer.userId);

                            return (
                              <div
                                key={answer.userId + '-' + answer.timestamp}
                                className={cn(
                                  "text-xs p-1.5 rounded relative flex justify-center items-center", 
                                  answer.isCorrect
                                    ? "bg-green-600/70 border border-green-400 shadow-[0_0_8px_1px_rgba(77,255,77,0.5)]" 
                                    : "bg-red-600/70 border border-red-400" 
                                )}
                              >
                                <span className="truncate font-medium text-white">{playerName}</span>
                                {isNewCorrectAnswerForAnimation && (
                                  <motion.span
                                    key={`popup-${answer.userId}-${answer.timestamp}`} 
                                    initial={{ opacity: 1, y: 0, scale: 0.7, x: "-50%" }}
                                    animate={{ opacity: 0, y: -25, scale: 1.1 }} 
                                    transition={{ duration: 0.8, ease: "easeOut" }}
                                    className="absolute -top-1 left-1/2 text-yellow-300 font-bold text-sm whitespace-nowrap"
                                    style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.7)' }}
                                  >
                                    +10 🏈
                                  </motion.span>
                                )}
                              </div>
                            );
                          })
                      )}
                    </div>
                    <div className="mt-auto border-t border-slate-700 pt-2">
                      <p className="text-xs text-gray-500 text-center">Game in Progress! Answer quickly!</p>
                    </div>
                  </>
                ) : (
                  // Fallback for 'finished' or other states
                  <div className="flex-1 flex items-center justify-center">
                    <p className="text-gray-400">
                      {currentRoomGameData?.status === 'finished' ? 'Game Finished!' : 'Loading room state...'}
                    </p>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </main>
  );
}

// New default export that wraps HomePageContent with Suspense
export default function HomePage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center text-white bg-gray-900">Loading page...</div>}>
      <HomePageContent />
    </Suspense>
  );
}

// Add animation keyframes to the global styles
const globalStyles = `
  @keyframes slide-in {
    0% {
      transform: translateX(100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out forwards;
  }
`;

// Add the styles to the document
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = globalStyles;
  document.head.appendChild(styleElement);
}

```

---


## Data and Asset Files

### Game Data: public/data/players_game_data.json

**Description:** Main game data file containing all player information used for questions.

**Key Features:**
- Player profiles with images
- Team information

**File Size:** 32866 lines (large data file)

**Data Structure Sample:**
```json
[
    {
        "id": 1,
        "team_name": "Arizona Cardinals",
```

**Data Summary:**
- Total players: 2528
- Sample player structure:
```json
**Description:** Directory containing all player images referenced in the game data.

**Key Features:**
- Player headshot images
- Optimized for web display
- Referenced by local_image_path in JSON

## Styling Files

### Styling: src/app/globals.css

**Description:** Global CSS including football animation keyframes and game-specific styles.

**Key Features:**
- Football bounce animation keyframes
- Loading spinner styles
- Game container styles
- Button focus overrides
- Responsive design utilities

**File Size:** 217 lines

**File Content:**
```typescript
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-gray-900; /* Fallback color if image fails to load */
    background-image: url('/images/field_background.png');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
  }

  button, 
  [type='button'], 
  [type='reset'], 
  [type='submit'] {
    -webkit-appearance: button; /* Modern reset */
    background-color: transparent; /* Reset background */
    background-image: none; /* Reset background image */
    outline: none !important; /* FORCE no outline */
    box-shadow: none !important; /* FORCE no box-shadow by default */
  }

  button:focus,
  [type='button']:focus,
  [type='reset']:focus,
  [type='submit']:focus,
  button:focus-visible,
  [type='button']:focus-visible,
  [type='reset']:focus-visible,
  [type='submit']:focus-visible {
    outline: none !important; /* FORCE no outline on focus too */
    box-shadow: none !important; /* FORCE no box-shadow on focus too */
  }
}

@layer utilities {
  .jumbotron-title {
    font-family: var(--font-archivo-black);
    color: white;
    font-size: 3.5rem;
    font-weight: 800;
    -webkit-text-stroke-width: 2.5px;
    -webkit-text-stroke-color: #002800;
    text-shadow:
      4px 4px 0px #001a00,
      0px 0px 18px rgba(173, 255, 47, 0.35);
    letter-spacing: 0.045em;
  }
}

.game-loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 20px;
  box-sizing: border-box;
  text-align: center;
  color: white;
}

.loading-text {
  font-size: 1.8em;
  font-weight: bold;
  margin-bottom: 20px;
  color: #FFFFFF;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.football-spinner {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.football-spinner span {
  font-size: 3em;
  display: inline-block;
  animation: footballBounce 1.5s infinite ease-in-out;
}

.football-spinner span:nth-child(1) { animation-delay: 0s; }
.football-spinner span:nth-child(2) { animation-delay: 0.15s; }
.football-spinner span:nth-child(3) { animation-delay: 0.3s; }
.football-spinner span:nth-child(4) { animation-delay: 0.45s; }
.football-spinner span:nth-child(5) { animation-delay: 0.6s; }

@keyframes footballBounce {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15px) rotate(-15deg);
    opacity: 1;
  }
  50% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  75% {
    transform: translateY(-10px) rotate(15deg);
    opacity: 0.9;
  }
}

.loading-subtext {
  font-size: 1em;
  color: #DDDDDD;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}
```

---


## Implementation Notes for Multiplayer

### Football Emoji Celebration System
The football celebration animation system consists of:

1. **FootballFx.tsx** - Main celebration component
   - Triggered by animationTrigger prop
   - Number of footballs based on streak count
   - Uses Framer Motion for smooth animations
   - Can be easily integrated into multiplayer by passing trigger and streak values

2. **Animation Integration** - In main page component
   `	ypescript
   <FootballFx trigger={animationTrigger} streak={streak} />
   `

3. **CSS Animations** - In globals.css
   - footballBounce keyframes for loading animation
   - Staggered timing for multiple footballs
   - GPU-optimized transforms

### Key Integration Points for Multiplayer

1. **Data Source** - Use same players_game_data.json and players_images/ directory
2. **Question Generation** - Reuse generateQuestion() function from playerData.ts
3. **Animation Triggers** - Implement similar trigger system for celebrations
4. **Image Loading** - Use same PlayerImageDisplay component
5. **Choice Buttons** - Reuse ChoiceButton component with multiplayer answer handling

### Recommended Replication Strategy

1. **Copy Animation Components** - FootballFx, ScorePopup, TimeChangePopup
2. **Reuse Data Layer** - playerData.ts functions and JSON data
3. **Adapt UI Components** - Modify existing components for multiplayer context
4. **Implement Trigger System** - Create multiplayer-specific animation triggers
5. **Maintain Styling** - Use same CSS classes and animations

## File Summary

**Total Files Documented:** 16

**Categories:**
- Core Game Logic: 3 files
- UI Components: 5 files  
- Animation System: 4 files
- Main Application: 1 files
- Data & Assets: 2 files
- Styling: 1 files

This documentation provides a complete reference for implementing similar functionality in the multiplayer experience while maintaining consistency with the singleplayer game.
