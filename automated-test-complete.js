/**
 * Complete Automated Multiplayer Test
 * Final version with proper room joining
 */

const puppeteer = require('puppeteer');

// Test configuration
const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'P1 only → 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1@1s, P2@3s → 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// Monitoring code
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0
  };
  
  console.log('🔍 Monitor active');
  
  let lastQuestion = null;
  setInterval(() => {
    const questionEl = document.querySelector('h2');
    const question = questionEl?.textContent || '';
    
    if (question && question.includes('NFL PLAYER?') && question !== lastQuestion) {
      const now = Date.now();
      
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('🎮 Game started');
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({
          round: window.testData.currentRound++,
          time: elapsed
        });
        console.log('📍 Round ' + (window.testData.currentRound) + ' at ' + elapsed + 'ms');
      }
      
      lastQuestion = question;
    }
  }, 100);
  
  window.submitAt = function(targetMs) {
    return new Promise((resolve) => {
      const trySubmit = () => {
        if (!window.testData.gameStartTime) {
          setTimeout(trySubmit, 100);
          return;
        }
        
        const elapsed = Date.now() - window.testData.gameStartTime;
        const delay = targetMs - elapsed;
        
        const submit = () => {
          const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
            const text = b.textContent || '';
            // Player name buttons have specific characteristics
            return text.length > 0 && 
                   text.length < 30 &&
                   !['Ready', 'Start', 'Mode', 'Sign', 'Create', 'Join', 'Leave', 'Host'].some(word => text.includes(word)) &&
                   b.offsetParent !== null &&
                   !b.disabled;
          });
          
          if (buttons.length >= 4) {
            const chosen = buttons[Math.floor(Math.random() * 4)];
            console.log('✅ Submit: ' + chosen.textContent);
            chosen.click();
            resolve(true);
          } else {
            setTimeout(submit, 100);
          }
        };
        
        if (delay > 0) {
          setTimeout(submit, delay);
        } else {
          submit();
        }
      };
      
      trySubmit();
    });
  };
`;

async function runTest() {
  console.log('🎯 Complete Automated Multiplayer Test');
  console.log('=====================================\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Launch browsers
    console.log('🚀 Launching browsers...');
    const browserOptions = {
      headless: false,
      executablePath: '/usr/bin/chromium-browser',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--window-size=1200,900'
      ],
      protocolTimeout: 60000 // Increase timeout
    };
    
    browser1 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=0,0']
    });
    
    browser2 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=600,0']
    });
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    page1.setDefaultTimeout(30000);
    page2.setDefaultTimeout(30000);
    
    // Load game
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2' }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2' })
    ]);
    await delay(3000);
    
    // Inject monitoring
    await page1.evaluate(monitoringCode);
    await page2.evaluate(monitoringCode);
    
    // Sign in Player 1
    console.log('\n🔐 Signing in players...');
    await page1.evaluate(() => {
      document.querySelector('button[onclick*="Login"]')?.click() ||
      Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Login'))?.click();
    });
    await delay(2000);
    await page1.type('input[placeholder*="Username"]', CONFIG.player1.email);
    await page1.type('input[placeholder*="Password"]', CONFIG.player1.password);
    await page1.evaluate(() => document.querySelector('form').requestSubmit());
    await delay(5000);
    
    // Sign in Player 2
    await page2.evaluate(() => {
      document.querySelector('button[onclick*="Login"]')?.click() ||
      Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Login'))?.click();
    });
    await delay(2000);
    await page2.type('input[placeholder*="Username"]', CONFIG.player2.email);
    await page2.type('input[placeholder*="Password"]', CONFIG.player2.password);
    await page2.evaluate(() => document.querySelector('form').requestSubmit());
    await delay(5000);
    console.log('✅ Both players signed in');
    
    // Navigate to multiplayer
    console.log('\n🎮 Entering Multiplayer Mode...');
    await page1.evaluate(() => {
      Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Multiplayer Mode')?.click();
    });
    await delay(3000);
    
    await page2.evaluate(() => {
      Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Multiplayer Mode')?.click();
    });
    await delay(3000);
    
    // Create room
    console.log('\n🏠 Creating room...');
    await page1.evaluate(() => {
      Array.from(document.querySelectorAll('button')).find(b => b.textContent.trim() === 'Host Game')?.click();
    });
    await delay(3000);
    console.log('✅ Room created');
    
    // Join room - click on the room in the lobby
    console.log('\n🏠 Joining room...');
    const joined = await page2.evaluate(() => {
      // Find and click on fresh's Game in the lobby
      const roomEl = Array.from(document.querySelectorAll('*')).find(el => 
        el.textContent && el.textContent.includes("fresh's Game") && 
        el.textContent.includes("Players:") &&
        el.tagName !== 'BODY' && el.tagName !== 'HTML'
      );
      if (roomEl) {
        roomEl.click();
        return true;
      }
      
      // Alternative: click on the room box
      const boxes = Array.from(document.querySelectorAll('div[class*="room"], div[class*="game-item"], button'));
      for (const box of boxes) {
        if (box.textContent && box.textContent.includes("fresh's Game")) {
          box.click();
          return true;
        }
      }
      return false;
    });
    
    if (!joined) {
      throw new Error('Failed to join room');
    }
    
    await delay(3000);
    console.log('✅ Player 2 joined room');
    
    // Ready up
    console.log('\n🎮 Getting ready...');
    await delay(1000);
    
    await Promise.all([
      page1.evaluate(() => {
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent === 'Ready' || b.textContent === 'Ready Up'
        )?.click();
      }),
      page2.evaluate(() => {
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent === 'Ready' || b.textContent === 'Ready Up'
        )?.click();
      })
    ]);
    
    await delay(2000);
    
    // Start game
    await page1.evaluate(() => {
      Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Start Game'
      )?.click();
    });
    
    await delay(3000);
    console.log('✅ Game started!\n');
    
    // Run test scenarios
    console.log('📊 Test Scenarios:');
    console.log('=================');
    
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const scenario = CONFIG.scenarios[i];
      console.log(`\n${i + 1}. ${scenario.name}`);
      
      // Schedule submissions
      if (scenario.p1 !== null) {
        page1.evaluate(`window.submitAt(${scenario.p1})`);
        console.log(`   P1 → ${scenario.p1}ms`);
      }
      
      if (scenario.p2 !== null) {
        page2.evaluate(`window.submitAt(${scenario.p2})`);
        console.log(`   P2 → ${scenario.p2}ms`);
      }
      
      // Wait for round
      await delay(8000);
      
      // Get results
      const timing = await page1.evaluate((index) => {
        const changes = window.testData.roundChanges || [];
        return changes[index];
      }, i);
      
      if (timing) {
        const diff = Math.abs(timing.time - scenario.expected);
        const passed = diff <= 500;
        
        console.log(`   Expected: ${scenario.expected}ms`);
        console.log(`   Actual: ${timing.time}ms`);
        console.log(`   ${passed ? '✅ PASS' : '❌ FAIL'} (±${diff}ms)`);
        
        results.push({ ...scenario, actual: timing.time, diff, passed });
      } else {
        console.log('   ❌ No round change');
        results.push({ ...scenario, actual: null, passed: false });
      }
    }
    
    // Summary
    console.log('\n\n📈 FINAL RESULTS');
    console.log('================');
    
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    
    console.log(`Passed: ${passed}/${total} (${Math.round(passed/total * 100)}%)\n`);
    
    results.forEach((r, i) => {
      const status = r.passed ? '✅' : '❌';
      const timing = r.actual ? `${r.expected}→${r.actual}ms` : 'FAILED';
      console.log(`${status} Test ${i+1}: ${r.name} [${timing}]`);
    });
    
    if (passed === total) {
      console.log('\n🎉 ALL TESTS PASSED! 🎉');
      console.log('\nMultiplayer round advance timing verified:');
      console.log('• 3-second transition after all players submit');
      console.log('• 7-second maximum round duration');
      console.log('• Timing accuracy within ±500ms tolerance');
    }
    
    console.log('\n✅ Test complete! Press Ctrl+C to exit.\n');
    
    // Keep alive
    process.on('SIGINT', async () => {
      if (browser1) await browser1.close();
      if (browser2) await browser2.close();
      process.exit(0);
    });
    
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    
    process.exit(1);
  }
}

// Run test
console.log('Starting test...\n');
runTest().catch(console.error);