# Run the server-side transitions migration

Write-Host "Running server-side transitions migration..." -ForegroundColor Cyan

# Get database URL from environment
$dbUrl = $env:DATABASE_URL
if (-not $dbUrl) {
    # Try to get from Supabase
    Write-Host "Getting database URL from Supabase..." -ForegroundColor Yellow
    $dbStatus = npx supabase db remote get 2>$null
    if ($dbStatus -match "postgresql://") {
        $dbUrl = $dbStatus
    } else {
        Write-Error "Could not get database URL. Please set DATABASE_URL environment variable."
        exit 1
    }
}

# Run the migration
$migrationFile = "supabase/migrations/20250630_server_side_transitions.sql"

if (Test-Path $migrationFile) {
    Write-Host "Applying migration: $migrationFile" -ForegroundColor Yellow
    
    # Use psql to run the migration
    $env:PGPASSWORD = $dbUrl.Split('@')[0].Split(':')[-1]
    $connectionString = $dbUrl -replace 'postgresql://[^:]+:[^@]+@', 'postgresql://'
    
    psql $dbUrl -f $migrationFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Migration applied successfully!" -ForegroundColor Green
        Write-Host "`nChanges made:" -ForegroundColor Cyan
        Write-Host "- Added question_sequence column to game_rooms table" -ForegroundColor White
        Write-Host "- Created auto_advance_question() function" -ForegroundColor White
        Write-Host "- Created index for efficient auto-advance queries" -ForegroundColor White
    } else {
        Write-Error "❌ Migration failed with exit code: $LASTEXITCODE"
    }
} else {
    Write-Error "Migration file not found: $migrationFile"
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Deploy the question-auto-advance edge function" -ForegroundColor White
Write-Host "2. Set up a cron job to call it every second" -ForegroundColor White
Write-Host "3. Test with multiple clients and tab switching" -ForegroundColor White