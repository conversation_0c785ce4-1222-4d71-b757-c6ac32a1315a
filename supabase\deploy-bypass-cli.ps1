# Deploy Edge Functions bypassing Supabase CLI completely
# This uses direct file operations and API calls

param(
    [Parameter(Mandatory=$false)]
    [string]$FunctionName = "start-game-handler"
)

Write-Host "`nDirect Edge Function Deployment (No CLI)" -ForegroundColor Cyan
Write-Host "Function: $FunctionName" -ForegroundColor Yellow

$projectRef = "xmyxuvuimebjltnaamox"
$projectUrl = "https://$projectRef.supabase.co"

# Check if we have the service role key
$envFile = Join-Path $PSScriptRoot ".env"
if (-not (Test-Path $envFile)) {
    Write-Host "Error: .env file not found at $envFile" -ForegroundColor Red
    exit 1
}

# Read service role key from .env
$serviceRoleKey = $null
$envContent = Get-Content $envFile
foreach ($line in $envContent) {
    if ($line -match "SUPABASE_SERVICE_ROLE_KEY=(.+)") {
        $serviceRoleKey = $matches[1].Trim('"')
        break
    }
}

if (-not $serviceRoleKey) {
    Write-Host "Error: SUPABASE_SERVICE_ROLE_KEY not found in .env file" -ForegroundColor Red
    exit 1
}

Write-Host "Service role key found." -ForegroundColor Green

# Read the function code
$functionPath = Join-Path $PSScriptRoot "functions\$FunctionName\index.ts"
if (-not (Test-Path $functionPath)) {
    Write-Host "Error: Function file not found at $functionPath" -ForegroundColor Red
    exit 1
}

$functionCode = Get-Content $functionPath -Raw
Write-Host "Function code loaded. Size: $($functionCode.Length) bytes" -ForegroundColor Gray

# Option 1: Try using the Supabase Dashboard method
Write-Host "`nOption 1: Using Supabase REST API..." -ForegroundColor Yellow

$apiEndpoint = "$projectUrl/functions/v1/$FunctionName"
$headers = @{
    "Authorization" = "Bearer $serviceRoleKey"
    "Content-Type" = "text/plain"
}

try {
    # Create a test invocation first to see if function exists
    Write-Host "Testing function endpoint..." -ForegroundColor Gray
    $testResponse = Invoke-WebRequest -Uri $apiEndpoint -Method POST -Headers $headers -Body "{}" -ErrorAction SilentlyContinue
    Write-Host "Function is already deployed and responding!" -ForegroundColor Green
} catch {
    Write-Host "Function needs deployment or update." -ForegroundColor Yellow
}

# Option 2: Use Deno Deploy API (if available)
Write-Host "`nOption 2: Manual deployment instructions..." -ForegroundColor Yellow
Write-Host @"

Since the CLI is hanging, you can deploy manually:

1. Open Supabase Dashboard: https://app.supabase.com/project/$projectRef/functions

2. Click on your function: $FunctionName

3. Click "Deploy function" or "Update function"

4. Copy and paste the code from:
   $functionPath

5. Save and deploy

Alternatively, try these commands from different locations:

From PowerShell (as Administrator):
cd C:\Projects\recognition-combine
npx supabase functions deploy $FunctionName --project-ref $projectRef --no-verify-jwt

From WSL/Linux:
cd /mnt/c/Projects/recognition-combine
npx supabase functions deploy $FunctionName --project-ref $projectRef --no-verify-jwt

From Git Bash:
cd /c/Projects/recognition-combine
npx supabase functions deploy $FunctionName --project-ref $projectRef --no-verify-jwt

"@ -ForegroundColor Cyan

# Option 3: Create a deployment package
Write-Host "`nOption 3: Creating deployment package..." -ForegroundColor Yellow
$packageDir = Join-Path $PSScriptRoot "deployment-package"
$packageFuncDir = Join-Path $packageDir $FunctionName

if (-not (Test-Path $packageDir)) {
    New-Item -ItemType Directory -Path $packageDir -Force | Out-Null
}

if (-not (Test-Path $packageFuncDir)) {
    New-Item -ItemType Directory -Path $packageFuncDir -Force | Out-Null
}

# Copy function files
Copy-Item -Path $functionPath -Destination $packageFuncDir -Force
$importMapPath = Join-Path $PSScriptRoot "functions\$FunctionName\import_map.json"
if (Test-Path $importMapPath) {
    Copy-Item -Path $importMapPath -Destination $packageFuncDir -Force
}

Write-Host "Deployment package created at: $packageFuncDir" -ForegroundColor Green
Write-Host "You can manually upload this to Supabase Dashboard." -ForegroundColor Cyan