import type { PlayerData } from '@/types';

interface Props {
  player: PlayerData | null | undefined;
}

export function PlayerInfoPanel({ player }: Props) {
  if (!player) {
    return (
      <div className="text-center text-gray-400 flex flex-col justify-center h-full">
        <h2 className="text-2xl font-bold mb-4">Player Info</h2>
        <p className="text-lg">Select a player to view details</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-between h-full">
      <div className="mt-10">
        <h2 className="text-3xl font-extrabold mb-2 text-center break-words">{player.player_name}</h2>
        <div className="text-center mb-3">
          <h3 className="text-lg font-bold leading-tight">Team</h3>
          <p className="text-xl font-extrabold leading-tight break-words">{player.team_name}</p>
        </div>
        <div className="text-center mb-3">
          <h3 className="text-lg font-bold leading-tight">College</h3>
          <p className="text-xl font-extrabold leading-tight">{player.college || '-'}</p>
        </div>
      </div>
      <div className="flex-1 flex flex-col justify-center -mt-25">
        <div className="grid grid-cols-2 gap-x-2 gap-y-1 mb-3 justify-center text-center">
          <div>
            <h3 className="text-lg font-bold leading-tight">Pos.</h3>
            <p className="text-xl font-extrabold leading-tight">{player.position}</p>
          </div>
          <div>
            <h3 className="text-lg font-bold leading-tight">No.</h3>
            <p className="text-xl font-extrabold leading-tight">#{player.jersey_number}</p>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-x-2 gap-y-1 mb-3 justify-center text-center">
          <div>
            <h3 className="text-lg font-bold leading-tight">Height</h3>
            <p className="text-xl font-extrabold leading-tight">{player.height || '-'}</p>
          </div>
          <div>
            <h3 className="text-lg font-bold leading-tight">Weight</h3>
            <p className="text-xl font-extrabold leading-tight">{player.weight ? `${player.weight} lbs` : '-'}</p>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-x-2 gap-y-1 mb-3 justify-center text-center">
          <div>
            <h3 className="text-lg font-bold leading-tight">Age</h3>
            <p className="text-xl font-extrabold leading-tight">{player.age_or_dob || '-'}</p>
          </div>
          <div>
            <h3 className="text-lg font-bold leading-tight">Exp.</h3>
            <p className="text-xl font-extrabold leading-tight">{player.experience ? `${player.experience} yrs` : '-'}</p>
          </div>
        </div>
      </div>
    </div>
  );
} 