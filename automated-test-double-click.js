/**
 * Double-Click Automated Test
 * Uses double-click to join rooms directly
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'P1 only → 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1@1s, P2@3s → 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

async function clickButton(page, text) {
  await page.evaluate((txt) => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent && b.textContent.includes(txt)
    );
    if (btn) btn.click();
  }, text);
}

const monitorCode = `
  window.testData = { gameStartTime: null, roundChanges: [], currentRound: 0 };
  let lastQ = null;
  console.log('Monitor initialized');
  
  setInterval(() => {
    // Try multiple selectors for questions
    const h2 = document.querySelector('h2')?.textContent || '';
    const h3 = document.querySelector('h3')?.textContent || '';
    const q = h2.includes('NFL PLAYER') ? h2 : h3.includes('NFL PLAYER') ? h3 : '';
    
    // Also check for answer buttons as indicator of active question
    const answerButtons = Array.from(document.querySelectorAll('button')).filter(b => {
      const t = b.textContent || '';
      return t.length > 0 && t.length < 30 && 
             !['Ready','Start','Mode','Sign','Create','Join','Leave','Host'].some(w => t.includes(w));
    });
    
    if ((q.includes('NFL PLAYER') && q !== lastQ) || 
        (answerButtons.length >= 4 && !window.testData.gameStartTime)) {
      const now = Date.now();
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('Game started!');
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({ round: window.testData.currentRound++, time: elapsed });
        console.log('Round ' + window.testData.currentRound + ' at ' + elapsed + 'ms');
      }
      lastQ = q;
    }
  }, 100);
  
  window.submitAt = ms => new Promise(resolve => {
    const check = () => {
      if (!window.testData.gameStartTime) return setTimeout(check, 100);
      
      const elapsed = Date.now() - window.testData.gameStartTime;
      const wait = ms - elapsed;
      
      setTimeout(() => {
        const btns = Array.from(document.querySelectorAll('button')).filter(b => {
          const t = b.textContent || '';
          return t.length > 0 && t.length < 30 && !['Ready','Start','Mode','Sign','Create','Join','Leave','Host'].some(w => t.includes(w));
        });
        if (btns.length >= 4) {
          btns[Math.floor(Math.random() * 4)].click();
          resolve();
        }
      }, Math.max(0, wait));
    };
    check();
  });
`;

async function runTest() {
  console.log('🎯 Double-Click Automated Test\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Launch
    const opts = {
      headless: false,
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || '/snap/bin/chromium',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--window-size=1200,900'],
      protocolTimeout: 60000 // Increase timeout to 60 seconds
    };
    
    browser1 = await puppeteer.launch({ ...opts, args: [...opts.args, '--window-position=0,0'] });
    browser2 = await puppeteer.launch({ ...opts, args: [...opts.args, '--window-position=600,0'] });
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    
    // Load
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2' }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2' })
    ]);
    await delay(3000);
    
    // Monitor
    await page1.evaluate(monitorCode);
    await page2.evaluate(monitorCode);
    
    // Setup console logging
    page1.on('console', msg => {
      if (msg.type() === 'log' && (
        msg.text().includes('PLAYER UPDATE EVENT') ||
        msg.text().includes('NEW PLAYER JOINED') ||
        msg.text().includes('HOST_STATE_CHANGE') ||
        msg.text().includes('START_BUTTON_DISABLED_CHECK') ||
        msg.text().includes('fetchPlayersInActiveRoom') ||
        msg.text().includes('Realtime')
      )) {
        console.log('[HOST CONSOLE]', msg.text());
      }
    });
    
    page2.on('console', msg => {
      if (msg.type() === 'log' && msg.text().includes('Join') && msg.text().includes('room')) {
        console.log('[PLAYER2 CONSOLE]', msg.text());
      }
    });
    
    // Sign in
    console.log('🔐 Signing in...');
    await clickButton(page1, 'Login');
    await delay(2000);
    await page1.type('[placeholder*="Username"]', CONFIG.player1.email);
    await page1.type('[placeholder*="Password"]', CONFIG.player1.password);
    await page1.evaluate(() => document.querySelector('form').requestSubmit());
    
    await clickButton(page2, 'Login');
    await delay(2000);
    await page2.type('[placeholder*="Username"]', CONFIG.player2.email);
    await page2.type('[placeholder*="Password"]', CONFIG.player2.password);
    await page2.evaluate(() => document.querySelector('form').requestSubmit());
    
    await delay(5000);
    console.log('✅ Signed in\n');
    
    // Multiplayer
    console.log('🎮 Multiplayer...');
    await clickButton(page1, 'Multiplayer Mode');
    await delay(3000);
    await clickButton(page2, 'Multiplayer Mode');
    await delay(3000);
    
    // Create & Join
    console.log('🏠 Room...');
    await clickButton(page1, 'Host Game');
    console.log('  Waiting for room creation and subscriptions...');
    await delay(5000); // Give more time for room creation and realtime subscriptions
    
    // Wait for room list to update on page2
    console.log('  Waiting for room list to refresh...');
    await page2.evaluate(() => {
      // Trigger a potential refresh of the room list
      const refreshBtn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent && b.textContent.includes('Refresh')
      );
      if (refreshBtn) refreshBtn.click();
    });
    await delay(2000);
    
    // Try double-click to join directly
    console.log('  Looking for room...');
    const roomInfo = await page2.evaluate(() => {
      // Debug: find all potential room elements
      const roomDivs = Array.from(document.querySelectorAll('div.bg-slate-800'));
      const allTexts = roomDivs.map(div => div.textContent);
      
      // Find the room element by looking for the div containing Host: fresh
      const roomEl = roomDivs.find(div => {
        const text = div.textContent || '';
        // Look for either "fresh's Game" or "Host: fresh"
        return (text.includes("fresh's Game") || text.includes("Host: fresh")) && 
               text.includes("Players:");
      });
      
      return {
        foundRoomDivs: roomDivs.length,
        roomTexts: allTexts.slice(0, 3), // First 3 for debugging
        foundTargetRoom: !!roomEl,
        targetRoomText: roomEl ? roomEl.textContent : null
      };
    });
    
    console.log('  Room search results:', JSON.stringify(roomInfo, null, 2));
    
    await page2.evaluate(() => {
      const roomDivs = Array.from(document.querySelectorAll('div.bg-slate-800'));
      const roomEl = roomDivs.find(div => {
        const text = div.textContent || '';
        return (text.includes("fresh's Game") || text.includes("Host: fresh")) && 
               text.includes("Players:");
      });
      
      if (roomEl) {
        console.log('Found room element, attempting double-click...');
        
        // Method 1: Native double-click
        const dblEvent = new MouseEvent('dblclick', {
          view: window,
          bubbles: true,
          cancelable: true,
          detail: 2,
          clientX: roomEl.getBoundingClientRect().left + 50,
          clientY: roomEl.getBoundingClientRect().top + 20
        });
        roomEl.dispatchEvent(dblEvent);
        
        // Method 2: Two clicks in quick succession
        setTimeout(() => {
          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true
          });
          roomEl.dispatchEvent(clickEvent);
          roomEl.dispatchEvent(clickEvent);
        }, 100);
        
        // Method 3: Direct click() calls
        setTimeout(() => {
          roomEl.click();
          setTimeout(() => roomEl.click(), 50);
        }, 200);
        
        return true;
      } else {
        console.log('Could not find room element, checking all divs...');
        
        // Try broader search
        const allDivs = Array.from(document.querySelectorAll('div'));
        const hostDiv = allDivs.find(div => {
          const text = div.textContent || '';
          return text.includes('Host: fresh') && text.includes('Players:') && 
                 div.className && div.className.includes('slate');
        });
        
        if (hostDiv) {
          console.log('Found room via broader search');
          hostDiv.click();
          
          setTimeout(() => {
            const joinBtn = Array.from(document.querySelectorAll('button')).find(b => 
              b.textContent === 'Join This Room' || b.textContent === 'Join'
            );
            if (joinBtn) {
              joinBtn.click();
              console.log('Clicked Join button');
            }
          }, 1000);
        }
      }
    });
    
    await delay(3000);
    
    // Check if double-click worked, if not try single-click + join button
    const needsFallback = await page2.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const inLobby = document.body.textContent?.includes("fresh's Game") || false;
      const hasJoinButton = buttons.some(b => b.textContent === 'Join This Room' || b.textContent === 'Join');
      return !inLobby && hasJoinButton;
    });
    
    if (needsFallback) {
      console.log('  Double-click failed, using single-click + join button...');
      await page2.evaluate(() => {
        // Single click the room
        const roomDivs = Array.from(document.querySelectorAll('div.bg-slate-800'));
        const roomEl = roomDivs.find(div => {
          const text = div.textContent || '';
          return text.includes('Host: fresh') && text.includes('Players:');
        });
        if (roomEl) roomEl.click();
      });
      
      await delay(1000);
      await clickButton(page2, 'Join This Room') || await clickButton(page2, 'Join');
      await delay(2000);
    }
    
    // Verify join status
    const joinStatus = await page2.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const hasReady = buttons.some(b => b.textContent === 'Ready');
      const hasLeave = buttons.some(b => b.textContent === 'Leave Room');
      
      // Check if we're in a different room or state
      const hasLeaveGame = buttons.some(b => b.textContent === 'Leave Game');
      const hasReadyUp = buttons.some(b => b.textContent === 'Ready Up');
      
      // Get current page text to understand state
      const pageText = document.body.textContent || '';
      const inLobby = pageText.includes("fresh's Game");
      
      return { 
        hasReady, 
        hasLeave, 
        hasLeaveGame,
        hasReadyUp,
        inLobby,
        buttonTexts: buttons.map(b => b.textContent).filter(t => t) 
      };
    });
    
    console.log('  Join status:', JSON.stringify(joinStatus, null, 2));
    
    // If we have "Leave Game" button, we might be in a different state
    if (joinStatus.hasLeaveGame && !joinStatus.inLobby) {
      console.log('  Player 2 seems to be in a different game state. Clicking Leave Game...');
      await clickButton(page2, 'Leave Game');
      await delay(2000);
      
      // Try joining again
      console.log('  Retrying join...');
      await page2.evaluate(() => {
        const roomDivs = Array.from(document.querySelectorAll('div.bg-slate-800'));
        const roomEl = roomDivs.find(div => {
          const text = div.textContent || '';
          return text.includes('Host: fresh') && text.includes('Players:');
        });
        
        if (roomEl) {
          roomEl.dispatchEvent(new MouseEvent('dblclick', {
            view: window,
            bubbles: true,
            cancelable: true,
            detail: 2
          }));
        }
      });
      await delay(2000);
    }
    
    if (joinStatus.hasReady || joinStatus.hasLeave || joinStatus.inLobby) {
      console.log('✅ Joined successfully!\n');
      
      // Give time for realtime updates to propagate
      console.log('  Waiting for realtime updates to propagate...');
      await delay(3000);
      
      // Check player count on host side
      const hostView = await page1.evaluate(() => {
        const playerElements = Array.from(document.querySelectorAll('*')).filter(el => 
          el.textContent && el.textContent.includes('Players (') && el.textContent.includes('/')
        );
        const playerText = playerElements[0]?.textContent || 'not found';
        
        // Also look for individual player entries
        const playerListItems = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent || '';
          return text.includes('fresh') && (text.includes('Ready') || text.includes('Not Ready'));
        });
        
        return {
          playerCountText: playerText,
          playerListCount: playerListItems.length,
          playerListTexts: playerListItems.slice(0, 5).map(el => el.textContent)
        };
      });
      
      console.log('  Host view after join:', JSON.stringify(hostView, null, 2));
      
      if (hostView.playerCountText.includes('(1/')) {
        console.log('  ⚠️ Host still shows only 1 player. Trying to trigger refresh...');
        
        // Try clicking anywhere to trigger a potential refresh
        await page1.evaluate(() => {
          // Click on the room title to potentially trigger a refresh
          const roomTitle = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent === "Room: fresh's Game"
          );
          if (roomTitle) {
            roomTitle.click();
            console.log('Clicked room title');
          }
          
          // Also try clicking the player count area
          const playerCount = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.includes('Players (1/')
          );
          if (playerCount) {
            playerCount.click();
            console.log('Clicked player count');
          }
        });
        
        await delay(5000);
        
        // Check again
        const updatedHostView = await page1.evaluate(() => {
          const playerElements = Array.from(document.querySelectorAll('*')).filter(el => 
            el.textContent && el.textContent.includes('Players (') && el.textContent.includes('/')
          );
          return playerElements[0]?.textContent || 'not found';
        });
        
        console.log('  Updated host view:', updatedHostView);
        
        // Try having player 2 ready up to trigger an update
        console.log('  Having player 2 ready up to trigger update...');
        await clickButton(page2, 'Ready Up');
        await delay(3000);
        
        // Check one more time
        const finalHostView = await page1.evaluate(() => {
          const playerElements = Array.from(document.querySelectorAll('*')).filter(el => 
            el.textContent && el.textContent.includes('Players (') && el.textContent.includes('/')
          );
          const playerText = playerElements[0]?.textContent || 'not found';
          
          // Count player list items
          const playerItems = Array.from(document.querySelectorAll('*')).filter(el => {
            const text = el.textContent || '';
            return (text.includes('fresh') || text.includes('fresh2')) && 
                   (text.includes('Ready') || text.includes('Not Ready'));
          });
          
          return {
            playerCountText: playerText,
            playerItemsFound: playerItems.length,
            playerTexts: playerItems.slice(0, 4).map(el => el.textContent?.trim())
          };
        });
        
        console.log('  Final host view after player 2 ready:', JSON.stringify(finalHostView, null, 2));
        
        if (finalHostView.playerItemsFound < 2) {
          console.log('  ⚠️ Still not seeing player 2. Trying host leave/rejoin workaround...');
          
          // Have host leave and rejoin to force refresh
          await clickButton(page1, 'Leave Game');
          await delay(2000);
          
          // Host rejoins their own room
          await page1.evaluate(() => {
            const roomDivs = Array.from(document.querySelectorAll('div.bg-slate-800'));
            const roomEl = roomDivs.find(div => {
              const text = div.textContent || '';
              return text.includes('Host: fresh') && text.includes('Players:');
            });
            
            if (roomEl) {
              // Double-click to join
              const dblEvent = new MouseEvent('dblclick', {
                view: window,
                bubbles: true,
                cancelable: true,
                detail: 2
              });
              roomEl.dispatchEvent(dblEvent);
            }
          });
          
          await delay(3000);
          
          // Both players ready up again
          await clickButton(page1, 'Ready Up');
          await delay(1000);
          await clickButton(page2, 'Ready Up');
          await delay(2000);
        } else {
          // Alternative: Simple polling approach for CI/testing
          console.log('  ⚠️ Still not seeing player 2. Using polling approach...');
          
          // Poll for up to 10 seconds
          let found = false;
          for (let i = 0; i < 10; i++) {
            await delay(1000);
            
            // Force a UI update by toggling ready state
            await clickButton(page1, 'Ready Up');
            await delay(500);
            
            const pollResult = await page1.evaluate(() => {
              const playerItems = Array.from(document.querySelectorAll('*')).filter(el => {
                const text = el.textContent || '';
                return (text.includes('fresh') || text.includes('fresh2')) && 
                       (text.includes('Ready') || text.includes('Not Ready'));
              });
              
              return {
                playerCount: playerItems.length,
                hasStartButton: !!Array.from(document.querySelectorAll('button')).find(b => 
                  b.textContent === 'Start Game' || b.textContent?.includes('Start Game')
                )
              };
            });
            
            if (pollResult.playerCount >= 2 || pollResult.hasStartButton) {
              console.log(`  ✅ Found ${pollResult.playerCount} players after ${i+1} seconds`);
              found = true;
              break;
            }
          }
          
          if (!found) {
            console.log('  ❌ Failed to see player 2 after polling');
          }
        }
      }
    } else {
      console.log('⚠️  Join might have failed\n');
    }
    
    // Start
    console.log('🎮 Starting...');
    
    // Ready up player 1 (host)
    await clickButton(page1, 'Ready');
    await delay(1000);
    
    // Ready up player 2
    await clickButton(page2, 'Ready');
    await delay(2000);
    
    // Check if both players are ready
    const readyStatus = await page1.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const startButton = buttons.find(b => b.textContent === 'Start Game' || b.textContent?.includes('Start Game'));
      const isDisabled = startButton ? startButton.disabled : true;
      const buttonText = startButton ? startButton.textContent : 'not found';
      
      // Look for player ready indicators
      const readyIndicators = Array.from(document.querySelectorAll('*')).filter(el => 
        el.textContent && el.textContent.includes('Ready ✓')
      );
      
      return {
        startButtonFound: !!startButton,
        startButtonDisabled: isDisabled,
        startButtonText: buttonText,
        readyIndicatorCount: readyIndicators.length,
        allButtons: buttons.map(b => b.textContent).filter(t => t)
      };
    });
    
    console.log('  Ready status:', JSON.stringify(readyStatus, null, 2));
    
    if (!readyStatus.startButtonFound) {
      console.log('❌ Start Game button not found!');
      process.exit(1);
    }
    
    if (readyStatus.startButtonDisabled) {
      console.log('⚠️  Start Game button is disabled. Waiting additional time...');
      await delay(3000);
    }
    
    await clickButton(page1, 'Start Game');
    await delay(3000);
    console.log('✅ Clicked Start Game button\n');
    
    // Wait for game to actually start
    console.log('⏳ Waiting for questions to load...');
    const gameStarted = await Promise.race([
      page1.evaluate(() => new Promise(resolve => {
        const checkStart = setInterval(() => {
          if (window.testData && window.testData.gameStartTime) {
            clearInterval(checkStart);
            resolve(true);
          }
          // Also check for answer buttons
          const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
            const t = b.textContent || '';
            return t.length > 0 && t.length < 30 && 
                   !['Ready','Start','Mode','Sign','Create','Join','Leave','Host'].some(w => t.includes(w));
          });
          if (buttons.length >= 4) {
            clearInterval(checkStart);
            resolve(true);
          }
        }, 100);
      })),
      new Promise(resolve => setTimeout(() => resolve(false), 10000))
    ]);
    
    if (!gameStarted) {
      console.log('❌ Game failed to start - no questions loaded');
      process.exit(1);
    }
    
    console.log('✅ Questions loaded!\n');
    
    // Tests
    console.log('📊 Tests:');
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const s = CONFIG.scenarios[i];
      console.log(`\n${i+1}. ${s.name}`);
      
      // Check game started before submitting
      const gameStatus = await page1.evaluate(() => ({
        started: !!window.testData.gameStartTime,
        buttons: Array.from(document.querySelectorAll('button')).filter(b => {
          const t = b.textContent || '';
          return t.length > 0 && t.length < 30;
        }).map(b => b.textContent)
      }));
      
      if (!gameStatus.started) {
        console.log(`   ⚠️  Game not started yet. Found buttons:`, gameStatus.buttons.slice(0, 5).join(', '));
        results.push({ pass: false });
        continue;
      }
      
      const tasks = [];
      if (s.p1) tasks.push(page1.evaluate(`window.submitAt(${s.p1})`).catch(e => console.log('P1 submit error:', e.message)));
      if (s.p2) tasks.push(page2.evaluate(`window.submitAt(${s.p2})`).catch(e => console.log('P2 submit error:', e.message)));
      
      await Promise.all(tasks);
      await delay(8000);
      
      const timing = await page1.evaluate(i => window.testData.roundChanges[i], i).catch(() => null);
      
      if (timing) {
        const diff = Math.abs(timing.time - s.expected);
        const pass = diff <= 500;
        console.log(`   ${s.expected} → ${timing.time}ms (${pass ? '✅' : '❌'})`);
        results.push({ pass });
      } else {
        console.log('   ❌ Failed');
        results.push({ pass: false });
      }
    }
    
    const passed = results.filter(r => r.pass).length;
    console.log(`\n📈 ${passed}/${results.length} passed`);
    
    if (passed === results.length) {
      console.log('\n🎉 ALL TESTS PASSED! 🎉');
      console.log('\nMultiplayer timing verified:');
      console.log('• 3s after all submit');
      console.log('• 7s max duration');
      console.log('• ±500ms accuracy');
    }
    
    console.log('\nCtrl+C to exit\n');
    
    process.on('SIGINT', async () => {
      if (browser1) await browser1.close();
      if (browser2) await browser2.close();
      process.exit(0);
    });
    
    await new Promise(() => {});
    
  } catch (e) {
    console.error('\n❌', e.message);
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    process.exit(1);
  }
}

runTest();