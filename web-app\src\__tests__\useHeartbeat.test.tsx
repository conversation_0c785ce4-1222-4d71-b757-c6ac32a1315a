import { renderHook, act, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useHeartbeat } from '../hooks/useHeartbeat';
import { AuthProvider } from '../providers/AuthProvider';
import supabase from '../lib/supabaseClient';

// Mock Supabase client
vi.mock('../lib/supabaseClient', () => ({
  default: {
    auth: {
      getSession: vi.fn(),
      refreshSession: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: { subscription: { unsubscribe: vi.fn() } }
      }))
    },
    functions: {
      invoke: vi.fn()
    }
  }
}));

// Mock useAuth hook
vi.mock('../providers/AuthProvider', () => ({
  useAuth: vi.fn(),
  AuthProvider: ({ children }: { children: React.ReactNode }) => children
}));

import { useAuth } from '../providers/AuthProvider';

describe('useHeartbeat Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should not send heartbeat when user is not authenticated', async () => {
    (useAuth as any).mockReturnValue({
      user: null,
      loading: false
    });

    const { result } = renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    // Advance timers
    act(() => {
      vi.advanceTimersByTime(6000);
    });

    expect(supabase.functions.invoke).not.toHaveBeenCalled();
  });

  it('should not send heartbeat when auth is loading', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: true
    });

    renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    act(() => {
      vi.advanceTimersByTime(6000);
    });

    expect(supabase.functions.invoke).not.toHaveBeenCalled();
  });

  it('should send heartbeat when authenticated with valid session', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'valid-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
        }
      },
      error: null
    });

    (supabase.functions.invoke as any).mockResolvedValue({
      data: { success: true },
      error: null
    });

    renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    // Wait for initial heartbeat (1 second delay)
    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(supabase.functions.invoke).toHaveBeenCalledWith('heartbeat-handler', {
      body: { roomId: 'test-room-id', action: 'ping' }
    });

    // Advance to next interval
    act(() => {
      vi.advanceTimersByTime(5000);
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(2);
  });

  it('should refresh session when token is close to expiry', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    // Session expires in 4 minutes (240 seconds)
    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'expiring-token',
          expires_at: Math.floor(Date.now() / 1000) + 240
        }
      },
      error: null
    });

    (supabase.auth.refreshSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'new-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    (supabase.functions.invoke as any).mockResolvedValue({
      data: { success: true },
      error: null
    });

    renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(supabase.auth.refreshSession).toHaveBeenCalled();
    expect(supabase.functions.invoke).toHaveBeenCalled();
  });

  it('should call onSessionExpired when receiving 401 error', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'valid-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    const error401 = new Error('Unauthorized');
    (error401 as any).status = 401;

    (supabase.functions.invoke as any).mockResolvedValue({
      data: null,
      error: error401
    });

    const onSessionExpired = vi.fn();

    renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000,
        onSessionExpired
      })
    );

    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(onSessionExpired).toHaveBeenCalled();
    
    // Should stop heartbeat after 401
    act(() => {
      vi.advanceTimersByTime(5000);
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(1);
  });

  it('should implement retry logic with max retries', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'valid-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    const networkError = new Error('Network error');
    (supabase.functions.invoke as any).mockResolvedValue({
      data: null,
      error: networkError
    });

    const onError = vi.fn();

    renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000,
        onError
      })
    );

    // First attempt
    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    // Second attempt
    act(() => {
      vi.advanceTimersByTime(5000);
    });

    // Third attempt (max retries)
    act(() => {
      vi.advanceTimersByTime(5000);
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(3);
    expect(onError).toHaveBeenCalledWith(networkError);

    // Should stop after max retries
    act(() => {
      vi.advanceTimersByTime(5000);
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(3);
  });

  it('should stop heartbeat when component unmounts', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'valid-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    (supabase.functions.invoke as any).mockResolvedValue({
      data: { success: true },
      error: null
    });

    const { unmount } = renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(1);

    // Unmount
    unmount();

    // Advance time - no more heartbeats should be sent
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(1);
  });

  it('should handle stopHeartbeat function', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'valid-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    (supabase.functions.invoke as any).mockResolvedValue({
      data: { success: true },
      error: null
    });

    const { result } = renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(1);

    // Stop heartbeat
    act(() => {
      result.current.stopHeartbeat();
    });

    // Advance time - no more heartbeats
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(1);
  });
});