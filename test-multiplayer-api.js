const fetch = require('node-fetch');

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'test123'
};

class MultiplayerAPITester {
  constructor() {
    this.session = null;
    this.accessToken = null;
  }

  async signIn() {
    console.log('1. Attempting to sign in...');
    
    try {
      const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_ANON_KEY
        },
        body: JSON.stringify({
          email: TEST_USER.email,
          password: TEST_USER.password
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        this.session = data;
        this.accessToken = data.access_token;
        console.log('   ✓ Sign in successful');
        console.log(`   User ID: ${data.user.id}`);
        console.log(`   Email: ${data.user.email}`);
        return true;
      } else {
        console.log('   ✗ Sign in failed:', data.error || data.message);
        return false;
      }
    } catch (error) {
      console.log('   ✗ Sign in error:', error.message);
      return false;
    }
  }

  async getRooms() {
    console.log('\n2. Fetching active game rooms...');
    
    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/game_rooms?status=eq.waiting&select=*,profiles(username),game_players(count)`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      const rooms = await response.json();
      
      if (response.ok) {
        console.log(`   ✓ Found ${rooms.length} waiting rooms`);
        rooms.forEach(room => {
          console.log(`   - Room: ${room.title || room.room_code}`);
          console.log(`     Host: ${room.profiles?.username || 'Unknown'}`);
          console.log(`     Players: ${room.game_players?.[0]?.count || 0}/${room.max_players}`);
        });
        return rooms;
      } else {
        console.log('   ✗ Failed to fetch rooms:', rooms);
        return [];
      }
    } catch (error) {
      console.log('   ✗ Error fetching rooms:', error.message);
      return [];
    }
  }

  async createRoom() {
    console.log('\n3. Creating a new game room...');
    
    const roomData = {
      title: `Test Room ${Date.now()}`,
      multiplayer_mode: 'competitive',
      max_players: 4,
      host_id: this.session?.user?.id
    };

    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/game_rooms`, {
        method: 'POST',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        },
        body: JSON.stringify(roomData)
      });

      const room = await response.json();
      
      if (response.ok && room[0]) {
        console.log('   ✓ Room created successfully');
        console.log(`   Room ID: ${room[0].id}`);
        console.log(`   Room Code: ${room[0].room_code}`);
        console.log(`   Title: ${room[0].title}`);
        return room[0];
      } else {
        console.log('   ✗ Failed to create room:', room);
        return null;
      }
    } catch (error) {
      console.log('   ✗ Error creating room:', error.message);
      return null;
    }
  }

  async getProfile() {
    console.log('\n4. Fetching user profile...');
    
    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/profiles?id=eq.${this.session?.user?.id}&select=*`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      const profiles = await response.json();
      
      if (response.ok && profiles[0]) {
        console.log('   ✓ Profile found');
        console.log(`   Username: ${profiles[0].username}`);
        console.log(`   Created: ${profiles[0].created_at}`);
        return profiles[0];
      } else {
        console.log('   ✗ Profile not found');
        return null;
      }
    } catch (error) {
      console.log('   ✗ Error fetching profile:', error.message);
      return null;
    }
  }

  async testSupabaseConnection() {
    console.log('Testing Supabase connection...');
    console.log(`URL: ${SUPABASE_URL}`);
    console.log(`Key: ${SUPABASE_ANON_KEY.substring(0, 10)}...`);
    
    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY
        }
      });
      
      if (response.ok) {
        console.log('   ✓ Supabase connection successful');
        return true;
      } else {
        console.log('   ✗ Supabase connection failed:', response.status);
        return false;
      }
    } catch (error) {
      console.log('   ✗ Connection error:', error.message);
      return false;
    }
  }
}

// Main test function
async function runTests() {
  console.log('=== Multiplayer API Testing ===\n');
  
  const tester = new MultiplayerAPITester();
  
  // Test connection
  const connected = await tester.testSupabaseConnection();
  if (!connected) {
    console.log('\nPlease check your environment variables:');
    console.log('- NEXT_PUBLIC_SUPABASE_URL');
    console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY');
    return;
  }
  
  // Sign in
  const signedIn = await tester.signIn();
  if (!signedIn) {
    console.log('\nAuthentication failed. Please check credentials.');
    return;
  }
  
  // Get profile
  await tester.getProfile();
  
  // Get existing rooms
  await tester.getRooms();
  
  // Create a new room
  const room = await tester.createRoom();
  
  if (room) {
    console.log('\n=== Summary ===');
    console.log('✓ Authentication working');
    console.log('✓ API access working');
    console.log('✓ Room creation working');
    console.log('\nYou can now:');
    console.log(`1. Join the room with code: ${room.room_code}`);
    console.log('2. Test real-time features in the browser');
  }
}

// Check if we have the required environment variables
if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  console.log('Environment variables not found. Loading from .env.local...');
  require('dotenv').config({ path: './web-app/.env.local' });
}

// Run the tests
runTests().catch(console.error);