'use client';

import { useGameStore } from '@/stores/gameStore';
import { useEffect } from 'react';

export function ImagePreloader() {
  const nextImageUrl = useGameStore((state) => state.nextQuestionImageToPreload);
  
  useEffect(() => {
    if (nextImageUrl) {
      console.log('[ImagePreloader] Attempting to preload:', nextImageUrl);
      const img = new window.Image();
      img.src = nextImageUrl;
      img.onload = () => console.log('[ImagePreloader] Successfully preloaded:', nextImageUrl);
      img.onerror = (err) => console.error('[ImagePreloader] Error preloading:', nextImageUrl, err);
    }
  }, [nextImageUrl]);

  return null;
} 