import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';

interface Props {
  title: string;
  score: number;
  correctAnswers: number;
  totalQuestions: number;
}

export function ScorePanel({ title, score, correctAnswers, totalQuestions }: Props) {
  return (
    <Card className="bg-transparent border-none text-white shadow-none">
      <CardHeader className="pb-2">
        <CardTitle className="text-3xl font-extrabold text-center text-white mb-2">{title}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center text-center text-white p-0">
        <div className="mb-3">
          <p className="text-lg font-bold leading-tight">Score</p>
          <p className="text-xl font-extrabold leading-tight text-yellow-400">{score}</p>
        </div>
        <div className="mb-3">
          <p className="text-lg font-bold leading-tight">Correct</p>
          <p className="text-xl font-extrabold leading-tight">{correctAnswers}/{totalQuestions}</p>
        </div>
        <div>
          <p className="text-lg font-bold leading-tight">Accuracy</p>
          <p className="text-xl font-extrabold leading-tight">{totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0}%</p>
        </div>
      </CardContent>
    </Card>
  );
} 