# Round Submissions Animation Fix Test Plan

## What Was Fixed
- Modified the `useEffect` that tracks new answers for animation (line 414-462 in page.tsx)
- Added a check to prevent answers that have already landed from being re-added to the animating set
- This prevents the slide-up animation from replaying when alt-tabbing back to the game

## Key Change
```typescript
// Before: Only checked if already animating
if (!prev.has(key)) {
  newSet.add(key);
  hasNewAnswers = true;
}

// After: Check both animating AND landed states
if (!prev.has(key) && !landedAnswers.has(key)) {
  newSet.add(key);
  hasNewAnswers = true;
  console.log('[ANIMATION_FIX] Adding new answer to animate:', key);
}
```

## Test Steps
1. Start a multiplayer game with 2+ players
2. Have players submit answers to see the slide-up animation play once
3. Alt-tab away from the game tab
4. Alt-tab back to the game tab
5. **Expected**: The username cards should remain in their settled positions without replaying the slide-up animation
6. **Previous behavior**: Cards would slide up again from the bottom

## Console Logs to Monitor
- `[ANIMATION_FIX] Adding new answer to animate:` - Should only appear once per answer
- `[FOOTBALL_ANIMATION_DEBUG] Slide-up animation ended for:` - Confirms animation completion
- `[FOOTBALL_ANIMATION_DEBUG] Answer marked as landed:` - Confirms answer is tracked as landed

## Additional Verification
- The `landedAnswers` dependency was added to the useEffect to ensure React tracks changes
- Answers that have completed their animation will never be re-added to `animatingAnswers`
- The animation state properly persists across tab focus changes