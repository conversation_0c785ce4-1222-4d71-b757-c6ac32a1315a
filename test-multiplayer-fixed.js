const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };
const SCREENSHOT_DIR = path.join(__dirname, 'test-screenshots-fixed');

// Helper to take screenshots
async function takeScreenshot(page, name, prefix) {
  try {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    const filename = `${prefix}-${name}.png`;
    const filepath = path.join(SCREENSHOT_DIR, filename);
    await page.screenshot({ path: filepath, fullPage: true });
    console.log(`[${prefix}] Screenshot saved: ${filename}`);
  } catch (error) {
    console.error(`[${prefix}] Screenshot failed:`, error.message);
  }
}

// Helper to wait for condition
async function waitFor(fn, description, timeout = 30000) {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    try {
      const result = await fn();
      if (result) return result;
    } catch (e) {
      // Continue
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  throw new Error(`Timeout waiting for ${description}`);
}

// Main test
async function testMultiplayerFixed() {
  console.log('=== FIXED MULTIPLAYER TEST ===\n');
  console.log('This test properly handles the room detail view flow');
  console.log('---\n');
  
  const browserOptions = {
    headless: true,
    defaultViewport: { width: 1200, height: 900 },
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    executablePath: '/usr/bin/chromium-browser'
  };

  let hostBrowser, guestBrowser;
  let hostPage, guestPage;

  try {
    // Launch browsers
    console.log('Launching browsers...');
    hostBrowser = await puppeteer.launch(browserOptions);
    guestBrowser = await puppeteer.launch(browserOptions);
    
    hostPage = await hostBrowser.newPage();
    guestPage = await guestBrowser.newPage();
    
    // Enable console logging for debugging
    hostPage.on('console', msg => {
      if (msg.type() === 'error' || msg.text().includes('JOIN') || msg.text().includes('ROOM')) {
        console.log(`[HOST CONSOLE] ${msg.text()}`);
      }
    });
    
    guestPage.on('console', msg => {
      if (msg.type() === 'error' || msg.text().includes('JOIN') || msg.text().includes('ROOM')) {
        console.log(`[GUEST CONSOLE] ${msg.text()}`);
      }
    });
    
    // Navigate
    await Promise.all([
      hostPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' }),
      guestPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' })
    ]);
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === Quick Authentication ===
    console.log('\nAuthenticating users...');
    
    async function authenticate(page, creds, name) {
      // Click Multiplayer Mode
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Fill credentials
      await page.type('input[type="email"], input[type="text"]:not([type="password"])', creds.username);
      await page.type('input[type="password"]', creds.password);
      await page.keyboard.press('Enter');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Click Multiplayer Mode again
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log(`${name} authenticated`);
    }
    
    await authenticate(hostPage, HOST_CREDENTIALS, 'HOST');
    await authenticate(guestPage, GUEST_CREDENTIALS, 'GUEST');
    
    // === Host Creates Room ===
    console.log('\nHost creating room...');
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Host Game');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Get room info and verify host is in room
    const roomInfo = await hostPage.evaluate(() => {
      const roomTitle = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.includes("Room:"))?.textContent;
      const playerCount = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.match(/Players.*\(\d+\/\d+\)/))?.textContent;
      return { roomTitle, playerCount };
    });
    
    console.log('Room created:', roomInfo);
    await takeScreenshot(hostPage, '01-room-created', 'host');
    
    if (!roomInfo.playerCount || !roomInfo.playerCount.includes('1/')) {
      throw new Error(`Host room shows incorrect player count: ${roomInfo.playerCount}`);
    }
    
    // === Guest Joins Room ===
    console.log('\nGuest joining room...');
    
    // First refresh the room list
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Refresh List' || b.textContent.includes('Refresh'));
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    await takeScreenshot(guestPage, '02-room-list', 'guest');
    
    // Click on the room DIV to view details
    console.log('Guest clicking on room to view details...');
    const roomClicked = await guestPage.evaluate(() => {
      // Find the room div that contains fresh's game
      const roomDivs = Array.from(document.querySelectorAll('div[class*="bg-slate-800"]'));
      const freshRoom = roomDivs.find(div => 
        div.textContent.includes('fresh') && 
        div.textContent.includes('Players:')
      );
      
      if (freshRoom) {
        console.log('Found room div, clicking it');
        freshRoom.click();
        return true;
      }
      
      // Fallback: try any element with room info
      const anyRoom = Array.from(document.querySelectorAll('*'))
        .find(el => 
          el.textContent.includes("fresh's Game") || 
          (el.textContent.includes('fresh') && el.textContent.includes('Players:'))
        );
      
      if (anyRoom) {
        console.log('Found room element (fallback), clicking it');
        anyRoom.click();
        return true;
      }
      
      return false;
    });
    
    console.log('Room clicked to view details:', roomClicked);
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Take screenshot to see what guest sees
    await takeScreenshot(guestPage, '03-room-detail-view', 'guest');
    
    // Check what the guest sees in detail view
    const detailViewInfo = await guestPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button')).map(b => b.textContent);
      const hasJoinButton = buttons.some(b => b.includes('Join This Room'));
      const playerInfo = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.match(/Players:\s*\d+\s*\/\s*\d+/))?.textContent;
      
      return {
        buttons,
        hasJoinButton,
        playerInfo,
        pageContent: document.body.innerText.substring(0, 500)
      };
    });
    
    console.log('Detail view info:', {
      hasJoinButton: detailViewInfo.hasJoinButton,
      playerInfo: detailViewInfo.playerInfo,
      buttons: detailViewInfo.buttons
    });
    
    // Now click the Join This Room button
    const joinClicked = await guestPage.evaluate(() => {
      const joinBtn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Join This Room' || b.textContent.includes('Join This Room'));
      
      if (joinBtn && !joinBtn.disabled) {
        console.log('[JOIN_BUTTON] Clicking Join This Room button');
        joinBtn.click();
        return true;
      } else if (joinBtn && joinBtn.disabled) {
        console.log('[JOIN_BUTTON] Join button found but disabled');
        return false;
      }
      
      console.log('[JOIN_BUTTON] No Join This Room button found');
      return false;
    });
    
    console.log('Join button clicked:', joinClicked);
    
    if (!joinClicked && !detailViewInfo.hasJoinButton) {
      // Try double-clicking the room if join button not available
      console.log('Attempting double-click join fallback...');
      await guestPage.evaluate(() => {
        const roomDivs = Array.from(document.querySelectorAll('div[class*="bg-slate-800"]'));
        const freshRoom = roomDivs.find(div => 
          div.textContent.includes('fresh') && 
          div.textContent.includes('Players:')
        );
        
        if (freshRoom) {
          const event = new MouseEvent('dblclick', {
            bubbles: true,
            cancelable: true,
            view: window
          });
          freshRoom.dispatchEvent(event);
        }
      });
    }
    
    // Wait for join to complete
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    // Verify guest joined
    const guestState = await guestPage.evaluate(() => {
      const inRoom = document.body.textContent.includes('Leave Game') || 
                     document.body.textContent.includes('Ready Up') ||
                     document.body.textContent.includes('Ready');
      const buttons = Array.from(document.querySelectorAll('button')).map(b => b.textContent);
      
      return { 
        inRoom, 
        buttons,
        pageText: document.body.innerText.substring(0, 300)
      };
    });
    
    console.log('Guest state after join:', {
      inRoom: guestState.inRoom,
      buttons: guestState.buttons
    });
    await takeScreenshot(guestPage, '04-after-join', 'guest');
    
    if (!guestState.inRoom) {
      // Debug why join failed
      const debugInfo = await guestPage.evaluate(() => {
        const errorElements = Array.from(document.querySelectorAll('*'))
          .filter(el => el.textContent.includes('Error') || 
                       el.textContent.includes('error') ||
                       el.textContent.includes('Cannot') ||
                       el.textContent.includes('failed'))
          .map(el => el.textContent.substring(0, 100));
        
        return {
          errors: errorElements,
          url: window.location.href,
          centerPanel: Array.from(document.querySelectorAll('*'))
            .find(el => el.textContent.includes('Game Lobby') || 
                       el.textContent.includes('Room:'))?.textContent?.substring(0, 50)
        };
      });
      
      console.log('Debug info:', debugInfo);
      throw new Error('Guest failed to join room');
    }
    
    // Check host's player count updated
    const hostPlayerCount = await hostPage.evaluate(() => {
      const playerCount = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.match(/Players.*\(\d+\/\d+\)/))?.textContent;
      return playerCount;
    });
    
    console.log('Host player count after guest join:', hostPlayerCount);
    await takeScreenshot(hostPage, '05-host-with-guest', 'host');
    
    if (!hostPlayerCount || !hostPlayerCount.includes('2/')) {
      console.warn(`Warning: Host shows incorrect player count: ${hostPlayerCount}`);
    }
    
    // === Both Players Ready Up ===
    console.log('\nPlayers readying up...');
    
    // Host ready
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Ready Up' || b.textContent === 'Ready');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Guest ready
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Ready Up' || b.textContent === 'Ready');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await takeScreenshot(hostPage, '06-players-ready', 'host');
    await takeScreenshot(guestPage, '06-players-ready', 'guest');
    
    // === Start Game ===
    console.log('\nStarting game...');
    
    // Wait for Start Game button
    await waitFor(async () => {
      return await hostPage.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Start Game');
        return btn && !btn.disabled;
      });
    }, 'Start Game button', 10000);
    
    // Click Start Game
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Start Game');
      if (btn) btn.click();
    });
    
    console.log('Game started!');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === Play One Round with Timing ===
    console.log('\nPlaying round 1...');
    
    // Wait for question
    await waitFor(async () => {
      const hasQuestion = await Promise.all([
        hostPage.evaluate(() => !!document.querySelector('img[alt*="player"], img[alt*="Player"]')),
        guestPage.evaluate(() => !!document.querySelector('img[alt*="player"], img[alt*="Player"]'))
      ]);
      return hasQuestion[0] && hasQuestion[1];
    }, 'question to appear', 10000);
    
    await takeScreenshot(hostPage, '07-question', 'host');
    await takeScreenshot(guestPage, '07-question', 'guest');
    
    // Both answer quickly to test timing
    const answerTime = Date.now();
    await Promise.all([
      hostPage.evaluate(() => {
        const choices = Array.from(document.querySelectorAll('button'))
          .filter(b => b.className.includes('choice') || 
                      b.textContent.match(/^[A-Z]\./));
        if (choices[0]) choices[0].click();
      }),
      guestPage.evaluate(() => {
        const choices = Array.from(document.querySelectorAll('button'))
          .filter(b => b.className.includes('choice') || 
                      b.textContent.match(/^[A-Z]\./));
        if (choices[1]) choices[1].click();
      })
    ]);
    
    console.log('Both players answered');
    
    // Wait for round advance
    const startTime = Date.now();
    await waitFor(async () => {
      return await hostPage.evaluate(() => {
        return document.body.textContent.includes('Next question in') ||
               document.body.textContent.includes('Game Over');
      });
    }, 'round to advance', 10000);
    
    const advanceTime = Date.now() - startTime;
    const totalRoundTime = Date.now() - answerTime;
    
    console.log(`Round timing: Answered in ~${Date.now() - answerTime - advanceTime}ms, advanced in ${advanceTime}ms, total ${totalRoundTime}ms`);
    
    // Verify timing rules
    if (advanceTime < 5000) {
      console.log('✓ Fast advance detected (both answered quickly)');
    } else if (advanceTime <= 7000) {
      console.log('✓ Standard 7s max timing respected');
    } else {
      console.log(`⚠️  Round took longer than expected: ${advanceTime}ms`);
    }
    
    await takeScreenshot(hostPage, '08-round-complete', 'host');
    await takeScreenshot(guestPage, '08-round-complete', 'guest');
    
    console.log('\n✅ MULTIPLAYER TEST SUCCESSFUL!');
    console.log('- Guest successfully joined room using detail view');
    console.log('- Both players readied up');
    console.log('- Game started successfully');
    console.log(`- Round played and advanced in ${advanceTime}ms`);
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    await takeScreenshot(hostPage, 'error-host', 'host');
    await takeScreenshot(guestPage, 'error-guest', 'guest');
    
    // Additional debugging
    if (hostPage) {
      const hostDebug = await hostPage.evaluate(() => ({
        url: window.location.href,
        buttons: Array.from(document.querySelectorAll('button')).map(b => b.textContent),
        bodyText: document.body.innerText.substring(0, 500)
      }));
      console.log('\nHost debug:', hostDebug);
    }
    
    if (guestPage) {
      const guestDebug = await guestPage.evaluate(() => ({
        url: window.location.href,
        buttons: Array.from(document.querySelectorAll('button')).map(b => b.textContent),
        bodyText: document.body.innerText.substring(0, 500)
      }));
      console.log('\nGuest debug:', guestDebug);
    }
    
    throw error;
    
  } finally {
    if (hostBrowser) await hostBrowser.close();
    if (guestBrowser) await guestBrowser.close();
  }
}

// Run test
testMultiplayerFixed()
  .then(() => {
    console.log('\nTest completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\nTest failed with error:', error.message);
    process.exit(1);
  });