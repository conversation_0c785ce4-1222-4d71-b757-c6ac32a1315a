# Supabase Realtime Connection Recovery Fixes

## Problem Summary
The application was experiencing Realtime connection failures when browser tabs were backgrounded (minimized or switched away from). This is caused by browser throttling of JavaScript timers in inactive tabs, which breaks the WebSocket heartbeat mechanism that Supabase Realtime relies on.

## Root Cause
1. **Browser Throttling**: When tabs are backgrounded, browsers drastically reduce the frequency of `setTimeout` and `setInterval` callbacks to conserve resources
2. **WebSocket Heartbeat Failure**: Supabase's Realtime client relies on these timers for heartbeat messages to keep the connection alive
3. **Connection Abandonment**: When heartbeats stop, the server assumes the client disconnected and closes the connection
4. **Partial Recovery**: The existing recovery mechanism fetched data but didn't fully restore the live WebSocket connection

## Implemented Solutions

### 1. Enhanced Page Visibility Recovery
**Location**: `web-app/src/app/page.tsx` - `handleVisibilityChange` function

**Changes**:
- Added forced Supabase Realtime reconnection when tab becomes visible
- Calls `supabase.realtime.connect()` to re-establish WebSocket connection
- Includes error handling for reconnection failures
- Enhanced logging for debugging

```typescript
// **NEW: Force Supabase Realtime reconnection when tab becomes visible**
try {
  console.log('[DISCONNECT_DETECTION] Forcing Supabase Realtime reconnection...');
  supabase.realtime.connect();
  
  setTimeout(() => {
    console.log('[DISCONNECT_DETECTION] Reconnection initiated, channels should auto-resubscribe');
  }, 1000);
  
} catch (reconnectError) {
  console.error('[DISCONNECT_DETECTION] Error during forced reconnection:', reconnectError);
}
```

### 2. Redundant Fetch Prevention
**Location**: `web-app/src/app/page.tsx` - `fetchAndSetGameRooms` function

**Changes**:
- Added loading state guard to prevent multiple simultaneous fetches
- Updated dependency array to include `isLoadingRooms`
- Reduces the "three rapid calls" issue identified in the logs

```typescript
// **NEW: Prevent redundant fetches with loading state guard**
if (isLoadingRooms) {
  console.log('[LOBBY_FETCH] Fetch already in progress, skipping to prevent redundant calls');
  return;
}
```

### 3. Enhanced Recovery Mechanism
**Location**: `web-app/src/app/page.tsx` - Subscription error handlers

**Changes**:
- Added comprehensive recovery that includes both data fetching AND Realtime reconnection
- Enhanced logging with recovery method tracking
- Improved timeout handling for failed recovery attempts

**For game_players subscription**:
```typescript
// **NEW: Force Realtime reconnection as part of recovery**
console.log(`${logPrefix} [RECOVERY_ATTEMPT] Step 1: Forcing Realtime reconnection...`);
try {
  supabase.realtime.connect();
  console.log(`${logPrefix} [RECOVERY_ATTEMPT] Realtime reconnection initiated`);
} catch (reconnectError) {
  console.warn(`${logPrefix} [RECOVERY_ATTEMPT] Realtime reconnection failed, continuing with data fetch:`, reconnectError);
}

// **ENHANCED: Wait a bit for connection to establish before fetching data**
await new Promise(resolve => setTimeout(resolve, 2000));
```

**For game_rooms subscription**: Similar enhancements with connection stabilization delays.

## Testing the Fixes

### Manual Testing Steps
1. **Setup**: Join a multiplayer room with another user
2. **Background Tab**: Switch to another tab or minimize the browser
3. **Wait**: Leave the tab backgrounded for 30+ seconds
4. **Return**: Switch back to the application tab
5. **Verify**: Check that:
   - Connection status shows "Reconnecting..." briefly, then "Connected"
   - Real-time updates continue to work (other player actions are visible)
   - No "OFFLINE" status appears
   - Console shows successful reconnection logs

### Debug Console Commands
The application includes debug functions accessible via browser console:

```javascript
// Check current connection status and trigger manual recovery
window.debugConnectionRecovery()

// Simulate different connection states for testing
window.debugConnectionRecovery().actions.simulateReconnecting()
window.debugConnectionRecovery().actions.simulateConnected()
window.debugConnectionRecovery().actions.triggerRecovery()
```

## Expected Behavior After Fixes

### Before Fixes
1. Tab backgrounded → WebSocket connection lost
2. Tab returns → Data fetched successfully but connection remains broken
3. Real-time updates stop working
4. Connection status eventually shows "OFFLINE"

### After Fixes
1. Tab backgrounded → WebSocket connection lost (expected)
2. Tab returns → Automatic reconnection triggered
3. Connection re-established within 2-3 seconds
4. Real-time updates resume working
5. Connection status shows "CONNECTED"

## Additional Improvements

### Connection Status State Machine
The application uses a clear connection status system:
- `INITIALIZING`: Setting up subscriptions
- `CONNECTED`: All systems operational
- `RECONNECTING`: Temporary connection issues, attempting recovery
- `OFFLINE`: Connection failed, manual refresh may be needed

### User Experience Enhancements
- Users see a calm "Reconnecting..." message instead of error alerts
- Actions are disabled only when truly offline, not during reconnection
- Visual feedback is functionally apparent rather than technical

## Future Considerations

### Web Worker Implementation (Advanced)
For the most resilient solution, consider running Supabase Realtime in a Web Worker:
- Web Workers are not subject to the same aggressive throttling
- Requires more complex setup with `postMessage` communication
- Recommended for production-grade applications with critical real-time requirements

### Connection Monitoring
The existing heartbeat system (20-second intervals) works well with these fixes:
- Only sends heartbeats when tab is visible
- Server-side cleanup handles disconnected players
- Client-side recovery handles connection restoration
