# Deploy the robust event-based transition system with safety mechanisms
Write-Host "Deploying robust event-based transition system..." -ForegroundColor Green

# Change to supabase directory
Set-Location supabase

# Deploy the check-game-transition function
Write-Host "`n1. Deploying check-game-transition function..." -ForegroundColor Yellow
supabase functions deploy check-game-transition

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to deploy check-game-transition" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Deploy the updated transition-monitor function
Write-Host "`n2. Deploying transition-monitor function..." -ForegroundColor Yellow
supabase functions deploy transition-monitor

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to deploy transition-monitor" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Deploy the transition-safety-monitor function
Write-Host "`n3. Deploying transition-safety-monitor function..." -ForegroundColor Yellow
supabase functions deploy transition-safety-monitor

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to deploy transition-safety-monitor" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Return to root directory
Set-Location ..

Write-Host "`n✅ Robust transition system deployed successfully!" -ForegroundColor Green
Write-Host "`nThe system now includes:" -ForegroundColor Cyan
Write-Host "1. Event-based transitions - Clients remind server when transitions are due" -ForegroundColor Gray
Write-Host "2. Race condition protection - Server validates question IDs before transitioning" -ForegroundColor Gray
Write-Host "3. Safety monitor - Catches truly abandoned games (no connected players)" -ForegroundColor Gray
Write-Host "`nTo schedule the safety monitor (optional):" -ForegroundColor Yellow
Write-Host "  - Run every 30 seconds via cron job or scheduled task" -ForegroundColor Gray
Write-Host "  - Only processes games with no connected players" -ForegroundColor Gray
Write-Host "  - Minimal overhead - typically finds 0 abandoned games" -ForegroundColor Gray
Write-Host "`nNo external dependencies required for normal operation! 🎉" -ForegroundColor Green