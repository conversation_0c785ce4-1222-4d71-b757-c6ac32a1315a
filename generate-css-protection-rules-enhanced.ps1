#!/usr/bin/env pwsh

# Enhanced CSS Protection Rules Generator for Recognition Combine
# Version 2.0 Enhanced

param(
    [switch]$Force
)

Write-Host "Enhanced CSS Protection Rules Generator" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$outputFile = "CSS_PROTECTION_RULES_ENHANCED.md"

Write-Host "Updating enhanced CSS protection documentation..." -ForegroundColor Yellow
Write-Host "Timestamp: $timestamp" -ForegroundColor Cyan

# Check if the enhanced documentation exists
if (Test-Path $outputFile) {
    # Update the timestamp in the existing file
    $content = Get-Content $outputFile -Raw
    $updatedContent = $content -replace '\*\*Last Updated:\*\* \$\(Get-Date -Format "yyyy-MM-dd HH:mm:ss"\)', "**Last Updated:** $timestamp"
    
    $updatedContent | Out-File -FilePath $outputFile -Encoding UTF8 -Force
    
    Write-Host "Enhanced CSS protection documentation updated!" -ForegroundColor Green
    
    # Display file size
    $size = (Get-Item $outputFile).Length
    Write-Host "Documentation size: $([math]::Round($size/1KB, 2)) KB" -ForegroundColor Cyan
    
    Write-Host "`nENHANCED PROTECTION FEATURES:" -ForegroundColor Yellow
    Write-Host "Next.js global CSS import verification" -ForegroundColor Green
    Write-Host "Tailwind content path validation" -ForegroundColor Green
    Write-Host "Automated CSS output verification" -ForegroundColor Green
    Write-Host "Browser diagnostic guides" -ForegroundColor Green
    Write-Host "Emergency restoration protocols" -ForegroundColor Green
    Write-Host "Build-time CSS protection" -ForegroundColor Green
    
    Write-Host "`nNEXT STEPS:" -ForegroundColor Yellow
    Write-Host "1. Review documentation: $outputFile" -ForegroundColor White
    Write-Host "2. Test CSS verification: cd web-app" -ForegroundColor White
    Write-Host "3. Run: npm run verify-css" -ForegroundColor White
    Write-Host "4. Test enhanced build: npm run build" -ForegroundColor White
    
} else {
    Write-Warning "Enhanced documentation file not found: $outputFile"
    Write-Host "The file should have been created earlier in the process." -ForegroundColor Yellow
}

Write-Host "`nEnhanced CSS Protection System is ACTIVE!" -ForegroundColor Green 