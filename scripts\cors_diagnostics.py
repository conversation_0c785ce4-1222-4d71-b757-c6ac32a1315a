#!/usr/bin/env python3
"""
CORS Diagnostics Script for Supabase Edge Functions

This script helps diagnose CORS issues by:
1. Displaying the content of login-handler and CORS files
2. Testing CORS response with curl
3. Providing instructions for capturing Supabase logs
4. Ensuring proper timing for Supabase startup
"""

import os
import subprocess
import time
import json
from pathlib import Path

def print_section(title):
    """Print a formatted section header"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def read_file_content(file_path):
    """Read and display file content"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        return f"ERROR: File not found at {file_path}"
    except Exception as e:
        return f"ERROR reading file: {str(e)}"

def run_command(command, timeout=30):
    """Run a command and return the output"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return {
            'stdout': result.stdout,
            'stderr': result.stderr,
            'returncode': result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            'stdout': '',
            'stderr': f'Command timed out after {timeout} seconds',
            'returncode': -1
        }
    except Exception as e:
        return {
            'stdout': '',
            'stderr': str(e),
            'returncode': -1
        }

def main():
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    print("CORS Diagnostics Script for Supabase Edge Functions")
    print(f"Project Root: {project_root}")
    
    # Step 1: Verify and display login-handler content
    print_section("1. LOGIN-HANDLER CONTENT")
    login_handler_path = project_root / "supabase" / "functions" / "login-handler" / "index.ts"
    print(f"File: {login_handler_path}")
    print("\nContent:")
    print("-" * 40)
    login_content = read_file_content(login_handler_path)
    print(login_content)
    
    # Step 2: Verify and display CORS file content
    print_section("2. CORS CONFIGURATION CONTENT")
    cors_path = project_root / "supabase" / "functions" / "_shared" / "cors.ts"
    print(f"File: {cors_path}")
    print("\nContent:")
    print("-" * 40)
    cors_content = read_file_content(cors_path)
    print(cors_content)
    
    # Step 3: Check if Supabase is running
    print_section("3. SUPABASE STATUS CHECK")
    status_result = run_command("supabase status", timeout=10)
    print("Supabase Status:")
    print(status_result['stdout'])
    if status_result['stderr']:
        print("Errors:")
        print(status_result['stderr'])
    
    # Step 4: Test CORS with curl
    print_section("4. CORS PREFLIGHT TEST WITH CURL")
    
    # First, let's check if curl is available
    curl_check = run_command("curl --version", timeout=5)
    if curl_check['returncode'] != 0:
        print("ERROR: curl is not available. Please install curl or use PowerShell's Invoke-WebRequest")
        print("\nAlternative PowerShell command to test CORS:")
        print("Invoke-WebRequest -Uri 'http://127.0.0.1:54321/functions/v1/login-handler' -Method OPTIONS -Headers @{'Origin'='http://localhost:3000'; 'Access-Control-Request-Method'='POST'; 'Access-Control-Request-Headers'='content-type,authorization'}")
    else:
        print("Testing CORS preflight request...")
        curl_command = (
            'curl -i -X OPTIONS "http://127.0.0.1:54321/functions/v1/login-handler" '
            '-H "Origin: http://localhost:3000" '
            '-H "Access-Control-Request-Method: POST" '
            '-H "Access-Control-Request-Headers: content-type,authorization"'
        )
        
        print(f"Command: {curl_command}")
        print("\nResponse:")
        print("-" * 40)
        
        curl_result = run_command(curl_command, timeout=15)
        print("STDOUT:")
        print(curl_result['stdout'])
        if curl_result['stderr']:
            print("\nSTDERR:")
            print(curl_result['stderr'])
        print(f"\nReturn Code: {curl_result['returncode']}")
        
        # Analyze the response
        if curl_result['returncode'] == 0:
            response = curl_result['stdout'].lower()  # Convert to lowercase for case-insensitive check
            if 'access-control-allow-origin' in response:
                print("\n✅ CORS headers found in response!")
                print("The Edge Function IS properly handling OPTIONS requests.")
                print("\nSince CORS headers are present but you're still getting browser errors,")
                print("this suggests a timing or caching issue. Try the following:")
                print("1. Hard refresh the browser (Ctrl+Shift+R)")
                print("2. Clear browser cache completely")
                print("3. Try in an incognito/private window")
                print("4. Restart both Supabase and Next.js dev server")
            else:
                print("\n❌ CORS headers NOT found in response!")
                print("This indicates the Edge Function is not properly handling OPTIONS requests.")
        else:
            print("\n❌ curl command failed. This might indicate:")
            print("- Supabase is not running")
            print("- The Edge Function is not deployed")
            print("- Network connectivity issues")
    
    # Step 5: Instructions for manual testing and log capture
    print_section("5. MANUAL TESTING INSTRUCTIONS")
    
    print("""
STEP-BY-STEP TROUBLESHOOTING:

0. CHECK ENVIRONMENT VARIABLES FIRST:
   If no environment files were found above, create web-app/.env.local:
   
   echo "NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321" > web-app/.env.local
   echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" >> web-app/.env.local

1. RESTART SUPABASE (Clean slate):
   Open PowerShell in project root and run:
   
   supabase stop --no-backup
   supabase start
   
   ⏰ IMPORTANT: Wait a full 60 seconds after 'supabase start' completes 
   before testing to avoid race conditions!

2. MONITOR SUPABASE LOGS:
   Keep the terminal with 'supabase start' open and visible.
   When you test the login, watch for:
   - Requests to /functions/v1/login-handler
   - Any error messages
   - Function initialization messages

3. TEST CORS MANUALLY:
   In a NEW PowerShell window, run:
   
   curl -i -X OPTIONS "http://127.0.0.1:54321/functions/v1/login-handler" -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: POST" -H "Access-Control-Request-Headers: content-type,authorization"
   
   Look for these headers in the response:
   - Access-Control-Allow-Origin: *
   - Access-Control-Allow-Headers: authorization, x-client-info, apikey, content-type
   - Access-Control-Allow-Methods: POST, OPTIONS

4. TEST FROM BROWSER:
   - Clear browser cache (Ctrl+Shift+R)
   - Open browser dev tools (F12)
   - Go to Network tab
   - Attempt login
   - Check the OPTIONS request to login-handler
   - Verify response headers

5. CAPTURE LOGS:
   When testing, copy and save:
   - The complete curl response from step 3
   - All output from the Supabase terminal during login attempt
   - Browser network tab details for the OPTIONS request
""")
    
    # Step 6: Environment verification
    print_section("6. ENVIRONMENT VERIFICATION")
    
    # Check if we're in the right directory
    package_json = project_root / "package.json"
    if package_json.exists():
        print("✅ Found package.json - we're in the right directory")
    else:
        print("❌ package.json not found - make sure you're in the project root")
    
    # Check Supabase config
    supabase_config = project_root / "supabase" / "config.toml"
    if supabase_config.exists():
        print("✅ Found supabase/config.toml")
    else:
        print("❌ supabase/config.toml not found")
    
    # Check if login-handler directory exists
    if login_handler_path.parent.exists():
        print("✅ login-handler directory exists")
        files_in_dir = list(login_handler_path.parent.glob("*"))
        print(f"   Files: {[f.name for f in files_in_dir]}")
    else:
        print("❌ login-handler directory not found")
    
    # Check for environment files in web-app directory
    web_app_dir = project_root / "web-app"
    env_files = [
        web_app_dir / ".env.local",
        web_app_dir / ".env",
        web_app_dir / ".env.development"
    ]
    
    env_found = False
    for env_file in env_files:
        if env_file.exists():
            print(f"✅ Found environment file: {env_file.name}")
            env_found = True
            # Try to read and check for NEXT_PUBLIC_SUPABASE_URL
            try:
                with open(env_file, 'r') as f:
                    content = f.read()
                if 'NEXT_PUBLIC_SUPABASE_URL' in content:
                    print("   ✅ Contains NEXT_PUBLIC_SUPABASE_URL")
                    # Extract the URL value
                    for line in content.split('\n'):
                        if line.startswith('NEXT_PUBLIC_SUPABASE_URL='):
                            url = line.split('=', 1)[1].strip()
                            print(f"   📍 URL: {url}")
                            if url != "http://127.0.0.1:54321":
                                print("   ⚠️  URL doesn't match Supabase local URL!")
                else:
                    print("   ❌ Missing NEXT_PUBLIC_SUPABASE_URL")
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
    
    if not env_found:
        print("❌ No environment files found in web-app directory!")
        print("   This is likely the cause of your CORS issue!")
        print("   The frontend can't find the Supabase URL.")
        print("\n   🔧 SOLUTION: Create web-app/.env.local with:")
        print("   NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321")
        print("   NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0")
    
    print_section("7. BROWSER-SPECIFIC TROUBLESHOOTING")
    print("""
If curl shows CORS headers but browser still fails:

1. BROWSER CACHE ISSUES:
   - Hard refresh: Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)
   - Clear all browser data for localhost
   - Try incognito/private browsing mode
   - Try a different browser entirely

2. TIMING ISSUES:
   - Ensure Supabase has been running for at least 60 seconds
   - Restart Next.js dev server: Ctrl+C then npm run dev
   - Wait 10-15 seconds between stopping and starting services

3. NETWORK ISSUES:
   - Check if localhost:3000 can reach 127.0.0.1:54321
   - Try using localhost instead of 127.0.0.1 in your frontend code
   - Or vice versa - try 127.0.0.1 instead of localhost

4. BROWSER DEV TOOLS DEBUGGING:
   - Open F12 dev tools
   - Go to Network tab
   - Clear network log
   - Attempt login
   - Look for the OPTIONS request to login-handler
   - Check if it shows "CORS error" or if it gets a 200 response
   - If 200 response, check the Response Headers tab for CORS headers
""")

    print_section("8. NEXT STEPS")
    print("""
If CORS is still not working after following all steps:

1. Check that the login-handler/index.ts file shown above has:
   - The OPTIONS handling block at the beginning
   - Proper import of corsHeaders
   - corsHeaders included in all responses

2. Verify the _shared/cors.ts file has the correct headers

3. Try recreating the Edge Function:
   - Delete supabase/functions/login-handler directory
   - Recreate it with the exact content shown above
   - Restart Supabase

4. Check Supabase CLI version:
   supabase --version
   
   Consider updating if it's old:
   npm install -g @supabase/cli@latest

5. If all else fails, try deploying to Supabase cloud to test:
   supabase functions deploy login-handler

6. LAST RESORT - Check frontend code:
   - Verify the fetch request is going to the correct URL
   - Check if any custom headers are being sent that aren't allowed
   - Try a simple test request without authentication headers
""")

if __name__ == "__main__":
    main() 