# Multiplayer Transition Timing Fix

## Overview
Fixed the transition timing between rounds in multiplayer games to be exactly 3 seconds after the last person submits OR 7 seconds total (whichever comes first).

## Changes Made

### 1. Database Schema
- Added `question_started_at` column to `game_rooms` table
- Migration: `supabase/migrations/20250630100000_add_question_started_at.sql`

### 2. Edge Functions
- **start-game-handler**: Sets `question_started_at` when game starts
- **next-question-handler**: Sets `question_started_at` when advancing to next question
- **submit-answer-handler**: Already sets `transition_until` and pre-generates `next_question_data`

### 3. Client-Side Logic (web-app/src/app/page.tsx)
**Fixed duplicate 3-second delay issue** by using server-controlled timing:
- **Server sets `transition_until`**: When all players answer, server sets transition to 3 seconds
- **Client monitors `transition_until`**: No additional client-side delay
- **Uses pre-generated question**: Applies `next_question_data` without API call
- **7-second hard cap**: Still enforced from `question_started_at`

### 4. TypeScript Types
- Added `question_started_at: string | null` to GameRoom interface
- Added `transition_until: string | null` to useMultiplayerRoom interface
- Added `next_question_data: any | null` to useMultiplayerRoom interface

## How It Works

1. When last player answers, server:
   - Sets `transition_until` to 3 seconds from now
   - Pre-generates next question in `next_question_data`
   
2. Client monitors `transition_until`:
   - When timestamp expires, applies `next_question_data` directly
   - No API call needed, no additional delay
   
3. 7-second hard cap still applies if not all players answer

## Fixed Issues

- **Before**: ~6 second delay (3s server + 3s client + network)
- **After**: Exactly 3 seconds after last answer

## Deployment

Run the deployment script:
```powershell
.\deploy-timing-fix.ps1
```

This will:
1. Apply the database migration
2. Deploy updated edge functions

## Testing

1. **Test 3-second transition**: All players answer - should advance exactly 3s after last answer
2. **Test 7-second hard cap**: Don't answer - should advance after 7s from question start
3. **Test edge case**: Answer at 6s - should advance at 9s (6s + 3s)