/**
 * Final Automated Multiplayer Test with Rob<PERSON> Error Handling
 * Tests round advance timing with two players
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Test configuration
const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → Round at 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → Round at 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → Round at 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'Only P1 → Round at 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1 at 1s, P2 at 3s → Round at 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

// Create screenshots directory
const screenshotsDir = path.join(__dirname, 'test-screenshots');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

let screenshotCounter = 0;

// Helper functions
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

async function takeScreenshot(page, name) {
  const filename = `${String(screenshotCounter++).padStart(3, '0')}-${name}.png`;
  const filepath = path.join(screenshotsDir, filename);
  await page.screenshot({ path: filepath, fullPage: true });
  console.log(`  📸 Screenshot saved: ${filename}`);
}

async function findChromePath() {
  const possiblePaths = [
    '/usr/bin/chromium-browser',
    '/usr/bin/chromium',
    '/usr/bin/google-chrome-stable',
    '/usr/bin/google-chrome',
    '/snap/bin/chromium',
    process.env.PUPPETEER_EXECUTABLE_PATH,
    // Try WSL Chrome paths
    '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe',
    '/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe'
  ].filter(Boolean);

  for (const chromePath of possiblePaths) {
    if (fs.existsSync(chromePath)) {
      console.log(`  ✅ Found Chrome at: ${chromePath}`);
      return chromePath;
    }
  }
  
  // If no Chrome found, try to use Puppeteer's bundled Chrome
  console.log('  ⚠️  No system Chrome found, using Puppeteer bundled Chrome');
  return undefined;
}

async function waitAndClick(page, selector, description, options = {}) {
  console.log(`  Waiting for ${description}...`);
  try {
    await page.waitForSelector(selector, { timeout: 10000, ...options });
    await page.click(selector);
    console.log(`  ✅ Clicked ${description}`);
    return true;
  } catch (error) {
    console.log(`  ❌ Failed to click ${description}: ${error.message}`);
    return false;
  }
}

async function waitAndType(page, selector, text, description) {
  console.log(`  Typing ${description}...`);
  try {
    await page.waitForSelector(selector, { timeout: 10000 });
    await page.click(selector);
    await page.type(selector, text);
    console.log(`  ✅ Typed ${description}`);
    return true;
  } catch (error) {
    console.log(`  ❌ Failed to type ${description}: ${error.message}`);
    return false;
  }
}

async function signIn(page, credentials, playerName) {
  console.log(`\n🔐 Signing in ${playerName}...`);
  
  await takeScreenshot(page, `${playerName}-before-signin`);
  
  // Click Login button using text content
  const loginClicked = await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const loginBtn = buttons.find(b => b.textContent && b.textContent.includes('Login'));
    if (loginBtn) {
      loginBtn.click();
      return true;
    }
    return false;
  });
  
  if (!loginClicked) {
    throw new Error(`Failed to find login button for ${playerName}`);
  }
  
  await delay(2000);
  await takeScreenshot(page, `${playerName}-login-modal`);
  
  // Fill credentials
  await waitAndType(page, 'input[placeholder="Username or Email"]', credentials.email, 'email');
  await waitAndType(page, 'input[placeholder="Password"]', credentials.password, 'password');
  
  await takeScreenshot(page, `${playerName}-credentials-filled`);
  
  // Submit form
  await page.evaluate(() => {
    const form = document.querySelector('form');
    if (form) form.requestSubmit();
  });
  
  console.log(`  ⏳ Waiting for authentication...`);
  await delay(5000);
  
  // Verify signed in
  const signedIn = await page.evaluate(() => {
    const userText = Array.from(document.querySelectorAll('*')).find(el => 
      el.textContent && el.textContent.includes('User:')
    );
    return !!userText;
  });
  
  if (signedIn) {
    console.log(`  ✅ ${playerName} signed in successfully`);
    await takeScreenshot(page, `${playerName}-signed-in`);
  } else {
    console.log(`  ⚠️  ${playerName} sign-in status unclear`);
  }
}

async function navigateToMultiplayer(page, playerName) {
  console.log(`\n🎮 ${playerName} navigating to multiplayer...`);
  
  const clicked = await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const mpBtn = buttons.find(b => b.textContent === 'Multiplayer Mode');
    if (mpBtn) {
      mpBtn.click();
      return true;
    }
    return false;
  });
  
  if (!clicked) {
    throw new Error(`Failed to find Multiplayer Mode button for ${playerName}`);
  }
  
  await delay(3000);
  await takeScreenshot(page, `${playerName}-multiplayer-mode`);
}

async function createRoom(page) {
  console.log('\n🏠 Creating room...');
  
  await takeScreenshot(page, 'before-create-room');
  
  // Find and click Host Game button
  const hostClicked = await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const hostBtn = buttons.find(b => b.textContent && b.textContent.trim() === 'Host Game');
    if (hostBtn) {
      hostBtn.click();
      return true;
    }
    return false;
  });
  
  if (!hostClicked) {
    // Log available buttons for debugging
    const buttons = await page.evaluate(() => {
      return Array.from(document.querySelectorAll('button')).map(b => b.textContent);
    });
    console.log('  Available buttons:', buttons);
    throw new Error('Failed to find Host Game button');
  }
  
  console.log('  ⏳ Waiting for room creation...');
  await delay(3000);
  
  await takeScreenshot(page, 'after-create-room');
  
  // Get room code
  const roomCode = await page.evaluate(() => {
    // Look for 6-character uppercase alphanumeric code
    const elements = Array.from(document.querySelectorAll('*'));
    for (const el of elements) {
      const text = el.textContent || '';
      if (text.match(/^[A-Z0-9]{6}$/) && el.children.length === 0) {
        return text;
      }
    }
    return null;
  });
  
  if (!roomCode) {
    // Try alternative method
    const altCode = await page.evaluate(() => {
      const codeEl = document.querySelector('[class*="room-code"]');
      return codeEl ? codeEl.textContent : null;
    });
    
    if (altCode) {
      return altCode;
    }
  }
  
  return roomCode;
}

async function joinRoom(page, roomCode) {
  console.log(`\n🏠 Joining room ${roomCode}...`);
  
  await takeScreenshot(page, 'before-join-room');
  
  // Click Join Room
  const joinClicked = await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const joinBtn = buttons.find(b => b.textContent === 'Join Room');
    if (joinBtn) {
      joinBtn.click();
      return true;
    }
    return false;
  });
  
  if (!joinClicked) {
    throw new Error('Failed to find Join Room button');
  }
  
  await delay(1500);
  await takeScreenshot(page, 'join-room-modal');
  
  // Enter room code
  await waitAndType(page, 'input[placeholder*="room code" i]', roomCode, 'room code');
  
  // Submit
  await page.evaluate(() => {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) submitBtn.click();
  });
  
  await delay(3000);
  await takeScreenshot(page, 'after-join-room');
}

// Monitoring code
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0,
    submissions: []
  };
  
  console.log('🔍 Monitoring code injected');
  
  let lastQuestion = null;
  setInterval(() => {
    const questionEl = document.querySelector('h2');
    const question = questionEl?.textContent || '';
    
    if (question && question.includes('?') && question !== lastQuestion) {
      const now = Date.now();
      
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('🎮 Game started at', new Date(now).toISOString());
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({
          round: window.testData.currentRound++,
          time: elapsed,
          question: question.substring(0, 50)
        });
        console.log('📍 Round ' + (window.testData.currentRound) + ' advanced at ' + elapsed + 'ms');
      }
      
      lastQuestion = question;
    }
  }, 100);
  
  window.submitAnswerAt = function(targetMs) {
    return new Promise((resolve) => {
      if (!window.testData.gameStartTime) {
        console.error('Game not started yet');
        resolve(false);
        return;
      }
      
      const elapsed = Date.now() - window.testData.gameStartTime;
      const delay = targetMs - elapsed;
      
      const submit = () => {
        const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
          const text = b.textContent || '';
          // Look for player name buttons (typically 4 choices)
          return text.length > 0 && 
                 text.match(/^[A-Za-z\\s\\.]+$/) && 
                 !text.includes('Ready') && 
                 !text.includes('Start') &&
                 !text.includes('Mode') &&
                 !text.includes('Sign') &&
                 !text.includes('Create') &&
                 !text.includes('Join') &&
                 b.offsetParent !== null;
        });
        
        console.log('Found ' + buttons.length + ' answer buttons');
        
        if (buttons.length >= 4) {
          const chosen = buttons[Math.floor(Math.random() * 4)];
          const submitTime = Date.now() - window.testData.gameStartTime;
          console.log('✅ Submitting answer: ' + chosen.textContent + ' at ' + submitTime + 'ms');
          window.testData.submissions.push({
            time: submitTime,
            answer: chosen.textContent
          });
          chosen.click();
          resolve(true);
        } else {
          console.error('Not enough answer buttons found');
          resolve(false);
        }
      };
      
      if (delay > 0) {
        console.log('⏰ Scheduling answer submission in ' + delay + 'ms');
        setTimeout(submit, delay);
      } else {
        submit();
      }
    });
  };
`;

async function runTest() {
  console.log('🎯 Final Automated Multiplayer Test');
  console.log('===================================\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Find Chrome executable
    console.log('🔍 Checking Chrome installation...');
    const chromePath = await findChromePath();
    
    // Browser options
    const browserOptions = {
      headless: false,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=IsolateOrigins,site-per-process',
        '--window-size=1000,800'
      ]
    };
    
    if (chromePath) {
      browserOptions.executablePath = chromePath;
    }
    
    // Launch browsers
    console.log('\n🚀 Launching browsers...');
    
    try {
      browser1 = await puppeteer.launch({
        ...browserOptions,
        args: [...browserOptions.args, '--window-position=0,0']
      });
      console.log('  ✅ Browser 1 launched');
    } catch (error) {
      console.error('  ❌ Failed to launch Browser 1:', error.message);
      throw error;
    }
    
    try {
      browser2 = await puppeteer.launch({
        ...browserOptions,
        args: [...browserOptions.args, '--window-position=1000,0']
      });
      console.log('  ✅ Browser 2 launched');
    } catch (error) {
      console.error('  ❌ Failed to launch Browser 2:', error.message);
      throw error;
    }
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    
    // Set viewport
    await page1.setViewport({ width: 1000, height: 800 });
    await page2.setViewport({ width: 1000, height: 800 });
    
    // Navigate to game
    console.log('\n📱 Loading game...');
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2', timeout: 30000 }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2', timeout: 30000 })
    ]);
    console.log('  ✅ Both browsers loaded the game');
    
    await delay(3000);
    
    // Inject monitoring code
    console.log('\n💉 Injecting monitoring code...');
    await page1.evaluate(monitoringCode);
    await page2.evaluate(monitoringCode);
    console.log('  ✅ Monitoring code injected in both browsers');
    
    // Sign in both players
    await signIn(page1, CONFIG.player1, 'Player 1');
    await signIn(page2, CONFIG.player2, 'Player 2');
    
    // Navigate to multiplayer
    await navigateToMultiplayer(page1, 'Player 1');
    await navigateToMultiplayer(page2, 'Player 2');
    
    // Create and join room
    const roomCode = await createRoom(page1);
    if (!roomCode) {
      throw new Error('Failed to create room - no room code found');
    }
    console.log(`  ✅ Room created: ${roomCode}`);
    
    await joinRoom(page2, roomCode);
    console.log('  ✅ Player 2 joined room');
    
    // Ready up and start game
    console.log('\n🎮 Starting game...');
    
    // Both players ready up
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready'
      );
      if (btn) btn.click();
    });
    
    await page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready'
      );
      if (btn) btn.click();
    });
    
    await delay(2000);
    await takeScreenshot(page1, 'both-ready');
    
    // Host starts game
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Start Game'
      );
      if (btn) btn.click();
    });
    
    await delay(3000);
    await takeScreenshot(page1, 'game-started');
    console.log('  ✅ Game started!');
    
    // Run test scenarios
    console.log('\n📊 Running test scenarios:');
    console.log('========================\n');
    
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const scenario = CONFIG.scenarios[i];
      console.log(`Test ${i + 1}: ${scenario.name}`);
      
      // Schedule submissions
      const submissions = [];
      
      if (scenario.p1 !== null) {
        submissions.push(page1.evaluate(`window.submitAnswerAt(${scenario.p1})`));
        console.log(`  Player 1 will answer at ${scenario.p1}ms`);
      }
      
      if (scenario.p2 !== null) {
        submissions.push(page2.evaluate(`window.submitAnswerAt(${scenario.p2})`));
        console.log(`  Player 2 will answer at ${scenario.p2}ms`);
      }
      
      // Wait for submissions
      await Promise.all(submissions);
      
      console.log('  ⏳ Waiting for round to advance...');
      await delay(8000);
      
      // Get results
      const [timing1, timing2] = await Promise.all([
        page1.evaluate((index) => {
          const changes = window.testData.roundChanges || [];
          return changes[index];
        }, i),
        page2.evaluate((index) => {
          const changes = window.testData.roundChanges || [];
          return changes[index];
        }, i)
      ]);
      
      if (timing1 || timing2) {
        const actualTime = timing1?.time || timing2?.time;
        const diff = Math.abs(actualTime - scenario.expected);
        const passed = diff <= 500;
        
        console.log(`  Expected: ${scenario.expected}ms`);
        console.log(`  Actual: ${actualTime}ms`);
        console.log(`  Difference: ${diff}ms`);
        console.log(`  Result: ${passed ? '✅ PASS' : '❌ FAIL'}\n`);
        
        results.push({ 
          ...scenario, 
          actual: actualTime, 
          difference: diff,
          passed 
        });
      } else {
        console.log('  ❌ No round change detected\n');
        results.push({ 
          ...scenario, 
          actual: null, 
          difference: null,
          passed: false 
        });
      }
      
      await takeScreenshot(page1, `test-${i + 1}-complete`);
    }
    
    // Final summary
    console.log('\n📈 Test Summary');
    console.log('==============');
    const passed = results.filter(r => r.passed).length;
    const failed = results.filter(r => !r.passed).length;
    
    console.log(`Total Tests: ${results.length}`);
    console.log(`Passed: ${passed} ✅`);
    console.log(`Failed: ${failed} ❌`);
    console.log(`Success Rate: ${((passed/results.length) * 100).toFixed(1)}%\n`);
    
    console.log('Detailed Results:');
    results.forEach((r, i) => {
      console.log(`${i + 1}. ${r.name}`);
      if (r.actual !== null) {
        console.log(`   Expected: ${r.expected}ms, Actual: ${r.actual}ms, Diff: ${r.difference}ms`);
      }
      console.log(`   Status: ${r.passed ? '✅ PASS' : '❌ FAIL'}`);
    });
    
    console.log(`\n📸 Screenshots saved to: ${screenshotsDir}`);
    console.log('\n✅ Test complete! Browsers will remain open for inspection.');
    console.log('Press Ctrl+C to close browsers and exit.\n');
    
    // Keep process alive
    process.on('SIGINT', async () => {
      console.log('\n🔚 Closing browsers...');
      if (browser1) await browser1.close();
      if (browser2) await browser2.close();
      process.exit(0);
    });
    
    // Keep running
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
    
    // Take error screenshots if possible
    try {
      if (page1) await takeScreenshot(page1, 'error-page1');
      if (page2) await takeScreenshot(page2, 'error-page2');
    } catch (e) {
      console.error('Failed to take error screenshots:', e.message);
    }
    
    // Clean up
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    
    process.exit(1);
  }
}

// Run the test
console.log('Starting automated test...\n');
runTest().catch(console.error);