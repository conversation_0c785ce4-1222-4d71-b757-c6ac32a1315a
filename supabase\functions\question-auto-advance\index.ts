import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

console.log('[EDGE_FN_LOAD] question-auto-advance function script loaded.');

serve(async (req: Request) => {
  console.log(`[AUTO_ADVANCE] Request received. Method: ${req.method}, URL: ${req.url}`);
  
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // This function is meant to be called by a scheduler (like Supabase's pg_cron)
    // It doesn't require authentication since it's an internal process
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)
    
    console.log('[AUTO_ADVANCE] Checking for rooms that need question advancement...');
    
    // Find all active rooms where the question has been active for more than 7 seconds
    const sevenSecondsAgo = new Date(Date.now() - 7000).toISOString();
    
    const { data: roomsToAdvance, error: fetchError } = await supabaseAdmin
      .from('game_rooms')
      .select('*, game_players(user_id)')
      .eq('status', 'active')
      .not('current_question_data', 'is', null)
      .not('question_started_at', 'is', null)
      .lt('question_started_at', sevenSecondsAgo)
      .is('transition_until', null);
    
    if (fetchError) {
      console.error('[AUTO_ADVANCE] Error fetching rooms:', fetchError);
      return new Response(JSON.stringify({ 
        error: 'Failed to fetch rooms',
        details: fetchError.message
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }
    
    console.log(`[AUTO_ADVANCE] Found ${roomsToAdvance?.length || 0} rooms to potentially advance`);
    
    let advanced = 0;
    
    for (const room of roomsToAdvance || []) {
      try {
        console.log(`[AUTO_ADVANCE] Processing room ${room.id}...`);
        
        // Get the current question ID and player count
        const currentQuestionId = room.current_question_data?.questionId;
        const playerCount = room.game_players?.length || 0;
        
        if (!currentQuestionId) {
          console.log(`[AUTO_ADVANCE] Room ${room.id} has no current question, skipping`);
          continue;
        }
        
        // Count answers for the current question
        const currentAnswers = Array.isArray(room.current_round_answers) 
          ? room.current_round_answers 
          : [];
        const answersForCurrentQuestion = currentAnswers.filter((answer: any) => 
          answer.questionId === currentQuestionId
        );
        
        console.log(`[AUTO_ADVANCE] Room ${room.id}: ${answersForCurrentQuestion.length}/${playerCount} players answered`);
        
        // Load player data for question generation
        const { data: playersData, error: playersError } = await supabaseAdmin
          .from('players_data')
          .select('*')
          .not('local_image_path', 'is', null)
          .limit(100);
        
        if (playersError || !playersData || playersData.length < 4) {
          console.error(`[AUTO_ADVANCE] Failed to load player data for room ${room.id}`);
          continue;
        }
        
        // Select random correct player
        const correctPlayer = playersData[Math.floor(Math.random() * playersData.length)];
        
        // Select 3 random distractors (different from correct player)
        const distractors = playersData
          .filter(p => p.id !== correctPlayer.id)
          .sort(() => Math.random() - 0.5)
          .slice(0, 3);
        
        // Create choices array
        const choices = [
          { name: correctPlayer.player_name, isCorrect: true },
          ...distractors.map(p => ({ name: p.player_name, isCorrect: false }))
        ].sort(() => Math.random() - 0.5); // Shuffle choices
        
        // Generate next question with real player data
        const nextQuestion = {
          questionId: crypto.randomUUID(),
          correctPlayerId: correctPlayer.id,
          imageUrl: correctPlayer.local_image_path ? `/players_images/${correctPlayer.local_image_path}` : '/images/placeholder.jpg',
          choices: choices,
          correctChoiceName: correctPlayer.player_name
        };
        
        // Update the room with optimistic concurrency control
        const { data: updateResult, error: updateError } = await supabaseAdmin
          .from('game_rooms')
          .update({
            current_question_data: nextQuestion,
            current_round_answers: [],
            current_round_number: (room.current_round_number || 1) + 1,
            question_sequence: (room.question_sequence || 0) + 1,
            question_started_at: new Date().toISOString(),
            last_activity_timestamp: new Date().toISOString()
          })
          .eq('id', room.id)
          .eq('status', 'active')
          .eq('question_sequence', room.question_sequence || 0) // Prevent race conditions
          .select();
        
        if (updateError) {
          console.error(`[AUTO_ADVANCE] Failed to update room ${room.id}:`, updateError);
          continue;
        }
        
        if (!updateResult || updateResult.length === 0) {
          console.log(`[AUTO_ADVANCE] Room ${room.id} was already updated by another process`);
          continue;
        }
        
        console.log(`[AUTO_ADVANCE] Successfully advanced room ${room.id} to next question`);
        advanced++;
        
      } catch (roomError) {
        console.error(`[AUTO_ADVANCE] Error processing room ${room.id}:`, roomError);
      }
    }
    
    return new Response(JSON.stringify({ 
      message: 'Auto-advance check complete',
      roomsChecked: roomsToAdvance?.length || 0,
      roomsAdvanced: advanced,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
    
  } catch (error: any) {
    console.error('[AUTO_ADVANCE] Unexpected error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});