=== Starting Multiplayer Game Flow Test ===
Testing at: http://localhost:3002
✓ Testing on port 3002
Detected WSL environment, using system chromium-browser
Running in headless mode
Using browser at: /usr/bin/chromium-browser

[HOST] Navigating to app...
[HOST Browser] [Realtime] Realtime subscription effect triggered <PERSON><PERSON><PERSON><PERSON><PERSON>@object
[HOST Browser] [Realtime] Skipping subscription setup - auth still loading
[HOST Browser] [Realtime] Realtime subscription effect triggered <PERSON><PERSON><PERSON><PERSON><PERSON>@object
[HOST Browser] [Realtime] Skipping subscription setup - auth still loading
[HOST Browser] [Realtime] Realtime subscription effect triggered J<PERSON><PERSON><PERSON><PERSON>@object
[HOST Browser] [Realtime] No user or no supabase client - cleaning up all channels
[host] Screenshot saved: test-screenshots-game-flow/host-01-initial-load-2025-07-08T22-19-27-846Z.png

[GUEST] Navigating to app...
[GUEST Browser] [Realtime] Realtime subscription effect triggered J<PERSON><PERSON><PERSON><PERSON>@object
[GUEST Browser] [Realtime] Skipping subscription setup - auth still loading
[GUEST Browser] [Realtime] Realtime subscription effect triggered <PERSON><PERSON><PERSON><PERSON><PERSON>@object
[GUEST Browser] [Realtime] Skipping subscription setup - auth still loading
[GUEST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[GUEST Browser] [Realtime] No user or no supabase client - cleaning up all channels
[guest] Screenshot saved: test-screenshots-game-flow/guest-01-initial-load-2025-07-08T22-19-31-555Z.png
Waiting for app to fully initialize...

=== PHASE 1: Authentication ===
[HOST] Checking authentication status...
[HOST] Waiting for page to finish loading...
[HOST] Page loaded, checking auth state
[HOST] Not logged in, looking for Multiplayer Mode button...
[HOST] Page info: {
  bodyText: 'Login / Sign Up\n' +
    'Timed Mode\n' +
    'RECOGNITION COMBINE\n' +
    'Normal Mode\n' +
    'Single-player Mode\n' +
    'Multiplayer Mode\n' +
    'Score\n' +
    '0\n' +
    '\n' +
    'Streak: 0\n' +
    '\n' +
    'Best Streak: 0\n' +
    '\n' +
    'Best Normal: 0\n' +
    '\n' +
    'Best Timed: 0\n' +
    '\n' +
    'Recent Answers\n' +
    '\n' +
    'No answers yet.\n' +
    '\n' +
    "WHO'S THIS ACTIVE NFL PLAYER?\n" +
    'Max Melton\n' +
    'Quinton Bohanna\n' +
    'Danny Striggow\n' +
    'Brandon Graham\n' +
    'Player Info\n' +
    '\n' +
    'Select a player to view details',
  buttonTexts: [
    'Login / Sign Up',
    'Timed Mode',
    'Normal Mode',
    'Single-player Mode',
    'Multiplayer Mode',
    'Max Melton',
    'Quinton Bohanna',
    'Danny Striggow',
    'Brandon Graham'
  ],
  hasLoadingText: false,
  visibleElements: 16
}
[HOST] Clicked Multiplayer Mode, waiting for auth modal...
[host] Screenshot saved: test-screenshots-game-flow/host-02-after-multiplayer-click-2025-07-08T22-19-36-948Z.png
[HOST] Modal check: { hasModal: false, inputCount: 2, inputNames: [ 'text', 'password' ] }
[HOST] Auth form is visible
[HOST] Filling in credentials...
[HOST] Credentials entered
[host] Screenshot saved: test-screenshots-game-flow/host-03-credentials-entered-2025-07-08T22-19-38-206Z.png
[HOST] Looking for submit button...
[HOST] Clicked submit button
[host] Screenshot saved: test-screenshots-game-flow/host-02-after-submit-2025-07-08T22-19-41-334Z.png
[HOST] ✓ Logged in successfully - auth modal closed and game visible
[host] Screenshot saved: test-screenshots-game-flow/host-02-logged-in-2025-07-08T22-19-41-513Z.png

[GUEST] Checking authentication status...
[GUEST] Page loaded, checking auth state
[GUEST] Not logged in, clicking Multiplayer Mode to trigger auth modal...
[GUEST] Clicked Multiplayer Mode, waiting for auth modal...
[GUEST] Auth form is visible
[GUEST] Filling in credentials...
[GUEST] Credentials entered
[guest] Screenshot saved: test-screenshots-game-flow/guest-03-credentials-entered-2025-07-08T22-19-43-817Z.png
[GUEST] Looking for submit button...
[GUEST] ✓ Logged in successfully - auth modal closed and game visible
[guest] Screenshot saved: test-screenshots-game-flow/guest-02-logged-in-2025-07-08T22-19-46-013Z.png

=== PHASE 2: Create and Join Room ===
[HOST] Switching to multiplayer mode...
[host] Screenshot saved: test-screenshots-game-flow/host-03-multiplayer-mode-2025-07-08T22-19-48-188Z.png
[HOST] Need to sign in again for multiplayer mode...
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW GLOBAL subscriptions for user in lobby
[HOST] Signed in for multiplayer
[host] Screenshot saved: test-screenshots-game-flow/host-03-multiplayer-signed-in-2025-07-08T22-19-53-931Z.png
[HOST] Waiting for multiplayer lobby to load...
[HOST] Creating game room...
[HOST Browser] [Client] [CREATE_ROOM] handleCreateRoom called
[HOST Browser] [Client] [CREATE_ROOM] User 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 attempting to create room
[HOST Browser] [Client] [CREATE_ROOM] Checking for existing rooms hosted by user 6b6b4242-a0b8-4f36-8811-14bc815c0ef3...
[HOST Browser] [Realtime] Error subscribing to lobby channel
[HOST Browser] [Realtime] Handling channel error - attempting recovery
[HOST Browser] [Realtime] Attempting retry 1/5 after timeout JSHandle@object
[HOST Browser] [Client] [CREATE_ROOM] Found 1 existing rooms to analyze: JSHandle@array
[HOST Browser] [Client] [CREATE_ROOM] Found 1 stale rooms to clean up
[HOST Browser] [Client] [CREATE_ROOM] Analyzing room for cleanup: 2ee3d5ab-00ac-44e1-930d-a5a4026abab4 (fresh's Game)
[HOST Browser] [Client] [CREATE_ROOM] Room 2ee3d5ab-00ac-44e1-930d-a5a4026abab4 analysis: JSHandle@object
[HOST Browser] [Client] [CREATE_ROOM] Processing room 2ee3d5ab-00ac-44e1-930d-a5a4026abab4 for host departure...
[HOST Browser] [Client] [CREATE_ROOM] Removing host from active room 2ee3d5ab-00ac-44e1-930d-a5a4026abab4 with 2 other players...
[HOST Browser] [Client] [CREATE_ROOM] ✅ Removed host from room 2ee3d5ab-00ac-44e1-930d-a5a4026abab4. Game continues with other players.
[HOST Browser] [Client] [CREATE_ROOM] Promoting player 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 to host of room 2ee3d5ab-00ac-44e1-930d-a5a4026abab4...
[HOST Browser] [Client] [CREATE_ROOM] ✅ Promoted player 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 to host of room 2ee3d5ab-00ac-44e1-930d-a5a4026abab4
[HOST Browser] [Client] [CREATE_ROOM] Refreshing lobby after cleanup...
[HOST Browser] [Realtime] Executing scheduled retry
[HOST Browser] [Client] [CREATE_ROOM] Creating new room...
[HOST Browser] [Client] [CREATE_ROOM] Inserting new room with data: JSHandle@object
[HOST Browser] [Realtime] Cleaning up channel: realtime:lobby-global
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW GLOBAL subscriptions for user in lobby
[HOST Browser] [Client] [CREATE_ROOM] *** ROOM CREATED SUCCESSFULLY *** {"id":"072320d9-91ca-479b-8867-ea68eb0614a5","created_at":"2025-07-08T22:19:55.504+00:00","status":"waiting","host_id":"6b6b4242-a0b8-4f36-8811-14bc815c0ef3","current_question_data":null,"multiplayer_mode":"competitive","room_code":null,"title":"fresh's Game","game_duration_seconds":300,"max_players":4,"current_round_number":1,"current_bonus_level":0,"current_turn_user_id":null,"game_state_details":null,"game_start_timestamp":null,"game_end_timestamp_target":null,"player_scores":{},"current_round_ends_at":null,"game_ended_at":null,"last_activity_timestamp":"2025-07-08T22:19:55.504+00:00","current_round_answers":[],"current_question_ends_at":null,"first_correct_answer_timestamp":null,"qualified_for_bonus":[],"updated_at":"2025-07-08T22:19:55.121412+00:00","asked_q0_player_ids":[],"original_player_ids":[],"player_bonus_levels":{},"transition_until":null,"next_question_data":null,"question_started_at":null,"question_sequence":0,"transition_deadline":null}
[HOST Browser] [Client] [CREATE_ROOM] *** ABOUT TO CALL HANDLEJOINROOM *** with room ID: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Client] *** HANDLEJOINROOM ENTRY *** handleJoinRoomAttempt called for room: 072320d9-91ca-479b-8867-ea68eb0614a5. Current activeRoomId: null, User: 6b6b4242-a0b8-4f36-8811-14bc815c0ef3
[HOST Browser] [Client] *** CRITICAL DEBUG *** Room ID analysis: JSHandle@object
[HOST Browser] [Client] Joining room - React useEffect hooks will handle subscription transitions
[HOST Browser] [Client] *** STARTING JOIN PROCESS *** User 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 attempting to join/rejoin room: 072320d9-91ca-479b-8867-ea68eb0614a5 via server-side logic.
[HOST Browser] [Client] *** STANDARD JOIN/REJOIN *** Performing full validation checks.
[HOST Browser] [Client] *** STEP 1: ROOM VALIDATION *** Fetching room details for validation
[HOST Browser] [Realtime] Successfully subscribed to lobby channel
[HOST Browser] [Realtime] Subscription successful, resetting retry count and connection status
[HOST Browser] [Client] STEP 1 COMPLETE: Room validation successful: JSHandle@object
[HOST Browser] [Client] *** STANDARD JOIN PATH *** Performing full player existence validation
[HOST Browser] [Client] *** STEP 2: PLAYER EXISTENCE CHECK *** Checking if player 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 is already in game_players for room 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Client] STEP 2 COMPLETE: Player 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 does NOT exist in game_players for room 072320d9-91ca-479b-8867-ea68eb0614a5. This is a NEW JOIN.
[HOST Browser] [Client] *** STEP 3: NEW JOIN VALIDATION *** Performing additional checks for new join
[HOST Browser] [Client] *** STEP 3.1: STATUS VALIDATION *** Checking room status for new join. Current status: waiting
[HOST Browser] [Client] STEP 3 COMPLETE: New join validation successful. Room has capacity: 0/4
[HOST Browser] [Client] *** STEP 4: INSERT ATTEMPT *** Attempting INSERT into game_players
[HOST Browser] [Client] VALIDATED INSERT: Attempting INSERT into game_players with validated data: JSHandle@object
[HOST Browser] [Client] Step 4 complete: User 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 successfully made NEW JOIN (inserted) to game_players for room 072320d9-91ca-479b-8867-ea68eb0614a5. Data: JSHandle@object
[HOST Browser] [Client] *** USING COMPREHENSIVE STATE SYNC for new join ***
[HOST Browser] [SYNC_STATE] *** STARTING FULL ROOM STATE SYNC *** for room 072320d9-91ca-479b-8867-ea68eb0614a5, called by: new_join_success
[HOST Browser] [SYNC_STATE] This should ELIMINATE the "Waiting for game data..." issue
[HOST Browser] [SYNC_STATE] Fetching room details and players concurrently...
[HOST Browser] [SYNC_STATE] *** FETCHED DATA SUCCESS *** JSHandle@object
[HOST Browser] [SYNC_STATE] *** SETTING ALL GAME STATES ATOMICALLY ***
[HOST Browser] [SYNC_STATE] This is the MISSING PIECE that fixes the race condition
[HOST Browser] [SYNC_STATE] *** STATE SYNC COMPLETE *** JSHandle@object
[HOST Browser] [Client] Client active state set for room 072320d9-91ca-479b-8867-ea68eb0614a5. Awaiting data fetches and UI transition.
[HOST] ✓ Room created, in waiting room
[HOST Browser] [Realtime] Cleaning up channel: realtime:lobby-global
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Player subscription setup complete for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] ✅ Successfully subscribed to room channel: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Subscriptions active for: JSHandle@object
[HOST Browser] [Realtime] Subscription successful, resetting retry count and connection status
[host] Screenshot saved: test-screenshots-game-flow/host-03-waiting-room-2025-07-08T22-19-57-414Z.png
[HOST] Waiting for player count to update...
[HOST Browser] [Client] [CREATE_ROOM] *** AUTO-JOIN COMPLETED SUCCESSFULLY ***
[HOST Browser] [Client] [CREATE_ROOM] Fetching initial player list for host after auto-join
[HOST Browser] [Client] [CREATE_ROOM] *** ROOM CREATION AND HOST AUTO-JOIN FULLY COMPLETED *** Room: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST] Switching to multiplayer mode...
[HOST Browser] [Realtime] Cleaning up channel: realtime:room-072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] 🔒 Channel closed: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Player subscription setup complete for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[GUEST] Need to sign in again for multiplayer mode...
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] ✅ Successfully subscribed to room channel: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Subscriptions active for: JSHandle@object
[GUEST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[GUEST Browser] [Realtime] ✅ Setting up NEW GLOBAL subscriptions for user in lobby
[GUEST Browser] [Realtime] Successfully subscribed to lobby channel
[GUEST Browser] [Realtime] Subscription successful, resetting retry count and connection status
[GUEST] Signed in for multiplayer
[GUEST] Looking for room to join...
[GUEST Browser] [Client] Refresh List button clicked.
[GUEST] Refreshed room list
[GUEST] Step 1: Looking for room to click and select...
[GUEST] Room clicked, waiting for details panel...
[GUEST] Step 2: Looking for Join This Room button...
[GUEST Browser] [Client] *** HANDLEJOINROOM ENTRY *** handleJoinRoomAttempt called for room: 072320d9-91ca-479b-8867-ea68eb0614a5. Current activeRoomId: null, User: 348453c6-4975-42fe-a34e-8f9a291ef982
[GUEST Browser] [Client] *** CRITICAL DEBUG *** Room ID analysis: JSHandle@object
[GUEST Browser] [Client] Joining room - React useEffect hooks will handle subscription transitions
[GUEST Browser] [Client] *** STARTING JOIN PROCESS *** User 348453c6-4975-42fe-a34e-8f9a291ef982 attempting to join/rejoin room: 072320d9-91ca-479b-8867-ea68eb0614a5 via server-side logic.
[GUEST Browser] [Client] *** STANDARD JOIN/REJOIN *** Performing full validation checks.
[GUEST Browser] [Client] *** STEP 1: ROOM VALIDATION *** Fetching room details for validation
[GUEST Browser] [Client] STEP 1 COMPLETE: Room validation successful: JSHandle@object
[GUEST Browser] [Client] *** STANDARD JOIN PATH *** Performing full player existence validation
[GUEST Browser] [Client] *** STEP 2: PLAYER EXISTENCE CHECK *** Checking if player 348453c6-4975-42fe-a34e-8f9a291ef982 is already in game_players for room 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Client] STEP 2 COMPLETE: Player 348453c6-4975-42fe-a34e-8f9a291ef982 does NOT exist in game_players for room 072320d9-91ca-479b-8867-ea68eb0614a5. This is a NEW JOIN.
[GUEST Browser] [Client] *** STEP 3: NEW JOIN VALIDATION *** Performing additional checks for new join
[GUEST Browser] [Client] *** STEP 3.1: STATUS VALIDATION *** Checking room status for new join. Current status: waiting
[GUEST Browser] [Client] STEP 3 COMPLETE: New join validation successful. Room has capacity: 0/4
[GUEST Browser] [Client] *** STEP 4: INSERT ATTEMPT *** Attempting INSERT into game_players
[GUEST Browser] [Client] VALIDATED INSERT: Attempting INSERT into game_players with validated data: JSHandle@object
[GUEST Browser] [Client] Step 4 complete: User 348453c6-4975-42fe-a34e-8f9a291ef982 successfully made NEW JOIN (inserted) to game_players for room 072320d9-91ca-479b-8867-ea68eb0614a5. Data: JSHandle@object
[GUEST Browser] [Client] *** USING COMPREHENSIVE STATE SYNC for new join ***
[GUEST Browser] [SYNC_STATE] *** STARTING FULL ROOM STATE SYNC *** for room 072320d9-91ca-479b-8867-ea68eb0614a5, called by: new_join_success
[GUEST Browser] [SYNC_STATE] This should ELIMINATE the "Waiting for game data..." issue
[GUEST Browser] [SYNC_STATE] Fetching room details and players concurrently...
[HOST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[HOST Browser] [Realtime] *** NEW PLAYER JOINED *** Room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[HOST Browser] [Realtime] Triggering player fetch due to realtime event (INSERT)
[GUEST Browser] [SYNC_STATE] *** FETCHED DATA SUCCESS *** JSHandle@object
[GUEST Browser] [SYNC_STATE] *** SETTING ALL GAME STATES ATOMICALLY ***
[GUEST Browser] [SYNC_STATE] This is the MISSING PIECE that fixes the race condition
[GUEST Browser] [SYNC_STATE] *** STATE SYNC COMPLETE *** JSHandle@object
[GUEST Browser] [Client] Client active state set for room 072320d9-91ca-479b-8867-ea68eb0614a5. Awaiting data fetches and UI transition.
[GUEST Browser] [Realtime] Cleaning up channel: realtime:lobby-global
[GUEST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[GUEST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Player subscription setup complete for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[GUEST Browser] [Realtime] Channel subscription status change: JSHandle@object
[GUEST Browser] [Realtime] ✅ Successfully subscribed to room channel: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Subscriptions active for: JSHandle@object
[GUEST Browser] [Realtime] Subscription successful, resetting retry count and connection status
[GUEST] ✓ Joined room successfully
[guest] Screenshot saved: test-screenshots-game-flow/guest-03-waiting-room-2025-07-08T22-20-15-403Z.png

[VERIFICATION] Checking player counts...
[HOST] Sees 2 player(s)
[GUEST] Sees 2 player(s)

[HOST] Marking ready...
[GUEST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[GUEST Browser] [Realtime] Triggering player fetch due to realtime event (UPDATE)
[HOST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[HOST Browser] [Realtime] Triggering player fetch due to realtime event (UPDATE)
[GUEST] Marking ready...
[GUEST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[HOST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[GUEST Browser] [Realtime] Triggering player fetch due to realtime event (UPDATE)
[HOST Browser] [Realtime] Triggering player fetch due to realtime event (UPDATE)
[host] Screenshot saved: test-screenshots-game-flow/host-04-both-ready-2025-07-08T22-20-16-650Z.png
[guest] Screenshot saved: test-screenshots-game-flow/guest-04-both-ready-2025-07-08T22-20-16-815Z.png
[HOST] Room state: {
  "textContent": [
    "Single-player Mode",
    "Multiplayer Mode",
    "Personal BestsNo records yet.Global Top 5 ❯",
    "No records yet.",
    "Room: fresh's Game",
    "Players (2/4):",
    "Ready to start game!",
    "Refresh Players",
    "Leave Game"
  ],
  "playerCount": 0,
  "buttons": [
    "Sign Out",
    "Single-player Mode",
    "Multiplayer Mode",
    "Global Top 5 ❯",
    "Ready ✓",
    "Start Game",
    "Refresh Players",
    "Leave Game"
  ]
}
[HOST] Waiting for room state to stabilize...
[GUEST Browser] [Realtime] Cleaning up channel: realtime:room-072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Channel subscription status change: JSHandle@object
[GUEST Browser] [Realtime] 🔒 Channel closed: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[GUEST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Player subscription setup complete for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[GUEST Browser] [Realtime] Channel subscription status change: JSHandle@object
[GUEST Browser] [Realtime] ✅ Successfully subscribed to room channel: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Subscriptions active for: JSHandle@object
[HOST] Waiting for Start Game button to appear...
[HOST] ✓ Start Game button is now available

=== PHASE 3: Start Game and Verify Transition ===
[HOST] Starting game...
[HOST Browser] [Client] Found Start Game button, clicking it
[HOST Browser] [Client] [START_GAME_ATTEMPT] Beginning start game process JSHandle@object
[HOST Browser] [Client] [START_GAME] Fetching latest room details from server before host check...
[HOST] Waiting for Edge Function to process...
[HOST Browser] [Client] [START_GAME] Fresh room data fetched: JSHandle@object
[HOST Browser] [Client] [START_GAME] Host verification check: JSHandle@object
[HOST Browser] [Client] [START_GAME] Pre-flight validation: JSHandle@object
[HOST Browser] [Client] Host 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 attempting to start game in room 072320d9-91ca-479b-8867-ea68eb0614a5 - setting isStartingGame to TRUE
[HOST Browser] [Client] [START_GAME_EDGE_CALL] Invoking start-game-handler for room 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Client] Error invoking start-game-handler: JSHandle@error
[HOST Browser] [Client] [START_GAME_ERROR] Setting error message: Edge Function returned a non-2xx status code
[HOST Browser] [Client] Client-side exception invoking start-game-handler: JSHandle@error
[HOST Browser] [Client] [START_GAME_CLEANUP] Clearing isStartingGame flags

[VERIFICATION] Checking game state transition...
[VERIFICATION] Waiting for realtime updates to propagate...
[HOST] Pre-check state: {
  hasGameImage: false,
  hasChoiceButtons: false,
  bodyText: "User: freshSign OutRecognition CombineSingle-player ModeMultiplayer ModeLeaderboardsPersonal BestsNo records yet.Global Top 5 ❯Waiting for game to start...Room: fresh's GameRoom: fresh's GameClient-si"
}
[HOST] Waiting for game player image: img[alt="Guess the player"]
[HOST Browser] [Realtime] Cleaning up channel: realtime:room-072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] 🔒 Channel closed: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Player subscription setup complete for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] ✅ Successfully subscribed to room channel: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Subscriptions active for: JSHandle@object
[GUEST Browser] [Realtime] Cleaning up channel: realtime:room-072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Channel subscription status change: JSHandle@object
[GUEST Browser] [Realtime] 🔒 Channel closed: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[GUEST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Player subscription setup complete for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[GUEST Browser] [Realtime] Channel subscription status change: JSHandle@object
[GUEST Browser] [Realtime] ✅ Successfully subscribed to room channel: 072320d9-91ca-479b-8867-ea68eb0614a5
[GUEST Browser] [Realtime] Subscriptions active for: JSHandle@object
[HOST Browser] [Realtime] Cleaning up channel: realtime:room-072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] 🔒 Channel closed: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Player subscription setup complete for room 072320d9-91ca-479b-8867-ea68eb0614a5 JSHandle@object
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] ✅ Successfully subscribed to room channel: 072320d9-91ca-479b-8867-ea68eb0614a5
[HOST Browser] [Realtime] Subscriptions active for: JSHandle@object
[HOST] Screenshot saved: test-screenshots-game-flow/HOST-error-game-player-image-2025-07-08T22-20-54-034Z.png
[host] Screenshot saved: test-screenshots-game-flow/host-HOST-error-final-debug-2025-07-08T22-20-54-200Z.png
[HOST] Final debug state: {
  "hasGameImage": false,
  "hasPlaceholderImage": false,
  "allImages": [],
  "errorMessages": [
    "@font-face{font-family:'__nextjs-Geist';font-style:normal;font-weight:400 600;font-display:swap;src:url(/__nextjs_font/geist-latin-ext.woff2) format('woff2');unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:'__nextjs-Geist Mono';font-style:normal;font-weight:400 600;font-display:swap;src:url(/__nextjs_font/geist-mono-latin-ext.woff2) format('woff2');unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:'__nextjs-Geist';font-style:normal;font-weight:400 600;font-display:swap;src:url(/__nextjs_font/geist-latin.woff2) format('woff2');unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:'__nextjs-Geist Mono';font-style:normal;font-weight:400 600;font-display:swap;src:url(/__nextjs_font/geist-mono-latin.woff2) format('woff2');unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}User: freshSign OutRecognition CombineSingle-player ModeMultiplayer ModeLeaderboardsPersonal BestsNo records yet.Global Top 5 ❯Waiting for game to start...Room: fresh's GameRoom: fresh's GameClient-side exception: shouldTransitionToGame is not definedPlayers (2/4):Ready ✓fresh (You) (Host)✓fresh2✓Start GameReady to start game!Refresh PlayersLeave Game(self.__next_f=self.__next_f||[]).push([0])self.__next_f.push([1,\"3:\\\"$Sreact.fragment\\\"\\n5:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n6:I[\\\"(app-pages-browser)/./src/app/error.tsx\\\",[\\\"app/error\\\",\\\"static/chunks/app/error.js\\\"],\\\"default\\\"]\\n7:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n9:I[\\\"(app-pages-browser)/./src/providers/AuthProvider.tsx\\\",[\\\"app/layout\\\",\\\"static/chunks/app/layout.js\\\"],\\\"AuthProvider\\\"]\\na:I[\\\"(app-pages-browser)/./src/app/not-found.tsx\\\",[\\\"app/not-found\\\",\\\"static/chunks/app/not-found.js\\\"],\\\"default\\\"]\\nb:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"ClientPageRoot\\\"]\\nc:I[\\\"(app-pages-browser)/./src/app/page.tsx\\\",[\\\"app/page\\\",\\\"static/chunks/app/page.js\\\"],\\\"default\\\"]\\n10:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"MetadataBoundary\\\"]\\n13:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"OutletBoundary\\\"]\\n1a:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"AsyncMetadataOutlet\\\"]\\n20:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"ViewportBoundary\\\"]\\n26:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n27:\\\"$Sreact.suspense\\\"\\n28:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"AsyncMetadata\\\"]\\n:HL[\\\"/_next/static/media/e4a\"])self.__next_f.push([1,\"f272ccee01ff0-s.p.woff2\\\",\\\"font\\\",{\\\"crossOrigin\\\":\\\"\\\",\\\"type\\\":\\\"font/woff2\\\"}]\\n:HL[\\\"/_next/static/css/app/layout.css?v=1752013161526\\\",\\\"style\\\"]\\n2:{\\\"name\\\":\\\"Preloads\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"preloadCallbacks\\\":[\\\"$E(()=\\u003e{ctx.componentMod.preloadFont(href,type,ctx.renderOpts.crossOrigin,ctx.nonce)})\\\",\\\"$E(()=\\u003e{ctx.componentMod.preloadStyle(fullHref,ctx.renderOpts.crossOrigin,ctx.nonce)})\\\"]}}\\n1:D\\\"$2\\\"\\n1:null\\n8:{\\\"name\\\":\\\"RootLayout\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"children\\\":[\\\"$\\\",\\\"$L5\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"error\\\":\\\"$6\\\",\\\"errorStyles\\\":[],\\\"errorScripts\\\":[],\\\"template\\\":[\\\"$\\\",\\\"$3\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"$L7\\\",null,{},null,[],1]},null,[],0],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":\\\"$Y\\\",\\\"forbidden\\\":\\\"$undefined\\\",\\\"unauthorized\\\":\\\"$undefined\\\"},null,[],1],\\\"params\\\":\\\"$Y\\\"}}\\n4:D\\\"$8\\\"\\n4:[\\\"$\\\",\\\"html\\\",null,{\\\"lang\\\":\\\"en\\\",\\\"children\\\":[\\\"$\\\",\\\"body\\\",null,{\\\"className\\\":\\\"__className_e8ce0c\\\",\\\"children\\\":[[\\\"$\\\",\\\"$L9\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"$L5\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"error\\\":\\\"$6\\\",\\\"errorStyles\\\":[],\\\"errorScripts\\\":[],\\\"template\\\":[\\\"$\\\",\\\"$L7\\\",null,{},null,[],1],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":[[\\\"$\\\",\\\"$La\\\",null,{},null,[],1],[]],\\\"forbidden\\\":\\\"$undefined\\\",\\\"unauthorized\\\":\\\"$undefined\\\"},null,[],1]},\\\"$8\\\",[[\\\"RootLayout\\\",\\\"webpack-internal:///(rsc)/./src/app/layout.tsx\\\",26,92]],1],[\\\"$\\\",\\\"div\\\",null,{\\\"id\\\":\\\"global-animation-portal\\\",\\\"style\\\":{\\\"position\\\":\\\"fixed\\\",\\\"top\\\":0,\\\"left\\\":0,\\\"width\\\":\\\"100%\\\",\\\"height\\\":\\\"100%\\\",\\\"pointerEvents\\\":\\\"none\\\",\\\"zIndex\\\":9999}},\\\"$8\\\",[[\\\"RootLayout\\\",\\\"webpack-internal:///(rsc)/./src/app/layout.tsx\\\",33,92]],1]]},\\\"$8\\\",[[\\\"RootLayout\\\",\\\"webpack-internal:///(rsc)/./src/app/layout.tsx\\\",23,94]],1]},\\\"$8\\\",[[\\\"RootLayout\\\",\\\"webpack-internal:///(rsc)/./src/app/layout.tsx\\\",21,87]],1]\\ne:{\\\"name\\\":\\\"\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\nd:D\\\"$e\\\"\\nf:{\\\"name\\\":\\\"MetadataTree\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\nd:D\\\"$f\\\"\\n12:{\\\"name\\\":\\\"__next_metadata_boundary__\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\"\"])self.__next_f.push([1,\":\\\"$f\\\",\\\"stack\\\":[],\\\"props\\\":{}}\\n11:D\\\"$12\\\"\\nd:[\\\"$\\\",\\\"$L10\\\",null,{\\\"children\\\":\\\"$L11\\\"},\\\"$f\\\",[],1]\\n15:{\\\"name\\\":\\\"__next_outlet_boundary__\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"ready\\\":\\\"$E(async function getViewportReady() {\\\\n        await viewport();\\\\n        return undefined;\\\\n    })\\\"}}\\n14:D\\\"$15\\\"\\n17:{\\\"name\\\":\\\"__next_outlet_boundary__\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"ready\\\":\\\"$E(async function getMetadataReady() {\\\\n        // Only warm up metadata() call when it's blocking metadata,\\\\n        // otherwise it will be fully managed by AsyncMetadata component.\\\\n        if (!serveStreamingMetadata) {\\\\n            await metadata();\\\\n        }\\\\n        return undefined;\\\\n    })\\\"}}\\n16:D\\\"$17\\\"\\n19:{\\\"name\\\":\\\"StreamingMetadataOutlet\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\n18:D\\\"$19\\\"\\n18:[\\\"$\\\",\\\"$L1a\\\",null,{\\\"promise\\\":\\\"$@1b\\\"},\\\"$19\\\",[],1]\\n1d:{\\\"name\\\":\\\"NonIndex\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"pagePath\\\":\\\"/\\\",\\\"statusCode\\\":200,\\\"isPossibleServerAction\\\":false}}\\n1c:D\\\"$1d\\\"\\n1c:null\\n1f:{\\\"name\\\":\\\"ViewportTree\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":\\\"3BbMCKyYb7U7FZSkHl3FI\\\",\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\n1e:D\\\"$1f\\\"\\n22:{\\\"name\\\":\\\"__next_viewport_boundary__\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":\\\"$1f\\\",\\\"stack\\\":[],\\\"props\\\":{}}\\n21:D\\\"$22\\\"\\n1e:[\\\"$\\\",\\\"$3\\\",\\\"3BbMCKyYb7U7FZSkHl3FI\\\",{\\\"children\\\":[[\\\"$\\\",\\\"$L20\\\",null,{\\\"children\\\":\\\"$L21\\\"},\\\"$1f\\\",[],1],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"next-size-adjust\\\",\\\"content\\\":\\\"\\\"},\\\"$1f\\\",[],1]]},null,null,0]\\n24:{\\\"name\\\":\\\"\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\n23:D\\\"$24\\\"\\n23:null\\n25:[]\\n0:{\\\"P\\\":\\\"$1\\\",\\\"b\\\":\\\"development\\\",\\\"p\\\":\\\"\\\",\\\"c\\\":[\\\"\\\",\\\"\\\"],\\\"i\\\":false,\\\"f\\\":[[[\\\"\\\",{\\\"children\\\":[\\\"__PAGE__\\\",{}]},\\\"$undefined\\\",\\\"$undefined\\\",true],[\\\"\\\",[\\\"$\\\",\\\"$3\\\",\\\"c\\\",{\\\"children\\\":[[[\\\"$\\\",\\\"link\\\",\\\"0\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/app/layout.css?v=1752013161526\\\",\\\"precedence\\\":\\\"next_static/css/app/layout.css\\\",\\\"crossOrigin\\\":\\\"$undefined\\\",\\\"nonce\\\":\\\"$undefined\\\"},null,[],0]],\\\"$4\\\"]},null,[],0],{\\\"children\\\":[\\\"__PAGE__\\\",[\\\"$\\\",\\\"$3\\\",\\\"c\\\",{\\\"children\\\":[[\\\"$\\\",\\\"$Lb\\\",null,{\\\"Component\\\":\\\"$c\\\",\\\"searchParams\\\":\"])self.__next_f.push([1,\"{},\\\"params\\\":{}},null,[],1],\\\"$d\\\",null,[\\\"$\\\",\\\"$L13\\\",null,{\\\"children\\\":[\\\"$L14\\\",\\\"$L16\\\",\\\"$18\\\"]},null,[],1]]},null,[],0],{},null,false]},null,false],[\\\"$\\\",\\\"$3\\\",\\\"h\\\",{\\\"children\\\":[\\\"$1c\\\",\\\"$1e\\\",\\\"$23\\\"]},null,[],0],false]],\\\"m\\\":\\\"$W25\\\",\\\"G\\\":[\\\"$26\\\",\\\"$undefined\\\"],\\\"s\\\":false,\\\"S\\\":false}\\n11:[\\\"$\\\",\\\"$27\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[\\\"$\\\",\\\"$L28\\\",null,{\\\"promise\\\":\\\"$@29\\\"},\\\"$12\\\",[],1]},\\\"$12\\\",[],1]\\n16:null\\n21:[[\\\"$\\\",\\\"meta\\\",\\\"0\\\",{\\\"charSet\\\":\\\"utf-8\\\"},\\\"$15\\\",[],0],[\\\"$\\\",\\\"meta\\\",\\\"1\\\",{\\\"name\\\":\\\"viewport\\\",\\\"content\\\":\\\"width=device-width, initial-scale=1\\\"},\\\"$15\\\",[],0]]\\n14:null\\n\"])self.__next_f.push([1,\"29:{\\\"metadata\\\":[[\\\"$\\\",\\\"title\\\",\\\"0\\\",{\\\"children\\\":\\\"Recognition Combine\\\"},\\\"$12\\\",[],0],[\\\"$\\\",\\\"meta\\\",\\\"1\\\",{\\\"name\\\":\\\"description\\\",\\\"content\\\":\\\"The premier platform for athletic recognition\\\"},\\\"$12\\\",[],0],[\\\"$\\\",\\\"link\\\",\\\"2\\\",{\\\"rel\\\":\\\"icon\\\",\\\"href\\\":\\\"/favicon.ico\\\",\\\"type\\\":\\\"image/x-icon\\\",\\\"sizes\\\":\\\"16x16\\\"},\\\"$12\\\",[],0]],\\\"error\\\":null,\\\"digest\\\":\\\"$undefined\\\"}\\n1b:{\\\"metadata\\\":\\\"$29:metadata\\\",\\\"error\\\":null,\\\"digest\\\":\\\"$undefined\\\"}\\n\"])Recognition Combinedocument.querySelectorAll('body link[rel=\"icon\"], body link[rel=\"apple-touch-icon\"]').forEach(el => document.head.appendChild(el))$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data=\"$!\",a.setAttribute(\"data-dgst\",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if(\"/$\"===d)if(0===f)break;else f--;else\"$\"!==d&&\"$?\"!==d&&\"$!\"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data=\"$\"}b._reactRetry&&b._reactRetry()}};$RC(\"B:0\",\"S:0\")$RC(\"B:1\",\"S:1\")",
    "User: freshSign OutRecognition CombineSingle-player ModeMultiplayer ModeLeaderboardsPersonal BestsNo records yet.Global Top 5 ❯Waiting for game to start...Room: fresh's GameRoom: fresh's GameClient-side exception: shouldTransitionToGame is not definedPlayers (2/4):Ready ✓fresh (You) (Host)✓fresh2✓Start GameReady to start game!Refresh PlayersLeave Game(self.__next_f=self.__next_f||[]).push([0])self.__next_f.push([1,\"3:\\\"$Sreact.fragment\\\"\\n5:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n6:I[\\\"(app-pages-browser)/./src/app/error.tsx\\\",[\\\"app/error\\\",\\\"static/chunks/app/error.js\\\"],\\\"default\\\"]\\n7:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n9:I[\\\"(app-pages-browser)/./src/providers/AuthProvider.tsx\\\",[\\\"app/layout\\\",\\\"static/chunks/app/layout.js\\\"],\\\"AuthProvider\\\"]\\na:I[\\\"(app-pages-browser)/./src/app/not-found.tsx\\\",[\\\"app/not-found\\\",\\\"static/chunks/app/not-found.js\\\"],\\\"default\\\"]\\nb:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"ClientPageRoot\\\"]\\nc:I[\\\"(app-pages-browser)/./src/app/page.tsx\\\",[\\\"app/page\\\",\\\"static/chunks/app/page.js\\\"],\\\"default\\\"]\\n10:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"MetadataBoundary\\\"]\\n13:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"OutletBoundary\\\"]\\n1a:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"AsyncMetadataOutlet\\\"]\\n20:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"ViewportBoundary\\\"]\\n26:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n27:\\\"$Sreact.suspense\\\"\\n28:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"AsyncMetadata\\\"]\\n:HL[\\\"/_next/static/media/e4a\"])self.__next_f.push([1,\"f272ccee01ff0-s.p.woff2\\\",\\\"font\\\",{\\\"crossOrigin\\\":\\\"\\\",\\\"type\\\":\\\"font/woff2\\\"}]\\n:HL[\\\"/_next/static/css/app/layout.css?v=1752013161526\\\",\\\"style\\\"]\\n2:{\\\"name\\\":\\\"Preloads\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"preloadCallbacks\\\":[\\\"$E(()=\\u003e{ctx.componentMod.preloadFont(href,type,ctx.renderOpts.crossOrigin,ctx.nonce)})\\\",\\\"$E(()=\\u003e{ctx.componentMod.preloadStyle(fullHref,ctx.renderOpts.crossOrigin,ctx.nonce)})\\\"]}}\\n1:D\\\"$2\\\"\\n1:null\\n8:{\\\"name\\\":\\\"RootLayout\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"children\\\":[\\\"$\\\",\\\"$L5\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"error\\\":\\\"$6\\\",\\\"errorStyles\\\":[],\\\"errorScripts\\\":[],\\\"template\\\":[\\\"$\\\",\\\"$3\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"$L7\\\",null,{},null,[],1]},null,[],0],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":\\\"$Y\\\",\\\"forbidden\\\":\\\"$undefined\\\",\\\"unauthorized\\\":\\\"$undefined\\\"},null,[],1],\\\"params\\\":\\\"$Y\\\"}}\\n4:D\\\"$8\\\"\\n4:[\\\"$\\\",\\\"html\\\",null,{\\\"lang\\\":\\\"en\\\",\\\"children\\\":[\\\"$\\\",\\\"body\\\",null,{\\\"className\\\":\\\"__className_e8ce0c\\\",\\\"children\\\":[[\\\"$\\\",\\\"$L9\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"$L5\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"error\\\":\\\"$6\\\",\\\"errorStyles\\\":[],\\\"errorScripts\\\":[],\\\"template\\\":[\\\"$\\\",\\\"$L7\\\",null,{},null,[],1],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":[[\\\"$\\\",\\\"$La\\\",null,{},null,[],1],[]],\\\"forbidden\\\":\\\"$undefined\\\",\\\"unauthorized\\\":\\\"$undefined\\\"},null,[],1]},\\\"$8\\\",[[\\\"RootLayout\\\",\\\"webpack-internal:///(rsc)/./src/app/layout.tsx\\\",26,92]],1],[\\\"$\\\",\\\"div\\\",null,{\\\"id\\\":\\\"global-animation-portal\\\",\\\"style\\\":{\\\"position\\\":\\\"fixed\\\",\\\"top\\\":0,\\\"left\\\":0,\\\"width\\\":\\\"100%\\\",\\\"height\\\":\\\"100%\\\",\\\"pointerEvents\\\":\\\"none\\\",\\\"zIndex\\\":9999}},\\\"$8\\\",[[\\\"RootLayout\\\",\\\"webpack-internal:///(rsc)/./src/app/layout.tsx\\\",33,92]],1]]},\\\"$8\\\",[[\\\"RootLayout\\\",\\\"webpack-internal:///(rsc)/./src/app/layout.tsx\\\",23,94]],1]},\\\"$8\\\",[[\\\"RootLayout\\\",\\\"webpack-internal:///(rsc)/./src/app/layout.tsx\\\",21,87]],1]\\ne:{\\\"name\\\":\\\"\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\nd:D\\\"$e\\\"\\nf:{\\\"name\\\":\\\"MetadataTree\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\nd:D\\\"$f\\\"\\n12:{\\\"name\\\":\\\"__next_metadata_boundary__\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\"\"])self.__next_f.push([1,\":\\\"$f\\\",\\\"stack\\\":[],\\\"props\\\":{}}\\n11:D\\\"$12\\\"\\nd:[\\\"$\\\",\\\"$L10\\\",null,{\\\"children\\\":\\\"$L11\\\"},\\\"$f\\\",[],1]\\n15:{\\\"name\\\":\\\"__next_outlet_boundary__\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"ready\\\":\\\"$E(async function getViewportReady() {\\\\n        await viewport();\\\\n        return undefined;\\\\n    })\\\"}}\\n14:D\\\"$15\\\"\\n17:{\\\"name\\\":\\\"__next_outlet_boundary__\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"ready\\\":\\\"$E(async function getMetadataReady() {\\\\n        // Only warm up metadata() call when it's blocking metadata,\\\\n        // otherwise it will be fully managed by AsyncMetadata component.\\\\n        if (!serveStreamingMetadata) {\\\\n            await metadata();\\\\n        }\\\\n        return undefined;\\\\n    })\\\"}}\\n16:D\\\"$17\\\"\\n19:{\\\"name\\\":\\\"StreamingMetadataOutlet\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\n18:D\\\"$19\\\"\\n18:[\\\"$\\\",\\\"$L1a\\\",null,{\\\"promise\\\":\\\"$@1b\\\"},\\\"$19\\\",[],1]\\n1d:{\\\"name\\\":\\\"NonIndex\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{\\\"pagePath\\\":\\\"/\\\",\\\"statusCode\\\":200,\\\"isPossibleServerAction\\\":false}}\\n1c:D\\\"$1d\\\"\\n1c:null\\n1f:{\\\"name\\\":\\\"ViewportTree\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":\\\"3BbMCKyYb7U7FZSkHl3FI\\\",\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\n1e:D\\\"$1f\\\"\\n22:{\\\"name\\\":\\\"__next_viewport_boundary__\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":\\\"$1f\\\",\\\"stack\\\":[],\\\"props\\\":{}}\\n21:D\\\"$22\\\"\\n1e:[\\\"$\\\",\\\"$3\\\",\\\"3BbMCKyYb7U7FZSkHl3FI\\\",{\\\"children\\\":[[\\\"$\\\",\\\"$L20\\\",null,{\\\"children\\\":\\\"$L21\\\"},\\\"$1f\\\",[],1],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"next-size-adjust\\\",\\\"content\\\":\\\"\\\"},\\\"$1f\\\",[],1]]},null,null,0]\\n24:{\\\"name\\\":\\\"\\\",\\\"env\\\":\\\"Server\\\",\\\"key\\\":null,\\\"owner\\\":null,\\\"stack\\\":[],\\\"props\\\":{}}\\n23:D\\\"$24\\\"\\n23:null\\n25:[]\\n0:{\\\"P\\\":\\\"$1\\\",\\\"b\\\":\\\"development\\\",\\\"p\\\":\\\"\\\",\\\"c\\\":[\\\"\\\",\\\"\\\"],\\\"i\\\":false,\\\"f\\\":[[[\\\"\\\",{\\\"children\\\":[\\\"__PAGE__\\\",{}]},\\\"$undefined\\\",\\\"$undefined\\\",true],[\\\"\\\",[\\\"$\\\",\\\"$3\\\",\\\"c\\\",{\\\"children\\\":[[[\\\"$\\\",\\\"link\\\",\\\"0\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/app/layout.css?v=1752013161526\\\",\\\"precedence\\\":\\\"next_static/css/app/layout.css\\\",\\\"crossOrigin\\\":\\\"$undefined\\\",\\\"nonce\\\":\\\"$undefined\\\"},null,[],0]],\\\"$4\\\"]},null,[],0],{\\\"children\\\":[\\\"__PAGE__\\\",[\\\"$\\\",\\\"$3\\\",\\\"c\\\",{\\\"children\\\":[[\\\"$\\\",\\\"$Lb\\\",null,{\\\"Component\\\":\\\"$c\\\",\\\"searchParams\\\":\"])self.__next_f.push([1,\"{},\\\"params\\\":{}},null,[],1],\\\"$d\\\",null,[\\\"$\\\",\\\"$L13\\\",null,{\\\"children\\\":[\\\"$L14\\\",\\\"$L16\\\",\\\"$18\\\"]},null,[],1]]},null,[],0],{},null,false]},null,false],[\\\"$\\\",\\\"$3\\\",\\\"h\\\",{\\\"children\\\":[\\\"$1c\\\",\\\"$1e\\\",\\\"$23\\\"]},null,[],0],false]],\\\"m\\\":\\\"$W25\\\",\\\"G\\\":[\\\"$26\\\",\\\"$undefined\\\"],\\\"s\\\":false,\\\"S\\\":false}\\n11:[\\\"$\\\",\\\"$27\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[\\\"$\\\",\\\"$L28\\\",null,{\\\"promise\\\":\\\"$@29\\\"},\\\"$12\\\",[],1]},\\\"$12\\\",[],1]\\n16:null\\n21:[[\\\"$\\\",\\\"meta\\\",\\\"0\\\",{\\\"charSet\\\":\\\"utf-8\\\"},\\\"$15\\\",[],0],[\\\"$\\\",\\\"meta\\\",\\\"1\\\",{\\\"name\\\":\\\"viewport\\\",\\\"content\\\":\\\"width=device-width, initial-scale=1\\\"},\\\"$15\\\",[],0]]\\n14:null\\n\"])self.__next_f.push([1,\"29:{\\\"metadata\\\":[[\\\"$\\\",\\\"title\\\",\\\"0\\\",{\\\"children\\\":\\\"Recognition Combine\\\"},\\\"$12\\\",[],0],[\\\"$\\\",\\\"meta\\\",\\\"1\\\",{\\\"name\\\":\\\"description\\\",\\\"content\\\":\\\"The premier platform for athletic recognition\\\"},\\\"$12\\\",[],0],[\\\"$\\\",\\\"link\\\",\\\"2\\\",{\\\"rel\\\":\\\"icon\\\",\\\"href\\\":\\\"/favicon.ico\\\",\\\"type\\\":\\\"image/x-icon\\\",\\\"sizes\\\":\\\"16x16\\\"},\\\"$12\\\",[],0]],\\\"error\\\":null,\\\"digest\\\":\\\"$undefined\\\"}\\n1b:{\\\"metadata\\\":\\\"$29:metadata\\\",\\\"error\\\":null,\\\"digest\\\":\\\"$undefined\\\"}\\n\"])Recognition Combinedocument.querySelectorAll('body link[rel=\"icon\"], body link[rel=\"apple-touch-icon\"]').forEach(el => document.head.appendChild(el))$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data=\"$!\",a.setAttribute(\"data-dgst\",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if(\"/$\"===d)if(0===f)break;else f--;else\"$\"!==d&&\"$?\"!==d&&\"$!\"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data=\"$\"}b._reactRetry&&b._reactRetry()}};$RC(\"B:0\",\"S:0\")$RC(\"B:1\",\"S:1\")",
    "self.__next_f.push([1,\"3:\\\"$Sreact.fragment\\\"\\n5:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n6:I[\\\"(app-pages-browser)/./src/app/error.tsx\\\",[\\\"app/error\\\",\\\"static/chunks/app/error.js\\\"],\\\"default\\\"]\\n7:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n9:I[\\\"(app-pages-browser)/./src/providers/AuthProvider.tsx\\\",[\\\"app/layout\\\",\\\"static/chunks/app/layout.js\\\"],\\\"AuthProvider\\\"]\\na:I[\\\"(app-pages-browser)/./src/app/not-found.tsx\\\",[\\\"app/not-found\\\",\\\"static/chunks/app/not-found.js\\\"],\\\"default\\\"]\\nb:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"ClientPageRoot\\\"]\\nc:I[\\\"(app-pages-browser)/./src/app/page.tsx\\\",[\\\"app/page\\\",\\\"static/chunks/app/page.js\\\"],\\\"default\\\"]\\n10:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"MetadataBoundary\\\"]\\n13:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"OutletBoundary\\\"]\\n1a:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"AsyncMetadataOutlet\\\"]\\n20:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"ViewportBoundary\\\"]\\n26:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n27:\\\"$Sreact.suspense\\\"\\n28:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"AsyncMetadata\\\"]\\n:HL[\\\"/_next/static/media/e4a\"])"
  ]
}

Keeping browsers open for observation...

❌ MULTIPLAYER GAME FLOW TEST FAILED
