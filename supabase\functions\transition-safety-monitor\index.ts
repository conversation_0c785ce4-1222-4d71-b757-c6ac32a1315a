// Transition Safety Monitor - Ensures games don't get stuck when all clients disconnect
// This is a lightweight scheduled function that only processes truly abandoned games

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

console.log('[TRANSITION_SAFETY] Function loaded');

serve(async (req: Request) => {
  console.log(`[TRANSITION_SAFETY] Request received. Method: ${req.method}`);
  
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)

    console.log('[TRANSITION_SAFETY] Checking for abandoned games with expired transitions...');

    // Only check games that:
    // 1. Are active
    // 2. Have a transition_deadline that passed more than 5 seconds ago
    // 3. Have no connected players (truly abandoned)
    const fiveSecondsAgo = new Date(Date.now() - 5000).toISOString();

    const { data: abandonedGames, error: fetchError } = await supabaseAdmin
      .from('game_rooms')
      .select(`
        id,
        transition_deadline,
        game_players!inner(
          user_id,
          is_connected
        )
      `)
      .eq('status', 'active')
      .not('transition_deadline', 'is', null)
      .lt('transition_deadline', fiveSecondsAgo);

    if (fetchError) {
      console.error('[TRANSITION_SAFETY] Error fetching games:', fetchError);
      throw fetchError;
    }

    if (!abandonedGames || abandonedGames.length === 0) {
      console.log('[TRANSITION_SAFETY] No abandoned games found');
      return new Response(JSON.stringify({ 
        message: 'No abandoned games to process',
        processed: 0 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      });
    }

    // Filter to only truly abandoned games (no connected players)
    const trulyAbandonedGames = abandonedGames.filter(game => {
      const connectedPlayers = game.game_players.filter((p: any) => p.is_connected);
      return connectedPlayers.length === 0;
    });

    console.log(`[TRANSITION_SAFETY] Found ${trulyAbandonedGames.length} truly abandoned games (no connected players)`);

    let transitionsProcessed = 0;

    // Process each abandoned game
    for (const game of trulyAbandonedGames) {
      try {
        console.log(`[TRANSITION_SAFETY] Processing abandoned game ${game.id}`);
        
        // Call the transition-monitor to handle the transition
        const { data, error } = await supabaseAdmin.functions.invoke('transition-monitor', {
          body: { specificRoomId: game.id }
        });

        if (error) {
          console.error(`[TRANSITION_SAFETY] Error transitioning game ${game.id}:`, error);
        } else {
          console.log(`[TRANSITION_SAFETY] Successfully transitioned game ${game.id}`);
          transitionsProcessed++;
        }
      } catch (err) {
        console.error(`[TRANSITION_SAFETY] Exception processing game ${game.id}:`, err);
      }
    }

    return new Response(JSON.stringify({ 
      message: 'Safety monitor completed',
      gamesChecked: abandonedGames.length,
      trulyAbandoned: trulyAbandonedGames.length,
      transitionsProcessed 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('[TRANSITION_SAFETY] Unexpected error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})