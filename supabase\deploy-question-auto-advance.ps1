# Deploy question-auto-advance edge function
# This script handles the deployment when standard Supabase CLI fails

Write-Host "Deploying question-auto-advance edge function..." -ForegroundColor Cyan

# Get the project ref from .env or config
$projectRef = "xmyxuvuimebjltnaamox"

if (-not $projectRef) {
    Write-Error "Project ref not found. Please set SUPABASE_PROJECT_REF in your .env file"
    exit 1
}

# Deploy using the direct method
Write-Host "Using direct deployment method for project: $projectRef" -ForegroundColor Yellow

$deployCommand = "npx supabase functions deploy question-auto-advance --project-ref $projectRef"

Write-Host "Executing: $deployCommand" -ForegroundColor Gray
Invoke-Expression $deployCommand

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ question-auto-advance function deployed successfully!" -ForegroundColor Green
    
    Write-Host "`nNext steps:" -ForegroundColor Cyan
    Write-Host "1. Set up a cron job to call this function every second" -ForegroundColor White
    Write-Host "2. Or call it from your existing edge functions periodically" -ForegroundColor White
    Write-Host "3. Test with: curl https://$projectRef.supabase.co/functions/v1/question-auto-advance" -ForegroundColor White
} else {
    Write-Error "❌ Deployment failed with exit code: $LASTEXITCODE"
    Write-Host "`nTroubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Check if you're logged in: npx supabase login" -ForegroundColor White
    Write-Host "2. Verify the function exists at: supabase/functions/question-auto-advance/index.ts" -ForegroundColor White
    Write-Host "3. Check for TypeScript errors in the function code" -ForegroundColor White
}