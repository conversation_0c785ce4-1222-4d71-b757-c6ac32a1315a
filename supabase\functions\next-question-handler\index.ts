import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PlayerData {
  id: number;
  team_name: string;
  player_name: string;
  local_image_path: string | null;
  jersey_number: string | number | null;
  position: string | null;
  height: string | null;
  weight: string | number | null;
  age_or_dob: string | number | null;
  experience: string | number | null;
  college: string | null;
}

interface PlayerChoice {
  name: string;
  isCorrect: boolean;
}

interface PlayerQuestion {
  questionId: string;
  correctPlayerId: number;
  imageUrl: string;
  choices: PlayerChoice[];
  correctChoiceName: string;
}

interface NextQuestionRequestBody {
  roomId: string;
  forceTransition?: boolean;
}

// Helper to shuffle array (<PERSON><PERSON><PERSON> algorithm)
function shuffleArray<T>(array: T[]): T[] {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]]; // Swap elements
  }
  return array;
}

// Load player data from the database
async function loadPlayerData(supabaseAdmin: any): Promise<PlayerData[]> {
  try {
    // Query real player data from the players_data table
    console.log('[EDGE_NEXT_QUESTION] Fetching players from players_data table...');
    const { data: playersData, error: fetchError } = await supabaseAdmin
      .from('players_data')
      .select('*')
      .not('local_image_path', 'is', null)
      .limit(500); // Get a larger sample for variety

    if (fetchError) {
      console.error('[EDGE_NEXT_QUESTION] Error fetching players from database:', {
        message: fetchError.message,
        code: fetchError.code,
        details: fetchError.details,
        hint: fetchError.hint,
        fullError: JSON.stringify(fetchError)
      });
      throw fetchError;
    }

    if (!playersData || playersData.length === 0) {
      console.error('[EDGE_NEXT_QUESTION] No players found in database');
      throw new Error('No players found in database');
    }

    console.log(`[EDGE_NEXT_QUESTION] Successfully fetched ${playersData.length} players from database`);
    return playersData;
  } catch (error) {
    console.error('[EDGE_NEXT_QUESTION] Error loading player data:', error);
    // Fallback to minimal sample
    return [
      { id: 1, team_name: 'Arizona Cardinals', player_name: 'Isaiah Adams', local_image_path: 'arizona-cardinals/isaiah-adams.jpg', jersey_number: '74', position: 'OL', height: '6-4', weight: '315', age_or_dob: '24', experience: '2', college: 'Illinois' },
      { id: 2, team_name: 'Arizona Cardinals', player_name: 'Andre Baccellia', local_image_path: 'arizona-cardinals/andre-baccellia.jpg', jersey_number: '82', position: 'WR', height: '5-10', weight: '175', age_or_dob: '28', experience: '2', college: 'Washington' },
      { id: 3, team_name: 'Arizona Cardinals', player_name: 'Budda Baker', local_image_path: 'arizona-cardinals/budda-baker.jpg', jersey_number: '3', position: 'S', height: '5-10', weight: '195', age_or_dob: '29', experience: '9', college: 'Washington' },
      { id: 4, team_name: 'Arizona Cardinals', player_name: 'Kelvin Beachum', local_image_path: 'arizona-cardinals/kelvin-beachum.jpg', jersey_number: '68', position: 'OL', height: '6-3', weight: '308', age_or_dob: '35', experience: '14', college: 'Southern Methodist' }
    ];
  }
}

// Generate a question using the same logic as singleplayer
function generateQuestion(allPlayers: PlayerData[], excludeIds: Set<number> = new Set()): PlayerQuestion | null {
  if (!allPlayers || allPlayers.length < 4) {
    console.error("[EDGE_NEXT_QUESTION] Not enough player data loaded or available to generate a question.");
    return null;
  }

  // Filter out players already asked
  const availablePlayers = allPlayers.filter(
    (p) => p.id != null && !excludeIds.has(p.id)
  );

  if (availablePlayers.length < 1) {
    console.warn("[EDGE_NEXT_QUESTION] No more available players to ask questions about.");
    return null;
  }

  // Select the correct player
  const correctPlayerIndex = Math.floor(Math.random() * availablePlayers.length);
  const correctPlayer = availablePlayers[correctPlayerIndex];

  // Select 3 distractors (ensure they are different from correct player and each other)
  const distractors: PlayerData[] = [];
  const potentialDistractors = allPlayers.filter(p => p.id !== correctPlayer.id);
  shuffleArray(potentialDistractors); // Shuffle to get random distractors

  for (const p of potentialDistractors) {
    if (distractors.length < 3) {
      distractors.push(p);
    } else {
      break;
    }
  }

  if (distractors.length < 3) {
    console.error("[EDGE_NEXT_QUESTION] Could not find enough unique distractors.");
    return null;
  }

  // Create choices array
  const choices: PlayerChoice[] = [
    { name: correctPlayer.player_name, isCorrect: true },
    ...distractors.map(p => ({ name: p.player_name, isCorrect: false }))
  ];

  // Shuffle choices
  const shuffledChoices = shuffleArray(choices);

  // Construct the full image URL
  const imageUrl = correctPlayer.local_image_path
    ? `/players_images/${correctPlayer.local_image_path}`
    : '/images/placeholder.jpg';

  return {
    questionId: crypto.randomUUID(),
    correctPlayerId: correctPlayer.id,
    imageUrl: imageUrl,
    choices: shuffledChoices,
    correctChoiceName: correctPlayer.player_name
  };
}

console.log('[EDGE_FN_LOAD] next-question-handler function script loaded.');

serve(async (req: Request) => {
  console.log(`[EDGE_NEXT_QUESTION] Request received. Method: ${req.method}, URL: ${req.url}`);
  
  if (req.method === 'OPTIONS') {
    console.log('[EDGE_NEXT_QUESTION] OPTIONS request, responding with CORS.');
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    console.log(`[EDGE_NEXT_QUESTION] Invalid method: ${req.method}`);
    return new Response('Method not allowed', { 
      headers: corsHeaders, 
      status: 405 
    })
  }

  try {
    // Parse request body
    const body: NextQuestionRequestBody = await req.json()
    const { roomId, forceTransition = false } = body

    console.log(`[EDGE_NEXT_QUESTION] Processing next question request for room: ${roomId}, forceTransition: ${forceTransition}`);

    if (!roomId) {
      console.error('[EDGE_NEXT_QUESTION] Missing roomId in request body');
      return new Response(JSON.stringify({ 
        error: 'Missing roomId in request body' 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      })
    }

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

    // Authenticate user
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('[EDGE_NEXT_QUESTION] Missing Authorization header');
      return new Response(JSON.stringify({ 
        error: 'Missing Authorization header' 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    // Create client for user authentication
    const supabaseAuth = createClient(supabaseUrl, Deno.env.get('SUPABASE_ANON_KEY')!, {
      global: { headers: { Authorization: authHeader } }
    })

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()
    if (authError || !user) {
      console.error('[EDGE_NEXT_QUESTION] Authentication failed:', authError);
      return new Response(JSON.stringify({ 
        error: 'Authentication failed' 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    console.log(`[EDGE_NEXT_QUESTION] User authenticated successfully. User ID: ${user.id}`);
    const userId = user.id

    // Create admin client
    console.log('[EDGE_NEXT_QUESTION] Creating admin client...');
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)
    console.log('[EDGE_NEXT_QUESTION] Admin client created successfully');

    console.log(`[EDGE_NEXT_QUESTION] Processing next question request for room ${roomId} by user ${userId}`)

    // Fetch current room state and validate
    console.log(`[EDGE_NEXT_QUESTION] Fetching current room state for validation...`);
    const { data: roomData, error: roomFetchError } = await supabaseAdmin
      .from('game_rooms')
      .select('*, game_players(user_id, is_ready)')
      .eq('id', roomId)
      .single()

    if (roomFetchError) {
      console.error(`[EDGE_NEXT_QUESTION] Error fetching room data:`, roomFetchError);
      return new Response(JSON.stringify({ 
        error: 'Room not found.',
        details: roomFetchError.message
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    console.log(`[EDGE_NEXT_QUESTION] Room data fetched successfully:`, {
      roomId: roomData.id,
      status: roomData.status,
      hostId: roomData.host_id,
      playersCount: roomData.game_players?.length || 0
    });

    // Validate game is active
    if (roomData.status !== 'active') {
      console.error(`[EDGE_NEXT_QUESTION] Game is not active. Status: ${roomData.status}`);
      return new Response(JSON.stringify({ 
        error: 'Game is not active.',
        details: `Game status is ${roomData.status}`
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Any player can advance to the next question when all have answered
    console.log(`[EDGE_NEXT_QUESTION] User ${userId} is attempting to advance the question in room ${roomId}`);

    // Validate all players have submitted answers
    const playersInRoom = roomData.game_players || [];
    const currentAnswers = Array.isArray(roomData.current_round_answers) ? roomData.current_round_answers : [];
    const currentQuestionId = roomData.current_question_data?.questionId;
    
    if (!currentQuestionId) {
      console.error(`[EDGE_NEXT_QUESTION] No current question in room ${roomId}`);
      return new Response(JSON.stringify({ 
        error: 'No current question.',
        details: 'No question is currently active in this game'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const answersForCurrentQuestion = currentAnswers.filter((answer: any) => 
      answer.questionId === currentQuestionId
    );

    // Only check if all players have answered if this is not a forced transition (7-second hard cap)
    if (!forceTransition && answersForCurrentQuestion.length !== playersInRoom.length) {
      console.error(`[EDGE_NEXT_QUESTION] Not all players have submitted answers. ${answersForCurrentQuestion.length}/${playersInRoom.length} submitted`);
      return new Response(JSON.stringify({ 
        error: 'Not all players have submitted answers.',
        details: `${answersForCurrentQuestion.length} out of ${playersInRoom.length} players have submitted`
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }
    
    if (forceTransition) {
      console.log(`[EDGE_NEXT_QUESTION] Force transition requested - proceeding regardless of answer count. ${answersForCurrentQuestion.length}/${playersInRoom.length} have answered`);
    }

    console.log(`[EDGE_NEXT_QUESTION] All validations passed. Generating next question...`);

    // Load player data and generate next question
    const allPlayers = await loadPlayerData(supabaseAdmin);

    // Get previously asked player IDs from the room data (if we want to track this)
    // For now, we'll just generate a random question without exclusions
    const excludeIds = new Set<number>();

    const nextQuestion = generateQuestion(allPlayers, excludeIds);

    if (!nextQuestion) {
      console.error(`[EDGE_NEXT_QUESTION] Failed to generate next question`);
      return new Response(JSON.stringify({
        error: 'Failed to generate next question.',
        details: 'Could not create a valid question with available player data'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    console.log(`[EDGE_NEXT_QUESTION] Generated next question:`, {
      questionId: nextQuestion.questionId,
      correctPlayerId: nextQuestion.correctPlayerId,
      choicesCount: nextQuestion.choices.length
    });

    // Update room with new question and reset answers
    const now = new Date();
    const updateData = {
      current_question_data: nextQuestion,
      current_round_answers: [], // Reset answers for new question
      current_round_number: (roomData.current_round_number || 1) + 1,
      question_sequence: (roomData.question_sequence || 0) + 1, // Increment sequence
      question_started_at: now.toISOString(), // Track when this new question starts
      last_activity_timestamp: now.toISOString(),
      transition_until: null, // Clear any transition period
      next_question_data: null // Clear pre-generated question
    };

    console.log(`[EDGE_NEXT_QUESTION] Updating room with new question...`);
    const { data: updateResult, error: updateError } = await supabaseAdmin
      .from('game_rooms')
      .update(updateData)
      .eq('id', roomId)
      .eq('status', 'active') // Ensure game is still active
      .eq('question_sequence', roomData.question_sequence || 0) // Prevent race conditions with sequence
      .select('id, current_question_data, current_round_number, question_sequence')

    if (updateError) {
      console.error(`[EDGE_NEXT_QUESTION] Error updating room:`, updateError);
      return new Response(JSON.stringify({ 
        error: 'Failed to update game.',
        details: updateError.message
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    if (!updateResult || updateResult.length === 0) {
      console.error(`[EDGE_NEXT_QUESTION] Room update failed - no rows affected. Another player may have already advanced the question.`);
      return new Response(JSON.stringify({ 
        error: 'Another player already advanced to the next question.',
        details: 'The question was already updated by another player',
        alreadyAdvanced: true
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 409,
      });
    }

    console.log(`[EDGE_NEXT_QUESTION] Next question set successfully for room ${roomId}`);
    console.log(`[EDGE_NEXT_QUESTION] New question details:`, {
      questionId: nextQuestion.questionId,
      roundNumber: updateData.current_round_number,
      timestamp: new Date().toISOString()
    });

    // Return success response
    return new Response(JSON.stringify({ 
      message: 'Next question set successfully!',
      questionId: nextQuestion.questionId,
      roundNumber: updateData.current_round_number,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error: any) {
    console.error('[EDGE_NEXT_QUESTION] Unexpected error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error.message
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})
