-- Create a trigger-based approach for transition monitoring
-- This uses pg_notify to alert when transitions are needed

-- Function to check if transition is needed
CREATE OR REPLACE FUNCTION check_transition_needed()
RETURNS TRIGGER AS $$
BEGIN
  -- Only check if the game is active and has a transition deadline
  IF NEW.status = 'active' AND NEW.transition_deadline IS NOT NULL THEN
    -- Check if deadline has passed
    IF NEW.transition_deadline <= NOW() THEN
      -- Send notification that transition is needed
      PERFORM pg_notify('transition_needed', json_build_object(
        'room_id', NEW.id,
        'deadline', NEW.transition_deadline
      )::text);
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on game_rooms updates
DROP TRIGGER IF EXISTS check_game_transitions ON game_rooms;
CREATE TRIGGER check_game_transitions
  AFTER INSERT OR UPDATE ON game_rooms
  FOR EACH ROW
  EXECUTE FUNCTION check_transition_needed();

-- Create a function to manually check all games
CREATE OR REPLACE FUNCTION check_all_game_transitions()
<PERSON><PERSON><PERSON><PERSON> void AS $$
DECLARE
  game_record RECORD;
BEGIN
  -- Find all games that need transitions
  FOR game_record IN 
    SELECT id, transition_deadline
    FROM game_rooms
    WHERE status = 'active' 
      AND transition_deadline IS NOT NULL
      AND transition_deadline <= NOW()
  LOOP
    -- Trigger the transition monitor for each game
    PERFORM pg_notify('transition_needed', json_build_object(
      'room_id', game_record.id,
      'deadline', game_record.transition_deadline
    )::text);
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Schedule periodic checks using pg_cron (if available)
-- Note: This requires pg_cron extension to be enabled
-- SELECT cron.schedule('check-transitions', '*/5 * * * * *', 'SELECT check_all_game_transitions();');