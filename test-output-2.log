=== Starting Multiplayer Game Flow Test ===
Testing at: http://localhost:3000
✓ Dev server is running
Detected WSL environment, using system chromium-browser
Running in headless mode
Using browser at: /usr/bin/chromium-browser

[HOST] Navigating to app...
[HOST Browser] [Realtime] Realtime subscription effect triggered <PERSON><PERSON><PERSON><PERSON><PERSON>@object
[HOST Browser] [Realtime] Skipping subscription setup - auth still loading
[HOST Browser] [Realtime] Realtime subscription effect triggered <PERSON><PERSON><PERSON><PERSON><PERSON>@object
[HOST Browser] [Realtime] Skipping subscription setup - auth still loading
[HOST Browser] [Realtime] Realtime subscription effect triggered J<PERSON>Hand<PERSON>@object
[HOST Browser] [Realtime] No user or no supabase client - cleaning up all channels
[host] Screenshot saved: test-screenshots-game-flow/host-01-initial-load-2025-07-08T00-44-34-267Z.png

[GUEST] Navigating to app...
[GUEST Browser] [Realtime] Realtime subscription effect triggered J<PERSON><PERSON><PERSON><PERSON>@object
[GUEST Browser] [Realtime] Skipping subscription setup - auth still loading
[GUEST Browser] [Realtime] Realtime subscription effect triggered <PERSON><PERSON><PERSON><PERSON><PERSON>@object
[GUEST Browser] [Realtime] Skipping subscription setup - auth still loading
[GUEST Browser] [Realtime] Realtime subscription effect triggered JS<PERSON>andle@object
[GUEST Browser] [Realtime] No user or no supabase client - cleaning up all channels
[guest] Screenshot saved: test-screenshots-game-flow/guest-01-initial-load-2025-07-08T00-44-37-935Z.png
Waiting for app to fully initialize...

=== PHASE 1: Authentication ===
[HOST] Checking authentication status...
[HOST] Waiting for page to finish loading...
[HOST] Page loaded, checking auth state
[HOST] Not logged in, looking for Multiplayer Mode button...
[HOST] Page info: {
  bodyText: 'Login / Sign Up\n' +
    'Timed Mode\n' +
    'RECOGNITION COMBINE\n' +
    'Normal Mode\n' +
    'Single-player Mode\n' +
    'Multiplayer Mode\n' +
    'Score\n' +
    '0\n' +
    '\n' +
    'Streak: 0\n' +
    '\n' +
    'Best Streak: 0\n' +
    '\n' +
    'Best Normal: 0\n' +
    '\n' +
    'Best Timed: 0\n' +
    '\n' +
    'Recent Answers\n' +
    '\n' +
    'No answers yet.\n' +
    '\n' +
    "WHO'S THIS ACTIVE NFL PLAYER?\n" +
    'Tylan Grable\n' +
    'Jordan Magee\n' +
    'Jason Ivey\n' +
    'Ashton Dulin\n' +
    'Player Info\n' +
    '\n' +
    'Select a player to view details',
  buttonTexts: [
    'Login / Sign Up',
    'Timed Mode',
    'Normal Mode',
    'Single-player Mode',
    'Multiplayer Mode',
    'Tylan Grable',
    'Jordan Magee',
    'Jason Ivey',
    'Ashton Dulin'
  ],
  hasLoadingText: false,
  visibleElements: 16
}
[HOST] Clicked Multiplayer Mode, waiting for auth modal...
[host] Screenshot saved: test-screenshots-game-flow/host-02-after-multiplayer-click-2025-07-08T00-44-44-287Z.png
[HOST] Modal check: { hasModal: false, inputCount: 2, inputNames: [ 'text', 'password' ] }
[HOST] Auth form is visible
[HOST] Filling in credentials...
[HOST] Credentials entered
[host] Screenshot saved: test-screenshots-game-flow/host-03-credentials-entered-2025-07-08T00-44-45-649Z.png
[HOST] Looking for submit button...
[HOST] Clicked submit button
[host] Screenshot saved: test-screenshots-game-flow/host-02-after-submit-2025-07-08T00-44-48-847Z.png
[HOST] ✓ Logged in successfully - auth modal closed and game visible
[host] Screenshot saved: test-screenshots-game-flow/host-02-logged-in-2025-07-08T00-44-48-992Z.png

[GUEST] Checking authentication status...
[GUEST] Page loaded, checking auth state
[GUEST] Not logged in, clicking Multiplayer Mode to trigger auth modal...
[GUEST] Clicked Multiplayer Mode, waiting for auth modal...
[GUEST] Auth form is visible
[GUEST] Filling in credentials...
[GUEST] Credentials entered
[guest] Screenshot saved: test-screenshots-game-flow/guest-03-credentials-entered-2025-07-08T00-44-51-423Z.png
[GUEST] Looking for submit button...
[GUEST] ✓ Logged in successfully - auth modal closed and game visible
[guest] Screenshot saved: test-screenshots-game-flow/guest-02-logged-in-2025-07-08T00-44-53-625Z.png

=== PHASE 2: Create and Join Room ===
[HOST] Switching to multiplayer mode...
[host] Screenshot saved: test-screenshots-game-flow/host-03-multiplayer-mode-2025-07-08T00-44-55-786Z.png
[HOST] Need to sign in again for multiplayer mode...
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW GLOBAL subscriptions for user in lobby
[HOST Browser] [Realtime] Successfully subscribed to lobby channel
[HOST Browser] [Realtime] Subscription successful, resetting retry count and connection status
[HOST] Signed in for multiplayer
[host] Screenshot saved: test-screenshots-game-flow/host-03-multiplayer-signed-in-2025-07-08T00-45-01-501Z.png
[HOST] Waiting for multiplayer lobby to load...
[HOST] Creating game room...
[HOST Browser] [Client] [CREATE_ROOM] handleCreateRoom called
[HOST Browser] [Client] [CREATE_ROOM] User 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 attempting to create room
[HOST Browser] [Client] [CREATE_ROOM] Checking for existing rooms hosted by user 6b6b4242-a0b8-4f36-8811-14bc815c0ef3...
[HOST Browser] [Client] [CREATE_ROOM] Found 1 existing rooms to analyze: JSHandle@array
[HOST Browser] [Client] [CREATE_ROOM] Found 1 stale rooms to clean up
[HOST Browser] [Client] [CREATE_ROOM] Analyzing room for cleanup: 46e9bdca-e58d-4ad1-bb48-0df40b022cdf (fresh's Game)
[HOST Browser] [Client] [CREATE_ROOM] Room 46e9bdca-e58d-4ad1-bb48-0df40b022cdf analysis: JSHandle@object
[HOST Browser] [Client] [CREATE_ROOM] Processing room 46e9bdca-e58d-4ad1-bb48-0df40b022cdf for host departure...
[HOST Browser] [Client] [CREATE_ROOM] Deleting empty/waiting room 46e9bdca-e58d-4ad1-bb48-0df40b022cdf...
[HOST Browser] [Client] [CREATE_ROOM] ✅ Deleted all players from room 46e9bdca-e58d-4ad1-bb48-0df40b022cdf
[HOST Browser] [Client] [CREATE_ROOM] ✅ Successfully deleted room 46e9bdca-e58d-4ad1-bb48-0df40b022cdf
[HOST Browser] [Client] [CREATE_ROOM] Refreshing lobby after cleanup...
[HOST Browser] [Client] [CREATE_ROOM] Creating new room...
[HOST Browser] [Client] [CREATE_ROOM] Inserting new room with data: JSHandle@object
[HOST Browser] [Client] [CREATE_ROOM] *** ROOM CREATED SUCCESSFULLY *** {"id":"71252c8b-922e-4af7-920f-e57f327123c3","created_at":"2025-07-08T00:45:00.905+00:00","status":"waiting","host_id":"6b6b4242-a0b8-4f36-8811-14bc815c0ef3","current_question_data":null,"multiplayer_mode":"competitive","room_code":null,"title":"fresh's Game","game_duration_seconds":300,"max_players":4,"current_round_number":1,"current_bonus_level":0,"current_turn_user_id":null,"game_state_details":null,"game_start_timestamp":null,"game_end_timestamp_target":null,"player_scores":{},"current_round_ends_at":null,"game_ended_at":null,"last_activity_timestamp":"2025-07-08T00:45:00.905+00:00","current_round_answers":[],"current_question_ends_at":null,"first_correct_answer_timestamp":null,"qualified_for_bonus":[],"updated_at":"2025-07-08T00:45:00.916111+00:00","asked_q0_player_ids":[],"original_player_ids":[],"player_bonus_levels":{},"transition_until":null,"next_question_data":null,"question_started_at":null,"question_sequence":0,"transition_deadline":null}
[HOST Browser] [Client] [CREATE_ROOM] *** ABOUT TO CALL HANDLEJOINROOM *** with room ID: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Client] *** HANDLEJOINROOM ENTRY *** handleJoinRoomAttempt called for room: 71252c8b-922e-4af7-920f-e57f327123c3. Current activeRoomId: null, User: 6b6b4242-a0b8-4f36-8811-14bc815c0ef3
[HOST Browser] [Client] *** CRITICAL DEBUG *** Room ID analysis: JSHandle@object
[HOST Browser] [Client] Joining room - React useEffect hooks will handle subscription transitions
[HOST Browser] [Client] *** STARTING JOIN PROCESS *** User 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 attempting to join/rejoin room: 71252c8b-922e-4af7-920f-e57f327123c3 via server-side logic.
[HOST Browser] [Client] *** STANDARD JOIN/REJOIN *** Performing full validation checks.
[HOST Browser] [Client] *** STEP 1: ROOM VALIDATION *** Fetching room details for validation
[HOST Browser] [Client] STEP 1 COMPLETE: Room validation successful: JSHandle@object
[HOST Browser] [Client] *** STANDARD JOIN PATH *** Performing full player existence validation
[HOST Browser] [Client] *** STEP 2: PLAYER EXISTENCE CHECK *** Checking if player 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 is already in game_players for room 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Client] STEP 2 COMPLETE: Player 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 does NOT exist in game_players for room 71252c8b-922e-4af7-920f-e57f327123c3. This is a NEW JOIN.
[HOST Browser] [Client] *** STEP 3: NEW JOIN VALIDATION *** Performing additional checks for new join
[HOST Browser] [Client] *** STEP 3.1: STATUS VALIDATION *** Checking room status for new join. Current status: waiting
[HOST Browser] [Client] STEP 3 COMPLETE: New join validation successful. Room has capacity: 0/4
[HOST Browser] [Client] *** STEP 4: INSERT ATTEMPT *** Attempting INSERT into game_players
[HOST Browser] [Client] VALIDATED INSERT: Attempting INSERT into game_players with validated data: JSHandle@object
[HOST] Request failed: https://xmyxuvuimebjltnaamox.supabase.co/rest/v1/game_players?select=*&room_id=eq.71252c8b-922e-4af7-920f-e57f327123c3&is_connected=eq.true net::ERR_ABORTED
[HOST Browser] [Client] Step 4 complete: User 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 successfully made NEW JOIN (inserted) to game_players for room 71252c8b-922e-4af7-920f-e57f327123c3. Data: JSHandle@object
[HOST Browser] [Client] *** USING COMPREHENSIVE STATE SYNC for new join ***
[HOST Browser] [SYNC_STATE] *** STARTING FULL ROOM STATE SYNC *** for room 71252c8b-922e-4af7-920f-e57f327123c3, called by: new_join_success
[HOST Browser] [SYNC_STATE] This should ELIMINATE the "Waiting for game data..." issue
[HOST Browser] [SYNC_STATE] Fetching room details and players concurrently...
[HOST Browser] [SYNC_STATE] *** FETCHED DATA SUCCESS *** JSHandle@object
[HOST Browser] [SYNC_STATE] *** SETTING ALL GAME STATES ATOMICALLY ***
[HOST Browser] [SYNC_STATE] This is the MISSING PIECE that fixes the race condition
[HOST Browser] [SYNC_STATE] *** STATE SYNC COMPLETE *** JSHandle@object
[HOST Browser] [Client] Client active state set for room 71252c8b-922e-4af7-920f-e57f327123c3. Awaiting data fetches and UI transition.
[HOST] ✓ Room created, in waiting room
[HOST Browser] [Realtime] Cleaning up channel: realtime:lobby-global
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Player subscription setup complete for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] ✅ Successfully subscribed to room channel: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Subscriptions active for: JSHandle@object
[HOST Browser] [Realtime] Subscription successful, resetting retry count and connection status
[host] Screenshot saved: test-screenshots-game-flow/host-03-waiting-room-2025-07-08T00-45-02-428Z.png
[HOST] Waiting for player count to update...
[HOST Browser] [Client] [CREATE_ROOM] *** AUTO-JOIN COMPLETED SUCCESSFULLY ***
[HOST Browser] [Client] [CREATE_ROOM] Fetching initial player list for host after auto-join
[HOST Browser] [Client] [CREATE_ROOM] *** ROOM CREATION AND HOST AUTO-JOIN FULLY COMPLETED *** Room: 71252c8b-922e-4af7-920f-e57f327123c3
[GUEST] Switching to multiplayer mode...
[HOST Browser] [Realtime] Cleaning up channel: realtime:room-71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] 🔒 Channel closed: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Player subscription setup complete for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] ✅ Successfully subscribed to room channel: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Subscriptions active for: JSHandle@object
[GUEST] Need to sign in again for multiplayer mode...
[GUEST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[GUEST Browser] [Realtime] ✅ Setting up NEW GLOBAL subscriptions for user in lobby
[GUEST Browser] [Realtime] Successfully subscribed to lobby channel
[GUEST Browser] [Realtime] Subscription successful, resetting retry count and connection status
[GUEST] Signed in for multiplayer
[GUEST] Looking for room to join...
[GUEST Browser] [Client] Refresh List button clicked.
[GUEST] Refreshed room list
[GUEST] Step 1: Looking for room to click and select...
[GUEST] Room clicked, waiting for details panel...
[GUEST] Step 2: Looking for Join This Room button...
[GUEST Browser] [Client] *** HANDLEJOINROOM ENTRY *** handleJoinRoomAttempt called for room: 71252c8b-922e-4af7-920f-e57f327123c3. Current activeRoomId: null, User: 348453c6-4975-42fe-a34e-8f9a291ef982
[GUEST Browser] [Client] *** CRITICAL DEBUG *** Room ID analysis: JSHandle@object
[GUEST Browser] [Client] Joining room - React useEffect hooks will handle subscription transitions
[GUEST Browser] [Client] *** STARTING JOIN PROCESS *** User 348453c6-4975-42fe-a34e-8f9a291ef982 attempting to join/rejoin room: 71252c8b-922e-4af7-920f-e57f327123c3 via server-side logic.
[GUEST Browser] [Client] *** STANDARD JOIN/REJOIN *** Performing full validation checks.
[GUEST Browser] [Client] *** STEP 1: ROOM VALIDATION *** Fetching room details for validation
[GUEST Browser] [Client] STEP 1 COMPLETE: Room validation successful: JSHandle@object
[GUEST Browser] [Client] *** STANDARD JOIN PATH *** Performing full player existence validation
[GUEST Browser] [Client] *** STEP 2: PLAYER EXISTENCE CHECK *** Checking if player 348453c6-4975-42fe-a34e-8f9a291ef982 is already in game_players for room 71252c8b-922e-4af7-920f-e57f327123c3
[GUEST Browser] [Client] STEP 2 COMPLETE: Player 348453c6-4975-42fe-a34e-8f9a291ef982 does NOT exist in game_players for room 71252c8b-922e-4af7-920f-e57f327123c3. This is a NEW JOIN.
[GUEST Browser] [Client] *** STEP 3: NEW JOIN VALIDATION *** Performing additional checks for new join
[GUEST Browser] [Client] *** STEP 3.1: STATUS VALIDATION *** Checking room status for new join. Current status: waiting
[GUEST Browser] [Client] STEP 3 COMPLETE: New join validation successful. Room has capacity: 0/4
[GUEST Browser] [Client] *** STEP 4: INSERT ATTEMPT *** Attempting INSERT into game_players
[GUEST Browser] [Client] VALIDATED INSERT: Attempting INSERT into game_players with validated data: JSHandle@object
[GUEST] Request failed: https://xmyxuvuimebjltnaamox.supabase.co/rest/v1/game_players?select=*&room_id=eq.71252c8b-922e-4af7-920f-e57f327123c3&is_connected=eq.true net::ERR_ABORTED
[GUEST Browser] [Client] Step 4 complete: User 348453c6-4975-42fe-a34e-8f9a291ef982 successfully made NEW JOIN (inserted) to game_players for room 71252c8b-922e-4af7-920f-e57f327123c3. Data: JSHandle@object
[GUEST Browser] [Client] *** USING COMPREHENSIVE STATE SYNC for new join ***
[GUEST Browser] [SYNC_STATE] *** STARTING FULL ROOM STATE SYNC *** for room 71252c8b-922e-4af7-920f-e57f327123c3, called by: new_join_success
[GUEST Browser] [SYNC_STATE] This should ELIMINATE the "Waiting for game data..." issue
[GUEST Browser] [SYNC_STATE] Fetching room details and players concurrently...
[HOST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[HOST Browser] [Realtime] *** NEW PLAYER JOINED *** Room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[HOST Browser] [Realtime] Triggering player fetch due to realtime event (INSERT)
[GUEST Browser] [SYNC_STATE] *** FETCHED DATA SUCCESS *** JSHandle@object
[GUEST Browser] [SYNC_STATE] *** SETTING ALL GAME STATES ATOMICALLY ***
[GUEST Browser] [SYNC_STATE] This is the MISSING PIECE that fixes the race condition
[GUEST Browser] [SYNC_STATE] *** STATE SYNC COMPLETE *** JSHandle@object
[GUEST Browser] [Client] Client active state set for room 71252c8b-922e-4af7-920f-e57f327123c3. Awaiting data fetches and UI transition.
[GUEST Browser] [Realtime] Cleaning up channel: realtime:lobby-global
[GUEST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[GUEST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 71252c8b-922e-4af7-920f-e57f327123c3
[GUEST Browser] [Realtime] Player subscription setup complete for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[GUEST Browser] [Realtime] Channel subscription status change: JSHandle@object
[GUEST Browser] [Realtime] ✅ Successfully subscribed to room channel: 71252c8b-922e-4af7-920f-e57f327123c3
[GUEST Browser] [Realtime] Subscriptions active for: JSHandle@object
[GUEST Browser] [Realtime] Subscription successful, resetting retry count and connection status
[GUEST] ✓ Joined room successfully
[guest] Screenshot saved: test-screenshots-game-flow/guest-03-waiting-room-2025-07-08T00-45-21-203Z.png

[VERIFICATION] Checking player counts...
[HOST] Sees 2 player(s)
[GUEST] Sees 2 player(s)

[HOST] Marking ready...
[HOST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[HOST Browser] [Realtime] Triggering player fetch due to realtime event (UPDATE)
[GUEST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[GUEST Browser] [Realtime] Triggering player fetch due to realtime event (UPDATE)
[GUEST] Marking ready...
[GUEST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[GUEST Browser] [Realtime] Triggering player fetch due to realtime event (UPDATE)
[HOST Browser] [Realtime] *** PLAYER UPDATE EVENT *** for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[HOST Browser] [Realtime] Triggering player fetch due to realtime event (UPDATE)
[host] Screenshot saved: test-screenshots-game-flow/host-04-both-ready-2025-07-08T00-45-22-460Z.png
[guest] Screenshot saved: test-screenshots-game-flow/guest-04-both-ready-2025-07-08T00-45-22-653Z.png
[HOST] Room state: {
  "textContent": [
    "Single-player Mode",
    "Multiplayer Mode",
    "Personal BestsNo records yet.Global Top 5 ❯",
    "No records yet.",
    "Room: fresh's Game",
    "Players (2/4):",
    "Ready to start game!",
    "Refresh Players",
    "Leave Game"
  ],
  "playerCount": 0,
  "buttons": [
    "Sign Out",
    "Single-player Mode",
    "Multiplayer Mode",
    "Global Top 5 ❯",
    "Ready ✓",
    "Start Game",
    "Refresh Players",
    "Leave Game"
  ]
}
[HOST] Waiting for room state to stabilize...
[GUEST Browser] [Realtime] Cleaning up channel: realtime:room-71252c8b-922e-4af7-920f-e57f327123c3
[GUEST Browser] [Realtime] Channel subscription status change: JSHandle@object
[GUEST Browser] [Realtime] 🔒 Channel closed: 71252c8b-922e-4af7-920f-e57f327123c3
[GUEST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[GUEST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 71252c8b-922e-4af7-920f-e57f327123c3
[GUEST Browser] [Realtime] Player subscription setup complete for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[GUEST Browser] [Realtime] Channel subscription status change: JSHandle@object
[GUEST Browser] [Realtime] ✅ Successfully subscribed to room channel: 71252c8b-922e-4af7-920f-e57f327123c3
[GUEST Browser] [Realtime] Subscriptions active for: JSHandle@object
[HOST] Waiting for Start Game button to appear...
[HOST] ✓ Start Game button is now available

=== PHASE 3: Start Game and Verify Transition ===
[HOST] Starting game...
[HOST Browser] [Client] Found Start Game button, clicking it
[HOST Browser] [Client] [START_GAME_ATTEMPT] Beginning start game process JSHandle@object
[HOST Browser] [Client] [START_GAME] Fetching latest room details from server before host check...
[HOST] Waiting for Edge Function to process...
[HOST Browser] [Client] [START_GAME] Fresh room data fetched: JSHandle@object
[HOST Browser] [Client] [START_GAME] Host verification check: JSHandle@object
[HOST Browser] [Client] [START_GAME] Pre-flight validation: JSHandle@object
[HOST Browser] [Client] Host 6b6b4242-a0b8-4f36-8811-14bc815c0ef3 attempting to start game in room 71252c8b-922e-4af7-920f-e57f327123c3 - setting isStartingGame to TRUE
[HOST Browser] [Client] [START_GAME_EDGE_CALL] Invoking start-game-handler for room 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Client] Error invoking start-game-handler: JSHandle@error
[HOST Browser] [Client] [START_GAME_ERROR] Setting error message: Edge Function returned a non-2xx status code
[HOST Browser] [Client] [START_GAME_CLEANUP] Clearing isStartingGame flags

[VERIFICATION] Checking game state transition...
[HOST] Waiting for game player image: img[alt="Guess the player"]
[HOST Browser] [Realtime] Cleaning up channel: realtime:room-71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] 🔒 Channel closed: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Realtime subscription effect triggered JSHandle@object
[HOST Browser] [Realtime] ✅ Setting up NEW subscriptions for room: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Player subscription setup complete for room 71252c8b-922e-4af7-920f-e57f327123c3 JSHandle@object
[HOST Browser] [Realtime] Channel subscription status change: JSHandle@object
[HOST Browser] [Realtime] ✅ Successfully subscribed to room channel: 71252c8b-922e-4af7-920f-e57f327123c3
[HOST Browser] [Realtime] Subscriptions active for: JSHandle@object
[HOST] ✗ Timeout waiting for game player image: img[alt="Guess the player"]
[HOST] Screenshot saved: test-screenshots-game-flow/HOST-error-game-player-image-2025-07-08T00-45-40-873Z.png
[HOST] ✗ Failed to transition to active game

=== TEST FAILED ===
Error: Host failed to transition to active game
    at testMultiplayerGameFlow (/mnt/c/Projects/recognition-combine/test-multiplayer-game-flow.js:1086:13)

Keeping browsers open for observation...

❌ MULTIPLAYER GAME FLOW TEST FAILED
