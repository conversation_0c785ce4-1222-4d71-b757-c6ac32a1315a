import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface Props {
  /** Unique key for the animation */
  animationKey: string;
  /** Absolute viewport position where animation should originate */
  originPosition: { x: number; y: number } | null;
}

export function GlobalFallbackAnimation({ animationKey, originPosition }: Props) {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  console.log('[FALLBACK_ANIMATION_DEBUG] GlobalFallbackAnimation rendered:', {
    animationKey,
    originPosition,
    portalContainer: !!portalContainer
  });
  
  useEffect(() => {
    // Find the global animation portal container with retry logic
    const findContainer = () => {
      const container = document.getElementById('global-animation-portal');
      if (container) {
        setPortalContainer(container);
      } else {
        // Retry after a short delay if container not found
        setTimeout(find<PERSON><PERSON><PERSON>, 50);
      }
    };
    findContainer();
  }, []);

  if (!originPosition) return null;

  const animationContent = (
    <div 
      className="absolute pointer-events-none"
      style={{
        left: originPosition.x,
        top: originPosition.y,
        transform: 'translate(-50%, -50%)',
      }}
    >
      <motion.span
        key={animationKey}
        initial={{ opacity: 1, y: -10, scale: 0.9 }}
        animate={{ 
          opacity: 0, 
          y: [-10, -40, -60], // Spring-like upward motion: bounce up, then continue
          scale: [0.9, 1.3, 1.1] // Slight bounce in scale
        }}
        transition={{ 
          duration: 1.0, 
          ease: [0.68, -0.55, 0.265, 1.55], // Bouncy spring easing
          y: {
            duration: 1.0,
            ease: [0.68, -0.55, 0.265, 1.55], // Spring easing for Y movement
          }
        }}
        className="absolute text-yellow-300 font-bold text-lg whitespace-nowrap"
        style={{ textShadow: '2px 2px 3px rgba(0,0,0,0.8)' }}
      >
        +10 🏈
      </motion.span>
    </div>
  );

  // Render through portal to the global container, with fallback to document.body
  return createPortal(animationContent, portalContainer || document.body);
}
