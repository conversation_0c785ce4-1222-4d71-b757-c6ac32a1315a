import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useMemo, useState, useRef } from 'react';
import { createPortal } from 'react-dom';

interface Props {
  /** Every time this number increments (> 0), a new animation plays. */
  trigger: number | null;
  /** Score increase amount (e.g., 10, 15, 25) */
  scoreIncrease: number;
  /** Bonus level for this answer (0 = no bonus, 1 = BQ1, 2 = BQ2, 3+ = BQ3) */
  bonusLevel: number;
  /** Absolute viewport position where animation should originate */
  originPosition: { x: number; y: number } | null;
}

interface FootballCfg {
  initScale: number;
  initX: number;
  angle: number;
  distance: number;
  duration: number;
  delay: number;
  spin: number;
  stretch: number;
}

const MAX_FOOTBALLS = 5;

export function GlobalMultiplayerScoreAnimationEnhanced({ 
  trigger, 
  scoreIncrease, 
  bonusLevel, 
  originPosition 
}: Props) {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [shouldPlay, setShouldPlay] = useState(false);
  const hasStartedRef = useRef(false);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Enhanced visibility handling
  useEffect(() => {
    if (!trigger) return;

    const checkAndStartAnimation = () => {
      // If document is hidden, wait for it to become visible
      if (document.hidden) {
        console.log('[ANIMATION_ENHANCED] Tab hidden, waiting for visibility...');
        
        const handleVisibilityChange = () => {
          if (!document.hidden && !hasStartedRef.current) {
            console.log('[ANIMATION_ENHANCED] Tab now visible, starting animation!');
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            setShouldPlay(true);
            hasStartedRef.current = true;
          }
        };
        
        document.addEventListener('visibilitychange', handleVisibilityChange);
        
        // Cleanup after 10 seconds if tab never becomes visible
        animationTimeoutRef.current = setTimeout(() => {
          document.removeEventListener('visibilitychange', handleVisibilityChange);
          console.log('[ANIMATION_ENHANCED] Animation timeout, tab never became visible');
        }, 10000);
        
        return () => {
          document.removeEventListener('visibilitychange', handleVisibilityChange);
          if (animationTimeoutRef.current) {
            clearTimeout(animationTimeoutRef.current);
          }
        };
      } else {
        // Tab is visible, start animation immediately
        console.log('[ANIMATION_ENHANCED] Tab visible, starting animation immediately');
        setShouldPlay(true);
        hasStartedRef.current = true;
      }
    };

    checkAndStartAnimation();
  }, [trigger]);

  // Portal container setup
  useEffect(() => {
    const findContainer = () => {
      const container = document.getElementById('global-animation-portal');
      if (container) {
        setPortalContainer(container);
        setIsReady(true);
      } else {
        setTimeout(findContainer, 50);
      }
    };
    findContainer();
  }, []);

  // Calculate football configurations
  const footballConfigs = useMemo((): FootballCfg[] => {
    if (!trigger || !shouldPlay) return [];

    const footballCount = Math.min(Math.max(bonusLevel, 1), MAX_FOOTBALLS);
    
    return Array.from({ length: footballCount }, (_, i): FootballCfg => {
      const initScale = 0.9 + Math.random() * 0.2;
      const initX = (Math.random() - 0.5) * 40;
      const angle = -Math.PI / 2 + (Math.random() - 0.5) * (Math.PI / 4);
      const distance = 150 + Math.random() * 80;

      return {
        initScale,
        initX,
        angle,
        distance,
        duration: 0.8 + Math.random() * 0.4,
        delay: i * 0.06,
        spin: (Math.random() - 0.5) * 120,
        stretch: 1.35 + Math.random() * 0.1,
      };
    });
  }, [bonusLevel, trigger, shouldPlay]);

  // Force animation to play when component mounts or visibility changes
  useEffect(() => {
    if (shouldPlay && isReady && portalContainer) {
      // Force a repaint to ensure animations start
      void (portalContainer.offsetHeight);
      
      // Use Web Animations API to ensure animation plays
      const elements = portalContainer.querySelectorAll('.force-animation-play');
      elements.forEach((el) => {
        if (el instanceof HTMLElement) {
          // Force animation restart
          el.style.animationPlayState = 'paused';
          void el.offsetHeight; // Force reflow
          el.style.animationPlayState = 'running';
        }
      });
    }
  }, [shouldPlay, isReady, portalContainer]);

  if (!trigger || scoreIncrease <= 0 || !originPosition || !isReady || !shouldPlay) {
    return null;
  }

  const getScoreDisplay = () => {
    if (bonusLevel === 0) {
      return { text: `+${scoreIncrease}`, color: 'text-yellow-300' };
    } else if (bonusLevel === 1) {
      return { text: `+${scoreIncrease} BQ1!`, color: 'text-green-300' };
    } else if (bonusLevel === 2) {
      return { text: `+${scoreIncrease} BQ2!`, color: 'text-blue-300' };
    } else {
      return { text: `+${scoreIncrease} BQ3!`, color: 'text-purple-300' };
    }
  };

  const scoreDisplay = getScoreDisplay();

  const animationContent = (
    <div
      className="fixed pointer-events-none z-[9999] no-debug-box"
      style={{
        left: originPosition.x,
        top: originPosition.y,
        transform: 'translate(-50%, -50%)',
        width: '300px',
        height: '300px'
      }}
    >
      {/* Score Popup */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`score-${trigger}-${shouldPlay}`}
          initial={{ opacity: 1, y: -25, scale: 1.75 }}
          animate={{ opacity: 0, y: -100, scale: 2.75 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1.2, ease: 'easeOut' }}
          className="absolute left-0 top-0"
          onAnimationStart={() => console.log('[ANIMATION_ENHANCED] Score animation started')}
        >
          <span
            className={`${scoreDisplay.color} text-2xl font-bold whitespace-nowrap`}
            style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.9)' }}
          >
            {scoreDisplay.text}
          </span>
        </motion.div>
      </AnimatePresence>

      {/* Football Animation */}
      <AnimatePresence mode="wait">
        <div
          className="absolute inset-0 flex items-center justify-center pointer-events-none z-40 no-debug-box"
          style={{ transform: "translateY(-150px)", overflow: "visible" }}
        >
          {footballConfigs.map((cfg, i) => {
            const targetX = cfg.initX + Math.cos(cfg.angle) * cfg.distance * 0.35;
            const targetY = Math.sin(cfg.angle) * cfg.distance;

            return (
              <motion.span
                key={`football-${trigger}-${i}-${shouldPlay}`}
                initial={{
                  opacity: 1,
                  scale: cfg.initScale,
                  x: cfg.initX,
                  y: 0,
                  rotate: 0,
                }}
                animate={{
                  opacity: [1, 1, 0],
                  scale: [cfg.initScale, cfg.stretch, cfg.stretch * 0.95],
                  x: [cfg.initX, targetX],
                  y: [0, targetY],
                  rotate: [0, cfg.spin],
                }}
                transition={{
                  duration: cfg.duration,
                  delay: cfg.delay,
                  ease: "easeOut",
                  opacity: {
                    duration: cfg.duration * 0.35,
                    delay: cfg.delay + cfg.duration * 0.65,
                  },
                }}
                exit={{ opacity: 0 }}
                className="text-4xl md:text-5xl select-none force-animation-play"
                style={{
                  textShadow: "1px 1px 3px rgba(0,0,0,0.6)",
                  willChange: "transform, opacity",
                  transformOrigin: "center",
                  transform: "translateZ(0)",
                  animationPlayState: "running !important",
                }}
                onAnimationStart={() => {
                  console.log(`[ANIMATION_ENHANCED] Football ${i} animation started`);
                }}
              >
                🏈
              </motion.span>
            );
          })}
        </div>
      </AnimatePresence>
    </div>
  );

  return createPortal(animationContent, portalContainer || document.body);
}