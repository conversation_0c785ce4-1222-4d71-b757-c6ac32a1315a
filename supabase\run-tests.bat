@echo off
echo ====================================================================
echo            AUTOMATED MULTIPLAYER ROUND ADVANCE TESTS
echo ====================================================================
echo.

REM Check for Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js not found. Please install from https://nodejs.org/
    pause
    exit /b 1
)

REM Check for Puppeteer
if not exist "node_modules\puppeteer" (
    echo Installing Puppeteer...
    call npm install puppeteer
)

echo.
echo Choose test type:
echo 1. Simple version (manual login)
echo 2. Full automated version
echo.
set /p choice="Enter choice (1 or 2): "

if "%choice%"=="1" (
    echo.
    echo Running simple test...
    node test-multiplayer-simple.js
) else if "%choice%"=="2" (
    echo.
    echo Running automated test...
    node test-multiplayer-automated.js
) else (
    echo Invalid choice
)

echo.
pause