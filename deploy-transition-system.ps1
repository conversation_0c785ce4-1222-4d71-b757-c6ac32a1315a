# Deploy the server-authoritative transition system
Write-Host "Deploying server-authoritative transition system..." -ForegroundColor Green

# Change to supabase directory
Set-Location supabase

# Step 1: Apply database migration
Write-Host "`n1. Applying database migration for transition_deadline..." -ForegroundColor Yellow
npx supabase db push

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to apply database migration" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Write-Host "Database migration applied successfully!" -ForegroundColor Green

# Step 2: Deploy updated edge functions
Write-Host "`n2. Deploying updated submit-answer-handler..." -ForegroundColor Yellow
supabase functions deploy submit-answer-handler

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to deploy submit-answer-handler" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Write-Host "`n3. Deploying updated start-game-handler..." -ForegroundColor Yellow
supabase functions deploy start-game-handler

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to deploy start-game-handler" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Write-Host "`n4. Deploying new transition-monitor function..." -ForegroundColor Yellow
supabase functions deploy transition-monitor

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to deploy transition-monitor" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Return to root directory
Set-Location ..

Write-Host "`n✅ Server-authoritative transition system deployed successfully!" -ForegroundColor Green
Write-Host "`nNOTE: You'll need to set up a cron job to call transition-monitor periodically (e.g., every second)" -ForegroundColor Cyan
Write-Host "Example cron expression: */1 * * * * *" -ForegroundColor Cyan