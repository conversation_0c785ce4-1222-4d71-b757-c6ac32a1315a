# Fix the scope issue in the Edge Function
Write-Host "Fixing Edge Function scope issue..." -ForegroundColor Yellow

$functionPath = "functions\start-game-handler\index.ts"
$content = Get-Content $functionPath -Raw

# Fix 1: Update the function signature to accept userClient
$content = $content -replace `
    '(async function generateNewQuestionForRoom\()(supabaseAdmin: ReturnType<typeof createClient>, )(roomId: string\))', `
    '${1}supabaseAdmin: ReturnType<typeof createClient>, userClient: ReturnType<typeof createClient>, ${3}'

# Fix 2: Update the call to generateNewQuestionForRoom to pass userClient
$content = $content -replace `
    '(const firstQuestion = await generateNewQuestionForRoom\()(supabaseAdmin, )(roomId\))', `
    '${1}supabaseAdmin, userClient, ${3}'

# Write the fixed content
$content | Out-File -FilePath $functionPath -Encoding UTF8

Write-Host "Edge Function scope issue fixed!" -ForegroundColor Green
Write-Host "The function now properly passes userClient to generateNewQuestionForRoom" -ForegroundColor Cyan
Write-Host "`nPlease redeploy the Edge Function using one of these methods:" -ForegroundColor Yellow
Write-Host "1. Supabase Dashboard: https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/functions" -ForegroundColor White
Write-Host "2. WSL: cd /mnt/c/Projects/recognition-combine/supabase && npx supabase functions deploy start-game-handler --project-ref xmyxuvuimebjltnaamox" -ForegroundColor White