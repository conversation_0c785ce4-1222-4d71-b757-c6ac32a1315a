# Authentication Fixes Validation Script
Write-Host "Authentication Fixes Validation" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$webAppDir = Join-Path $scriptDir "web-app"
$pageFile = Join-Path $webAppDir "src\app\page.tsx"
$authModalFile = Join-Path $webAppDir "src\components\auth\AuthModal.tsx"

Write-Host "Testing files:" -ForegroundColor Cyan
Write-Host "  Page.tsx: $pageFile" -ForegroundColor Gray
Write-Host "  AuthModal.tsx: $authModalFile" -ForegroundColor Gray
Write-Host ""

# Test 1: Database Schema Fix
Write-Host "TEST 1: Database Schema Fix" -ForegroundColor Yellow
if (Test-Path $pageFile) {
    $pageContent = Get-Content $pageFile -Raw
    
    # Check for actual code (not comments) that references profiles.last_seen_at
    $profilesLastSeenMatches = [regex]::Matches($pageContent, "^[^/]*profiles.*last_seen_at", "Multiline")
    if ($profilesLastSeenMatches.Count -gt 0) {
        Write-Host "  FAILED: Still references profiles.last_seen_at in code" -ForegroundColor Red
    } else {
        Write-Host "  PASSED: No references to profiles.last_seen_at in code" -ForegroundColor Green
    }
    
    if ($pageContent -match "game_players.*last_seen_at") {
        Write-Host "  PASSED: Correctly references game_players.last_seen_at" -ForegroundColor Green
    } else {
        Write-Host "  WARNING: No game_players.last_seen_at references" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ERROR: Cannot find page.tsx" -ForegroundColor Red
}

# Test 2: Authentication State Fix
Write-Host ""
Write-Host "TEST 2: Authentication State Fix" -ForegroundColor Yellow
if (Test-Path $pageFile) {
    $pageContent = Get-Content $pageFile -Raw
    
    if ($pageContent -match "getSession") {
        Write-Host "  PASSED: Uses getSession for fresh auth state" -ForegroundColor Green
    } else {
        Write-Host "  FAILED: Does not get fresh session" -ForegroundColor Red
    }
    
    if ($pageContent -match "freshUser") {
        Write-Host "  PASSED: Uses freshUser variable" -ForegroundColor Green
    } else {
        Write-Host "  FAILED: May use stale user state" -ForegroundColor Red
    }
}

# Test 3: Realtime Cleanup Fix
Write-Host ""
Write-Host "TEST 3: Realtime Cleanup Fix" -ForegroundColor Yellow
if (Test-Path $authModalFile) {
    $authContent = Get-Content $authModalFile -Raw
    
    if ($authContent -match "removeAllChannels") {
        Write-Host "  PASSED: Calls removeAllChannels in sign-out" -ForegroundColor Green
    } else {
        Write-Host "  FAILED: No realtime cleanup in sign-out" -ForegroundColor Red
    }
} else {
    Write-Host "  ERROR: Cannot find AuthModal.tsx" -ForegroundColor Red
}

Write-Host ""
Write-Host "EXPECTED RESULTS:" -ForegroundColor Cyan
Write-Host "  - No more 400 Bad Request errors" -ForegroundColor White
Write-Host "  - No more 401 Unauthorized errors" -ForegroundColor White  
Write-Host "  - No more WebSocket connection errors" -ForegroundColor White
Write-Host "  - No more 406 Not Acceptable errors" -ForegroundColor White

Write-Host ""
Write-Host "Validation completed!" -ForegroundColor Green 