# Comprehensive multiplayer timing test runner

Write-Host @"
=========================================
MULTIPLAYER ROUND ADVANCE TIMING TESTS
=========================================

This will guide you through 5 test scenarios to verify round advance timing.

RULES REMINDER:
- 7-second hard cap from round start
- When all answer: (last_answer_time + 3s) OR 7s, whichever is FIRST

"@ -ForegroundColor Cyan

# First, show the injection script
Write-Host "Step 1: Getting monitor code ready..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-File", "$PSScriptRoot\inject-timing-monitor.ps1"
Start-Sleep -Seconds 2

# Open timing helper
Write-Host "Step 2: Opening timing helper..." -ForegroundColor Yellow
./test-multiplayer-timing.ps1

Write-Host @"

Step 3: Test Scenarios
"@ -ForegroundColor Yellow

Write-Host @"

SCENARIO A: Fast Response (Both at 1s)
--------------------------------------
1. Start the game round
2. BOTH players answer at the 1-second mark
3. EXPECTED: Round advances at 4.0s (1+3)
4. VERIFY: Console shows "ROUND ADVANCED AT: 4.0s"

"@ -ForegroundColor White -BackgroundColor DarkGreen

Write-Host @"

SCENARIO B: Medium Response (Both at 2s)  
---------------------------------------
1. Start the next round
2. BOTH players answer at the 2-second mark
3. EXPECTED: Round advances at 5.0s (2+3)
4. VERIFY: Console shows "ROUND ADVANCED AT: 5.0s"

"@ -ForegroundColor White -BackgroundColor DarkBlue

Write-Host @"

SCENARIO C: Late Response (Both at 5s)
-------------------------------------
1. Start the next round
2. BOTH players answer at the 5-second mark
3. EXPECTED: Round advances at 7.0s (NOT 8s!)
4. VERIFY: Console shows "ROUND ADVANCED AT: 7.0s"
5. NOTE: The 7s cap overrides (5+3=8s)

"@ -ForegroundColor White -BackgroundColor DarkRed

Write-Host @"

SCENARIO D: Single Player (One at 1s)
------------------------------------
1. Start the next round
2. ONLY Player 1 answers at 1-second mark
3. Player 2 does NOT answer
4. EXPECTED: Round advances at 7.0s
5. VERIFY: Console shows "ROUND ADVANCED AT: 7.0s"

"@ -ForegroundColor Black -BackgroundColor Yellow

Write-Host @"

SCENARIO E: Staggered (P1 at 1s, P2 at 3s)
------------------------------------------
1. Start the next round
2. Player 1 answers at 1-second mark
3. Player 2 answers at 3-second mark
4. EXPECTED: Round advances at 6.0s (3+3)
5. VERIFY: Console shows "ROUND ADVANCED AT: 6.0s"
6. NOTE: Uses P2's time (3s) + 3s transition

"@ -ForegroundColor White -BackgroundColor DarkMagenta

Write-Host @"

WHAT TO LOOK FOR IN CONSOLE:
- CYAN messages: Answer submission times
- PURPLE messages: 3-second transition detection
- GREEN messages: Round advance times

VERIFICATION CHECKLIST:
[ ] Scenario A: Advances at 4.0s ✓
[ ] Scenario B: Advances at 5.0s ✓
[ ] Scenario C: Advances at 7.0s (not 8s) ✓
[ ] Scenario D: Advances at 7.0s ✓
[ ] Scenario E: Advances at 6.0s ✓
[ ] 3-second transition timer appears when appropriate
[ ] No rounds advance before expected time
[ ] Score updates happen after round advance

"@ -ForegroundColor Yellow

Write-Host "Press Enter to see the monitor code again..." -ForegroundColor Green
Read-Host

Get-Content "$env:TEMP\monitor-code.js"