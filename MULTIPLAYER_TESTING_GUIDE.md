# Multiplayer Game Testing Guide

## Overview
This guide provides steps to manually test the multiplayer functionality of the Recognition Combine game.

## Architecture Summary
- **Frontend**: Next.js 15 with React 19
- **Backend**: Supabase (PostgreSQL + Realtime + Edge Functions)
- **Authentication**: <PERSON>pa<PERSON> Auth with email/username
- **Game Modes**: Single-player and Multiplayer (Competitive/Cooperative)

## Prerequisites
1. Start the development server:
   ```bash
   cd web-app
   npm run dev
   ```
2. Ensure Supabase is running (check your Supabase project dashboard)

## Testing Steps

### 1. Initial Page Load
- Navigate to http://localhost:3000
- You should see the main game page with:
  - Game title/header
  - Authentication status (signed out initially)
  - Game mode options

### 2. Authentication Flow
- Look for a "Sign In" or authentication button
- Click to open the authentication modal
- The modal should have:
  - Sign In tab (default)
  - Sign Up tab
  - Username/Email field
  - Password field
  - Submit button

#### Test Account Creation:
1. Switch to "Sign Up" tab
2. Enter:
   - Username: `testplayer1`
   - Email: `<EMAIL>`
   - Password: `test123456`
3. Submit the form
4. Check for success message

#### Test Sign In:
1. Use "Sign In" tab
2. Enter:
   - Username: `fresh` (or email if that's what was used)
   - Password: `test123`
3. Submit the form
4. After successful login, you should see:
   - User info/avatar
   - Sign Out option
   - Access to multiplayer features

### 3. Multiplayer Room Creation
After signing in:
1. Look for "Multiplayer" button or option
2. Click to enter multiplayer mode
3. Options should include:
   - Create Room
   - Join Room
   - Room List

#### Create a Room:
1. Click "Create Room"
2. Enter room details:
   - Room Name/Title
   - Game Mode (Competitive/Cooperative)
   - Max Players
3. Create the room
4. You should see:
   - Room code (for others to join)
   - Player list (you as host)
   - Ready/Start button

### 4. Testing with Multiple Players
To properly test multiplayer:

#### Option A: Multiple Browser Windows
1. Open an incognito/private window
2. Navigate to http://localhost:3000
3. Sign in with a different account:
   - Username: `testplayer2`
   - Password: `test123456`
4. Join the room using the room code

#### Option B: Multiple Browser Tabs
1. Open a new tab
2. Sign in with different credentials
3. Join the existing room

### 5. Game Flow Testing
Once multiple players are in a room:

1. **Ready Up Phase**:
   - Each player clicks "Ready"
   - Host sees when all players are ready
   - Host can start the game

2. **Active Game**:
   - Questions appear for all players
   - Each player submits answers
   - Scores update in real-time
   - Check leaderboard updates

3. **Game Completion**:
   - Final scores displayed
   - Option to play again
   - Return to lobby

## Key Features to Test

### Real-time Synchronization
- [ ] Player join/leave notifications
- [ ] Ready status updates
- [ ] Score updates during gameplay
- [ ] Question synchronization

### Connection Handling
- [ ] Reconnection after refresh
- [ ] Handling disconnected players
- [ ] Tab focus/blur behavior
- [ ] Network interruption recovery

### Edge Cases
- [ ] Host leaving the room
- [ ] All players leaving and rejoining
- [ ] Starting game with minimum players
- [ ] Maximum player limit enforcement

## Debugging Tools

### Browser Console
Check for:
- WebSocket connection status
- Supabase realtime events
- Error messages
- State updates

### Network Tab
Monitor:
- API calls to Supabase
- WebSocket messages
- Authentication tokens

## Common Issues

1. **"Sign In Required" for Multiplayer**
   - Ensure you're properly authenticated
   - Check session in browser DevTools

2. **Can't See Other Players**
   - Verify both players joined the same room code
   - Check realtime connection status

3. **Game Not Starting**
   - Ensure all players clicked "Ready"
   - Only host can start the game

4. **Disconnection Issues**
   - Check network stability
   - Look for reconnection attempts in console

## API Endpoints
The game uses Supabase Edge Functions:
- `/functions/v1/start-game-handler`
- `/functions/v1/submit-answer-handler`
- `/functions/v1/leave-room-handler`
- `/functions/v1/heartbeat-handler`

## Database Tables
Key tables for multiplayer:
- `game_rooms` - Active game sessions
- `game_players` - Players in rooms
- `profiles` - User profiles
- `player_game_stats` - Historical stats