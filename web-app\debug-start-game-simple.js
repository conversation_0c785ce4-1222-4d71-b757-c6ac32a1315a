// Simple debug script using native fetch
const supabaseUrl = 'https://ktlncsmzllxmtykzrlcc.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0bG5jc216bGx4bXR5a3pybGNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzAzMjAxNDIsImV4cCI6MjA0NTg5NjE0Mn0.o9W-NODzqE8gzZaWGP6lPmUJpdzf54aBMRdZdiqxiSI';

async function debugStartGame() {
  console.log('Starting debug of start-game-handler...\n');

  try {
    // Step 1: Authenticate
    console.log('1. Authenticating as test user fresh...');
    const authResponse = await fetch(`${supabaseUrl}/auth/v1/token?grant_type=password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    if (!authResponse.ok) {
      const error = await authResponse.text();
      console.error('Authentication failed:', authResponse.status, error);
      return;
    }

    const authData = await authResponse.json();
    console.log('✓ Authentication successful');
    console.log('User ID:', authData.user.id);
    console.log('Access Token:', authData.access_token.substring(0, 20) + '...\n');

    // Step 2: Find or create room
    console.log('2. Looking for waiting rooms...');
    const roomsResponse = await fetch(
      `${supabaseUrl}/rest/v1/game_rooms?host_id=eq.${authData.user.id}&status=eq.waiting&order=created_at.desc&limit=1`,
      {
        headers: {
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${authData.access_token}`
        }
      }
    );

    const rooms = await roomsResponse.json();
    let room;

    if (!rooms || rooms.length === 0) {
      console.log('No waiting rooms found. Creating one...');
      
      const roomCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      const createResponse = await fetch(`${supabaseUrl}/rest/v1/game_rooms`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${authData.access_token}`,
          'Prefer': 'return=representation'
        },
        body: JSON.stringify({
          host_id: authData.user.id,
          status: 'waiting',
          max_players: 5,
          game_type: 'multiplayer',
          room_code: roomCode
        })
      });

      room = await createResponse.json();
      console.log('✓ Created room:', room.room_code);
    } else {
      room = rooms[0];
      console.log('✓ Found room:', room.room_code);
    }

    console.log('\nRoom details:');
    console.log('- Room ID:', room.id);
    console.log('- Status:', room.status);

    // Step 3: Call edge function
    console.log('\n3. Calling start-game-handler...');
    const functionResponse = await fetch(`${supabaseUrl}/functions/v1/start-game-handler`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.access_token}`
      },
      body: JSON.stringify({ roomId: room.id })
    });

    console.log('\nResponse:');
    console.log('- Status:', functionResponse.status, functionResponse.statusText);
    
    const responseText = await functionResponse.text();
    console.log('- Body:', responseText);

    try {
      const responseJson = JSON.parse(responseText);
      console.log('\nParsed response:');
      console.log(JSON.stringify(responseJson, null, 2));
    } catch (e) {
      console.log('(Response is not JSON)');
    }

  } catch (error) {
    console.error('\nError:', error.message);
  }
}

debugStartGame();