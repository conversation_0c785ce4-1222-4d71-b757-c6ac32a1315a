#!/usr/bin/env pwsh

Write-Host "Enhanced Start Game Handler Deployment Script" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Set working directory
Set-Location "C:\Projects\recognition-combine\supabase"
Write-Host "Working directory: $(Get-Location)" -ForegroundColor Yellow

# Deploy the function with automatic project selection
Write-Host "Deploying start-game-handler Edge Function..." -ForegroundColor Yellow

try {
    # Use echo to automatically select the first project (index 1)
    $deployOutput = "1" | supabase functions deploy start-game-handler
    
    Write-Host "Deployment completed!" -ForegroundColor Cyan
    Write-Host $deployOutput -ForegroundColor White
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ start-game-handler deployed successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Deployment failed with exit code: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Exception during deployment: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🚀 Enhanced start-game-handler deployment process completed." -ForegroundColor Green
Write-Host "📝 Key improvements implemented:" -ForegroundColor Cyan
Write-Host "   • Optimistic locking with atomic updates" -ForegroundColor White
Write-Host "   • Enhanced race condition detection" -ForegroundColor White
Write-Host "   • Comprehensive error responses with conflict types" -ForegroundColor White
Write-Host "   • Better client-side error handling" -ForegroundColor White
Write-Host "   • Improved logging and diagnostics" -ForegroundColor White 