# Deploy using Supabase API directly

Write-Host "Direct API deployment..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"

# Get your access token
Write-Host "Getting access token..." -ForegroundColor Yellow
$tokenOutput = npx supabase orgs list 2>&1 | Out-String

if ($tokenOutput -match "logged in") {
    Write-Host "Authenticated successfully" -ForegroundColor Green
} else {
    Write-Host "Not authenticated. Run 'npx supabase login' first" -ForegroundColor Red
    exit 1
}

# Try deployment with explicit non-workdir mode
Write-Host "`nAttempting deployment..." -ForegroundColor Cyan

# Change to a neutral directory first
Push-Location $env:TEMP

# Deploy from neutral location
$result = npx supabase functions deploy submit-answer-handler --project-ref $projectRef --no-verify-jwt --legacy-bundle 2>&1 | Out-String

Pop-Location

Write-Host $result

if ($result -match "Function deployed") {
    Write-Host "`nSuccess! Function deployed to: https://$projectRef.supabase.co/functions/v1/submit-answer-handler" -ForegroundColor Green
} else {
    Write-Host "`nDeployment may have failed. Check output above." -ForegroundColor Yellow
}