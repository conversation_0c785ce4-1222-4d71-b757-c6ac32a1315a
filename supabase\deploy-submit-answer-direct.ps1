# Direct Edge Function deployment without workdir context

Write-Host "Direct deployment to Supabase (no local context)..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"

# Change to functions directory
Set-Location "functions/submit-answer-handler"

Write-Host "Deploying from: $(Get-Location)" -ForegroundColor Yellow

# Use direct API deployment
$env:SUPABASE_PROJECT_ID = $projectRef
npx supabase functions deploy submit-answer-handler --project-ref $projectRef --no-verify-jwt 2>&1

# Return to supabase directory
Set-Location "../.."

Write-Host "`nDeployment command executed." -ForegroundColor Green