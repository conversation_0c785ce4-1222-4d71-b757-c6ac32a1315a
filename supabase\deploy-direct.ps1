# Direct deployment without status check

Write-Host "Direct deployment of submit-answer-handler..." -ForegroundColor Cyan

# Deploy directly to your Supabase project
npx supabase functions deploy submit-answer-handler --no-verify-jwt --project-ref $env:SUPABASE_PROJECT_ID

if ($LASTEXITCODE -ne 0) {
    Write-Host "`nTrying alternative deployment method..." -ForegroundColor Yellow
    # Try without project ref
    npx supabase functions deploy submit-answer-handler --no-verify-jwt
}

Write-Host "`nDeployment complete!" -ForegroundColor Green