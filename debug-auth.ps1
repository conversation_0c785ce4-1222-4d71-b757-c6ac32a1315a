# Debug Authentication Script
# Check if JWT token user ID matches profile ID

$token = $env:SUPABASE_AUTH_TOKEN

if (-not $token) {
    Write-Host "[ERROR] No auth token found" -ForegroundColor Red
    exit 1
}

Write-Host "[DEBUG] Decoding JWT token..." -ForegroundColor Cyan

# Split JWT token parts
$parts = $token.Split('.')
if ($parts.Length -ne 3) {
    Write-Host "[ERROR] Invalid JWT format" -ForegroundColor Red
    exit 1
}

# Decode payload (second part)
$payload = $parts[1]
# Add padding if needed
while ($payload.Length % 4 -ne 0) {
    $payload += "="
}

try {
    $bytes = [System.Convert]::FromBase64String($payload)
    $jsonString = [System.Text.Encoding]::UTF8.GetString($bytes)
    $tokenData = $jsonString | ConvertFrom-Json
    
    Write-Host "[TOKEN] User ID from JWT: $($tokenData.sub)" -ForegroundColor Yellow
    Write-Host "[TOKEN] Email: $($tokenData.email)" -ForegroundColor Yellow
    Write-Host "[TOKEN] Username: $($tokenData.user_metadata.username)" -ForegroundColor Yellow
    Write-Host "[TOKEN] Expires: $(([DateTimeOffset]::FromUnixTimeSeconds($tokenData.exp)).ToString())" -ForegroundColor Yellow
    
    # Check if token is expired
    $now = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
    if ($tokenData.exp -lt $now) {
        Write-Host "[WARNING] Token is EXPIRED!" -ForegroundColor Red
    } else {
        Write-Host "[OK] Token is valid" -ForegroundColor Green
    }
    
    # Compare with profile ID
    $baseUrl = "https://xmyxuvuimebjltnaamox.supabase.co"
    $anonKey = $env:SUPABASE_ANON_KEY
    
    $headers = @{
        "Content-Type" = "application/json"
        "Authorization" = "Bearer $token"
        "apikey" = $anonKey
    }
    
    Write-Host "[PROFILE] Fetching profile for comparison..." -ForegroundColor Cyan
    $profileResponse = Invoke-RestMethod -Uri "$baseUrl/rest/v1/profiles?select=id" -Method GET -Headers $headers
    
    if ($profileResponse.Count -gt 0) {
        $profileId = $profileResponse[0].id
        Write-Host "[PROFILE] Profile ID from database: $profileId" -ForegroundColor Yellow
        
        if ($tokenData.sub -eq $profileId) {
            Write-Host "[SUCCESS] JWT user ID matches profile ID!" -ForegroundColor Green
        } else {
            Write-Host "[ERROR] JWT user ID ($($tokenData.sub)) does NOT match profile ID ($profileId)" -ForegroundColor Red
        }
    } else {
        Write-Host "[ERROR] No profile found" -ForegroundColor Red
    }
    
} catch {
    Write-Host "[ERROR] Failed to decode token: $($_.Exception.Message)" -ForegroundColor Red
} 