import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';

interface Props {
  imageUrl: string;
  altText: string;
}

// Darker blurDataURL to match bg-gray-700 (#374151)
const DARK_BLUR_DATA_URL = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiMzNzQxNTEiLz48L3N2Zz4=`;

export function PlayerImageDisplay({ imageUrl, altText }: Props) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  useEffect(() => {
    setIsImageLoaded(false); // Reset on new image
  }, [imageUrl]);

  return (
    <div
      className="relative w-full max-w-lg h-80 bg-gray-700 border border-gray-600 rounded-lg mb-6 overflow-hidden shadow-lg flex items-center justify-center"
      style={{ position: 'relative' }}
    >
      {imageUrl ? (
        <Image
          key={imageUrl}
          src={imageUrl}
          alt={altText}
          fill
          sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, 448px"
          priority
          placeholder="blur"
          blurDataURL={DARK_BLUR_DATA_URL}
          unoptimized={imageUrl.endsWith('.gif')}
          onLoad={() => {
            setIsImageLoaded(true);
            console.log('[PlayerImageDisplay] Image loaded via onLoad:', imageUrl);
          }}
          className={cn(
            "object-contain",
            "transition-opacity duration-100 ease-in-out", // Slightly faster transition
            isImageLoaded ? "opacity-100" : "opacity-0"
          )}
        />
      ) : (
        <div className="flex flex-col items-center justify-center h-full text-gray-400">
          <svg className="animate-spin h-10 w-10 text-gray-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading Player...
        </div>
      )}
    </div>
  );
} 