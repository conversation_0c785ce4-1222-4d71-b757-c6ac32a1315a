{"name": "web-app", "version": "0.1.0", "private": true, "engines": {"node": "20.x"}, "scripts": {"dev": "next dev", "build": "next build && node ./scripts/verify-css.js", "build:safe": "next build", "start": "next start", "lint": "npx eslint src/", "lint:next": "next lint", "verify-css": "node ./scripts/verify-css.js", "css:backup": "Copy-Item -Path \"./src/app/globals.css\" -Destination \"./src/app/globals.css.backup\" -Force", "css:restore": "Copy-Item -Path \"./src/app/globals.css.backup\" -Destination \"./src/app/globals.css\" -Force", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.0.8", "lucide-react": "^0.508.0", "next": "15.3.3", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.2.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3.0.0", "@playwright/test": "^1.53.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-config-next": "15.3.3", "playwright": "^1.53.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tw-animate-css": "^1.2.9", "typescript": "^5"}}