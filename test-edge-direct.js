#!/usr/bin/env node

// Direct Edge Function test using curl equivalent
const { execSync } = require('child_process');

// Get a valid auth token first
const SUPABASE_URL = 'https://xmyxuvuimebjltnaamox.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjcyOTA2NjUsImV4cCI6MjA0Mjg2NjY2NX0.uI_gQzFkKlKMTb7hQItE6OtivgjLGByTNRi9Wvv18B4';

console.log('=== Testing Edge Function Directly ===\n');

// First, let's create a test room ID
const testRoomId = 'f6f2a544-bac1-4827-ac49-10104e7a0086'; // Using the room ID from the test log

console.log('Testing with room ID:', testRoomId);
console.log('Edge Function URL:', `${SUPABASE_URL}/functions/v1/start-game-handler`);

try {
  // Call the Edge Function directly with curl
  const curlCommand = `curl -i -X POST '${SUPABASE_URL}/functions/v1/start-game-handler' \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer ${SUPABASE_ANON_KEY}' \
    -H 'apikey: ${SUPABASE_ANON_KEY}' \
    -d '{"roomId": "${testRoomId}"}'`;
  
  console.log('\nExecuting curl command...\n');
  
  const result = execSync(curlCommand, { encoding: 'utf8' });
  console.log('Response:\n', result);
  
  // Parse the response to find the error
  const lines = result.split('\n');
  const bodyStart = lines.findIndex(line => line.trim() === '');
  if (bodyStart !== -1 && bodyStart < lines.length - 1) {
    const body = lines.slice(bodyStart + 1).join('\n');
    try {
      const json = JSON.parse(body);
      console.log('\nParsed response body:', JSON.stringify(json, null, 2));
    } catch (e) {
      console.log('\nResponse body (not JSON):', body);
    }
  }
  
} catch (error) {
  console.error('\nError calling Edge Function:', error.message);
  if (error.stdout) {
    console.log('\nStdout:', error.stdout);
  }
  if (error.stderr) {
    console.log('\nStderr:', error.stderr);
  }
}