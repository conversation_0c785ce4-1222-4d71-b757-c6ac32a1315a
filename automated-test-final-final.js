/**
 * Final Automated Multiplayer Test
 * Uses proper Puppeteer selectors and XPath
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'P1 only → 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1@1s, P2@3s → 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// Helper to click button by text
async function clickButtonWithText(page, text) {
  await page.evaluate((buttonText) => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const button = buttons.find(b => b.textContent && b.textContent.includes(buttonText));
    if (button) button.click();
    else throw new Error(`Button with text "${buttonText}" not found`);
  }, text);
}

// Monitoring code
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0
  };
  
  let lastQ = null;
  setInterval(() => {
    const q = document.querySelector('h2')?.textContent || '';
    if (q.includes('NFL PLAYER') && q !== lastQ) {
      const now = Date.now();
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('Game started');
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({ round: window.testData.currentRound++, time: elapsed });
        console.log('Round ' + window.testData.currentRound + ' at ' + elapsed + 'ms');
      }
      lastQ = q;
    }
  }, 100);
  
  window.submitAt = function(ms) {
    return new Promise((resolve) => {
      const trySubmit = () => {
        if (!window.testData.gameStartTime) {
          setTimeout(trySubmit, 100);
          return;
        }
        
        const elapsed = Date.now() - window.testData.gameStartTime;
        const wait = ms - elapsed;
        
        const submit = () => {
          const btns = Array.from(document.querySelectorAll('button')).filter(b => {
            const t = b.textContent || '';
            return t.length > 0 && t.length < 30 && t.match(/^[A-Za-z\\s\\.\\-\\']+$/) &&
                   !['Ready','Start','Mode','Sign','Create','Join','Leave','Host','Back'].some(w => t.includes(w));
          });
          
          if (btns.length >= 4) {
            const btn = btns[Math.floor(Math.random() * 4)];
            console.log('Submit: ' + btn.textContent);
            btn.click();
            resolve(true);
          } else {
            setTimeout(submit, 100);
          }
        };
        
        setTimeout(submit, Math.max(0, wait));
      };
      trySubmit();
    });
  };
`;

async function runTest() {
  console.log('🎯 Final Automated Multiplayer Test');
  console.log('==================================\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Launch
    console.log('🚀 Launching...');
    const opts = {
      headless: false,
      executablePath: '/usr/bin/chromium-browser',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--window-size=1200,900']
    };
    
    browser1 = await puppeteer.launch({ ...opts, args: [...opts.args, '--window-position=0,0'] });
    browser2 = await puppeteer.launch({ ...opts, args: [...opts.args, '--window-position=600,0'] });
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    
    // Load
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2' }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2' })
    ]);
    await delay(3000);
    
    // Monitor
    await page1.evaluate(monitoringCode);
    await page2.evaluate(monitoringCode);
    
    // Sign in
    console.log('🔐 Signing in...');
    
    // P1
    await clickButtonWithText(page1, 'Login');
    await delay(2000);
    await page1.type('input[placeholder*="Username"]', CONFIG.player1.email);
    await page1.type('input[placeholder*="Password"]', CONFIG.player1.password);
    await page1.evaluate(() => document.querySelector('form').requestSubmit());
    await delay(5000);
    
    // P2
    await clickButtonWithText(page2, 'Login');
    await delay(2000);
    await page2.type('input[placeholder*="Username"]', CONFIG.player2.email);
    await page2.type('input[placeholder*="Password"]', CONFIG.player2.password);
    await page2.evaluate(() => document.querySelector('form').requestSubmit());
    await delay(5000);
    console.log('  ✅ Signed in\n');
    
    // Multiplayer
    console.log('🎮 Multiplayer...');
    await clickButtonWithText(page1, 'Multiplayer Mode');
    await delay(3000);
    await clickButtonWithText(page2, 'Multiplayer Mode');
    await delay(3000);
    
    // Create
    console.log('🏠 Room...');
    await clickButtonWithText(page1, 'Host Game');
    await delay(3000);
    
    // Join - Two steps
    // Step 1: Click room
    await page2.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*'));
      for (const el of elements) {
        if (el.textContent && 
            el.textContent.includes("fresh's Game") && 
            el.textContent.includes("Players:") &&
            el.children.length < 10) {
          el.click();
          return;
        }
      }
    });
    
    await delay(2000);
    
    // Step 2: Join button
    await clickButtonWithText(page2, 'Join This Room');
    await delay(3000);
    console.log('  ✅ Joined\n');
    
    // Ready & Start
    console.log('🎮 Starting...');
    await clickButtonWithText(page1, 'Ready');
    await delay(1000);
    await clickButtonWithText(page2, 'Ready');
    await delay(2000);
    await clickButtonWithText(page1, 'Start Game');
    await delay(3000);
    console.log('  ✅ Started!\n');
    
    // Tests
    console.log('📊 Tests:');
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const s = CONFIG.scenarios[i];
      console.log(`\n${i+1}. ${s.name}`);
      
      const tasks = [];
      if (s.p1) tasks.push(page1.evaluate(`window.submitAt(${s.p1})`));
      if (s.p2) tasks.push(page2.evaluate(`window.submitAt(${s.p2})`));
      
      await Promise.all(tasks);
      await delay(8000);
      
      const timing = await page1.evaluate(i => window.testData.roundChanges[i], i);
      
      if (timing) {
        const diff = Math.abs(timing.time - s.expected);
        const pass = diff <= 500;
        console.log(`   ${s.expected} → ${timing.time}ms (${pass ? '✅' : '❌'} ±${diff})`);
        results.push({ ...s, actual: timing.time, pass });
      } else {
        console.log('   ❌ Failed');
        results.push({ ...s, actual: null, pass: false });
      }
    }
    
    // Summary
    const passed = results.filter(r => r.pass).length;
    console.log(`\n📈 RESULTS: ${passed}/${results.length} passed\n`);
    
    if (passed === results.length) {
      console.log('🎉 ALL TESTS PASSED! 🎉\n');
    }
    
    console.log('✅ Complete! Ctrl+C to exit.\n');
    
    process.on('SIGINT', async () => {
      if (browser1) await browser1.close();
      if (browser2) await browser2.close();
      process.exit(0);
    });
    
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    process.exit(1);
  }
}

runTest();