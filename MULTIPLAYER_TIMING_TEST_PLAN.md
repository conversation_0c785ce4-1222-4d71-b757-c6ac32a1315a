# Multiplayer Timing Test Plan

## Overview
This document outlines the manual testing steps to verify the new multiplayer round advance timing rules.

## Round Advance Rules
1. **7 seconds maximum** from start of round
2. **If all players answer within X seconds** (2s default), then round advances in **X + 3 seconds**
3. Take the **shorter** of these two timings

## Test Setup
1. Open two browser windows (one incognito/private)
2. Navigate to http://localhost:3001 in both
3. Login as different users:
   - Browser 1: <EMAIL> (host)
   - Browser 2: <EMAIL> (non-host)

## Test Scenarios

### Test 1: Fast Response (Both players answer within 2 seconds)
1. Host creates room
2. Player 2 joins room
3. Host starts game
4. **Both players answer within 2 seconds**
5. **Expected**: Round advances at 5 seconds (2s + 3s) from round start

### Test 2: Slow Response (Players take more than 2 seconds)
1. Continue from Test 1 (Round 2)
2. Player 1 answers immediately
3. Player 2 waits 3 seconds, then answers
4. **Expected**: Round advances 3 seconds after Player 2 answers

### Test 3: 7-Second Hard Cap
1. Continue from Test 2 (Round 3)
2. Player 1 answers at 1 second
3. Player 2 doesn't answer
4. **Expected**: Round advances at exactly 7 seconds from round start

### Test 4: Very Fast Response
1. Continue from Test 3 (Round 4)
2. Both players answer within 1 second
3. **Expected**: Round advances at 5 seconds (2s + 3s) from round start
   - Note: Even though they answered in 1s, we use the 2s window + 3s

### Test 5: Edge Case - Fast Answer Near 7s Limit
1. Continue from Test 4 (Round 5)
2. Player 1 answers at 4.5 seconds
3. Player 2 answers at 5 seconds (within 2s window)
4. **Expected**: Round should advance at 7 seconds (hard cap), not 7.5s

## Verification Points

### Console Logs to Check
Open browser developer console and look for these logs:

1. **First Answer Detection**:
   ```
   [EDGE_SUBMIT_ANSWER] First answer recorded at: [timestamp]
   ```

2. **Timing Analysis** (when all players answer):
   ```
   [EDGE_SUBMIT_ANSWER] Timing analysis: {
     questionStartTime: ...,
     firstAnswerTime: ...,
     lastAnswerTime: ...,
     allAnswersTimeSeconds: ...,
     responseWindowSeconds: 2
   }
   ```

3. **Fast Response Detection**:
   ```
   [EDGE_SUBMIT_ANSWER] Fast response detected! Using dynamic timing:
   ```

4. **Standard Timing** (slow response):
   ```
   [EDGE_SUBMIT_ANSWER] Standard timing (slow response):
   ```

### UI Elements to Monitor
1. **Timer Display**: Should show correct countdown
2. **Round Number**: Should advance after transition
3. **Score Updates**: Should happen immediately after answering

## Common Issues

### Issue 1: Player 2 doesn't see the game
- **Fix Applied**: Added delay and retry mechanism for non-host players
- **What to see**: "Game Starting... Loading game data..." spinner

### Issue 2: Timer jumps or resets
- **Fix Applied**: Proper timer synchronization with server timestamps
- **What to see**: Smooth countdown without jumps

### Issue 3: Round doesn't advance
- **Check**: transition-monitor logs in Supabase dashboard
- **Fix Applied**: Proper transition_deadline setting

## Success Criteria
✅ All 5 test scenarios pass as expected
✅ No console errors during gameplay
✅ Both players see synchronized game state
✅ Timers display correctly for both players
✅ Rounds advance according to the new timing rules

## Edge Functions to Deploy (if testing on production)
```powershell
cd supabase
.\deploy-submit-answer-handler.ps1
.\deploy-transition-monitor.ps1
.\deploy-start-game-handler.ps1
```

## Database Migration Required
```sql
-- Run this migration before testing:
-- 20250706_add_first_answer_tracking.sql
ALTER TABLE game_rooms
ADD COLUMN first_answer_at timestamptz;
ADD COLUMN all_answers_window_seconds numeric DEFAULT 2.0;
```