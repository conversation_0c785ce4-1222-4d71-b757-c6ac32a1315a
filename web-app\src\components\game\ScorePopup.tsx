import { motion, AnimatePresence } from 'framer-motion';

interface Props {
  scoreChange: number | null;
  trigger: number;
}

export function ScorePopup({ scoreChange, trigger }: Props) {
  if (!scoreChange || scoreChange <= 0) return null;
  
  const initialYOffset = -23; // Move starting position up by 23px
  const animationTravelY = -60; // It travels 60px upwards during animation

  return (
    <AnimatePresence>
      <motion.div
        key={trigger}
        initial={{ opacity: 1, y: initialYOffset, scale: 0.7 }}
        animate={{ opacity: 0, y: initialYOffset + animationTravelY, scale: 1.2 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 1.2, ease: 'easeOut' }}
        className="absolute left-1/2 top-1/3 -translate-x-1/2 z-50 pointer-events-none"
      >
        <span className="text-yellow-300 text-4xl font-bold" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}>
          +{scoreChange}
        </span>
      </motion.div>
    </AnimatePresence>
  );
} 