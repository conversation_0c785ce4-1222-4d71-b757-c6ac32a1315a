create table "public"."game_players" (
    "id" uuid not null default gen_random_uuid(),
    "room_id" uuid not null,
    "user_id" uuid not null,
    "score" integer not null default 0,
    "joined_at" timestamp with time zone default now(),
    "is_ready" boolean not null default false,
    "is_eliminated" boolean not null default false,
    "current_chain_bonus_level" smallint not null default '0'::smallint,
    "last_answer_timestamp" timestamp with time zone
);


alter table "public"."game_players" enable row level security;

create table "public"."game_rooms" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone default now(),
    "status" text not null default '''waiting'''::text,
    "host_id" uuid not null,
    "current_question_data" jsonb,
    "multiplayer_mode" text,
    "room_code" text,
    "title" text,
    "game_duration_seconds" integer not null default 300,
    "max_players" smallint not null default '10'::smallint,
    "current_round_number" integer not null default 1,
    "current_bonus_level" smallint not null default '0'::smallint,
    "current_turn_user_id" uuid,
    "game_state_details" jsonb,
    "game_start_timestamp" timestamp with time zone,
    "game_end_timestamp_target" timestamp with time zone
);


alter table "public"."game_rooms" enable row level security;

create table "public"."player_game_stats" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "game_room_id" uuid not null,
    "game_mode_played" text not null,
    "result" text not null,
    "score_achieved" integer not null default 0,
    "rounds_survived" integer not null default 0,
    "mmr_change" integer not null default 0,
    "played_at" timestamp with time zone not null default now()
);


alter table "public"."player_game_stats" enable row level security;

create table "public"."profiles" (
    "id" uuid not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "username" text not null,
    "mmr" integer not null default 1000,
    "wins" integer not null default 0,
    "losses" integer not null default 0
);


alter table "public"."profiles" enable row level security;

CREATE UNIQUE INDEX game_players_pkey ON public.game_players USING btree (id);

CREATE UNIQUE INDEX game_rooms_pkey ON public.game_rooms USING btree (id);

CREATE UNIQUE INDEX game_rooms_room_code_key ON public.game_rooms USING btree (room_code);

CREATE UNIQUE INDEX player_game_stats_pkey ON public.player_game_stats USING btree (id);

CREATE UNIQUE INDEX profiles_id_key ON public.profiles USING btree (id);

CREATE UNIQUE INDEX profiles_pkey ON public.profiles USING btree (id);

CREATE UNIQUE INDEX profiles_username_key ON public.profiles USING btree (username);

CREATE UNIQUE INDEX user_can_only_be_in_one_room ON public.game_players USING btree (user_id);

alter table "public"."game_players" add constraint "game_players_pkey" PRIMARY KEY using index "game_players_pkey";

alter table "public"."game_rooms" add constraint "game_rooms_pkey" PRIMARY KEY using index "game_rooms_pkey";

alter table "public"."player_game_stats" add constraint "player_game_stats_pkey" PRIMARY KEY using index "player_game_stats_pkey";

alter table "public"."profiles" add constraint "profiles_pkey" PRIMARY KEY using index "profiles_pkey";

alter table "public"."game_players" add constraint "game_players_room_id_fkey" FOREIGN KEY (room_id) REFERENCES game_rooms(id) ON DELETE CASCADE not valid;

alter table "public"."game_players" validate constraint "game_players_room_id_fkey";

alter table "public"."game_players" add constraint "game_players_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE not valid;

alter table "public"."game_players" validate constraint "game_players_user_id_fkey";

alter table "public"."game_players" add constraint "user_can_only_be_in_one_room" UNIQUE using index "user_can_only_be_in_one_room";

alter table "public"."game_rooms" add constraint "game_rooms_current_turn_user_id_fkey" FOREIGN KEY (current_turn_user_id) REFERENCES game_rooms(id) ON DELETE SET NULL not valid;

alter table "public"."game_rooms" validate constraint "game_rooms_current_turn_user_id_fkey";

alter table "public"."game_rooms" add constraint "game_rooms_host_id_fkey" FOREIGN KEY (host_id) REFERENCES profiles(id) ON DELETE CASCADE not valid;

alter table "public"."game_rooms" validate constraint "game_rooms_host_id_fkey";

alter table "public"."game_rooms" add constraint "game_rooms_room_code_key" UNIQUE using index "game_rooms_room_code_key";

alter table "public"."player_game_stats" add constraint "player_game_stats_game_room_id_fkey" FOREIGN KEY (game_room_id) REFERENCES game_rooms(id) ON DELETE SET NULL not valid;

alter table "public"."player_game_stats" validate constraint "player_game_stats_game_room_id_fkey";

alter table "public"."player_game_stats" add constraint "player_game_stats_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL not valid;

alter table "public"."player_game_stats" validate constraint "player_game_stats_user_id_fkey";

alter table "public"."profiles" add constraint "profiles_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."profiles" validate constraint "profiles_id_fkey";

alter table "public"."profiles" add constraint "profiles_id_key" UNIQUE using index "profiles_id_key";

alter table "public"."profiles" add constraint "profiles_username_key" UNIQUE using index "profiles_username_key";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  INSERT INTO public.profiles (id, username)
  VALUES (NEW.id, NEW.raw_user_meta_data->>'username'); -- NEW.id comes from auth.users, NEW.raw_user_meta_data from options.data in signUp
  RETURN NEW;
END;
$function$
;

grant delete on table "public"."game_players" to "anon";

grant insert on table "public"."game_players" to "anon";

grant references on table "public"."game_players" to "anon";

grant select on table "public"."game_players" to "anon";

grant trigger on table "public"."game_players" to "anon";

grant truncate on table "public"."game_players" to "anon";

grant update on table "public"."game_players" to "anon";

grant delete on table "public"."game_players" to "authenticated";

grant insert on table "public"."game_players" to "authenticated";

grant references on table "public"."game_players" to "authenticated";

grant select on table "public"."game_players" to "authenticated";

grant trigger on table "public"."game_players" to "authenticated";

grant truncate on table "public"."game_players" to "authenticated";

grant update on table "public"."game_players" to "authenticated";

grant delete on table "public"."game_players" to "service_role";

grant insert on table "public"."game_players" to "service_role";

grant references on table "public"."game_players" to "service_role";

grant select on table "public"."game_players" to "service_role";

grant trigger on table "public"."game_players" to "service_role";

grant truncate on table "public"."game_players" to "service_role";

grant update on table "public"."game_players" to "service_role";

grant delete on table "public"."game_rooms" to "anon";

grant insert on table "public"."game_rooms" to "anon";

grant references on table "public"."game_rooms" to "anon";

grant select on table "public"."game_rooms" to "anon";

grant trigger on table "public"."game_rooms" to "anon";

grant truncate on table "public"."game_rooms" to "anon";

grant update on table "public"."game_rooms" to "anon";

grant delete on table "public"."game_rooms" to "authenticated";

grant insert on table "public"."game_rooms" to "authenticated";

grant references on table "public"."game_rooms" to "authenticated";

grant select on table "public"."game_rooms" to "authenticated";

grant trigger on table "public"."game_rooms" to "authenticated";

grant truncate on table "public"."game_rooms" to "authenticated";

grant update on table "public"."game_rooms" to "authenticated";

grant delete on table "public"."game_rooms" to "service_role";

grant insert on table "public"."game_rooms" to "service_role";

grant references on table "public"."game_rooms" to "service_role";

grant select on table "public"."game_rooms" to "service_role";

grant trigger on table "public"."game_rooms" to "service_role";

grant truncate on table "public"."game_rooms" to "service_role";

grant update on table "public"."game_rooms" to "service_role";

grant delete on table "public"."player_game_stats" to "anon";

grant insert on table "public"."player_game_stats" to "anon";

grant references on table "public"."player_game_stats" to "anon";

grant select on table "public"."player_game_stats" to "anon";

grant trigger on table "public"."player_game_stats" to "anon";

grant truncate on table "public"."player_game_stats" to "anon";

grant update on table "public"."player_game_stats" to "anon";

grant delete on table "public"."player_game_stats" to "authenticated";

grant insert on table "public"."player_game_stats" to "authenticated";

grant references on table "public"."player_game_stats" to "authenticated";

grant select on table "public"."player_game_stats" to "authenticated";

grant trigger on table "public"."player_game_stats" to "authenticated";

grant truncate on table "public"."player_game_stats" to "authenticated";

grant update on table "public"."player_game_stats" to "authenticated";

grant delete on table "public"."player_game_stats" to "service_role";

grant insert on table "public"."player_game_stats" to "service_role";

grant references on table "public"."player_game_stats" to "service_role";

grant select on table "public"."player_game_stats" to "service_role";

grant trigger on table "public"."player_game_stats" to "service_role";

grant truncate on table "public"."player_game_stats" to "service_role";

grant update on table "public"."player_game_stats" to "service_role";

grant delete on table "public"."profiles" to "anon";

grant insert on table "public"."profiles" to "anon";

grant references on table "public"."profiles" to "anon";

grant select on table "public"."profiles" to "anon";

grant trigger on table "public"."profiles" to "anon";

grant truncate on table "public"."profiles" to "anon";

grant update on table "public"."profiles" to "anon";

grant delete on table "public"."profiles" to "authenticated";

grant insert on table "public"."profiles" to "authenticated";

grant references on table "public"."profiles" to "authenticated";

grant select on table "public"."profiles" to "authenticated";

grant trigger on table "public"."profiles" to "authenticated";

grant truncate on table "public"."profiles" to "authenticated";

grant update on table "public"."profiles" to "authenticated";

grant delete on table "public"."profiles" to "service_role";

grant insert on table "public"."profiles" to "service_role";

grant references on table "public"."profiles" to "service_role";

grant select on table "public"."profiles" to "service_role";

grant trigger on table "public"."profiles" to "service_role";

grant truncate on table "public"."profiles" to "service_role";

grant update on table "public"."profiles" to "service_role";

create policy "Allow player to update their own ready status/score"
on "public"."game_players"
as permissive
for update
to authenticated
using ((auth.uid() = user_id))
with check ((auth.uid() = user_id));


create policy "Allow players in room to see each other"
on "public"."game_players"
as permissive
for select
to authenticated
using (true);


create policy "Allow players to leave rooms they are in"
on "public"."game_players"
as permissive
for delete
to authenticated
using ((auth.uid() = user_id));


create policy "Allow users to join waiting rooms"
on "public"."game_players"
as permissive
for insert
to authenticated
with check (((auth.uid() = user_id) AND (EXISTS ( SELECT 1
   FROM game_rooms gr
  WHERE ((gr.id = game_players.room_id) AND (gr.status = 'waiting'::text))))));


create policy "Allow authenticated read access to active/waiting rooms"
on "public"."game_rooms"
as permissive
for select
to authenticated
using (((status = 'waiting'::text) OR (status = 'active'::text)));


create policy "Allow authenticated users to create rooms"
on "public"."game_rooms"
as permissive
for insert
to authenticated
with check ((auth.uid() = host_id));


create policy "Allow host to delete a waiting room"
on "public"."game_rooms"
as permissive
for delete
to authenticated
using (((auth.uid() = host_id) AND (status = 'waiting'::text)));


create policy "Allow host to update their room"
on "public"."game_rooms"
as permissive
for update
to authenticated
using ((auth.uid() = host_id))
with check ((auth.uid() = host_id));


create policy "Users can read their own game stats"
on "public"."player_game_stats"
as permissive
for select
to authenticated
using ((auth.uid() = user_id));


create policy "Allow users to update their own profile"
on "public"."profiles"
as permissive
for update
to authenticated
using ((auth.uid() = id))
with check ((auth.uid() = id));


create policy "Authenticated users can read public profile info"
on "public"."profiles"
as permissive
for select
to authenticated
using (true);


create policy "Disallow client-side profile deletion"
on "public"."profiles"
as permissive
for delete
to authenticated, anon
using (false);


create policy "Users can view their own profile"
on "public"."profiles"
as permissive
for select
to authenticated
using ((auth.uid() = id));



