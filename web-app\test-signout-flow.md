# Enhanced Sign-Out Flow Testing Guide

This guide helps test the comprehensive sign-out and state management fixes implemented to resolve the "You are already in this waiting room" issue.

## Problem Summary
Users were seeing "You are already in this waiting room" after signing out and back in because:
1. Room/player records weren't being cleaned up properly on sign-out
2. Client state wasn't being reset comprehensively
3. Lobby detail view logic was too aggressive in determining "already connected" status

## Testing Steps

### 1. Test Enhanced Sign-Out Process

1. **Sign in as a user (e.g., 'fresh')**
2. **Switch to Multiplayer mode**
3. **Create a room (becomes host)**
   - Verify you can see your room in the lobby
   - Verify you're in the "in_room" state
4. **Open browser console and run:**
   ```javascript
   debugCurrentStates()
   ```
   - Should show `activeRoomId` is set
   - Should show `playersInRoom` includes your user

5. **Sign out using the AuthModal**
   - Should trigger enhanced sign-out process
   - Watch for console logs: `[SignOut] Enhanced sign-out process initiated`
   - Should call `handleLeaveRoom` if in an active room
   - Should reset all client state

6. **Check state after sign-out:**
   ```javascript
   debugCurrentStates()
   ```
   - All states should be null/empty

### 2. Test Database Cleanup

1. **After sign-out, check if room was properly cleaned up:**
   ```javascript
   debugCheckRoomCleanup('ROOM_ID_FROM_STEP_1')
   ```
   - Should show `roomDeleted: true` if you were the only player
   - Should show `userStillInRoom: false`

### 3. Test Sign-In with Clean State

1. **Sign back in as the same user**
2. **Switch to Multiplayer mode**
3. **Verify clean lobby view:**
   - Should NOT see the old room (if it was properly deleted)
   - Should NOT automatically be "in a room"
   - Should show lobby list properly

### 4. Test Enhanced Lobby Detail Logic

1. **If old room still exists (edge case), click on it**
2. **Verify enhanced logic:**
   - Should NOT show "You are already in this waiting room"
   - Should show appropriate button based on actual connection status
   - Should allow rejoining if needed

### 5. Test Edge Function Directly

If needed, test the leave-room-handler directly:
```javascript
debugTestLeaveRoomHandler('ROOM_ID')
```

## Debug Functions Available

The following debug functions are available in the browser console:

- `debugCurrentStates()` - Shows all current state values
- `debugCheckRoomCleanup(roomId?)` - Checks database cleanup status
- `debugTestLeaveRoomHandler(roomId?)` - Tests Edge Function directly
- `debugInspectAllStates()` - Comprehensive state inspection
- `debugEnhancedSignOut()` - Manually trigger enhanced sign-out

## Expected Behavior After Fixes

1. **On Sign-Out:**
   - User is removed from any active room
   - Empty rooms are deleted
   - All client state is reset
   - Console shows comprehensive logging

2. **On Sign-In:**
   - Clean state verification in console
   - Lobby shows accurate room list
   - No automatic "already in room" assumptions

3. **Lobby Detail View:**
   - Accurate determination of connection status
   - Appropriate buttons (Join/Rejoin/Re-enter)
   - No false "already connected" messages

## Debugging Failed Tests

If tests fail:

1. **Check leave-room-handler logs** in Supabase Edge Functions
2. **Verify RLS policies** allow proper cleanup
3. **Check network requests** in browser dev tools
4. **Use debug functions** to inspect state and database

## Notes

- The enhanced sign-out handler is automatically used by the AuthModal
- All state resets happen in the auth listener
- Realtime subscriptions are properly cleaned up
- Debug functions help identify specific failure points 