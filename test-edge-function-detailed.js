const fetch = require('node-fetch');
require('dotenv').config({ path: './web-app/.env.local' });

async function testEdgeFunction() {
    console.log('=== Testing Edge Function Directly ===');
    
    // Get auth token first
    const authResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1/token?grant_type=password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        },
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123'
        })
    });
    
    const authData = await authResponse.json();
    if (!authData.access_token) {
        console.error('Failed to authenticate:', authData);
        return;
    }
    
    console.log('✓ Authenticated successfully');
    
    // Create a test room first
    const createRoomResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/create_multiplayer_room`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authData.access_token}`,
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        },
        body: JSON.stringify({
            room_name: 'Test Room',
            host_player_name: 'Test Host'
        })
    });
    
    const roomData = await createRoomResponse.json();
    console.log('Room created:', roomData);
    
    if (!roomData[0]?.room_id) {
        console.error('Failed to create room');
        return;
    }
    
    const roomId = roomData[0].room_id;
    console.log('Room ID:', roomId);
    
    // Test the edge function
    console.log('\n=== Calling start-game-handler ===');
    const edgeResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/start-game-handler`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authData.access_token}`,
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        },
        body: JSON.stringify({ roomId })
    });
    
    console.log('Response status:', edgeResponse.status);
    console.log('Response headers:', Object.fromEntries(edgeResponse.headers.entries()));
    
    const responseText = await edgeResponse.text();
    console.log('Response body:', responseText);
    
    try {
        const responseData = JSON.parse(responseText);
        console.log('Parsed response:', JSON.stringify(responseData, null, 2));
    } catch (e) {
        console.log('Response is not JSON');
    }
}

testEdgeFunction().catch(console.error);