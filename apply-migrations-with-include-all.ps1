# Apply Migrations with Include All Flag
# This script applies all pending migrations including those inserted before the last remote migration

Write-Host "Applying All Pending Migrations" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan
Write-Host ""

Write-Host "This will apply all pending migrations including:" -ForegroundColor Yellow
Write-Host "- 20250128000000_add_player_bonus_levels.sql"
Write-Host "- 20250128000001_add_stale_room_cleanup_index.sql"
Write-Host "- 20250129000000_add_transition_until_column.sql"
Write-Host "- 20250629091720_add_transition_columns_if_not_exists.sql"
Write-Host "- 20250629091952_safe_add_missing_columns.sql"
Write-Host ""

Write-Host "Running supabase db push with --include-all flag..." -ForegroundColor Green
Write-Host "Please enter database password when prompted: 9ACv!PEKEN5$@Mh"
Write-Host ""

# Apply migrations with include-all flag
supabase db push --include-all

Write-Host ""
Write-Host "Migration application completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Check if all columns were added successfully"
Write-Host "2. Deploy the edge functions for multiplayer transition feature"