import type { <PERSON><PERSON><PERSON>, <PERSON>Question, <PERSON>Choice } from '@/types'; // Use alias @

let allPlayersCache: PlayerData[] | null = null;

export async function loadPlayerData(): Promise<PlayerData[]> {
  // Basic caching to avoid re-fetching on every call
  if (allPlayersCache) {
    return allPlayersCache;
  }
  try {
    // Fetch from the game-ready data file
    const response = await fetch('/data/players_game_data.json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    
    // Basic validation (check if it's an array)
    if (!Array.isArray(data)) {
        throw new Error("Loaded game data is not an array");
    }
    
    // No need to filter by local_image_path anymore as prepare_game_data.py already did it
    allPlayersCache = data;
    
    console.log(`Loaded and cached ${allPlayersCache.length} game-ready players.`);
    return allPlayersCache;
  } catch (error) {
    console.error("Failed to load game-ready player data:", error);
    return []; // Return empty array on error
  }
}

// Helper to shuffle array (<PERSON><PERSON>Yates algorithm)
function shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]]; // Swap elements
    }
    return array;
}

export function generateQuestion(
    allPlayers: PlayerData[],
    excludeIds: Set<number> = new Set() // Set of player IDs already asked in current session
): PlayerQuestion | null {
  if (!allPlayers || allPlayers.length < 4) {
    console.error("Not enough player data loaded or available to generate a question.");
    return null; 
  }

  // Filter out players already asked (no need to check local_image_path anymore)
  const availablePlayers = allPlayers.filter(
      (p) => p.id != null && !excludeIds.has(p.id)
  );

  if (availablePlayers.length < 1) {
    console.warn("No more available players to ask questions about.");
    return null; // Or handle game end state
  }

  // Select the correct player
  const correctPlayerIndex = Math.floor(Math.random() * availablePlayers.length);
  const correctPlayer = availablePlayers[correctPlayerIndex];

  // Select 3 distractors (ensure they are different from correct player and each other)
  const distractors: PlayerData[] = [];
  const potentialDistractors = allPlayers.filter(p => p.id !== correctPlayer.id);
  shuffleArray(potentialDistractors); // Shuffle to get random distractors

  for (const p of potentialDistractors) {
      if (distractors.length < 3) {
          distractors.push(p);
      } else {
          break;
      }
  }
  
  if (distractors.length < 3) {
      console.error("Could not find enough unique distractors.");
      // Handle this case, maybe retry or use placeholders? For now, return null.
      return null;
  }

  // Create choices array
  const choices: PlayerChoice[] = [
    { name: correctPlayer.player_name, isCorrect: true },
    ...distractors.map(p => ({ name: p.player_name, isCorrect: false }))
  ];

  // Shuffle choices
  const shuffledChoices = shuffleArray(choices);

  // Construct the full image URL (assuming base path is /)
  const imageUrl = correctPlayer.local_image_path 
    ? `/players_images/${correctPlayer.local_image_path}` 
    : '/images/placeholder.jpg'; // Fallback image in public/images/

  return {
    correctPlayer: correctPlayer,
    choices: shuffledChoices,
    imageUrl: imageUrl,
  };
} 