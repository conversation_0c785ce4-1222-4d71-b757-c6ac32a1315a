# Test with Supabase auth headers
Write-Host "Testing POST request with Supabase auth headers..." -ForegroundColor Green

$anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"

try {
    $body = @{
        identifier = "<EMAIL>"
        password = "wrongpassword"
    } | ConvertTo-Json

    Write-Host "Request body: $body" -ForegroundColor Yellow

    $headers = @{
        "Origin" = "http://localhost:3000"
        "apikey" = $anonKey
        "Authorization" = "Bearer $anonKey"
    }

    $response = Invoke-WebRequest -Uri "http://127.0.0.1:54321/functions/v1/login-handler" -Method POST -Body $body -ContentType "application/json" -Headers $headers -UseBasicParsing

    Write-Host "Response Status: $($response.StatusCode)" -ForegroundColor Cyan
    Write-Host "Response Headers:" -ForegroundColor Cyan
    $response.Headers | Format-Table -AutoSize
    Write-Host "Response Content:" -ForegroundColor Cyan
    Write-Host $response.Content
    
    # Check for CORS headers
    if ($response.Headers.ContainsKey("access-control-allow-origin")) {
        Write-Host "✅ CORS headers are present!" -ForegroundColor Green
    } else {
        Write-Host "❌ CORS headers are missing!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Request failed with error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "Error Response Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Yellow
        Write-Host "Error Response Headers:" -ForegroundColor Yellow
        $_.Exception.Response.Headers | Format-Table -AutoSize
        
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorContent = $reader.ReadToEnd()
            Write-Host "Error Response Content: $errorContent" -ForegroundColor Yellow
        } catch {
            Write-Host "Could not read error response content" -ForegroundColor Red
        }
    }
}

Write-Host "Test completed." -ForegroundColor Green 