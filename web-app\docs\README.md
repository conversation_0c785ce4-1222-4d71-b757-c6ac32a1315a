# Web App Test Guide

This document describes how to run the available test scripts for the Next.js web application.

## Prerequisites

- **Node.js 20+** (matches the version declared in `package.json`)
- A running Supabase development environment. Start it with `supabase start`.
- The Next.js development server running locally via `npm run dev`.

## Running Tests

Test scripts are provided as PowerShell files in the repository root. Use `pwsh` or `powershell` to execute them. For example:

```bash
# run the simple login test
pwsh ./test-simple.ps1
```

To run a different test, pass the desired script:

```bash
pwsh ./test-host-reentry-fixes.ps1
```

## Common Errors and Fixes

- **Execution policy prevents running scripts**

  If PowerShell displays an error like `running scripts is disabled on this system`, execute:

  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
  ```

- **Connection errors**

  `connection refused` or `404` errors usually mean the Supabase services or Next.js dev server are not running. Ensure both are started before running tests.

- **Missing dependencies**

  When a script fails because a Node module is missing, install dependencies with:

  ```bash
  npm install
  ```

## Linting

ESLint can be run with:

```bash
npm run lint
```

If the lint command fails with `Cannot find package '@eslint/eslintrc'`, install the missing package in the `web-app` directory.

