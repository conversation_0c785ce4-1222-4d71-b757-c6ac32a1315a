type ErrorContext = 'auth' | 'multiplayer' | 'game' | 'network' | 'unknown';

interface ErrorHandlerOptions {
  context?: ErrorContext;
  showToUser?: boolean;
  logToConsole?: boolean;
  fallbackMessage?: string;
}

const isDev = process.env.NODE_ENV === 'development';

/**
 * Centralized error handler to replace scattered console.error calls
 */
export class ErrorHandler {
  private static errorQueue: Array<{ error: Error; context: ErrorContext; timestamp: Date }> = [];
  private static maxQueueSize = 50;

  static handle(error: unknown, options: ErrorHandlerOptions = {}): string {
    const {
      context = 'unknown',
      showToUser = true,
      logToConsole = isDev,
      fallbackMessage = 'An unexpected error occurred'
    } = options;

    // Convert to Error object if needed
    const errorObj = error instanceof Error ? error : new Error(String(error));
    
    // Add to error queue for debugging
    this.addToQueue(errorObj, context);

    // Log to console in dev mode
    if (logToConsole) {
      console.error(`[${context.toUpperCase()}] ${errorObj.message}`, errorObj);
    }

    // Return user-friendly message
    if (showToUser) {
      return this.getUserFriendlyMessage(errorObj, context, fallbackMessage);
    }

    return fallbackMessage;
  }

  private static addToQueue(error: Error, context: ErrorContext) {
    this.errorQueue.push({ error, context, timestamp: new Date() });
    
    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  private static getUserFriendlyMessage(error: Error, context: ErrorContext, fallback: string): string {
    // Map common error patterns to user-friendly messages
    const message = error.message.toLowerCase();

    // Auth errors
    if (context === 'auth') {
      if (message.includes('invalid credentials')) return 'Invalid email or password';
      if (message.includes('email not confirmed')) return 'Please confirm your email address';
      if (message.includes('user already exists')) return 'An account with this email already exists';
    }

    // Network errors
    if (context === 'network' || message.includes('network') || message.includes('fetch')) {
      return 'Connection error. Please check your internet connection';
    }

    // Multiplayer errors
    if (context === 'multiplayer') {
      if (message.includes('room is full')) return 'This room is full';
      if (message.includes('room not found')) return 'Room no longer exists';
      if (message.includes('already in room')) return 'You are already in a room';
    }

    // Game errors
    if (context === 'game') {
      if (message.includes('no questions')) return 'No more questions available';
      if (message.includes('invalid answer')) return 'Invalid answer submission';
    }

    // Default to original message if it's already user-friendly, otherwise use fallback
    return error.message.length < 100 ? error.message : fallback;
  }

  static getRecentErrors(count: number = 10): Array<{ error: Error; context: ErrorContext; timestamp: Date }> {
    return this.errorQueue.slice(-count);
  }

  static clearErrors() {
    this.errorQueue = [];
  }
}

/**
 * Wrapper for async operations with error handling
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    ErrorHandler.handle(error, options);
    return null;
  }
}

/**
 * React hook for error boundaries
 */
export function useErrorHandler(context: ErrorContext = 'unknown') {
  const handleError = (error: unknown, options?: Omit<ErrorHandlerOptions, 'context'>) => {
    return ErrorHandler.handle(error, { context, ...options });
  };

  return { handleError };
}