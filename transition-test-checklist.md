# Multiplayer Transition Test Checklist

## Test the Fix

### Basic Test
1. [ ] Start multiplayer game with 2 players
2. [ ] Both players answer question
3. [ ] Check console for: `alreadyTransitioned: false`
4. [ ] Wait 3 seconds
5. [ ] New question should appear automatically
6. [ ] Check console - should NOT see multiple "All players answered" for same question

### Tab Switch Test
1. [ ] Both players answer
2. [ ] Immediately switch to another tab
3. [ ] Wait 5+ seconds
4. [ ] Switch back
5. [ ] New question should be visible
6. [ ] No repeated transitions in console

### Console Verification
Look for these patterns:

✅ **Good:**
```
[QUESTION_TRANSITION] Checking transition conditions: { alreadyTransitioned: false, currentQuestionId: "abc123" }
[QUESTION_TRANSITION] All players answered, waiting 3 seconds to advance
[QUESTION_TRANSITION] Successfully advanced
[SYNC_STATE] *** STATE SYNC COMPLETE *** { currentQuestionId: "xyz789" }
```

❌ **Bad (should NOT see):**
```
[QUESTION_TRANSITION] All players answered, waiting 3 seconds to advance
[QUESTION_TRANSITION] All players answered, waiting 3 seconds to advance  // Duplicate!
```

### Edge Cases
1. [ ] One player disconnects after answering - game should still advance after 3 seconds
2. [ ] Rapid answer submissions - only one transition should occur
3. [ ] Network lag - transition should still work reliably

## Success Criteria
- Each question transitions exactly ONCE
- 3-second delay is consistent
- No UI changes during transition
- Works regardless of tab focus