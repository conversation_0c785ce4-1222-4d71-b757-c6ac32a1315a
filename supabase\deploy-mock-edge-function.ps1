# Deploy a mock version of the Edge Function for testing
Write-Host "Creating backup of original Edge Function..." -ForegroundColor Yellow
Copy-Item "functions/start-game-handler/index.ts" "functions/start-game-handler/index-original.ts" -Force

Write-Host "Creating simplified mock Edge Function..." -ForegroundColor Yellow

# Create a simplified version that uses mock data
@'
import { serve } from "https://deno.land/std@0.177.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { corsHeaders } from "../_shared/cors.ts"

console.log('[MOCK] Mock start-game-handler is starting...');

serve(async (req) => {
  console.log('[MOCK] Received request:', req.method);
  
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { roomId } = await req.json();
    console.log('[MOCK] Processing room:', roomId);
    
    // Simple mock question
    const mockQuestion = {
      questionId: crypto.randomUUID(),
      correctPlayerId: 1,
      imageUrl: '/players_images/kansas-city-chiefs/patrick-mahomes.jpg',
      choices: [
        { name: '<PERSON> Mahomes', isCorrect: true },
        { name: 'Josh Allen', isCorrect: false },
        { name: 'Lamar Jackson', isCorrect: false },
        { name: 'Dak <PERSON>', isCorrect: false }
      ],
      correctChoiceName: 'Patrick Mahomes'
    };
    
    // Get Supabase credentials
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (!supabaseUrl || !supabaseServiceRoleKey) {
      console.error('[MOCK] Missing Supabase credentials');
      throw new Error('Server configuration error');
    }
    
    // Create admin client
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);
    
    // Update room to active
    const { error: updateError } = await supabaseAdmin
      .from('game_rooms')
      .update({
        status: 'active',
        current_round: 1,
        current_question: mockQuestion,
        question_deadline: new Date(Date.now() + 15000).toISOString(),
        game_start_timestamp: new Date().toISOString(),
        last_activity_timestamp: new Date().toISOString()
      })
      .eq('id', roomId);
    
    if (updateError) {
      console.error('[MOCK] Update error:', updateError);
      throw new Error('Failed to update room');
    }
    
    console.log('[MOCK] Game started successfully');
    
    return new Response(JSON.stringify({ 
      success: true,
      message: 'Game started (MOCK)'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
    
  } catch (error) {
    console.error('[MOCK] Error:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'An error occurred'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
'@ | Out-File -FilePath "functions/start-game-handler/index.ts" -Encoding UTF8

Write-Host "`nDeploying mock Edge Function..." -ForegroundColor Cyan
npx supabase@latest functions deploy start-game-handler --no-verify-jwt

Write-Host "`nDeployment complete!" -ForegroundColor Green
Write-Host "To restore original: Copy-Item functions/start-game-handler/index-original.ts functions/start-game-handler/index.ts -Force" -ForegroundColor Yellow