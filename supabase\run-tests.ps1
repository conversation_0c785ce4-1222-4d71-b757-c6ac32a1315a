# Simple test runner

Write-Host "MULTIPLAYER TIMING TESTS" -ForegroundColor Cyan
Write-Host ""

# Check for Node.js
$nodeExists = Get-Command node -ErrorAction SilentlyContinue
if (-not $nodeExists) {
    Write-Host "ERROR: Node.js not installed" -ForegroundColor Red
    Write-Host "Download from: https://nodejs.org/" -ForegroundColor Yellow
    exit
}

# Install Puppeteer if needed
if (-not (Test-Path "node_modules/puppeteer")) {
    Write-Host "Installing Puppeteer..." -ForegroundColor Yellow
    npm install puppeteer
}

Write-Host ""
Write-Host "Choose test type:" -ForegroundColor Yellow
Write-Host "1. Simple (manual login)" -ForegroundColor Green
Write-Host "2. Full automated" -ForegroundColor Blue
Write-Host ""

$choice = Read-Host "Choice (1 or 2)"

if ($choice -eq "1") {
    if (Test-Path "test-multiplayer-simple.js") {
        node test-multiplayer-simple.js
    } else {
        Write-Host "Simple test not found" -ForegroundColor Red
    }
} else {
    if (Test-Path "test-multiplayer-automated.js") {
        node test-multiplayer-automated.js
    } else {
        Write-Host "Automated test not found" -ForegroundColor Red
    }
}