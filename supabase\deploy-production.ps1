# Deploy to production Supabase project

Write-Host "Deploying to production Supabase project..." -ForegroundColor Cyan

# Your project reference
$projectRef = "xmyxuvuimebjltnaamox"

Write-Host "Project: $projectRef" -ForegroundColor Yellow

# Deploy the function
npx supabase functions deploy submit-answer-handler --no-verify-jwt --project-ref $projectRef

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nsubmit-answer-handler deployed successfully to production!" -ForegroundColor Green
} else {
    Write-Host "`nDeployment failed!" -ForegroundColor Red
    exit 1
}