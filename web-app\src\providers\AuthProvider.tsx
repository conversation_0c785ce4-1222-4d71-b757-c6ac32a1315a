'use client';

import { createContext, useContext, useEffect, useState, useRef } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabaseClient';

// Profile type definition
export interface Profile {
  id: string;
  username: string | null;
}

// CRITICAL FIX: Complete AuthContextType interface with all required properties
export interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: Profile | null;
  isLoading: boolean;
  isLoadingProfile: boolean;
  loading: boolean; // Alias for isLoading for compatibility with useHeartbeat
}

// CRITICAL FIX: Create context with complete type definition and correct property names
const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  profile: null,
  isLoading: true,
  isLoadingProfile: false,
  loading: true // Alias for isLoading
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  
  // CRITICAL FIX: Track previous session state to differentiate initial sign-in from token refresh
  const previousSessionRef = useRef<Session | null>(null);

  // Profile fetching function
  const fetchProfile = async (userId: string) => {
    console.log(`[AuthProvider] Fetching profile for user: ${userId}`);
    setIsLoadingProfile(true);
    
    try {
      const { data, error, status } = await supabase
        .from('profiles')
        .select('id, username')
        .eq('id', userId)
        .single();

      if (error && status !== 406) {
        console.error("[AuthProvider] Error fetching user profile:", error.message);
        setProfile(null);
      } else if (data) {
        console.log("[AuthProvider] User profile fetched:", data);
        setProfile(data);
      } else {
        console.warn(`[AuthProvider] No profile found for user ID: ${userId}`);
        setProfile(null);
      }
    } catch (e) {
      console.error("[AuthProvider] Exception fetching user profile:", e);
      setProfile(null);
    } finally {
      setIsLoadingProfile(false);
    }
  };

  useEffect(() => {
    console.log('[AuthProvider] Setting up auth state management with tab focus race condition fix');
    
    // Initial session fetch
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('[AuthProvider] Initial session:', session?.user?.id);
      setSession(session);
      setUser(session?.user ?? null);
      setIsLoading(false);
      
      // Initialize the ref with the current session
      previousSessionRef.current = session;

      // Fetch profile if user exists and we don't have a matching profile
      if (session?.user && (!profile || profile.id !== session.user.id)) {
        fetchProfile(session.user.id);
      } else if (!session?.user) {
        // Clear profile on sign-out
        setProfile(null);
        setIsLoadingProfile(false);
      }
    });

    // Listen for auth state changes with intelligent session tracking
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, currentSession) => {
        console.log(`[AuthProvider] Auth state change: ${event}`, currentSession?.user?.id);
        
        // Update React state
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
        setIsLoading(false);

        // Handle profile fetching based on auth state
        if (currentSession?.user && (!profile || profile.id !== currentSession.user.id)) {
          // Fetch profile if we have a user but no matching profile
          fetchProfile(currentSession.user.id);
        } else if (!currentSession?.user) {
          // Clear profile on sign-out
          setProfile(null);
          setIsLoadingProfile(false);
        }

        // CRITICAL LOGIC: Only dispatch global auth success events for TRUE initial sign-ins
        // A true sign-in is when the previous session was null and the new one is not
        if (event === 'SIGNED_IN') {
          if (!previousSessionRef.current && currentSession) {
            console.log('[AuthProvider] ✅ DETECTED TRUE INITIAL SIGN-IN: Previous session was null, current session is valid');
            // This would be the place to dispatch global auth success events if needed
            // For now, we let the AuthModal handle its own onAuthSuccess callback
          } else {
            console.log('[AuthProvider] 🔄 DETECTED TOKEN REFRESH: Previous session existed, this is just a refresh on tab focus');
            console.log('[AuthProvider] Previous session existed:', !!previousSessionRef.current);
            console.log('[AuthProvider] Current session exists:', !!currentSession);
            // This is a token refresh, not a new login - do not trigger auth success callbacks
          }
        }
        
        // Update the ref to the current session for the next auth event
        previousSessionRef.current = currentSession;
      }
    );

    return () => subscription.unsubscribe();
  }, [profile]); // Add profile to dependency array

  // CRITICAL FIX: Return complete value object with correct property names
  const value: AuthContextType = {
    user,
    session,
    profile,
    isLoading,
    isLoadingProfile,
    loading: isLoading, // Alias for compatibility
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 