// src/components/ui/DisconnectedOverlay.tsx
import React from 'react';

interface DisconnectedOverlayProps {
  onReconnect: () => void;
}

export function DisconnectedOverlay({ onReconnect }: DisconnectedOverlayProps) {
  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-95 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-8 max-w-md mx-4 text-center">
        <div className="mb-6">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-500 bg-opacity-20 flex items-center justify-center">
            <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">Connection Lost</h2>
          <p className="text-gray-300 text-sm">
            You&apos;ve been disconnected from the game. Click below to rejoin.
          </p>
        </div>
        
        <button
          onClick={onReconnect}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
        >
          Rejoin Game
        </button>
      </div>
    </div>
  );
}
