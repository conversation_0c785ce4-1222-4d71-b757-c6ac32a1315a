# PowerShell script to deploy the stale-room-janitor Edge Function
# This script deploys the janitor function that automatically cleans up abandoned game rooms

Write-Host "🧹 Deploying Stale Room Janitor Edge Function..." -ForegroundColor Cyan

try {
    # Deploy the stale-room-janitor function
    Write-Host "📤 Deploying stale-room-janitor function..." -ForegroundColor Yellow
    supabase functions deploy stale-room-janitor --project-ref xmyxuvuimebjltnaamox

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ stale-room-janitor function deployed successfully!" -ForegroundColor Green
        
        Write-Host "📋 Function Details:" -ForegroundColor Cyan
        Write-Host "  • Function Name: stale-room-janitor" -ForegroundColor White
        Write-Host "  • Schedule: Every 5 minutes (*/5 * * * *)" -ForegroundColor White
        Write-Host "  • Purpose: Automatically clean up abandoned game rooms" -ForegroundColor White
        Write-Host "  • Threshold: Rooms inactive for 5+ minutes" -ForegroundColor White
        
        Write-Host "🔍 You can monitor the function at:" -ForegroundColor Cyan
        Write-Host "  https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/functions/stale-room-janitor" -ForegroundColor Blue
        
        Write-Host "⏰ The cron job will start automatically and run every 5 minutes." -ForegroundColor Green
        Write-Host "📊 Check the function logs to see cleanup activity." -ForegroundColor Yellow
    } else {
        Write-Host "❌ Failed to deploy stale-room-janitor function" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error during deployment: $_" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 Stale Room Janitor deployment complete!" -ForegroundColor Green
