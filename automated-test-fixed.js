/**
 * Fixed Automated Multiplayer Test Runner for WSL
 * Tests round advance timing with two players
 */

const puppeteer = require('puppeteer');

// Test configuration
const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → Round at 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → Round at 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → Round at 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'Only P1 → Round at 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1 at 1s, P2 at 3s → Round at 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

// Helper functions
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

async function signIn(page, credentials, playerName) {
  console.log(`  Signing in ${playerName}...`);
  
  // Click Login button
  await page.evaluate(() => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent.includes('Login')
    );
    if (btn) btn.click();
  });
  
  await delay(2000);
  
  // Fill credentials
  await page.type('input[placeholder="Username or Email"]', credentials.email);
  await page.type('input[placeholder="Password"]', credentials.password);
  
  // Submit
  await page.evaluate(() => {
    const form = document.querySelector('form');
    if (form) form.requestSubmit();
  });
  
  await delay(5000); // Give more time for auth
}

async function navigateToMultiplayer(page, playerName) {
  console.log(`  ${playerName} navigating to multiplayer...`);
  await page.evaluate(() => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent === 'Multiplayer Mode'
    );
    if (btn) btn.click();
  });
  await delay(3000);
}

async function createRoom(page) {
  console.log('  Player 1 creating room...');
  
  // Find and click Host Game button
  await page.evaluate(() => {
    const hostBtn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent.trim() === 'Host Game'
    );
    if (hostBtn) hostBtn.click();
  });
  
  await delay(3000);
  
  // Get room code
  const roomCode = await page.evaluate(() => {
    const codeEl = Array.from(document.querySelectorAll('*')).find(el => {
      const text = el.textContent || '';
      return text.match(/^[A-Z0-9]{6}$/) && el.children.length === 0;
    });
    return codeEl ? codeEl.textContent : null;
  });
  
  return roomCode;
}

async function joinRoom(page, roomCode) {
  console.log(`  Player 2 joining room ${roomCode}...`);
  
  // Click Join Room
  await page.evaluate(() => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent === 'Join Room'
    );
    if (btn) btn.click();
  });
  
  await delay(1500);
  
  // Enter room code
  await page.type('input[placeholder*="room code" i]', roomCode);
  
  // Submit
  await page.evaluate(() => {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) submitBtn.click();
  });
  
  await delay(3000);
}

async function startGame(page1, page2) {
  console.log('  Both players readying up...');
  
  // Both players ready up
  await Promise.all([
    page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready'
      );
      if (btn) btn.click();
    }),
    page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready'
      );
      if (btn) btn.click();
    })
  ]);
  
  await delay(2000);
  
  console.log('  Host starting game...');
  
  // Host starts game
  await page1.evaluate(() => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent === 'Start Game'
    );
    if (btn) btn.click();
  });
  
  await delay(3000);
}

// Monitoring injection
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0
  };
  
  let lastQuestion = null;
  setInterval(() => {
    const questionEl = document.querySelector('h2');
    const question = questionEl?.textContent || '';
    
    if (question && question.includes('?') && question !== lastQuestion) {
      const now = Date.now();
      
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('🎮 Game started at', new Date(now).toISOString());
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({
          round: window.testData.currentRound++,
          time: elapsed
        });
        console.log('📍 Round ' + (window.testData.currentRound) + ' advanced at ' + elapsed + 'ms');
      }
      
      lastQuestion = question;
    }
  }, 100);
  
  window.submitAt = function(targetMs) {
    if (!window.testData.gameStartTime) {
      console.log('Game not started yet');
      return;
    }
    
    const elapsed = Date.now() - window.testData.gameStartTime;
    const delay = targetMs - elapsed;
    
    const submit = () => {
      const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
        const text = b.textContent || '';
        return text.length > 0 && 
               text.match(/^[A-Za-z\\s]+$/) && 
               !text.includes('Ready') && 
               !text.includes('Start') &&
               !text.includes('Mode') &&
               !text.includes('Sign');
      });
      
      if (buttons.length >= 4) {
        const chosen = buttons[Math.floor(Math.random() * 4)];
        console.log('Submitting answer:', chosen.textContent);
        chosen.click();
      }
    };
    
    if (delay > 0) {
      setTimeout(submit, delay);
    } else {
      submit();
    }
  };
`;

async function runTest() {
  console.log('🎯 Automated Multiplayer Test (Fixed for WSL)');
  console.log('============================================\n');
  
  let browser1, browser2;
  
  try {
    // Launch browsers with proper configuration for WSL
    console.log('🚀 Launching browsers...');
    const browserOptions = {
      headless: false,
      executablePath: '/usr/bin/chromium-browser',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--window-size=1000,800'
      ]
    };
    
    browser1 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=0,0']
    });
    
    browser2 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=1000,0']
    });
    
    const page1 = await browser1.newPage();
    const page2 = await browser2.newPage();
    
    // Navigate to game
    console.log('\n📱 Loading game...');
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2', timeout: 30000 }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2', timeout: 30000 })
    ]);
    
    await delay(3000);
    
    // Inject monitoring
    console.log('\n💉 Injecting monitoring code...');
    await Promise.all([
      page1.evaluate(monitoringCode),
      page2.evaluate(monitoringCode)
    ]);
    
    // Sign in both players
    console.log('\n🔐 Authentication phase:');
    await signIn(page1, CONFIG.player1, 'Player 1');
    await signIn(page2, CONFIG.player2, 'Player 2');
    
    // Navigate to multiplayer
    console.log('\n🎮 Multiplayer setup:');
    await navigateToMultiplayer(page1, 'Player 1');
    await navigateToMultiplayer(page2, 'Player 2');
    
    // Create and join room
    console.log('\n🏠 Room management:');
    const roomCode = await createRoom(page1);
    if (!roomCode) {
      throw new Error('Failed to create room - no room code found');
    }
    console.log(`  ✅ Room created: ${roomCode}`);
    
    await joinRoom(page2, roomCode);
    console.log('  ✅ Player 2 joined room');
    
    // Start game
    console.log('\n🎮 Game initialization:');
    await startGame(page1, page2);
    console.log('  ✅ Game started!\n');
    
    // Run test scenarios
    console.log('📊 Running test scenarios:');
    console.log('========================\n');
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const scenario = CONFIG.scenarios[i];
      console.log(`Test ${i + 1}: ${scenario.name}`);
      
      // Schedule submissions
      if (scenario.p1 !== null) {
        await page1.evaluate(`window.submitAt(${scenario.p1})`);
        console.log(`  Player 1 will answer at ${scenario.p1}ms`);
      }
      if (scenario.p2 !== null) {
        await page2.evaluate(`window.submitAt(${scenario.p2})`);
        console.log(`  Player 2 will answer at ${scenario.p2}ms`);
      }
      
      // Wait for round to complete
      console.log('  Waiting for round to advance...');
      await delay(8000);
      
      // Get results from both players and compare
      const [timing1, timing2] = await Promise.all([
        page1.evaluate((index) => {
          const changes = window.testData.roundChanges || [];
          return changes[index];
        }, i),
        page2.evaluate((index) => {
          const changes = window.testData.roundChanges || [];
          return changes[index];
        }, i)
      ]);
      
      if (timing1 || timing2) {
        const actualTime = timing1?.time || timing2?.time;
        const diff = Math.abs(actualTime - scenario.expected);
        const passed = diff <= 500;
        
        console.log(`  Expected: ${scenario.expected}ms`);
        console.log(`  Actual: ${actualTime}ms`);
        console.log(`  Difference: ${diff}ms`);
        console.log(`  Result: ${passed ? '✅ PASS' : '❌ FAIL'}\n`);
        
        results.push({ 
          ...scenario, 
          actual: actualTime, 
          difference: diff,
          passed 
        });
      } else {
        console.log('  ❌ No round change detected\n');
        results.push({ 
          ...scenario, 
          actual: null, 
          difference: null,
          passed: false 
        });
      }
    }
    
    // Summary
    console.log('\n📈 Test Summary');
    console.log('==============');
    const passed = results.filter(r => r.passed).length;
    const failed = results.filter(r => !r.passed).length;
    
    console.log(`Total Tests: ${results.length}`);
    console.log(`Passed: ${passed} ✅`);
    console.log(`Failed: ${failed} ❌`);
    console.log(`Success Rate: ${((passed/results.length) * 100).toFixed(1)}%\n`);
    
    console.log('Detailed Results:');
    results.forEach((r, i) => {
      console.log(`${i + 1}. ${r.name}`);
      if (r.actual !== null) {
        console.log(`   Expected: ${r.expected}ms, Actual: ${r.actual}ms, Diff: ${r.difference}ms`);
      }
      console.log(`   Status: ${r.passed ? '✅ PASS' : '❌ FAIL'}`);
    });
    
    console.log('\n✅ Test complete! Keeping browsers open for manual inspection.');
    console.log('Press Ctrl+C to close browsers and exit.\n');
    
    // Keep process alive
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
    
    // Clean up on error
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    
    process.exit(1);
  }
}

// Run the test
runTest().catch(console.error);