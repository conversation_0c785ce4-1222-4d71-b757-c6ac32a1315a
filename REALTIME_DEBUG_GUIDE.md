# Realtime Debugging Guide for Multiplayer Game

## Problem Summary
The host client can see themselves in the room, but when a second player (`fresh2`) joins or toggles ready state, the host's client does NOT receive any Realtime events about these changes. The host's `playersInRoom` remains `Array(1)` instead of updating to `Array(2)`.

## Enhanced Logging Added
The code now includes comprehensive Realtime logging with:
- **RAW PAYLOAD LOGGING**: Every Realtime event will show a magenta-colored log with full payload details
- **Color-coded logs**: Green for INSERT, blue for UPDATE, red for DELETE
- **User-specific logging**: Each log shows which user's client generated it
- **Subscription status tracking**: Detailed SUBSCRIBED/ERROR logging

## Step-by-Step Debugging Process

### Step 1: Verify Supabase RLS and Publication Settings

#### 1.1 Check RLS Policies for `game_players` table
Go to **Supabase Dashboard → Database → Policies**:

1. Select the `game_players` table
2. Ensure you have a `SELECT` policy that allows users to see all players in their room:

```sql
-- This policy (or similar) must EXIST and be ENABLED
CREATE POLICY "Allow users to see players in their room"
ON public.game_players
FOR SELECT
USING (
  auth.uid() IN (
    SELECT gp.user_id
    FROM public.game_players gp
    WHERE gp.room_id = public.game_players.room_id
  )
);
```

3. **Critical**: Verify this policy is **ENABLED** (toggle should be ON)

#### 1.2 Check Publication Settings
Go to **Supabase Dashboard → Database → Publications**:

1. Click on `supabase_realtime`
2. Find `public.game_players` in the table list
3. **CRITICAL**: Ensure ALL these checkboxes are checked:
   - ✅ `insert`
   - ✅ `update` 
   - ✅ `delete`
4. If any are unchecked, click "Edit" → check them → "Save"

**This is the #1 cause of Realtime not working!**

### Step 2: Test with Enhanced Logging

#### 2.1 Host Creates Room and Joins
1. **Host (fresh)** opens browser console
2. Create a room and join it
3. **Look for these logs in Host console:**
   ```
   [REALTIME_SUBSCRIPTION - User: 8bef3251...] Successfully SUBSCRIBED to game_players for room f370bf61...! Fetching initial players...
   ```
4. Verify `playersInRoom: Array(1)` in render logs

#### 2.2 Second Player Joins Room
1. **Second Player (fresh2)** opens another browser/incognito and joins the room
2. **Focus on Host (fresh) Console** - THIS IS THE CRITICAL TEST:

**What you SHOULD see in Host console:**
```
%c[REALTIME_RAW_PAYLOAD - User: 8bef3251...] Event on 'game_players' for room 'f370bf61...' at [timestamp]:
Object: {
  eventType: 'INSERT',
  new: { user_id: 'a70ef842...', room_id: 'f370bf61...', is_connected: true, is_ready: false },
  old: null,
  ...
}

%c[REALTIME_HANDLER_game_players - User: 8bef3251...] Processing INSERT. New record:
{ user_id: 'a70ef842...', room_id: 'f370bf61...', ... }

[Realtime HOST] [INSERT OTHER] *** CRITICAL *** Received INSERT event for OTHER player joining:
```

**If you DON'T see the magenta `REALTIME_RAW_PAYLOAD` log:**
- The Realtime message is not reaching the host client
- This points to Step 1 issues (RLS policies or publication settings)
- Check network tab for any Realtime connection errors

**If you DO see the raw payload but no subsequent processing:**
- The message is arriving but the processing logic has issues
- Check for JavaScript errors in console

#### 2.3 Second Player Toggles Ready State
1. **Second Player (fresh2)** clicks "Ready Up"
2. **Host (fresh) Console should show:**
   ```
   %c[REALTIME_RAW_PAYLOAD - User: 8bef3251...] Event on 'game_players' for room 'f370bf61...' at [timestamp]:
   Object: {
     eventType: 'UPDATE',
     new: { user_id: 'a70ef842...', room_id: 'f370bf61...', is_ready: true },
     old: { user_id: 'a70ef842...', room_id: 'f370bf61...', is_ready: false },
     ...
   }
   ```

### Step 3: Common Issues and Solutions

#### Issue 1: No Raw Payload Logs on Host
**Cause**: RLS policies or publication settings
**Solution**: 
- Re-check Step 1.1 and 1.2
- Ensure the `SELECT` policy on `game_players` allows the host to see other players
- Verify `insert`/`update`/`delete` are enabled in `supabase_realtime` publication

#### Issue 2: Raw Payload Received but No State Updates
**Cause**: Processing logic errors
**Solution**:
- Check for JavaScript errors in console
- Verify `fetchPlayersInActiveRoom` is called and completes successfully
- Check if `setPlayersInRoom` is being called with updated data

#### Issue 3: Subscription Filter Issues
**Cause**: `activeRoomId` mismatch or stale closure values
**Solution**:
- Check the subscription filter: `room_id=eq.${activeRoomId}`
- Verify `capturedActiveRoomId` in logs matches the actual room ID
- Ensure `activeRoomId` is stable when subscription is created

#### Issue 4: Network/Connection Issues
**Cause**: Realtime WebSocket connection problems
**Solution**:
- Check browser Network tab for WebSocket connections
- Look for `wss://` connections to Supabase
- Check for connection errors or timeouts

### Step 4: Additional Debugging Commands

If the issue persists, you can run these database queries to verify data:

```sql
-- Check if both players are in the database
SELECT user_id, room_id, is_connected, is_ready, created_at 
FROM game_players 
WHERE room_id = 'your-room-id-here' 
ORDER BY created_at;

-- Check RLS policies
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'game_players';

-- Check publication tables
SELECT schemaname, tablename, insert, update, delete 
FROM pg_publication_tables 
WHERE pubname = 'supabase_realtime';
```

### Step 5: Expected Resolution

After fixing RLS/publication issues, you should see:
1. Host creates room → `playersInRoom: Array(1)`
2. Second player joins → Host sees magenta `REALTIME_RAW_PAYLOAD` → `playersInRoom: Array(2)`
3. Second player toggles ready → Host sees UPDATE event → UI updates to show ready status
4. Both clients can see each other and interact properly

## Key Files Modified
- `web-app/src/app/page.tsx`: Enhanced Realtime logging added
- Fixed duplicate `PlayerInRoom` type definitions
- Added raw payload logging with color coding
- Enhanced cleanup function with specific channel removal

## Next Steps
1. Follow Steps 1-2 above carefully
2. Test the scenario with focus on Host console logs
3. Share the specific console output if issues persist
4. The magenta `REALTIME_RAW_PAYLOAD` logs will definitively show if Realtime messages are arriving 