# Test Race Condition Fix - Combined Submit Answer and Advance Game Logic
# This tests the atomic operation that eliminates the race condition

Write-Host "[TEST] Testing Race Condition Fix - Combined Answer Submit + Game Advance" -ForegroundColor Cyan
Write-Host "==================================================================" -ForegroundColor Cyan

$baseUrl = "https://xmyxuvuimebjltnaamox.supabase.co".Trim()

# Validate base URL
if (-not [System.Uri]::TryCreate($baseUrl, 'Absolute', [ref]$null)) {
    Write-Host "[ERROR] Invalid base URL: '$baseUrl'" -ForegroundColor Red
    Write-Host "URL bytes: $([Text.Encoding]::UTF8.GetBytes($baseUrl) -join ' ')" -ForegroundColor Yellow
    exit 1
}
Write-Host "[OK] Base URL validated: $baseUrl" -ForegroundColor Green

# Get credentials from environment variables
$supabaseAnonKey = $env:SUPABASE_ANON_KEY
$supabaseAuthToken = $env:SUPABASE_AUTH_TOKEN

if (-not $supabaseAnonKey) {
    Write-Host "[ERROR] Missing environment variable: SUPABASE_ANON_KEY" -ForegroundColor Red
    Write-Host "Please set: " -ForegroundColor Yellow
    Write-Host "  `$env:SUPABASE_ANON_KEY = 'your-anon-key-here'" -ForegroundColor Yellow
    exit 1
}

if (-not $supabaseAuthToken) {
    Write-Host "[WARN] No SUPABASE_AUTH_TOKEN set. Testing without authentication..." -ForegroundColor Yellow
    Write-Host "To get a token, run: .\get-auth-token.ps1 -Email 'your-email' -Password 'your-password'" -ForegroundColor Yellow
    $headers = @{
        "Content-Type" = "application/json"
        "apikey" = $supabaseAnonKey
    }
} else {
    Write-Host "[OK] Using authentication token" -ForegroundColor Green
    $headers = @{
        "Content-Type" = "application/json"
        "Authorization" = "Bearer $supabaseAuthToken"
        "apikey" = $supabaseAnonKey
    }
}

Write-Host "[SCENARIO] Test Scenario:" -ForegroundColor Yellow
Write-Host "1. Create a game room with 2 players" -ForegroundColor White
Write-Host "2. Start the game (first question generated)" -ForegroundColor White
Write-Host "3. Player 1 submits answer (should NOT advance)" -ForegroundColor White
Write-Host "4. Player 2 submits answer (should ATOMICALLY advance to next question)" -ForegroundColor White
Write-Host "5. Verify game advanced without race condition" -ForegroundColor White
Write-Host ""

# Step 1: Get current user profile ID first
Write-Host "[PROFILE] Step 1a: Getting current user profile..." -ForegroundColor Green
$profileUri = "$baseUrl/rest/v1/profiles?select=id"
Write-Host "[CALL] Calling: $profileUri" -ForegroundColor Cyan

try {
    $profileData = Invoke-RestMethod -Uri $profileUri `
        -Method GET `
        -Headers $headers
    
    if ($profileData.Count -eq 0) {
        Write-Host "[ERROR] No profile found for current user" -ForegroundColor Red
        exit 1
    }
    
    $hostId = $profileData[0].id
    Write-Host "[OK] Profile found: $hostId" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to get profile: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 1b: Create a test room with proper fields
Write-Host "[ROOM] Step 1b: Creating test room..." -ForegroundColor Green
$createRoomBody = @{
    title = "Race Condition Test $(Get-Random)"
    max_players = 2
    host_id = $hostId
    status = "waiting"
} | ConvertTo-Json

$createRoomUri = "$baseUrl/rest/v1/game_rooms".Trim()
Write-Host "[CALL] Calling: $createRoomUri" -ForegroundColor Cyan

# Add Prefer header to return the created object
$headersWithReturn = $headers.Clone()
$headersWithReturn["Prefer"] = "return=representation"

try {
    $roomResponse = Invoke-RestMethod -Uri $createRoomUri `
        -Method POST `
        -Headers $headersWithReturn `
        -Body $createRoomBody
    
    # Handle different response formats
    if ($roomResponse -is [Array] -and $roomResponse.Count -gt 0) {
        $roomId = $roomResponse[0].id
    } else {
        $roomId = $roomResponse.id
    }
    
    Write-Host "[OK] Room created: $roomId" -ForegroundColor Green
    Write-Host "[DEBUG] Full response: $($roomResponse | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
} catch {
    Write-Host "[ERROR] Failed to create room: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Join players to the room directly via game_players table
Write-Host "[PLAYERS] Step 2: Joining players to room..." -ForegroundColor Green

# Join the current user (host) to the room
$joinPlayer1Body = @{
    room_id = $roomId
    user_id = $hostId
    is_ready = $true
} | ConvertTo-Json

$insertPlayerUri = "$baseUrl/rest/v1/game_players".Trim()
Write-Host "[CALL] Calling: $insertPlayerUri (Player 1)" -ForegroundColor Cyan

try {
    $player1Response = Invoke-RestMethod -Uri $insertPlayerUri `
        -Method POST `
        -Headers $headers `
        -Body $joinPlayer1Body
    
    Write-Host "[OK] Player 1 (host) joined room" -ForegroundColor Green
    
    # Add a second player for the minimum player requirement (using same user)
    # This is a test workaround since the start-game-handler requires min 2 players
    Write-Host "[NOTE] Adding second player entry for testing (start-game requires min 2 players)..." -ForegroundColor Yellow
    
    # First update room to allow 2 players
    $updateRoomBody = @{ max_players = 2 } | ConvertTo-Json
    $updateResponse = Invoke-RestMethod -Uri "$baseUrl/rest/v1/game_rooms?id=eq.$roomId" `
        -Method PATCH `
        -Headers $headers `
        -Body $updateRoomBody
    
    # Create a temporary test profile for the second player 
    $tempPlayerId = "temp-test-player-$(Get-Random)"
    $tempPlayerBody = @{
        id = $tempPlayerId
        username = "TestPlayer2"
    } | ConvertTo-Json
    
    try {
        $tempProfileResponse = Invoke-RestMethod -Uri "$baseUrl/rest/v1/profiles" `
            -Method POST `
            -Headers $headersWithReturn `
            -Body $tempPlayerBody
        
        # Add second player to game
        $joinPlayer2Body = @{
            room_id = $roomId
            user_id = $tempPlayerId
            is_ready = $true
        } | ConvertTo-Json
        
        $player2Response = Invoke-RestMethod -Uri $insertPlayerUri `
            -Method POST `
            -Headers $headers `
            -Body $joinPlayer2Body
        
        Write-Host "[OK] Player 2 (temp test player) added for minimum requirement" -ForegroundColor Green
    } catch {
        Write-Host "[WARN] Could not create second player, proceeding with 1 player: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "[ERROR] Failed to join player 1: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Start the game
Write-Host "[GAME] Step 3: Starting the game..." -ForegroundColor Green
$startGameBody = @{
    roomId = $roomId
} | ConvertTo-Json

$startGameUri = "$baseUrl/functions/v1/start-game-handler".Trim()
Write-Host "[CALL] Calling: $startGameUri" -ForegroundColor Cyan

try {
    $startResponse = Invoke-RestMethod -Uri $startGameUri `
        -Method POST `
        -Headers $headers `
        -Body $startGameBody
    
    Write-Host "[OK] Game started successfully" -ForegroundColor Green
    Write-Host "[STATUS] Game Status:" -ForegroundColor Cyan
    Write-Host "   Room ID: $roomId" -ForegroundColor White
    Write-Host "   Status: Active" -ForegroundColor White
    Write-Host "   Round: 1" -ForegroundColor White
} catch {
    Write-Host "[ERROR] Failed to start game: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Get the current question to make valid submissions
Write-Host "[QUESTION] Step 4: Getting current question..." -ForegroundColor Green
$questionUri = "$baseUrl/rest/v1/game_rooms?id=eq.$roomId`&select=*".Trim()
Write-Host "[CALL] Calling: $questionUri" -ForegroundColor Cyan

try {
    $roomData = Invoke-RestMethod -Uri $questionUri `
        -Method GET `
        -Headers $headers
    
    $currentQuestion = $roomData[0].current_question_data
    $choices = $currentQuestion.choices
    Write-Host "[OK] Current question loaded" -ForegroundColor Green
    Write-Host "   Question ID: $($currentQuestion.questionId)" -ForegroundColor White
    $choiceNames = $choices | ForEach-Object { $_.name }
    Write-Host "   Choices: $($choiceNames -join ', ')" -ForegroundColor White
} catch {
    Write-Host "[ERROR] Failed to get current question: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 5: Player 1 submits answer (should NOT advance game)
Write-Host "[ANSWER] Step 5: Player 1 submitting answer..." -ForegroundColor Green
$answer1Body = @{
    roomId = $roomId
    choiceName = $choices[0].name
} | ConvertTo-Json

$submit1Uri = "$baseUrl/functions/v1/submit-answer-handler".Trim()
Write-Host "[CALL] Calling: $submit1Uri" -ForegroundColor Cyan

try {
    $submit1Response = Invoke-RestMethod -Uri $submit1Uri `
        -Method POST `
        -Headers $headers `
        -Body $answer1Body
    
    Write-Host "[OK] Player 1 answer submitted" -ForegroundColor Green
    Write-Host "[STATUS] Response:" -ForegroundColor Cyan
    Write-Host "   Message: $($submit1Response.message)" -ForegroundColor White
    Write-Host "   Game Advanced: $($submit1Response.gameAdvanced)" -ForegroundColor White
    Write-Host "   Total Answers: $($submit1Response.totalAnswers)" -ForegroundColor White
    
    if ($submit1Response.gameAdvanced) {
        Write-Host "[WARN] WARNING: Game advanced on first answer (unexpected)" -ForegroundColor Yellow
    } else {
        Write-Host "[OK] Expected: Game did NOT advance (waiting for more players)" -ForegroundColor Green
    }
} catch {
    Write-Host "[ERROR] Failed to submit first answer: $($_.Exception.Message)" -ForegroundColor Red
    $errorDetails = $_.Exception.Response.Content | ConvertFrom-Json
    Write-Host "Error details: $($errorDetails | ConvertTo-Json -Depth 5)" -ForegroundColor Red
    exit 1
}

# Step 6: Player 2 submits final answer (should ATOMICALLY advance game)
Write-Host "[ANSWER] Step 6: Player 2 submitting FINAL answer (ATOMIC TEST)..." -ForegroundColor Green

# Create second player auth headers
$headers2 = $headers.Clone()
$headers2["Authorization"] = "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IlZMU25MaGVVTk5WK1p2WHMiLCJ0eXAiOiJKV1QifQ.eyJhdWQiOiJhdXRoZW50aWNhdGVkIiwiZXhwIjoxNzM3NDk3NzM5LCJpYXQiOjE3Mzc0OTQxMzksImlzcyI6Imh0dHBzOi8vdmxrYm1scWhreLGFmeG14Cc.xZGJhc2UuY28vYXV0aC92MSIsInN1YiI6IjZiNmJiMGI5LWRhYjQtNDY4Yi1hNzRjLTEzMGRhOWE5MzNhMSIsImVtYWlsIjoidGVzdHVzZXI1QGdtYWlsLmNvbSIsInBob25lIjoiIiwiYXBwX21ldGFkYXRhIjp7InByb3ZpZGVyIjoiZW1haWwiLCJwcm92aWRlcnMiOlsiZW1haWwiXX0sInVzZXJfbWV0YWRhdGEiOnt9LCJyb2xlIjoiYXV0aGVudGljYXRlZCIsImFhbCI6ImFhbDEiLCJhbXIiOlt7Im1ldGhvZCI6InBhc3N3b3JkIiwidGltZXN0YW1wIjoxNzM3NDk0MTM5fV0sInNlc3Npb25faWQiOiJlNDAzMTI4My0zNDhhLTRiZmMtODhkMC1lMDNjMDFjNzJjMzEiLCJpc19hbm9ueW1vdXMiOmZhbHNlfQ.F8gkUkqvn4yN0VvOsQ5pOhwMkxBsLkoPO8LlP1QtJW4"

$answer2Body = @{
    roomId = $roomId
    choiceName = $choices[1].name
} | ConvertTo-Json

$submit2Uri = "$baseUrl/functions/v1/submit-answer-handler".Trim()
Write-Host "[CALL] Calling: $submit2Uri" -ForegroundColor Cyan

try {
    Write-Host "[CRITICAL] CRITICAL TEST: Submitting final answer that should trigger atomic advance..." -ForegroundColor Magenta
    $submit2Response = Invoke-RestMethod -Uri $submit2Uri `
        -Method POST `
        -Headers $headers2 `
        -Body $answer2Body
    
    Write-Host "[OK] Player 2 (FINAL) answer submitted" -ForegroundColor Green
    Write-Host "[STATUS] ATOMIC OPERATION RESULTS:" -ForegroundColor Cyan
    Write-Host "   Message: $($submit2Response.message)" -ForegroundColor White
    Write-Host "   Game Advanced: $($submit2Response.gameAdvanced)" -ForegroundColor White
    Write-Host "   Total Answers: $($submit2Response.totalAnswers)" -ForegroundColor White
    
    if ($submit2Response.gameAdvanced) {
        Write-Host "[SUCCESS] SUCCESS: Game advanced atomically on final answer!" -ForegroundColor Green
        Write-Host "   Next Question ID: $($submit2Response.nextQuestionId)" -ForegroundColor White
        Write-Host "   Next Round Number: $($submit2Response.nextRoundNumber)" -ForegroundColor White
    } else {
        Write-Host "[ERROR] FAILURE: Game should have advanced on final answer" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] Failed to submit final answer: $($_.Exception.Message)" -ForegroundColor Red
    $errorDetails = $_.Exception.Response.Content | ConvertFrom-Json
    Write-Host "Error details: $($errorDetails | ConvertTo-Json -Depth 5)" -ForegroundColor Red
    exit 1
}

# Step 7: Verify the atomic operation worked
Write-Host "[VERIFY] Step 7: Verifying atomic operation results..." -ForegroundColor Green
$finalVerifyUri = "$baseUrl/rest/v1/game_rooms?id=eq.$roomId`&select=*".Trim()
Write-Host "[CALL] Calling: $finalVerifyUri" -ForegroundColor Cyan

try {
    $finalRoomData = Invoke-RestMethod -Uri $finalVerifyUri `
        -Method GET `
        -Headers $headers
    
    $finalRoom = $finalRoomData[0]
    Write-Host "[STATUS] Final Room State:" -ForegroundColor Cyan
    Write-Host "   Round Number: $($finalRoom.current_round_number)" -ForegroundColor White
    Write-Host "   Current Answers: $($finalRoom.current_round_answers.Count)" -ForegroundColor White
    Write-Host "   New Question ID: $($finalRoom.current_question_data.questionId)" -ForegroundColor White
    
    # Verify race condition was eliminated
    if ($finalRoom.current_round_number -eq 2 -and $finalRoom.current_round_answers.Count -eq 0) {
        Write-Host "[SUCCESS] RACE CONDITION FIX VERIFIED!" -ForegroundColor Green
        Write-Host "   [OK] Game advanced to round 2" -ForegroundColor Green
        Write-Host "   [OK] Answers reset for new round" -ForegroundColor Green
        Write-Host "   [OK] New question generated" -ForegroundColor Green
        Write-Host "   [OK] No race condition between submit-answer and next-question" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Race condition fix may not be working properly" -ForegroundColor Red
        Write-Host "   Expected: Round 2, 0 answers" -ForegroundColor Red
        Write-Host "   Actual: Round $($finalRoom.current_round_number), $($finalRoom.current_round_answers.Count) answers" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] Failed to verify final state: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "[COMPLETE] Test Complete!" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host "The race condition fix combines submit-answer and next-question logic" -ForegroundColor White
Write-Host "into a single atomic operation, eliminating the read-after-write" -ForegroundColor White
Write-Host "consistency issue that was causing the ""Not all players submitted"" error." -ForegroundColor White 
