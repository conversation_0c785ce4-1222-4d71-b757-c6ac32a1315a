/**
 * Fully Automated Multiplayer Round Advance Test
 * Uses MCP Puppeteer to test multiplayer functionality with no manual steps
 */

console.log('🎯 Fully Automated Multiplayer Round Advance Test');
console.log('================================================\n');

const TEST_URL = 'https://recognition-combine.vercel.app/';
const PLAYER1 = { email: 'fresh', password: 'test123' };
const PLAYER2 = { email: 'fresh2', password: 'test123' };

// Test scenarios with expected round advance timing
const TEST_SCENARIOS = [
  { name: 'Both at 1s → Round at 4s', p1: 1000, p2: 1000, expected: 4000 },
  { name: 'Both at 2s → Round at 5s', p1: 2000, p2: 2000, expected: 5000 },
  { name: 'Both at 5s → Round at 7s', p1: 5000, p2: 5000, expected: 7000 },
  { name: 'Only P1 → Round at 7s', p1: 1000, p2: null, expected: 7000 },
  { name: 'P1 at 1s, P2 at 3s → Round at 6s', p1: 1000, p2: 3000, expected: 6000 }
];

let browser1Context = { windowId: 'player1' };
let browser2Context = { windowId: 'player2' };
let testResults = [];

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function setupBrowsers() {
  console.log('🚀 Setting up browsers...');
  
  // Navigate both browsers to the game
  await mcp__puppeteer__puppeteer_navigate({ 
    url: TEST_URL,
    launchOptions: { headless: false }
  });
  
  // Take initial screenshot
  await mcp__puppeteer__puppeteer_screenshot({ 
    name: 'initial-load',
    width: 1280,
    height: 800
  });
  
  await delay(2000);
  console.log('✅ Browsers loaded');
}

async function injectHelpers() {
  console.log('💉 Injecting test helpers...');
  
  const helperScript = `
    // Test helper functions
    window.testData = {
      gameStartTime: null,
      roundChanges: [],
      submissions: []
    };
    
    // Submit answer at specific time
    window.submitAnswerAt = function(targetMs, playerName) {
      if (!window.testData.gameStartTime) {
        console.error('Game not started yet');
        return;
      }
      
      const elapsed = Date.now() - window.testData.gameStartTime;
      const delay = targetMs - elapsed;
      
      if (delay <= 0) {
        submitRandomAnswer(playerName);
      } else {
        setTimeout(() => submitRandomAnswer(playerName), delay);
      }
    };
    
    // Submit a random answer
    function submitRandomAnswer(playerName) {
      const buttons = Array.from(document.querySelectorAll('button')).filter(b => 
        b.className.includes('choice-button') || b.textContent.match(/^[A-Za-z\\s]+$/)
      );
      
      if (buttons.length >= 4) {
        const randomIndex = Math.floor(Math.random() * 4);
        const button = buttons[randomIndex];
        console.log(playerName + ' submitting:', button.textContent);
        window.testData.submissions.push({
          player: playerName,
          time: Date.now() - window.testData.gameStartTime,
          answer: button.textContent
        });
        button.click();
      }
    };
    
    // Monitor for round changes
    function startMonitoring() {
      let lastQuestion = null;
      
      const checkQuestion = () => {
        const questionEl = document.querySelector('h2');
        const currentQuestion = questionEl?.textContent || '';
        
        if (currentQuestion && currentQuestion.includes('?') && currentQuestion !== lastQuestion) {
          const now = Date.now();
          
          if (!window.testData.gameStartTime) {
            window.testData.gameStartTime = now;
            console.log('🎮 Game started');
          } else {
            const elapsed = now - window.testData.gameStartTime;
            window.testData.roundChanges.push({ 
              time: elapsed, 
              question: currentQuestion 
            });
            console.log('📍 Round changed at ' + elapsed + 'ms');
          }
          
          lastQuestion = currentQuestion;
        }
      };
      
      setInterval(checkQuestion, 100);
    }
    
    startMonitoring();
    console.log('✅ Helpers injected');
  `;
  
  await mcp__puppeteer__puppeteer_evaluate({ script: helperScript });
  console.log('✅ Test helpers ready');
}

async function signIn(playerNum, credentials) {
  console.log(`\n🔐 Signing in Player ${playerNum}...`);
  
  // Click Sign In button
  await mcp__puppeteer__puppeteer_click({ selector: 'button' });
  await delay(1000);
  
  // Fill email
  await mcp__puppeteer__puppeteer_fill({ 
    selector: 'input[type="email"]', 
    value: credentials.email 
  });
  
  // Fill password
  await mcp__puppeteer__puppeteer_fill({ 
    selector: 'input[type="password"]', 
    value: credentials.password 
  });
  
  // Submit form
  await mcp__puppeteer__puppeteer_click({ selector: 'button[type="submit"]' });
  
  await delay(2000);
  console.log(`✅ Player ${playerNum} signed in`);
  
  // Take screenshot after sign in
  await mcp__puppeteer__puppeteer_screenshot({ 
    name: `player${playerNum}-signed-in`,
    width: 1280,
    height: 800
  });
}

async function createRoom() {
  console.log('\n🏠 Creating room...');
  
  // Click Create Room
  const createRoomScript = `
    const buttons = Array.from(document.querySelectorAll('button'));
    const createBtn = buttons.find(b => b.textContent === 'Create Room');
    if (createBtn) {
      createBtn.click();
      true;
    } else false;
  `;
  
  await mcp__puppeteer__puppeteer_evaluate({ script: createRoomScript });
  await delay(1500);
  
  // Get room code
  const getRoomCodeScript = `
    const codeElements = Array.from(document.querySelectorAll('*'));
    const codeEl = codeElements.find(el => el.textContent.match(/^[A-Z0-9]{6}$/));
    codeEl ? codeEl.textContent : null;
  `;
  
  const roomCodeResult = await mcp__puppeteer__puppeteer_evaluate({ script: getRoomCodeScript });
  const roomCode = roomCodeResult.match(/[A-Z0-9]{6}/)?.[0];
  
  console.log(`✅ Room created: ${roomCode}`);
  
  await mcp__puppeteer__puppeteer_screenshot({ 
    name: 'room-created',
    width: 1280,
    height: 800
  });
  
  return roomCode;
}

async function switchToPlayer2() {
  console.log('\n🔄 Switching to Player 2 window...');
  
  // Open new window for player 2
  await mcp__puppeteer__puppeteer_navigate({ 
    url: TEST_URL + '?player=2',
    launchOptions: { headless: false }
  });
  
  await delay(2000);
  await injectHelpers();
}

async function joinRoom(roomCode) {
  console.log(`\n🏠 Joining room ${roomCode}...`);
  
  // Sign in Player 2
  await signIn(2, PLAYER2);
  
  // Click Join Room
  const joinRoomScript = `
    const buttons = Array.from(document.querySelectorAll('button'));
    const joinBtn = buttons.find(b => b.textContent === 'Join Room');
    if (joinBtn) {
      joinBtn.click();
      true;
    } else false;
  `;
  
  await mcp__puppeteer__puppeteer_evaluate({ script: joinRoomScript });
  await delay(1000);
  
  // Enter room code
  await mcp__puppeteer__puppeteer_fill({ 
    selector: 'input[placeholder*="room code" i]', 
    value: roomCode 
  });
  
  // Submit
  await mcp__puppeteer__puppeteer_click({ selector: 'button[type="submit"]' });
  
  await delay(2000);
  console.log('✅ Player 2 joined room');
  
  await mcp__puppeteer__puppeteer_screenshot({ 
    name: 'player2-joined',
    width: 1280,
    height: 800
  });
}

async function readyUpAndStart() {
  console.log('\n🎮 Getting ready to start...');
  
  // Player 1 ready
  console.log('Player 1 clicking Ready...');
  await mcp__puppeteer__puppeteer_navigate({ url: TEST_URL + '?player=1' });
  await delay(1000);
  
  const player1ReadyScript = `
    const buttons = Array.from(document.querySelectorAll('button'));
    const readyBtn = buttons.find(b => b.textContent === 'Ready');
    if (readyBtn) {
      readyBtn.click();
      true;
    } else false;
  `;
  await mcp__puppeteer__puppeteer_evaluate({ script: player1ReadyScript });
  
  // Player 2 ready
  console.log('Player 2 clicking Ready...');
  await mcp__puppeteer__puppeteer_navigate({ url: TEST_URL + '?player=2' });
  await delay(1000);
  
  const player2ReadyScript = `
    const buttons = Array.from(document.querySelectorAll('button'));
    const readyBtn = buttons.find(b => b.textContent === 'Ready');
    if (readyBtn) {
      readyBtn.click();
      true;
    } else false;
  `;
  await mcp__puppeteer__puppeteer_evaluate({ script: player2ReadyScript });
  
  await delay(1500);
  
  // Host starts game
  console.log('Host starting game...');
  await mcp__puppeteer__puppeteer_navigate({ url: TEST_URL + '?player=1' });
  await delay(1000);
  
  const startGameScript = `
    const buttons = Array.from(document.querySelectorAll('button'));
    const startBtn = buttons.find(b => b.textContent === 'Start Game');
    if (startBtn) {
      startBtn.click();
      true;
    } else false;
  `;
  await mcp__puppeteer__puppeteer_evaluate({ script: startGameScript });
  
  console.log('✅ Game started!');
  await delay(2000);
  
  await mcp__puppeteer__puppeteer_screenshot({ 
    name: 'game-started',
    width: 1280,
    height: 800
  });
}

async function runTestScenario(scenario, index) {
  console.log(`\n📊 Test ${index + 1}: ${scenario.name}`);
  
  // Schedule submissions for both players
  if (scenario.p1 !== null) {
    await mcp__puppeteer__puppeteer_navigate({ url: TEST_URL + '?player=1' });
    await delay(500);
    await mcp__puppeteer__puppeteer_evaluate({ 
      script: `window.submitAnswerAt(${scenario.p1}, 'Player 1')` 
    });
  }
  
  if (scenario.p2 !== null) {
    await mcp__puppeteer__puppeteer_navigate({ url: TEST_URL + '?player=2' });
    await delay(500);
    await mcp__puppeteer__puppeteer_evaluate({ 
      script: `window.submitAnswerAt(${scenario.p2}, 'Player 2')` 
    });
  }
  
  // Wait for round to complete
  await delay(8000);
  
  // Check results from Player 1's perspective
  await mcp__puppeteer__puppeteer_navigate({ url: TEST_URL + '?player=1' });
  await delay(500);
  
  const getResultsScript = `
    const changes = window.testData.roundChanges || [];
    const roundChange = changes[${index}];
    roundChange ? {
      actualTime: roundChange.time,
      expected: ${scenario.expected},
      difference: Math.abs(roundChange.time - ${scenario.expected}),
      passed: Math.abs(roundChange.time - ${scenario.expected}) <= 500
    } : null;
  `;
  
  const result = await mcp__puppeteer__puppeteer_evaluate({ script: getResultsScript });
  
  if (result) {
    const resultData = JSON.parse(result.replace(/(\w+):/g, '"$1":'));
    console.log(`Expected: ${resultData.expected}ms, Actual: ${resultData.actualTime}ms`);
    console.log(`Difference: ${resultData.difference}ms - ${resultData.passed ? '✅ PASS' : '❌ FAIL'}`);
    testResults.push({ scenario: scenario.name, ...resultData });
  } else {
    console.log('❌ No round change detected');
    testResults.push({ scenario: scenario.name, passed: false, error: 'No round change' });
  }
  
  // Take screenshot after each scenario
  await mcp__puppeteer__puppeteer_screenshot({ 
    name: `scenario-${index + 1}-complete`,
    width: 1280,
    height: 800
  });
}

async function runFullTest() {
  try {
    // Setup
    await setupBrowsers();
    await injectHelpers();
    
    // Player 1 signs in and creates room
    await signIn(1, PLAYER1);
    const roomCode = await createRoom();
    
    // Switch to Player 2 and join
    await switchToPlayer2();
    await joinRoom(roomCode);
    
    // Both players ready up and start
    await readyUpAndStart();
    
    // Run all test scenarios
    for (let i = 0; i < TEST_SCENARIOS.length; i++) {
      await runTestScenario(TEST_SCENARIOS[i], i);
    }
    
    // Final results
    console.log('\n📈 Final Test Results:');
    console.log('====================');
    
    const passed = testResults.filter(r => r.passed).length;
    const total = testResults.length;
    
    testResults.forEach((result, i) => {
      console.log(`${i + 1}. ${result.scenario}: ${result.passed ? '✅ PASS' : '❌ FAIL'}`);
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    console.log(`\nTotal: ${passed}/${total} tests passed`);
    
    // Take final screenshot
    await mcp__puppeteer__puppeteer_screenshot({ 
      name: 'test-complete',
      width: 1280,
      height: 800
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Take error screenshot
    await mcp__puppeteer__puppeteer_screenshot({ 
      name: 'test-error',
      width: 1280,
      height: 800
    });
  }
}

// Run the test
console.log('Starting automated test...\n');
runFullTest();