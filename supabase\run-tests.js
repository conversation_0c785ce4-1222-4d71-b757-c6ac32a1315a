// Simple Node.js test runner

const { exec } = require('child_process');
const readline = require('readline');
const fs = require('fs');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('\n====================================================================');
console.log('           AUTOMATED MULTIPLAYER ROUND ADVANCE TESTS');
console.log('====================================================================\n');

// Check if Puppeteer is installed
if (!fs.existsSync('node_modules/puppeteer')) {
  console.log('📦 Installing Puppeteer...');
  exec('npm install puppeteer', (error, stdout, stderr) => {
    if (error) {
      console.error('Failed to install Puppeteer:', error);
      process.exit(1);
    }
    console.log('✅ Puppeteer installed\n');
    showMenu();
  });
} else {
  showMenu();
}

function showMenu() {
  console.log('Choose test type:\n');
  console.log('1. Simple version (you sign in manually)');
  console.log('2. Full automated version (requires test accounts)');
  console.log('3. Exit\n');

  rl.question('Enter choice (1-3): ', (choice) => {
    switch(choice) {
      case '1':
        console.log('\n🚀 Running simple test...\n');
        runTest('test-multiplayer-simple.js');
        break;
      case '2':
        console.log('\n🚀 Running automated test...\n');
        runTest('test-multiplayer-automated.js');
        break;
      case '3':
        console.log('\nExiting...');
        rl.close();
        process.exit(0);
        break;
      default:
        console.log('\n❌ Invalid choice\n');
        showMenu();
    }
  });
}

function runTest(scriptName) {
  if (!fs.existsSync(scriptName)) {
    console.error(`❌ ${scriptName} not found`);
    rl.close();
    process.exit(1);
  }

  rl.close();
  
  // Run the test script
  const testProcess = exec(`node ${scriptName}`, (error) => {
    if (error && error.code !== null) {
      console.error('Test failed:', error);
    }
  });

  // Pipe output
  testProcess.stdout.pipe(process.stdout);
  testProcess.stderr.pipe(process.stderr);
  
  // Handle exit
  testProcess.on('exit', (code) => {
    process.exit(code);
  });
}