# Event-Based Transition Solution

## The Problem with Current Implementation

1. **Server sets deadline but doesn't act** - Just marks when transition should happen
2. **No automatic processor** - Requires external cron/monitor
3. **Clients can't help** - Their transition logic was removed
4. **Result**: Games get stuck waiting for a monitor that isn't running

## Why Edge Functions Can't Use setTimeout

- Edge functions are stateless and short-lived
- They terminate after sending the response
- Can't schedule future work within the function
- This is why the periodic monitor was suggested

## Better Event-Based Solutions

### Option 1: Database Trigger with HTTP Request (Recommended)

Create a PostgreSQL trigger that fires when transition_deadline passes:

```sql
-- Install pg_cron and http extensions in Supabase
CREATE EXTENSION IF NOT EXISTS pg_cron;
CREATE EXTENSION IF NOT EXISTS http;

-- Function to advance games that need transitions
CREATE OR REPLACE FUNCTION advance_game_if_needed()
RETURNS TRIGGER AS $$
DECLARE
  service_role_key TEXT;
  project_url TEXT;
BEGIN
  -- Only process if transition deadline has passed
  IF NEW.transition_deadline IS NOT NULL AND NEW.transition_deadline <= NOW() THEN
    -- Get credentials from vault
    SELECT decrypted_secret INTO service_role_key 
    FROM vault.decrypted_secrets WHERE name = 'service_role_key';
    
    SELECT decrypted_secret INTO project_url 
    FROM vault.decrypted_secrets WHERE name = 'project_url';
    
    -- Call the transition-monitor edge function
    PERFORM http_post(
      project_url || '/functions/v1/transition-monitor',
      json_build_object('roomId', NEW.id)::text,
      'application/json',
      headers => json_build_object(
        'Authorization', 'Bearer ' || service_role_key,
        'Content-Type', 'application/json'
      )::text
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger on any update to game_rooms
CREATE TRIGGER check_transition_deadline
AFTER UPDATE ON game_rooms
FOR EACH ROW
EXECUTE FUNCTION advance_game_if_needed();
```

### Option 2: Smart Client-Side Assistance

Re-enable a minimal client-side helper that:
1. Watches for `transition_deadline` in room data
2. When deadline passes, calls a "check-transition" endpoint
3. Server verifies and advances if appropriate

```typescript
// In page.tsx
useEffect(() => {
  if (!currentRoomGameData?.transition_deadline) return;
  
  const deadline = new Date(currentRoomGameData.transition_deadline).getTime();
  const now = Date.now();
  const delay = Math.max(0, deadline - now);
  
  const timer = setTimeout(() => {
    // Ask server to check if transition is needed
    supabase.functions.invoke('check-game-transition', {
      body: { roomId: activeRoomId }
    });
  }, delay);
  
  return () => clearTimeout(timer);
}, [currentRoomGameData?.transition_deadline]);
```

### Option 3: Realtime Channel with Presence

Use Supabase Realtime presence to elect a "leader" client:

```typescript
// One client becomes the transition coordinator
const channel = supabase.channel(`room:${roomId}`)
  .on('presence', { event: 'sync' }, () => {
    const state = channel.presenceState()
    const users = Object.keys(state)
    const isLeader = users.sort()[0] === userId
    
    if (isLeader && shouldTransition) {
      // Leader client triggers transition
      supabase.functions.invoke('advance-game', { 
        body: { roomId } 
      })
    }
  })
```

### Option 4: Two-Phase Commit Pattern

1. Last answer sets `ready_to_transition` flag
2. Any subsequent database operation triggers the transition
3. Guarantees eventual consistency

```sql
-- In submit-answer-handler
UPDATE game_rooms SET
  ready_to_transition = true,
  transition_at = NOW() + INTERVAL '3 seconds'
WHERE id = roomId;

-- Trigger fires on any update
CREATE TRIGGER process_ready_transitions
AFTER UPDATE ON game_rooms
FOR EACH ROW
WHEN (NEW.ready_to_transition = true AND NEW.transition_at <= NOW())
EXECUTE FUNCTION do_transition();
```

## Recommendation

**Option 2** (Smart Client-Side Assistance) is the cleanest:
- Event-based, not polling
- Server still authoritative (validates before transitioning)
- Works immediately without external dependencies
- Gracefully handles disconnections (any remaining client can trigger)

The key insight: The client doesn't *perform* the transition, it just *reminds* the server to check if one is needed. The server still validates everything.