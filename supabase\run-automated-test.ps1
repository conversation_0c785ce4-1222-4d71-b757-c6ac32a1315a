# Automated multiplayer test runner

Write-Host "AUTOMATED MULTIPLAYER TIMING TEST" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js is not installed!" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "test-multiplayer-automated.js")) {
    Write-Host "ERROR: test-multiplayer-automated.js not found!" -ForegroundColor Red
    Write-Host "Make sure you're in the supabase directory" -ForegroundColor Yellow
    exit 1
}

Write-Host "Setting up test environment..." -ForegroundColor Yellow

# Install dependencies
Write-Host "Installing Puppeteer..." -ForegroundColor White
npm install puppeteer

Write-Host ""
Write-Host "IMPORTANT: Before running tests, ensure you have:" -ForegroundColor Yellow
Write-Host "1. Two test accounts created in Supabase Auth" -ForegroundColor White
Write-Host "2. Update test-multiplayer-automated.js with your test credentials" -ForegroundColor White
Write-Host ""

Write-Host "Current test accounts configured:" -ForegroundColor Cyan
Write-Host "Player 1: <EMAIL>" -ForegroundColor Gray
Write-Host "Player 2: <EMAIL>" -ForegroundColor Gray
Write-Host ""

$continue = Read-Host "Have you created test accounts and updated credentials? (y/n)"
if ($continue -ne 'y') {
    Write-Host "Please create test accounts first." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To create test accounts:" -ForegroundColor Green
    Write-Host "1. Go to https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/auth/users" -ForegroundColor White
    Write-Host "2. Click 'Add user' and create two test accounts" -ForegroundColor White
    Write-Host "3. Update test-multiplayer-automated.js with the credentials" -ForegroundColor White
    exit 0
}

Write-Host ""
Write-Host "Starting automated tests..." -ForegroundColor Green
Write-Host "This will:" -ForegroundColor Yellow
Write-Host "- Open 2 browser windows" -ForegroundColor White
Write-Host "- Sign in both players" -ForegroundColor White
Write-Host "- Create/join a room" -ForegroundColor White
Write-Host "- Run 5 timing test scenarios" -ForegroundColor White
Write-Host "- Report exact timing measurements" -ForegroundColor White
Write-Host ""

# Run the tests
node test-multiplayer-automated.js

Write-Host ""
Write-Host "Test complete!" -ForegroundColor Green