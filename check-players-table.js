const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './web-app/.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkPlayersTable() {
  console.log('=== Checking players_data table ===\n');
  
  try {
    // Check if we can query the table
    const { data, error, count } = await supabase
      .from('players_data')
      .select('*', { count: 'exact', head: true });
    
    if (error) {
      console.error('Error querying players_data table:', error);
      return;
    }
    
    console.log(`✓ Table exists with ${count} total rows`);
    
    // Get a sample of players with images
    const { data: samplePlayers, error: sampleError } = await supabase
      .from('players_data')
      .select('id, player_name, team_name, local_image_path')
      .not('local_image_path', 'is', null)
      .limit(5);
    
    if (sampleError) {
      console.error('Error getting sample players:', sampleError);
    } else {
      console.log('\nSample players with images:');
      samplePlayers.forEach(p => {
        console.log(`- ${p.player_name} (${p.team_name}) - Image: ${p.local_image_path}`);
      });
    }
    
  } catch (err) {
    console.error('Exception:', err);
  }
}

checkPlayersTable();