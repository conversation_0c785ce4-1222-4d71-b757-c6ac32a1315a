# Query Logging Test Guide

This guide helps test the enhanced query logging in `fetchAndSetGameRooms` to diagnose why fresh2 cannot see fresh's waiting room.

## Key Changes Made

### 1. **CRITICAL FIX**: Removed `!inner` Join
- **Before**: `game_players!inner(user_id, is_connected)`
- **After**: `game_players(user_id, is_connected)`
- **Impact**: Now rooms with 0 players will appear in results
- **Why this matters**: The `!inner` join was filtering out rooms that had no players yet, which would prevent fresh2 from seeing fresh's newly created room

### 2. **Enhanced Logging**
- Comprehensive query construction logging
- Raw query result analysis
- Room enrichment step-by-step logging
- User context tracking throughout

## Testing Steps

### Step 1: Test as fresh (Host)
1. **Sign in as `fresh`**
2. **Switch to Multiplayer mode**
3. **Create a room**
4. **Check console logs for:**
   ```
   [LOBBY_FETCH] *** CRITICAL DEBUG START ***
   [LOBBY_FETCH] *** QUERY CONSTRUCTION ANALYSIS ***
   [LOBBY_FETCH] *** CRITICAL QUERY DETAILS ***
   [LOBBY_FETCH] *** QUERY OBJECT INSPECTION ***
   [LOBBY_FETCH] *** ABOUT TO EXECUTE QUERY ***
   [LOBBY_FETCH] *** RAW QUERY RESULT ***
   [LOBBY_FETCH] *** SUCCESSFUL RAW DATA ***
   [LOBBY_FETCH] *** ROOM ENRICHMENT ***
   [LOBBY_FETCH] *** FINAL ENRICHED RESULTS ***
   ```

### Step 2: Test as fresh2 (Non-Host)
1. **In a separate browser/incognito window, sign in as `fresh2`**
2. **Switch to Multiplayer mode**
3. **Check console logs for the same pattern**
4. **Key things to verify:**
   - `totalRoomsFromDB` should be > 0 if fresh's room exists
   - `fresh2CanSeeRoomsHostedByOthers` should be `true`
   - `roomsHostedByOthers` count should be > 0
   - Room should appear in `finalRoomsForUI`

### Step 3: Analyze the Logs

#### If fresh2 sees 0 rooms:
Check the `*** RAW QUERY RESULT ***` section:
- If `resultCount: 0` → RLS policy issue
- If `resultCount > 0` but `enrichedRoomsCount: 0` → Data transformation issue

#### If fresh2 sees rooms but not fresh's room:
Check the `*** SUCCESSFUL RAW DATA ***` section:
- Look at `roomsBreakdown` array
- Check if any room has `isHostedByOtherUser: true`
- Verify the `hostId` values

#### Critical Log Sections to Compare:

**fresh's logs (host creating room):**
```javascript
criticalAnalysis: {
  totalRoomsToShow: 1,
  roomsHostedByCurrentUser: 1,
  roomsHostedByOthers: 0,
  waitingRooms: 1,
  emptyRooms: 1
}
```

**fresh2's logs (should see fresh's room):**
```javascript
criticalAnalysis: {
  totalRoomsToShow: 1,
  roomsHostedByCurrentUser: 0,
  roomsHostedByOthers: 1, // ← This should be 1
  waitingRooms: 1,
  emptyRooms: 1
}
```

## Debug Commands

### Browser Console Commands:
```javascript
// Check current rooms for fresh2
debugCurrentStates()

// List all available rooms manually
debugListAllRooms()

// Force refresh the lobby
await window.fetchAndSetGameRooms?.()
```

## Expected Behavior After Fix

1. **fresh creates room** → Room appears in fresh's lobby
2. **fresh2 switches to multiplayer** → fresh's room appears in fresh2's lobby
3. **Room shows 0/4 players** (because fresh hasn't joined yet, just created)
4. **fresh2 can click and join** fresh's room

## Troubleshooting

### If fresh2 still sees 0 rooms:
1. **Check RLS policies** - `game_rooms` SELECT should allow authenticated users
2. **Check query in Supabase dashboard** - Run the same query manually
3. **Check user authentication** - Verify fresh2 is properly authenticated

### If fresh2 sees other rooms but not fresh's:
1. **Check room status** - Verify fresh's room is still 'waiting'
2. **Check database directly** - Look at `game_rooms` table manually
3. **Check timestamps** - Ensure fresh's room was created recently

## Success Criteria

✅ fresh2's console shows: `fresh2CanSeeRoomsHostedByOthers: true`  
✅ fresh2's console shows: `roomsHostedByOthers: 1`  
✅ fresh2's lobby UI displays fresh's room  
✅ fresh2 can click on fresh's room to see details  
✅ fresh2 can join fresh's room successfully  

This should resolve the visibility issue completely. 