========================================
MULTIPLAYER TIMING TEST - START HERE
========================================

To run the automated tests, use ANY of these methods:

METHOD 1 - PowerShell (if fixed):
  ./RUN-AUTOMATED-TESTS.ps1

METHOD 2 - Simple PowerShell:
  ./run-tests.ps1

METHOD 3 - Batch file (Windows):
  run-tests.bat

METHOD 4 - Direct with Node:
  node run-tests.js

METHOD 5 - Manual:
  npm install puppeteer
  node test-multiplayer-simple.js

========================================
WHAT GETS TESTED:
========================================
✓ Both players answer at 1s → Round advances at 4s
✓ Both players answer at 2s → Round advances at 5s  
✓ Both players answer at 5s → Round advances at 7s (cap)
✓ One player answers → Round advances at 7s
✓ P1 at 1s, P2 at 3s → Round advances at 6s

========================================
PREREQUISITES:
========================================
1. Node.js installed (https://nodejs.org/)
2. Two test accounts in your Supabase project
3. Edge Functions deployed (especially submit-answer-handler)

========================================
QUICK START:
========================================
Just run:
  node run-tests.js

Then choose option 1 for the simple version!