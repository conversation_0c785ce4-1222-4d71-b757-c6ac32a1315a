// Comprehensive multiplayer round advance testing with Puppeteer

const gameUrl = 'https://recognition-combine.vercel.app/';

// Test accounts - you may need to update these
const player1 = {
  email: '<EMAIL>',
  password: 'testpass123',
  name: 'Player 1'
};

const player2 = {
  email: '<EMAIL>', 
  password: 'testpass123',
  name: 'Player 2'
};

// Helper function to wait and measure time
async function measureTime(startTime) {
  return (Date.now() - startTime) / 1000;
}

// Helper to wait for element and click
async function waitAndClick(page, selector, options = {}) {
  await page.waitForSelector(selector, { visible: true, ...options });
  await page.click(selector);
}

// Helper to sign in
async function signIn(page, player) {
  console.log(`Signing in ${player.name}...`);
  
  // Click sign in button
  await waitAndClick(page, 'button:has-text("Sign In")');
  
  // Wait for auth modal
  await page.waitForSelector('input[type="email"]', { visible: true });
  
  // Enter credentials
  await page.type('input[type="email"]', player.email);
  await page.type('input[type="password"]', player.password);
  
  // Submit
  await waitAndClick(page, 'button[type="submit"]');
  
  // Wait for auth to complete
  await page.waitForFunction(() => {
    const signInButton = document.querySelector('button:has-text("Sign In")');
    return !signInButton || signInButton.style.display === 'none';
  }, { timeout: 10000 });
  
  console.log(`${player.name} signed in successfully`);
}

// Helper to create room
async function createRoom(page) {
  console.log('Creating room...');
  await waitAndClick(page, 'button:has-text("Multiplayer")');
  await waitAndClick(page, 'button:has-text("Create Room")');
  
  // Get room code
  await page.waitForSelector('[data-testid="room-code"], .room-code, div:has-text("Room Code:")', { visible: true });
  const roomCode = await page.evaluate(() => {
    const codeElement = document.querySelector('[data-testid="room-code"], .room-code, div:has-text("Room Code:")');
    return codeElement?.textContent?.match(/[A-Z0-9]{6}/)?.[0] || '';
  });
  
  console.log(`Room created: ${roomCode}`);
  return roomCode;
}

// Helper to join room
async function joinRoom(page, roomCode) {
  console.log(`Joining room ${roomCode}...`);
  await waitAndClick(page, 'button:has-text("Multiplayer")');
  await page.waitForSelector('input[placeholder*="room code"], input[placeholder*="Room code"]', { visible: true });
  await page.type('input[placeholder*="room code"], input[placeholder*="Room code"]', roomCode);
  await waitAndClick(page, 'button:has-text("Join Room")');
  console.log('Joined room successfully');
}

// Helper to submit answer at specific time
async function submitAnswerAtTime(page, targetTimeSeconds) {
  const startTime = Date.now();
  
  // Wait until target time
  while (measureTime(startTime) < targetTimeSeconds) {
    await new Promise(resolve => setTimeout(resolve, 50));
  }
  
  // Click first choice button
  await page.click('.choice-button:first-child, button[data-choice]:first-child, button:has-text("Choice"):first-child');
  
  const submitTime = measureTime(startTime);
  console.log(`Answer submitted at ${submitTime.toFixed(2)}s`);
  return submitTime;
}

// Helper to detect round advance
async function detectRoundAdvance(page) {
  return await page.evaluate(() => {
    return new Promise((resolve) => {
      let questionText = document.querySelector('.question-text, [data-testid="question"], h2')?.textContent || '';
      
      const checkInterval = setInterval(() => {
        const newQuestionText = document.querySelector('.question-text, [data-testid="question"], h2')?.textContent || '';
        if (newQuestionText !== questionText && newQuestionText !== '') {
          clearInterval(checkInterval);
          resolve(true);
        }
      }, 100);
      
      // Timeout after 10 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        resolve(false);
      }, 10000);
    });
  });
}

// Main test function
async function runMultiplayerTest() {
  console.log('Starting multiplayer round advance tests...\n');
  
  try {
    // Navigate both players to the game
    console.log('Opening game for both players...');
    await mcp__puppeteer__puppeteer_navigate({ url: gameUrl });
    
    // Take initial screenshot
    await mcp__puppeteer__puppeteer_screenshot({ 
      name: 'initial-load',
      encoded: true
    });
    
    // For now, let's test with manual intervention
    console.log('\n=== MANUAL TEST INSTRUCTIONS ===\n');
    console.log('1. Sign in with two different accounts');
    console.log('2. Create a room with Player 1');
    console.log('3. Join the room with Player 2');
    console.log('4. Start the game');
    console.log('5. Test these scenarios:\n');
    console.log('   SCENARIO A: Both answer quickly (within 2 seconds)');
    console.log('   - Expected: Round advances at ~5 seconds (2+3)');
    console.log('\n   SCENARIO B: Both answer at 5 seconds');
    console.log('   - Expected: Round advances at 7 seconds (hard cap)');
    console.log('\n   SCENARIO C: One player answers, other waits');
    console.log('   - Expected: Round advances at 7 seconds');
    console.log('\n6. Watch the console for timing information');
    console.log('\n================================\n');
    
    // Execute JavaScript to monitor game state
    await mcp__puppeteer__puppeteer_evaluate({
      script: `
        // Monitor round advances
        let roundStartTime = null;
        let currentRound = 1;
        
        console.log('[TEST] Monitoring round advances...');
        
        // Override console.log to capture game logs
        const originalLog = console.log;
        console.log = function(...args) {
          originalLog.apply(console, args);
          
          // Detect round start
          if (args[0]?.includes?.('Game started') || args[0]?.includes?.('Round')) {
            roundStartTime = Date.now();
            console.warn('[TEST] Round started at', new Date().toISOString());
          }
          
          // Detect answer submissions
          if (args[0]?.includes?.('Answer submitted')) {
            const elapsed = roundStartTime ? ((Date.now() - roundStartTime) / 1000).toFixed(2) : '?';
            console.warn('[TEST] Answer submitted at +' + elapsed + 's');
          }
          
          // Detect round advance
          if (args[0]?.includes?.('transition') || args[0]?.includes?.('next question')) {
            const elapsed = roundStartTime ? ((Date.now() - roundStartTime) / 1000).toFixed(2) : '?';
            console.warn('[TEST] Round advanced at +' + elapsed + 's');
            currentRound++;
            roundStartTime = Date.now();
          }
        };
        
        // Monitor DOM for visual round advances
        setInterval(() => {
          const timerElement = document.querySelector('.timer, [data-testid="timer"]');
          const transitionElement = document.querySelector('.transition-timer, [data-testid="transition"]');
          
          if (transitionElement && transitionElement.textContent.includes('3')) {
            console.warn('[TEST] 3-second transition timer detected!');
          }
        }, 100);
        
        console.log('[TEST] Monitoring setup complete. Play the game and watch for [TEST] messages.');
      `
    });
    
    // Take screenshot of game state
    await new Promise(resolve => setTimeout(resolve, 2000));
    await mcp__puppeteer__puppeteer_screenshot({ 
      name: 'game-monitoring',
      encoded: true
    });
    
    console.log('\nTest setup complete. Monitor the browser console for [TEST] messages showing exact timings.');
    console.log('The page will remain open for testing. Press Ctrl+C when done.');
    
    // Keep the test running
    await new Promise(() => {}); // This will run indefinitely
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
runMultiplayerTest();