-- Add timer state fields to game_rooms table for visual timer display
ALTER TABLE game_rooms
ADD COLUMN IF NOT EXISTS timer_type text CHECK (timer_type IN ('round', 'transition')),
ADD COLUMN IF NOT EXISTS timer_started_at timestamptz,
ADD COLUMN IF NOT EXISTS timer_duration_seconds numeric(3, 1);

-- Create index for active timers
CREATE INDEX IF NOT EXISTS idx_game_rooms_active_timers 
ON game_rooms(timer_started_at) 
WHERE timer_started_at IS NOT NULL;

-- Comment on new columns
COMMENT ON COLUMN game_rooms.timer_type IS 'Type of timer currently active: round (7s max) or transition (3s after all answers)';
COMMENT ON COLUMN game_rooms.timer_started_at IS 'When the current timer started, used for client synchronization';
COMMENT ON COLUMN game_rooms.timer_duration_seconds IS 'Duration of the current timer in seconds (7.0 for round, 3.0 for transition)';