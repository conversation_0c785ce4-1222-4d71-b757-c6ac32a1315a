const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://xmyxuvuimebjltnaamox.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testEdgeFunctionWithAuth() {
  console.log('Testing Edge Function with authentication...\n');

  try {
    // First, sign in
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'test123456'
    });

    if (authError) {
      console.error('Auth error:', authError);
      return;
    }

    console.log('✓ Authenticated successfully');
    console.log('User ID:', authData.user.id);

    // Create a test room
    const { data: room, error: roomError } = await supabase
      .from('game_rooms')
      .insert({
        room_code: 'TEST' + Math.random().toString(36).substr(2, 4).toUpperCase(),
        host_id: authData.user.id,
        status: 'waiting'
      })
      .select()
      .single();

    if (roomError) {
      console.error('Room creation error:', roomError);
      return;
    }

    console.log('✓ Created test room:', room.id);

    // Add player to room
    await supabase
      .from('game_players')
      .insert({
        room_id: room.id,
        user_id: authData.user.id,
        is_ready: true
      });

    // Test the Edge Function
    console.log('\nInvoking start-game-handler...');
    const { data, error } = await supabase.functions.invoke('start-game-handler', {
      body: { roomId: room.id }
    });

    if (error) {
      console.error('\n❌ Edge Function error:', error);
      if (error.context) {
        console.error('Context:', error.context);
      }
    } else {
      console.log('\n✓ Edge Function responded successfully!');
      console.log('Response:', JSON.stringify(data, null, 2));
      
      if (data.firstQuestion) {
        console.log('\n✓ Question generated:');
        console.log('  - Question ID:', data.firstQuestion.questionId);
        console.log('  - Image URL:', data.firstQuestion.imageUrl);
        console.log('  - Correct Player:', data.firstQuestion.correctChoiceName);
        console.log('  - Choices:', data.firstQuestion.choices.map(c => c.name).join(', '));
      }
    }

    // Cleanup
    await supabase.from('game_players').delete().eq('room_id', room.id);
    await supabase.from('game_rooms').delete().eq('id', room.id);
    console.log('\n✓ Cleanup completed');

  } catch (err) {
    console.error('Test error:', err);
  }
}

testEdgeFunctionWithAuth();