const puppeteer = require('puppeteer');

// Configuration
const BASE_URL = 'http://localhost:3000';

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testMultiplayerSync() {
  console.log('=== SIMPLE MULTIPLAYER SYNC TEST ===\n');
  
  const browser1 = await puppeteer.launch({
    headless: false,
    executablePath: '/snap/bin/chromium',
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--window-size=1200,800', '--window-position=0,0']
  });
  
  const browser2 = await puppeteer.launch({
    headless: false,
    executablePath: '/snap/bin/chromium',
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--window-size=1200,800', '--window-position=600,0']
  });
  
  try {
    const page1 = await browser1.newPage();
    const page2 = await browser2.newPage();
    
    // Navigate both pages
    console.log('Loading pages...');
    await page1.goto(BASE_URL);
    await page2.goto(BASE_URL);
    await sleep(3000);
    
    // Check if login is needed
    const needsLogin1 = await page1.evaluate(() => !document.body.textContent.includes('fresh'));
    const needsLogin2 = await page2.evaluate(() => !document.body.textContent.includes('fresh2'));
    
    // Login if needed
    if (needsLogin1) {
      console.log('Player 1 logging in...');
      await page1.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Login'));
        if (btn) btn.click();
      });
      await sleep(1000);
      
      // Type credentials
      await page1.waitForSelector('input[name="identifier"]', { visible: true });
      await page1.type('input[name="identifier"]', 'fresh');
      await page1.type('input[name="password"]', 'fresh123');
      await page1.click('button[type="submit"]');
      await sleep(3000);
      
      // Close auth modal if still open
      await page1.evaluate(() => {
        const closeBtn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Close Auth'));
        if (closeBtn) closeBtn.click();
      });
      await sleep(1000);
    }
    
    if (needsLogin2) {
      console.log('Player 2 logging in...');
      await page2.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Login'));
        if (btn) btn.click();
      });
      await sleep(1000);
      
      // Type credentials
      await page2.waitForSelector('input[name="identifier"]', { visible: true });
      await page2.type('input[name="identifier"]', 'fresh2');
      await page2.type('input[name="password"]', 'fresh123');
      await page2.click('button[type="submit"]');
      await sleep(3000);
      
      // Close auth modal if still open
      await page2.evaluate(() => {
        const closeBtn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Close Auth'));
        if (closeBtn) closeBtn.click();
      });
      await sleep(1000);
    }
    
    // Switch to multiplayer
    console.log('\nSwitching to multiplayer...');
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Multiplayer'));
      if (btn) btn.click();
    });
    await page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Multiplayer'));
      if (btn) btn.click();
    });
    await sleep(2000);
    
    // Host creates room
    console.log('Host creating room...');
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Host Game'));
      if (btn) btn.click();
    });
    await sleep(2000);
    
    // Guest joins
    console.log('Guest joining room...');
    await page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Join'));
      if (btn) btn.click();
    });
    await sleep(2000);
    
    // Both mark ready
    console.log('Both marking ready...');
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Ready');
      if (btn) btn.click();
    });
    await page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Ready');
      if (btn) btn.click();
    });
    await sleep(1000);
    
    // Host starts game
    console.log('\nHost starting game...');
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Start Game'));
      if (btn) btn.click();
    });
    
    // CRITICAL TEST: Check both pages for game state
    console.log('\n=== CHECKING GAME STATE TRANSITION ===');
    await sleep(5000); // Give time for transition
    
    // Take screenshots
    await page1.screenshot({ path: 'test-host-final.png' });
    await page2.screenshot({ path: 'test-guest-final.png' });
    console.log('Screenshots saved: test-host-final.png, test-guest-final.png');
    
    const page1State = await page1.evaluate(() => {
      const hasImage = !!document.querySelector('img[alt="Player to identify"]');
      const bodyText = document.body.textContent;
      const buttons = Array.from(document.querySelectorAll('button')).map(b => b.textContent);
      return {
        hasGameImage: hasImage,
        hasWaitingText: bodyText.includes('Waiting for game to start'),
        hasRoundText: bodyText.includes('Round'),
        url: window.location.href,
        buttons: buttons.slice(0, 10), // First 10 buttons
        pageTitle: document.title
      };
    });
    
    const page2State = await page2.evaluate(() => {
      const hasImage = !!document.querySelector('img[alt="Player to identify"]');
      const bodyText = document.body.textContent;
      const buttons = Array.from(document.querySelectorAll('button')).map(b => b.textContent);
      return {
        hasGameImage: hasImage,
        hasWaitingText: bodyText.includes('Waiting for game to start'),
        hasRoundText: bodyText.includes('Round'),
        url: window.location.href,
        buttons: buttons.slice(0, 10), // First 10 buttons
        pageTitle: document.title
      };
    });
    
    console.log('\nPLAYER 1 (HOST) STATE:', page1State);
    console.log('PLAYER 2 (GUEST) STATE:', page2State);
    
    // Check results
    if (page1State.hasGameImage && page2State.hasGameImage) {
      console.log('\n✅ SUCCESS: Both players transitioned to game!');
      return true;
    } else {
      console.log('\n❌ FAILURE: Multiplayer sync issue detected');
      if (!page1State.hasGameImage) console.log('- Host failed to transition');
      if (!page2State.hasGameImage) console.log('- Guest failed to transition');
      if (page2State.hasWaitingText) console.log('- Guest stuck on waiting screen');
      return false;
    }
    
  } finally {
    await browser1.close();
    await browser2.close();
  }
}

// Run test
testMultiplayerSync().then(success => {
  process.exit(success ? 0 : 1);
}).catch(err => {
  console.error('Test error:', err);
  process.exit(1);
});