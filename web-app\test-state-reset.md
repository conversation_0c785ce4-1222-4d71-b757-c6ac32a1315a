# State Reset Test Plan

## Issue: "You are already in this active game" after logout/login

### Root Cause
When a host logs out and logs back in, the client-side state (`activeRoomId`, `currentRoomGameData`, `playersInRoom`) was not being reset, causing the UI to incorrectly show "You are already in this active game" when viewing their old room.

### Fix Implemented
1. **Reset state on SIGNED_OUT event** in the authentication listener:
   - `setActiveRoomId(null)`
   - `setCurrentRoomGameData(null)`
   - `setPlayersInRoom([])`
   - `setSelectedRoomForDetail(null)`
   - `setMultiplayerPanelState('lobby_list')`
   - `setCenterPanelMpState('lobby_list_detail')`

2. **Enhanced "Back to List" button** to ensure proper navigation state

3. **Added comprehensive logging** to track state changes and room detail view logic

### Test Scenario
1. **Host creates a room**
   - Log: `[AuthListener] Event: SIGNED_IN, Session User: {user_id}`
   - Log: `[Client] User {user_id} attempting to join/rejoin room {room_id}`
   - Expected: `activeRoomId` is set to the new room ID

2. **Host logs out**
   - Log: `[AuthListener] Event: SIGNED_OUT, Session User: undefined`
   - Log: `[AuthListener] SIGNED_OUT - Resetting room/game state`
   - Log: `[AuthListener] Room/game state reset complete`
   - Expected: All room/game state variables are reset to null/empty

3. **Host logs back in**
   - Log: `[AuthListener] Event: SIGNED_IN, Session User: {user_id}`
   - Expected: User state is restored but room state remains reset

4. **Host switches to multiplayer and views old room**
   - Log: `[LOBBY_FETCH] Successfully fetched game_rooms with joins: Array(1)`
   - Log: `[LobbyDetail] Room detail view state check:` with `activeRoomId: null`
   - Expected: Shows "Join This Room" or "Rejoin Active Game" button instead of "You are already in this active game"

### Expected Console Logs for Success
```
[AuthListener] Event: SIGNED_OUT, Session User: undefined
[AuthListener] SIGNED_OUT - Resetting room/game state
[AuthListener] Room/game state reset complete
...
[AuthListener] Event: SIGNED_IN, Session User: {user_id}
...
[LobbyDetail] Room detail view state check: {
  roomId: "{room_id}",
  roomStatus: "waiting",
  userId: "{user_id}",
  activeRoomId: null,  // <-- This should be null after logout
  hasPlayerEntry: true,
  playerEntryConnected: false,
  isViewingActiveRoom: false  // <-- This should be false
}
```

### Verification Steps
1. Open browser console to monitor logs
2. Create a room as host
3. Log out (verify state reset logs appear)
4. Log back in
5. Go to multiplayer mode
6. Click on the old room in the lobby list
7. Verify the button shows "Rejoin Active Game" or "Join This Room" instead of "You are already in this active game"

### Additional Edge Cases to Test
- Multiple logout/login cycles
- Creating multiple rooms before logout
- Switching between single-player and multiplayer modes
- Using "Back to List" button functionality 