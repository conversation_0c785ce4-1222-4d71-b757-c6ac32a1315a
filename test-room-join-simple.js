const puppeteer = require('puppeteer');

async function testRoomJoin() {
  console.log('=== Simple Room Join Test ===');
  
  const browser = await puppeteer.launch({
    headless: false,
    executablePath: '/usr/bin/chromium-browser',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.goto('http://localhost:3000');
    
    // Wait for page load
    await new Promise(r => setTimeout(r, 3000));
    
    // Check current state
    const pageText = await page.evaluate(() => document.body.innerText);
    console.log('Page loaded. Checking for room list...');
    
    if (pageText.includes("fresh's Game")) {
      console.log("✓ Found fresh's Game in page");
      
      // Try to find and double-click the room
      const clicked = await page.evaluate(() => {
        // Find all elements with room info
        const elements = Array.from(document.querySelectorAll('*'));
        
        for (const el of elements) {
          if (el.textContent.includes("fresh's Game") && 
              el.textContent.includes("Players:") &&
              el.children.length < 10) { // Likely a room card, not a container
            
            console.log('Found room element:', el.tagName, el.className);
            
            // Double click it
            const rect = el.getBoundingClientRect();
            const event = new MouseEvent('dblclick', {
              view: window,
              bubbles: true,
              cancelable: true,
              clientX: rect.left + rect.width / 2,
              clientY: rect.top + rect.height / 2
            });
            el.dispatchEvent(event);
            
            // Also try regular clicks
            el.click();
            setTimeout(() => el.click(), 100);
            
            return true;
          }
        }
        return false;
      });
      
      console.log('Double-click attempted:', clicked);
      
      // Wait and check result
      await new Promise(r => setTimeout(r, 3000));
      
      const afterClickText = await page.evaluate(() => document.body.innerText);
      if (afterClickText.includes('Ready') || afterClickText.includes('Waiting for')) {
        console.log('✅ Successfully joined room!');
      } else {
        console.log('❌ Still in lobby after double-click');
      }
    } else {
      console.log('No rooms found in lobby');
    }
    
  } finally {
    await new Promise(r => setTimeout(r, 5000));
    await browser.close();
  }
}

testRoomJoin().catch(console.error);