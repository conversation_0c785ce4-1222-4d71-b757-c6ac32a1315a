Write-Host "Checking and applying realtime-related migrations..." -ForegroundColor Cyan

# Get the root directory
$rootDir = Split-Path $MyInvocation.MyCommand.Path -Parent
Set-Location $rootDir

Write-Host "Loading Supabase environment..." -ForegroundColor Yellow
if (Test-Path "supabase/.env") {
    Get-Content "supabase/.env" | ForEach-Object {
        if ($_ -match '^([^=]+)=(.*)$') {
            [System.Environment]::SetEnvironmentVariable($matches[1], $matches[2])
        }
    }
    Write-Host "Environment loaded successfully" -ForegroundColor Green
} else {
    Write-Host "Warning: supabase/.env file not found" -ForegroundColor Yellow
}

# Navigate to supabase directory
Set-Location supabase

Write-Host "`nChecking migration status..." -ForegroundColor Yellow

# List all migrations
Write-Host "`nAvailable migrations:" -ForegroundColor Cyan
Get-ChildItem migrations -Filter "*.sql" | ForEach-Object {
    Write-Host "  - $($_.Name)" -ForegroundColor Gray
}

# Apply migrations with include-all flag to ensure all migrations are applied
Write-Host "`nApplying migrations (including seed data)..." -ForegroundColor Yellow
$migrationResult = supabase db push --include-all 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nMigrations applied successfully!" -ForegroundColor Green
    Write-Host $migrationResult
    
    Write-Host "`nSpecifically checking realtime-related migrations:" -ForegroundColor Cyan
    Write-Host "  - 20250617000000_fix_realtime_publications.sql" -ForegroundColor Yellow
    Write-Host "  - 20250701000000_fix_realtime_rls_policy.sql" -ForegroundColor Yellow
    
    Write-Host "`nThese migrations should have:" -ForegroundColor Green
    Write-Host "  1. Added game_players to realtime publication" -ForegroundColor Gray
    Write-Host "  2. Fixed RLS policies for realtime access" -ForegroundColor Gray
    Write-Host "  3. Created helper function to avoid infinite recursion" -ForegroundColor Gray
    
} else {
    Write-Host "`nError applying migrations:" -ForegroundColor Red
    Write-Host $migrationResult
    
    Write-Host "`nTrying alternative approach..." -ForegroundColor Yellow
    # Try reset and push
    $resetResult = supabase db reset 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Database reset successful, migrations should be applied" -ForegroundColor Green
    } else {
        Write-Host "Database reset failed:" -ForegroundColor Red
        Write-Host $resetResult
    }
}

# Return to root directory
Set-Location ..

Write-Host "`nMigration check complete!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Test multiplayer functionality" -ForegroundColor Gray
Write-Host "2. Create a room as host" -ForegroundColor Gray
Write-Host "3. Join with another browser/user" -ForegroundColor Gray
Write-Host "4. Host should now see joining players in real-time" -ForegroundColor Gray