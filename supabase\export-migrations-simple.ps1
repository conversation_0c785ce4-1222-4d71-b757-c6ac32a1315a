# Simple Migration Export Script
Write-Host "Exporting SQL migrations..." -ForegroundColor Green

$outputFile = "all-migrations-export.txt"
$migrationFiles = Get-ChildItem -Path ".\migrations" -Filter "*.sql" | Sort-Object Name

# Create header
$content = @()
$content += "=" * 80
$content += "SQL MIGRATIONS EXPORT - $(Get-Date)"
$content += "Total Files: $($migrationFiles.Count)"
$content += "=" * 80
$content += ""

# Process each file
foreach ($file in $migrationFiles) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    $content += ""
    $content += "FILE: $($file.Name)"
    $content += "SIZE: $([math]::Round($file.Length/1KB, 2)) KB"
    $content += "MODIFIED: $($file.LastWriteTime)"
    $content += "-" * 80
    
    $fileContent = Get-Content -Path $file.FullName -Raw
    if ($fileContent) {
        $content += $fileContent
    } else {
        $content += "-- EMPTY FILE --"
    }
    
    $content += ""
    $content += "END OF: $($file.Name)"
    $content += "=" * 80
}

# Write output
$content | Out-File -FilePath $outputFile -Encoding UTF8
Write-Host "Exported to: $outputFile" -ForegroundColor Green
Write-Host "Files processed: $($migrationFiles.Count)" -ForegroundColor Cyan 