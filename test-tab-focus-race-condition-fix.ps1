#!/usr/bin/env pwsh

# Test script for Tab Focus Race Condition Fix
# Tests that token refresh on tab focus does not boot users from game rooms

Write-Host "🔧 TAB FOCUS RACE CONDITION FIX TEST" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 Problem Overview:" -ForegroundColor Yellow
Write-Host "BEFORE: Tabbing away and back in would boot users from game rooms and take them to the multiplayer lobby"
Write-Host "CAUSE: Token refresh on tab focus triggers SIGNED_IN event, which was misinterpreted as new login"
Write-Host "EFFECT: onAuthSuccess callback fired, auto-redirecting to multiplayer lobby and resetting game state"
Write-Host ""

Write-Host "🔧 Fixes Implemented:" -ForegroundColor Green
Write-Host "✅ FIX 1: AuthProvider - Track previous session state with useRef"
Write-Host "✅ FIX 2: AuthModal - Only call onAuthSuccess for TRUE initial sign-ins"
Write-Host "✅ FIX 3: Visibility Handler - Simplified, trust Supabase to handle reconnection"
Write-Host ""

Write-Host "🧪 Testing Steps:" -ForegroundColor Blue
Write-Host ""
Write-Host "SCENARIO 1: Game Room Stability Test"
Write-Host "1. Start dev server: npm run dev"
Write-Host "2. Sign in to the application"
Write-Host "3. Click 'Multiplayer' to enter lobby"
Write-Host "4. Create a new room and wait for it to activate"
Write-Host "5. Tab away from the browser (to another tab or application)"
Write-Host "6. Wait 10-15 seconds (to ensure token refresh occurs)"
Write-Host "7. Tab back to the application"
Write-Host "8. ✅ VERIFY: User should remain in the game room, NOT be redirected to lobby"
Write-Host ""

Write-Host "SCENARIO 2: Initial Sign-In Test (Should Still Work)"
Write-Host "1. Sign out if signed in"
Write-Host "2. Click 'Multiplayer' button"
Write-Host "3. Complete sign-in process"
Write-Host "4. ✅ VERIFY: User should be automatically taken to multiplayer lobby"
Write-Host ""

Write-Host "SCENARIO 3: Multiple Tab Focus Test"
Write-Host "1. Join an active game room with multiple players"
Write-Host "2. Perform tab focus cycle 5-10 times (tab away, wait, tab back)"
Write-Host "3. ✅ VERIFY: Game state remains stable, no unexpected redirects"
Write-Host "4. ✅ VERIFY: WebSocket connections remain stable (no error floods)"
Write-Host ""

Write-Host "🔍 Key Console Logs to Watch:" -ForegroundColor Magenta
Write-Host "SUCCESS INDICATORS:"
Write-Host "- '[AuthProvider] 🔄 DETECTED TOKEN REFRESH: Previous session existed, this is just a refresh on tab focus'"
Write-Host "- '[AuthModal] 🔄 DETECTED TOKEN REFRESH: Previous session existed, this is just a refresh on tab focus - NOT calling onAuthSuccess'"
Write-Host "- '[DISCONNECT_DETECTION] User is in an active room. Resyncing data.'"
Write-Host ""
Write-Host "FAILURE INDICATORS (Should NOT see these on tab focus):"
Write-Host "- '[AuthProvider] ✅ DETECTED TRUE INITIAL SIGN-IN'"
Write-Host "- '[AuthModal] ✅ DETECTED TRUE INITIAL SIGN-IN: Calling onAuthSuccess callback'"
Write-Host "- '[AUTH_SUCCESS] Automatically switching to multiplayer mode after successful authentication'"
Write-Host ""

Write-Host "🚨 Expected Behavior Changes:" -ForegroundColor Red
Write-Host "BEFORE: Tab focus → token refresh → SIGNED_IN event → onAuthSuccess → lobby redirect → game broken"
Write-Host "AFTER:  Tab focus → token refresh → SIGNED_IN event → detected as refresh → NO callback → game stable"
Write-Host ""

Write-Host "💡 Technical Details:" -ForegroundColor Yellow
Write-Host "- Uses useRef to track previous session state across auth events"
Write-Host "- Only triggers onAuthSuccess when transitioning from null to valid session"
Write-Host "- Simplifies visibility change handler to trust Supabase auto-reconnection"
Write-Host "- Maintains all existing functionality for true initial sign-ins"
Write-Host ""

Write-Host "🔍 Code Changes Made:" -ForegroundColor Cyan
Write-Host "FILES MODIFIED:"
Write-Host "- web-app/src/providers/AuthProvider.tsx (session state tracking)"
Write-Host "- web-app/src/components/auth/AuthModal.tsx (smart onAuthSuccess logic)"
Write-Host "- web-app/src/app/page.tsx (simplified visibility handler)"
Write-Host ""

Write-Host "🏗️ Starting Development Server..." -ForegroundColor Green
Write-Host "Test the scenarios above to verify the race condition is fixed."
Write-Host ""

# Start the development server
Set-Location "web-app"
npm run dev 