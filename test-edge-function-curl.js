#!/usr/bin/env node

const { execSync } = require('child_process');
require('dotenv').config({ path: './web-app/.env.local' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Testing edge function availability...');
console.log('SUPABASE_URL:', SUPABASE_URL);

// First get an auth token
const authCmd = `curl -s -X POST "${SUPABASE_URL}/auth/v1/token?grant_type=password" \
  -H "Content-Type: application/json" \
  -H "apikey: ${ANON_KEY}" \
  -d '{"email":"<EMAIL>","password":"password123"}'`;

console.log('\n1. Getting auth token...');
try {
  const authResult = execSync(authCmd, { encoding: 'utf8' });
  const authData = JSON.parse(authResult);
  
  if (!authData.access_token) {
    console.error('Failed to get auth token:', authData);
    process.exit(1);
  }
  
  console.log('✓ Got auth token');
  
  // Test the edge function with a simple request
  const testCmd = `curl -i -X POST "${SUPABASE_URL}/functions/v1/start-game-handler" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${authData.access_token}" \
    -H "apikey: ${ANON_KEY}" \
    -d '{"roomId":"test-room-id"}'`;
  
  console.log('\n2. Testing edge function...');
  const result = execSync(testCmd, { encoding: 'utf8' });
  console.log('\nResponse:');
  console.log(result);
  
} catch (error) {
  console.error('Error:', error.message);
  if (error.stdout) {
    console.log('Output:', error.stdout);
  }
  if (error.stderr) {
    console.error('Error output:', error.stderr);
  }
}