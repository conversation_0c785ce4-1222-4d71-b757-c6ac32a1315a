const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };

// Screenshot directory
const SCREENSHOT_DIR = path.join(__dirname, 'test-screenshots-comprehensive');

// Test state tracking
let testPhase = 'INIT';
let lastError = null;

// Helper to take screenshots with phase tracking
async function takeScreenshot(page, name, prefix) {
  try {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${prefix}-${testPhase}-${name}-${timestamp}.png`;
    const filepath = path.join(SCREENSHOT_DIR, filename);
    await page.screenshot({ path: filepath, fullPage: true });
    console.log(`[${prefix}] Screenshot: ${filename}`);
  } catch (error) {
    console.error(`[${prefix}] Screenshot failed:`, error.message);
  }
}

// Enhanced wait helper with better error messages
async function waitAndRetry(fn, options = {}) {
  const { timeout = 30000, interval = 1000, description = 'operation' } = options;
  const startTime = Date.now();
  let lastError = null;
  
  while (Date.now() - startTime < timeout) {
    try {
      const result = await fn();
      if (result) return result;
    } catch (e) {
      lastError = e;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  throw new Error(`Timeout waiting for ${description}${lastError ? ': ' + lastError.message : ''}`);
}

// Debug helper to log page state
async function logPageState(page, context) {
  try {
    const pageInfo = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button')).map(btn => ({
        text: btn.textContent.trim(),
        disabled: btn.disabled,
        className: btn.className
      }));
      
      const modals = Array.from(document.querySelectorAll('[role="dialog"], .modal')).map(modal => ({
        visible: modal.offsetParent !== null,
        content: modal.textContent.substring(0, 100)
      }));
      
      const authInputs = {
        email: !!document.querySelector('input[type="email"], input[placeholder*="email" i]'),
        password: !!document.querySelector('input[type="password"]')
      };
      
      return {
        url: window.location.href,
        title: document.title,
        buttons: buttons,
        modals: modals,
        authInputs: authInputs,
        bodyText: document.body.innerText.substring(0, 500),
        hasMultiplayerMode: document.body.textContent.includes('Multiplayer Mode'),
        hasSinglePlayerMode: document.body.textContent.includes('Single-player Mode'),
        hasHostGame: buttons.some(b => b.text.includes('Host Game')),
        hasJoinGame: buttons.some(b => b.text.includes('Join')),
        gameMode: document.querySelector('.text-yellow-400')?.textContent || 'unknown'
      };
    });
    
    console.log(`\n[DEBUG - ${context}]`, JSON.stringify(pageInfo, null, 2));
  } catch (e) {
    console.log(`[DEBUG - ${context}] Failed to get page state:`, e.message);
  }
}

// Main test function
async function testMultiplayerFlow() {
  console.log('=== COMPREHENSIVE MULTIPLAYER TEST ===\n');
  console.log('Running in headless mode (WSL environment)');
  console.log('Testing at:', BASE_URL);
  console.log('Host:', HOST_CREDENTIALS.username);
  console.log('Guest:', GUEST_CREDENTIALS.username);
  console.log('---\n');

  // Browser options
  const browserOptions = {
    headless: true, // Running headless in WSL
    defaultViewport: null,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-web-security',
      '--disable-features=IsolateOrigins,site-per-process',
      '--window-size=1200,900'
    ]
  };

  // Platform-specific browser detection
  let executablePath;
  if (process.platform === 'win32') {
    const possiblePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
    ];
    
    for (const chromePath of possiblePaths) {
      try {
        await fs.access(chromePath);
        executablePath = chromePath;
        console.log(`Found browser at: ${chromePath}`);
        break;
      } catch (e) {
        // Continue checking
      }
    }
  } else {
    // For Linux/WSL, use system chromium
    const possiblePaths = [
      '/usr/bin/chromium-browser',
      '/usr/bin/chromium',
      '/usr/bin/google-chrome',
      '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe',
      '/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe'
    ];
    
    for (const chromePath of possiblePaths) {
      try {
        await fs.access(chromePath);
        executablePath = chromePath;
        console.log(`Found browser at: ${chromePath}`);
        break;
      } catch (e) {
        // Continue checking
      }
    }
  }

  if (executablePath) {
    browserOptions.executablePath = executablePath;
  } else {
    console.log('No system browser found, using Puppeteer bundled browser');
  }

  let hostBrowser, guestBrowser;
  let hostPage, guestPage;
  let testPassed = false;

  try {
    // === PHASE 1: Browser Launch ===
    testPhase = 'LAUNCH';
    console.log('\n=== PHASE 1: Launching Browsers ===');
    
    // Launch browsers
    console.log('Launching HOST browser...');
    hostBrowser = await puppeteer.launch(browserOptions);

    console.log('Launching GUEST browser...');
    guestBrowser = await puppeteer.launch(browserOptions);

    hostPage = await hostBrowser.newPage();
    guestPage = await guestBrowser.newPage();

    // Set viewport
    await hostPage.setViewport({ width: 600, height: 900 });
    await guestPage.setViewport({ width: 600, height: 900 });

    // Enable console logging
    hostPage.on('console', msg => {
      if (msg.type() === 'error') console.log(`[HOST ERROR]`, msg.text());
    });
    
    guestPage.on('console', msg => {
      if (msg.type() === 'error') console.log(`[GUEST ERROR]`, msg.text());
    });

    // === PHASE 2: Navigation ===
    testPhase = 'NAVIGATION';
    console.log('\n=== PHASE 2: Navigating to App ===');
    
    console.log('[HOST] Navigating...');
    await hostPage.goto(BASE_URL, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await takeScreenshot(hostPage, 'initial-load', 'host');
    
    console.log('[GUEST] Navigating...');
    await guestPage.goto(BASE_URL, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await takeScreenshot(guestPage, 'initial-load', 'guest');

    // Wait for app initialization
    console.log('\nWaiting for app to initialize...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // === PHASE 3: Authentication Flow ===
    testPhase = 'AUTH';
    console.log('\n=== PHASE 3: Authentication ===');

    // Function to handle complete authentication and multiplayer navigation
    async function authenticateAndNavigateToMultiplayer(page, credentials, playerName) {
      console.log(`\n[${playerName}] Starting authentication flow...`);
      
      // Step 1: Click Multiplayer Mode to trigger auth modal
      console.log(`[${playerName}] Looking for Multiplayer Mode button...`);
      await logPageState(page, `${playerName} - Before MP Click`);
      
      const mpButtonClicked = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const mpBtn = buttons.find(btn => 
          btn.textContent.includes('Multiplayer Mode') || 
          btn.textContent === 'Multiplayer Mode'
        );
        if (mpBtn && !mpBtn.disabled) {
          console.log('Found MP button:', mpBtn.textContent);
          mpBtn.click();
          return true;
        }
        console.log('Available buttons:', buttons.map(b => b.textContent));
        return false;
      });
      
      if (!mpButtonClicked) {
        throw new Error(`[${playerName}] Could not find Multiplayer Mode button`);
      }
      
      console.log(`[${playerName}] Clicked Multiplayer Mode button`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Step 2: Wait for auth modal
      console.log(`[${playerName}] Waiting for auth modal...`);
      await waitAndRetry(async () => {
        const hasAuthModal = await page.evaluate(() => {
          return !!document.querySelector('input[type="password"]');
        });
        return hasAuthModal;
      }, { description: `${playerName} auth modal`, timeout: 5000 });
      
      await takeScreenshot(page, 'auth-modal-open', playerName.toLowerCase());
      
      // Step 3: Fill credentials
      console.log(`[${playerName}] Filling credentials...`);
      
      // Clear and fill email
      const emailInput = await page.$('input[type="email"], input[type="text"]:not([type="password"])');
      await emailInput.click({ clickCount: 3 }); // Triple click to select all
      await emailInput.type(credentials.username);
      
      // Clear and fill password
      const passwordInput = await page.$('input[type="password"]');
      await passwordInput.click({ clickCount: 3 });
      await passwordInput.type(credentials.password);
      
      await takeScreenshot(page, 'credentials-filled', playerName.toLowerCase());
      
      // Step 4: Submit login
      console.log(`[${playerName}] Submitting login...`);
      await passwordInput.press('Enter');
      
      // Step 5: Wait for auth to complete (modal to disappear)
      console.log(`[${playerName}] Waiting for authentication to complete...`);
      await waitAndRetry(async () => {
        const modalGone = await page.evaluate(() => {
          return !document.querySelector('input[type="password"]');
        });
        return modalGone;
      }, { description: `${playerName} auth completion`, timeout: 10000 });
      
      console.log(`[${playerName}] Auth modal closed`);
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Step 6: Check if we're in multiplayer mode, if not, click again
      console.log(`[${playerName}] Checking game mode after auth...`);
      await logPageState(page, `${playerName} - After Auth`);
      
      const inMultiplayerMode = await page.evaluate(() => {
        // Check for multiplayer-specific elements
        const hasHostButton = Array.from(document.querySelectorAll('button'))
          .some(btn => btn.textContent.includes('Host Game'));
        const hasMultiplayerUI = document.body.textContent.includes('Available Games') || 
                                document.body.textContent.includes('Create a Room');
        const stillHasMultiplayerModeButton = Array.from(document.querySelectorAll('button'))
          .some(btn => btn.textContent === 'Multiplayer Mode');
        
        return hasHostButton || hasMultiplayerUI || !stillHasMultiplayerModeButton;
      });
      
      if (!inMultiplayerMode) {
        console.log(`[${playerName}] Not in multiplayer mode yet, clicking Multiplayer Mode again...`);
        
        const secondMpClick = await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const mpBtn = buttons.find(btn => btn.textContent === 'Multiplayer Mode');
          if (mpBtn && !mpBtn.disabled) {
            mpBtn.click();
            return true;
          }
          return false;
        });
        
        if (!secondMpClick) {
          throw new Error(`[${playerName}] Could not click Multiplayer Mode after auth`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      // Step 7: Verify we're in multiplayer lobby
      console.log(`[${playerName}] Verifying multiplayer lobby...`);
      await waitAndRetry(async () => {
        const inLobby = await page.evaluate(() => {
          const hasHostButton = Array.from(document.querySelectorAll('button'))
            .some(btn => btn.textContent.includes('Host Game'));
          const hasRoomsList = document.body.textContent.includes('Available Games') || 
                             document.body.textContent.includes('Create a Room');
          return hasHostButton || hasRoomsList;
        });
        return inLobby;
      }, { description: `${playerName} multiplayer lobby`, timeout: 10000 });
      
      console.log(`[${playerName}] ✓ Successfully in multiplayer lobby`);
      await takeScreenshot(page, 'in-multiplayer-lobby', playerName.toLowerCase());
      
      return true;
    }

    // Authenticate both players
    await authenticateAndNavigateToMultiplayer(hostPage, HOST_CREDENTIALS, 'HOST');
    await authenticateAndNavigateToMultiplayer(guestPage, GUEST_CREDENTIALS, 'GUEST');

    // === PHASE 4: Create Room ===
    testPhase = 'CREATE_ROOM';
    console.log('\n=== PHASE 4: Creating Room ===');
    
    console.log('[HOST] Creating room...');
    await logPageState(hostPage, 'HOST - Before Create Room');
    
    const roomCreated = await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const hostBtn = buttons.find(btn => 
        btn.textContent.includes('Host Game') || 
        btn.textContent === 'Host Game'
      );
      if (hostBtn && !hostBtn.disabled) {
        console.log('Clicking Host Game button');
        hostBtn.click();
        return true;
      }
      console.log('Host Game button not found or disabled');
      return false;
    });
    
    if (!roomCreated) {
      throw new Error('[HOST] Could not create room');
    }
    
    // Wait for room creation
    console.log('[HOST] Waiting for room to be created...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verify room was created and get room code
    const roomInfo = await hostPage.evaluate(() => {
      const codeElement = document.querySelector('.text-3xl, .text-2xl');
      const inRoom = document.body.textContent.includes('Room:') || 
                    document.body.textContent.includes('Ready');
      return {
        inRoom,
        roomCode: codeElement?.textContent?.trim(),
        pageText: document.body.innerText.substring(0, 200)
      };
    });
    
    console.log('[HOST] Room info:', roomInfo);
    await takeScreenshot(hostPage, 'room-created', 'host');
    
    if (!roomInfo.inRoom) {
      throw new Error('[HOST] Failed to create room');
    }

    // === PHASE 5: Join Room ===
    testPhase = 'JOIN_ROOM';
    console.log('\n=== PHASE 5: Joining Room ===');
    
    // Guest refreshes and joins
    console.log('[GUEST] Refreshing room list...');
    await guestPage.evaluate(() => {
      const refreshBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Refresh'));
      if (refreshBtn) refreshBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Look for the room
    console.log('[GUEST] Looking for host\'s room...');
    await logPageState(guestPage, 'GUEST - Before Join');
    
    // First, look for and click on the room to select it
    const roomFound = await guestPage.evaluate((hostUsername) => {
      // Look for room elements that contain the host's name
      const roomElements = Array.from(document.querySelectorAll('div, button, tr, td'));
      
      // Find elements that contain both the host's name and game-related text
      const roomCandidates = roomElements.filter(el => {
        const text = el.textContent || '';
        return text.includes(hostUsername) && 
               (text.includes('Game') || text.includes('Players') || text.includes('/'));
      });
      
      console.log('Found room candidates:', roomCandidates.map(el => el.textContent));
      
      // Try to find the most specific element (usually a row or container)
      let roomToClick = null;
      for (const candidate of roomCandidates) {
        // Prefer elements that look like table rows or room containers
        if (candidate.tagName === 'TR' || 
            candidate.className.includes('room') || 
            candidate.className.includes('game')) {
          roomToClick = candidate;
          break;
        }
      }
      
      // If no specific container found, use the first candidate
      if (!roomToClick && roomCandidates.length > 0) {
        roomToClick = roomCandidates[0];
      }
      
      if (roomToClick) {
        console.log('Clicking on room element:', roomToClick.textContent);
        roomToClick.click();
        return true;
      }
      
      console.log('No room found for host:', hostUsername);
      return false;
    }, HOST_CREDENTIALS.username);
    
    if (!roomFound) {
      console.log('[GUEST] Could not find room by clicking, will try Join button...');
    }
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Click Join button
    const joinClicked = await guestPage.evaluate(() => {
      const joinBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent === 'Join' || btn.textContent.includes('Join'));
      if (joinBtn && !joinBtn.disabled) {
        joinBtn.click();
        return true;
      }
      return false;
    });
    
    if (!joinClicked) {
      console.log('[GUEST] No Join button found');
    }
    
    // Verify guest joined
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if guest successfully joined the room
    console.log('[GUEST] Verifying room join...');
    const guestRoomStatus = await guestPage.evaluate(() => {
      const inRoom = document.body.textContent.includes('Ready') || 
                    document.body.textContent.includes('Room:') ||
                    document.body.textContent.includes('Leave Game');
      
      const readyButton = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Ready'));
      
      const playersList = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('(You)') || 
                     el.textContent.includes('Host') ||
                     el.textContent.includes('Players ('));
      
      return {
        inRoom,
        hasReadyButton: !!readyButton,
        playersListCount: playersList.length,
        pageText: document.body.innerText.substring(0, 500)
      };
    });
    
    console.log('[GUEST] Room status:', guestRoomStatus);
    
    if (!guestRoomStatus.inRoom) {
      await takeScreenshot(guestPage, 'failed-to-join', 'guest');
      throw new Error('[GUEST] Failed to join room - still in lobby');
    }
    
    console.log('[GUEST] ✓ Successfully joined room');
    await takeScreenshot(guestPage, 'joined-room', 'guest');

    // === PHASE 6: Ready Up ===
    testPhase = 'READY_UP';
    console.log('\n=== PHASE 6: Ready Up ===');
    
    // Both players ready up
    console.log('[HOST] Clicking Ready...');
    await hostPage.evaluate(() => {
      const readyBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Ready'));
      if (readyBtn) readyBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    console.log('[GUEST] Clicking Ready...');
    await guestPage.evaluate(() => {
      const readyBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Ready'));
      if (readyBtn) readyBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    await takeScreenshot(hostPage, 'both-ready', 'host');
    await takeScreenshot(guestPage, 'both-ready', 'guest');

    // === PHASE 7: Start Game ===
    testPhase = 'START_GAME';
    console.log('\n=== PHASE 7: Starting Game ===');
    
    // Wait a bit to ensure both players are marked as ready
    console.log('Waiting for ready status to sync...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if Start Game button is enabled
    console.log('[HOST] Checking Start Game button status...');
    const startButtonStatus = await hostPage.evaluate(() => {
      const startBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Start Game'));
      
      if (!startBtn) {
        return { found: false, enabled: false, text: 'Button not found' };
      }
      
      return {
        found: true,
        enabled: !startBtn.disabled,
        text: startBtn.textContent,
        className: startBtn.className,
        playerCount: document.body.textContent.match(/Players.*\((\d+)\/\d+\)/)?.[1] || '0',
        needMorePlayers: document.body.textContent.includes('Need') && document.body.textContent.includes('more player')
      };
    });
    
    console.log('[HOST] Start button status:', startButtonStatus);
    
    if (!startButtonStatus.found) {
      throw new Error('[HOST] Start Game button not found');
    }
    
    if (!startButtonStatus.enabled) {
      // If disabled, check why
      if (startButtonStatus.needMorePlayers) {
        throw new Error('[HOST] Start Game disabled - need more players. Current players: ' + startButtonStatus.playerCount);
      } else {
        throw new Error('[HOST] Start Game disabled - unknown reason');
      }
    }
    
    // Click the button
    console.log('[HOST] Clicking Start Game...');
    const gameStarted = await hostPage.evaluate(() => {
      const startBtn = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('Start Game'));
      if (startBtn) {
        startBtn.click();
        return true;
      }
      return false;
    });
    
    if (!gameStarted) {
      throw new Error('[HOST] Failed to click Start Game button');
    }
    
    console.log('[HOST] Start Game clicked, waiting for game to begin...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // === PHASE 8: Test Game Flow and Timing ===
    testPhase = 'GAME_FLOW';
    console.log('\n=== PHASE 8: Testing Game Flow and Timing ===');
    
    // Track timing for round advances
    let roundTimings = [];
    
    for (let round = 1; round <= 5; round++) {
      console.log(`\n--- Round ${round} ---`);
      const roundStartTime = Date.now();
      
      // Wait for question to appear
      await waitAndRetry(async () => {
        const hostHasQuestion = await hostPage.evaluate(() => {
          return document.querySelector('img[alt*="player"], img[alt*="Player"], .player-image') !== null;
        });
        const guestHasQuestion = await guestPage.evaluate(() => {
          return document.querySelector('img[alt*="player"], img[alt*="Player"], .player-image') !== null;
        });
        return hostHasQuestion && guestHasQuestion;
      }, { description: `round ${round} question`, timeout: 10000 });
      
      await takeScreenshot(hostPage, `round${round}-question`, 'host');
      await takeScreenshot(guestPage, `round${round}-question`, 'guest');
      
      // Test different answer timing scenarios
      if (round === 1) {
        // Round 1: Both answer quickly (should advance in ~5s)
        console.log('Round 1: Testing quick answers from both players...');
        
        // Host answers immediately
        await hostPage.evaluate(() => {
          const choices = Array.from(document.querySelectorAll('button')).filter(btn => 
            btn.className.includes('choice') || btn.textContent.match(/^[A-Z]\./)
          );
          if (choices.length > 0) choices[0].click();
        });
        
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Guest answers quickly
        await guestPage.evaluate(() => {
          const choices = Array.from(document.querySelectorAll('button')).filter(btn => 
            btn.className.includes('choice') || btn.textContent.match(/^[A-Z]\./)
          );
          if (choices.length > 0) choices[1].click();
        });
        
      } else if (round === 2) {
        // Round 2: Only host answers (should advance at 7s)
        console.log('Round 2: Testing single player answer...');
        
        await hostPage.evaluate(() => {
          const choices = Array.from(document.querySelectorAll('button')).filter(btn => 
            btn.className.includes('choice') || btn.textContent.match(/^[A-Z]\./)
          );
          if (choices.length > 0) choices[0].click();
        });
        
      } else if (round === 3) {
        // Round 3: No one answers (should advance at 7s)
        console.log('Round 3: Testing no answers (timeout)...');
        
      } else {
        // Rounds 4-5: Both answer with delay
        console.log(`Round ${round}: Both players answering with delay...`);
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        await hostPage.evaluate(() => {
          const choices = Array.from(document.querySelectorAll('button')).filter(btn => 
            btn.className.includes('choice') || btn.textContent.match(/^[A-Z]\./)
          );
          if (choices.length > 0) choices[Math.floor(Math.random() * choices.length)].click();
        });
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        await guestPage.evaluate(() => {
          const choices = Array.from(document.querySelectorAll('button')).filter(btn => 
            btn.className.includes('choice') || btn.textContent.match(/^[A-Z]\./)
          );
          if (choices.length > 0) choices[Math.floor(Math.random() * choices.length)].click();
        });
      }
      
      // Wait for round to advance and measure timing
      const advanceStartTime = Date.now();
      
      await waitAndRetry(async () => {
        // Check if we're in transition or new question
        const hostState = await hostPage.evaluate(() => {
          const hasTransition = document.body.textContent.includes('Next question in') ||
                              document.body.textContent.includes('Transition');
          const hasNewImage = document.querySelector('img[alt*="player"], img[alt*="Player"], .player-image');
          const currentImageSrc = hasNewImage ? hasNewImage.src : null;
          return { hasTransition, currentImageSrc };
        });
        
        // Store previous image src to detect changes
        if (!this.previousImageSrc) {
          this.previousImageSrc = hostState.currentImageSrc;
          return false;
        }
        
        // Round advanced if image changed or in transition
        return hostState.hasTransition || (hostState.currentImageSrc && hostState.currentImageSrc !== this.previousImageSrc);
      }, { description: `round ${round} advance`, timeout: 10000 });
      
      const roundDuration = Date.now() - roundStartTime;
      const advanceDuration = Date.now() - advanceStartTime;
      
      roundTimings.push({
        round,
        totalDuration: roundDuration,
        advanceTime: advanceDuration
      });
      
      console.log(`Round ${round} timing: Total ${roundDuration}ms, Advance took ${advanceDuration}ms`);
      
      // Wait for next round to fully start
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    // === PHASE 9: Verify Timing Rules ===
    testPhase = 'VERIFY_TIMING';
    console.log('\n=== PHASE 9: Verifying Timing Rules ===');
    
    console.log('\nRound Timing Summary:');
    roundTimings.forEach(timing => {
      console.log(`Round ${timing.round}: ${timing.totalDuration}ms total, ${timing.advanceTime}ms to advance`);
    });
    
    // Verify timing rules
    const timingIssues = [];
    
    // Round 1: Both answered quickly, should advance in ~5s (2s + 3s)
    if (roundTimings[0] && roundTimings[0].advanceTime > 6000) {
      timingIssues.push(`Round 1: Expected ~5s advance, got ${roundTimings[0].advanceTime}ms`);
    }
    
    // Rounds 2-3: Should advance at 7s max
    [1, 2].forEach(idx => {
      if (roundTimings[idx] && roundTimings[idx].advanceTime > 8000) {
        timingIssues.push(`Round ${idx + 1}: Expected 7s max, got ${roundTimings[idx].advanceTime}ms`);
      }
    });
    
    if (timingIssues.length > 0) {
      console.log('\n⚠️  Timing Issues Found:');
      timingIssues.forEach(issue => console.log(`  - ${issue}`));
    } else {
      console.log('\n✓ All timing rules verified successfully!');
    }
    
    // Check if game completed
    const gameFinished = await waitAndRetry(async () => {
      const hostFinished = await hostPage.evaluate(() => {
        return document.body.textContent.includes('Game Over') || 
               document.body.textContent.includes('Final Score') ||
               document.body.textContent.includes('Winner');
      });
      return hostFinished;
    }, { description: 'game completion', timeout: 30000, interval: 2000 });
    
    if (gameFinished) {
      console.log('\n✓ Game completed successfully!');
      await takeScreenshot(hostPage, 'game-complete', 'host');
      await takeScreenshot(guestPage, 'game-complete', 'guest');
    }
    
    testPassed = true;
    console.log('\n✅ MULTIPLAYER COMPREHENSIVE TEST PASSED!');
    
    if (timingIssues.length > 0) {
      console.log('\nNote: Some timing issues were detected. See summary above.');
    }
    
  } catch (error) {
    console.error(`\n❌ TEST FAILED IN PHASE ${testPhase}:`, error.message);
    console.error(error.stack);
    lastError = error;
    
    // Take error screenshots
    if (hostPage) {
      await takeScreenshot(hostPage, 'error-state', 'host');
      await logPageState(hostPage, 'HOST - Error State');
    }
    if (guestPage) {
      await takeScreenshot(guestPage, 'error-state', 'guest');
      await logPageState(guestPage, 'GUEST - Error State');
    }
  } finally {
    console.log('\n=== Test Complete ===');
    console.log(`Final Phase: ${testPhase}`);
    console.log(`Test Passed: ${testPassed}`);
    
    if (!testPassed) {
      console.log('\nKeeping browsers open for debugging...');
      console.log('Press Ctrl+C to close browsers and exit');
      
      // Keep process alive
      await new Promise(() => {});
    } else {
      console.log('\nClosing browsers...');
      if (hostBrowser) await hostBrowser.close();
      if (guestBrowser) await guestBrowser.close();
    }
  }
}

// Run the test
testMultiplayerFlow().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});