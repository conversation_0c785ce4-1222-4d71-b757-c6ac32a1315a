const puppeteer = require('puppeteer');

async function testFootballAnimation() {
  console.log('🏈 Testing Football Animation for Submitting Client');

  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    devtools: true // Open dev tools to see console logs
  });

  const page = await browser.newPage();

  // Log all console messages from the page
  page.on('console', msg => {
    const text = msg.text();
    console.log(`📟 [${msg.type()}] ${text}`);
  });

  page.on('pageerror', error => {
    console.error('🔴 Page Error:', error.message);
  });

  try {
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    
    console.log('⏳ Page loaded. Please manually:');
    console.log('1. Sign in or create account');
    console.log('2. Switch to Multiplayer');
    console.log('3. Create a room');
    console.log('4. Start the game');
    console.log('5. Submit an answer');
    console.log('\n👀 Watch the console for animation logs...');
    console.log('💡 Look for "FOOTBALL_DEBUG" or animation-related messages');
    console.log('🔍 Check if FootballFx components are rendered');
    
    // Keep the browser open and monitor
    await new Promise(() => {}); // Keep running indefinitely

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testFootballAnimation();