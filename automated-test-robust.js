/**
 * Robust Automated Multiplayer Test
 * Uses page.click() and better selectors for room joining
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Test configuration
const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'P1 only → 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1@1s, P2@3s → 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

// Create screenshots directory
const screenshotsDir = path.join(__dirname, 'test-screenshots-robust');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

let screenshotCounter = 0;
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

async function takeScreenshot(page, name) {
  try {
    const filename = `${String(screenshotCounter++).padStart(3, '0')}-${name}.png`;
    const filepath = path.join(screenshotsDir, filename);
    await page.screenshot({ path: filepath });
    console.log(`  📸 ${filename}`);
  } catch (e) {
    console.log(`  ⚠️  Screenshot failed: ${name}`);
  }
}

// Monitoring code
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0
  };
  
  console.log('🔍 Monitor active');
  
  let lastQuestion = null;
  setInterval(() => {
    const questionEl = document.querySelector('h2');
    const question = questionEl?.textContent || '';
    
    if (question.includes('NFL PLAYER') && question !== lastQuestion) {
      const now = Date.now();
      
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('🎮 Game started');
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({
          round: window.testData.currentRound++,
          time: elapsed
        });
        console.log('📍 Round ' + window.testData.currentRound + ' at ' + elapsed + 'ms');
      }
      
      lastQuestion = question;
    }
  }, 100);
  
  window.submitAt = function(ms) {
    return new Promise((resolve) => {
      const trySubmit = () => {
        if (!window.testData.gameStartTime) {
          setTimeout(trySubmit, 100);
          return;
        }
        
        const elapsed = Date.now() - window.testData.gameStartTime;
        const wait = ms - elapsed;
        
        const submit = () => {
          const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
            const text = b.textContent || '';
            return text.length > 0 && 
                   text.length < 30 &&
                   text.match(/^[A-Za-z\\s\\.\\-\\']+$/) &&
                   !['Ready', 'Start', 'Mode', 'Sign', 'Create', 'Join', 'Leave', 'Host', 'Back'].some(w => text.includes(w));
          });
          
          if (buttons.length >= 4) {
            const btn = buttons[Math.floor(Math.random() * 4)];
            console.log('✅ Submit: ' + btn.textContent);
            btn.click();
            resolve(true);
          } else {
            setTimeout(submit, 100);
          }
        };
        
        if (wait > 0) {
          setTimeout(submit, wait);
        } else {
          submit();
        }
      };
      
      trySubmit();
    });
  };
`;

async function runTest() {
  console.log('🎯 Robust Automated Multiplayer Test');
  console.log('===================================\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Launch browsers
    console.log('🚀 Launching browsers...');
    const opts = {
      headless: false,
      executablePath: '/usr/bin/chromium-browser',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--window-size=1200,900'],
      protocolTimeout: 120000
    };
    
    browser1 = await puppeteer.launch({ ...opts, args: [...opts.args, '--window-position=0,0'] });
    browser2 = await puppeteer.launch({ ...opts, args: [...opts.args, '--window-position=600,0'] });
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    page1.setDefaultTimeout(60000);
    page2.setDefaultTimeout(60000);
    
    // Load game
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2' }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2' })
    ]);
    await delay(3000);
    
    // Inject monitoring
    await page1.evaluate(monitoringCode);
    await page2.evaluate(monitoringCode);
    
    // Sign in
    console.log('\n🔐 Signing in...');
    
    // Player 1
    await page1.click('button:has-text("Login")');
    await delay(2000);
    await page1.type('input[placeholder*="Username"]', CONFIG.player1.email);
    await page1.type('input[placeholder*="Password"]', CONFIG.player1.password);
    await page1.click('button[type="submit"]');
    await delay(5000);
    console.log('  ✅ Player 1');
    
    // Player 2
    await page2.click('button:has-text("Login")');
    await delay(2000);
    await page2.type('input[placeholder*="Username"]', CONFIG.player2.email);
    await page2.type('input[placeholder*="Password"]', CONFIG.player2.password);
    await page2.click('button[type="submit"]');
    await delay(5000);
    console.log('  ✅ Player 2');
    
    // Multiplayer mode
    console.log('\n🎮 Multiplayer Mode...');
    await page1.click('button:has-text("Multiplayer Mode")');
    await delay(3000);
    await page2.click('button:has-text("Multiplayer Mode")');
    await delay(3000);
    
    // Create room
    console.log('\n🏠 Creating room...');
    await page1.click('button:has-text("Host Game")');
    await delay(3000);
    await takeScreenshot(page1, 'p1-room-created');
    
    // Join room - click fresh's Game in lobby
    console.log('  Joining room...');
    await takeScreenshot(page2, 'p2-before-join');
    
    // Try different selectors for the room
    try {
      // Method 1: Click on text containing room name
      await page2.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*'));
        const roomEl = elements.find(el => 
          el.textContent && 
          el.textContent.includes("fresh's Game") && 
          el.textContent.includes("Players:")
        );
        if (roomEl) {
          roomEl.click();
          return true;
        }
        
        // Method 2: Click parent of room name
        const nameEl = elements.find(el => el.textContent === "fresh's Game");
        if (nameEl && nameEl.parentElement) {
          nameEl.parentElement.click();
          return true;
        }
        
        throw new Error('Room not found');
      });
    } catch (e) {
      console.log('  ⚠️  First click method failed, trying alternative...');
      
      // Alternative: Use XPath
      const [roomElement] = await page2.$x("//*[contains(text(), \"fresh's Game\")]");
      if (roomElement) {
        await roomElement.click();
      }
    }
    
    await delay(2000);
    await takeScreenshot(page2, 'p2-room-clicked');
    
    // Click Join This Room button
    try {
      await page2.click('button:has-text("Join This Room")');
    } catch (e) {
      // Alternative methods
      await page2.evaluate(() => {
        const btns = Array.from(document.querySelectorAll('button'));
        const joinBtn = btns.find(b => b.textContent === 'Join This Room');
        if (joinBtn) joinBtn.click();
        else throw new Error('Join button not found');
      });
    }
    
    await delay(3000);
    await takeScreenshot(page2, 'p2-after-join');
    console.log('  ✅ Joined');
    
    // Ready up
    console.log('\n🎮 Starting game...');
    await page1.click('button:has-text("Ready")');
    await delay(1000);
    await page2.click('button:has-text("Ready")');
    await delay(2000);
    
    await takeScreenshot(page1, 'both-ready');
    
    await page1.click('button:has-text("Start Game")');
    await delay(3000);
    await takeScreenshot(page1, 'game-started');
    console.log('  ✅ Started!\n');
    
    // Run tests
    console.log('📊 Test Scenarios:');
    console.log('=================');
    
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const s = CONFIG.scenarios[i];
      console.log(`\n${i + 1}. ${s.name}`);
      
      const tasks = [];
      if (s.p1) tasks.push(page1.evaluate(`window.submitAt(${s.p1})`));
      if (s.p2) tasks.push(page2.evaluate(`window.submitAt(${s.p2})`));
      
      await Promise.all(tasks);
      await delay(8000);
      
      const timing = await page1.evaluate(i => {
        const changes = window.testData.roundChanges || [];
        return changes[i];
      }, i);
      
      if (timing) {
        const diff = Math.abs(timing.time - s.expected);
        const pass = diff <= 500;
        console.log(`   ${s.expected}ms → ${timing.time}ms (${pass ? '✅' : '❌'} ±${diff}ms)`);
        results.push({ ...s, actual: timing.time, diff, pass });
      } else {
        console.log('   ❌ No round change');
        results.push({ ...s, actual: null, pass: false });
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(40));
    console.log('📈 RESULTS: ' + 
      results.filter(r => r.pass).length + '/' + results.length + ' passed');
    console.log('='.repeat(40));
    
    console.log('\n✅ Complete! Ctrl+C to exit.\n');
    
    process.on('SIGINT', async () => {
      if (browser1) await browser1.close();
      if (browser2) await browser2.close();
      process.exit(0);
    });
    
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    await takeScreenshot(page1, 'error-p1').catch(() => {});
    await takeScreenshot(page2, 'error-p2').catch(() => {});
    
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    
    process.exit(1);
  }
}

runTest().catch(console.error);