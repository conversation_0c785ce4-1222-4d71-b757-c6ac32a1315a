# Server-Authoritative Transition System

## Overview

This document describes the new server-authoritative transition system that fixes all multiplayer round progression issues. The server is now the single source of truth for when games advance to the next question.

## Key Changes

### 1. Database Schema
- Added `transition_deadline` column to `game_rooms` table
- This timestamp determines exactly when the server will advance to the next question
- Indexed for efficient querying

### 2. Server-Side Logic

#### Start Game Handler
- Sets initial `transition_deadline` to 7 seconds from game start
- Initializes `question_started_at` for tracking

#### Submit Answer Handler
- When first player answers: Sets `transition_deadline` if not already set (7-second hard cap)
- When all players answer: Updates `transition_deadline` to minimum of (current deadline, now + 3 seconds)
- No longer pre-generates next questions or manages transitions directly

#### Transition Monitor (New)
- Edge function that checks all active games
- Advances games where `transition_deadline` has passed
- Generates next question on-demand
- Updates game state atomically

### 3. Client-Side Simplification

#### Removed
- `TRANSITION_MONITOR` useEffect - no client-side transition management
- `QUESTION_TRANSITION` useEffect - no client-side timing logic
- Complex timer management and state tracking
- Full state sync on tab visibility changes

#### Added
- Simple `TransitionCountdown` component for visual feedback only
- Lightweight connection verification on tab focus

## Transition Rules

1. **7-Second Hard Cap**: Every question has a maximum of 7 seconds
2. **3-Second Review**: When all players answer, show results for 3 seconds
3. **Server Authority**: Only the server can advance to the next question

## Deployment Steps

1. **Apply Database Migration**
   ```powershell
   cd supabase
   npx supabase db push
   ```

2. **Deploy Updated Edge Functions**
   ```powershell
   .\deploy-transition-system.ps1
   ```

3. **Set Up Monitoring** (Choose one):
   - Option A: Database trigger (recommended)
   - Option B: External cron job calling transition-monitor every second
   - Option C: pg_cron job (limited to 1-minute intervals)

## Benefits

1. **No More Race Conditions**: Single source of truth eliminates conflicts
2. **No More Tab Focus Issues**: Minimal sync on visibility changes
3. **Predictable Timing**: Server controls all transitions
4. **Simpler Client Code**: ~500 lines removed, much easier to maintain
5. **Better Performance**: Less client-server communication

## Testing Checklist

- [ ] Single player answers, waits 7 seconds → advances
- [ ] All players answer quickly → 3-second review → advances
- [ ] Tab switching doesn't cause state jumps
- [ ] Multiple games run independently
- [ ] Server restarts don't affect active games
- [ ] Network disconnects/reconnects handle gracefully

## Monitoring

Monitor the system with:
```sql
-- Check games needing transitions
SELECT id, transition_deadline, question_started_at
FROM game_rooms
WHERE status = 'active' 
  AND transition_deadline < NOW()
ORDER BY transition_deadline;

-- View recent transitions
SELECT id, current_round_number, question_started_at, transition_deadline
FROM game_rooms
WHERE status = 'active'
ORDER BY last_activity_timestamp DESC
LIMIT 10;
```

## Testing Tools

### Test Script
Run the test script to verify all scenarios:
```powershell
.\test-server-transitions.ps1
```

### Test Report
Document results in `SERVER_TRANSITION_TEST_REPORT.md`

### Real-time Monitoring
Monitor transitions in real-time:
```powershell
.\monitor-transitions.ps1 -RefreshSeconds 2
```

## Rollback Plan

If issues arise:
1. Comment out transition-monitor cron/trigger
2. Re-enable client-side transition logic (uncomment the useEffects)
3. Deploy previous versions of edge functions

The system is designed to be backwards compatible during the transition period.