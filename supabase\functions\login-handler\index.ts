// supabase/functions/login-handler/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

console.log('[Login Handler] File loaded by Deno runtime - Version V_FINAL_ATTEMPT_2024 (FULL AUTH LOGIC)');

interface LoginRequestBody {
  identifier?: string;
  password?: string;
}

function createCorsJsonResponse(body: object, status: number, intendedHeaders: HeadersInit): Response {
  console.log(`[Login Handler] Creating JSON response with status ${status}. Intended headers:`, JSON.stringify(intendedHeaders));
  console.log(`[Login Handler] Response body:`, JSON.stringify(body));
  return new Response(JSON.stringify(body), {
    headers: intendedHeaders, // Use the passed-in headers
    status: status,
  });
}

function createOptionsResponse(): Response {
    const headers = { ...corsHeaders }; // Ensure corsHeaders is used
    console.log('[<PERSON><PERSON> Handler] Responding to OPTIONS (CORS preflight). Headers:', JSON.stringify(headers));
    return new Response(null, { headers, status: 204 }); // 204 No Content is standard for preflight
}

serve(async (req: Request) => {
  const requestTimestamp = new Date().toISOString();
  const originHeader = req.headers.get('origin') || 'no-origin';
  console.log(`\n[Login Handler] ${requestTimestamp} - Received request: ${req.method} ${req.url}`);
  console.log(`[Login Handler] Origin header: ${originHeader}`);
  console.log(`[Login Handler] Content-Type header: ${req.headers.get('content-type') || 'not-set'}`);

  if (req.method === 'OPTIONS') {
    console.log('[Login Handler] Handling CORS preflight request');
    return createOptionsResponse();
  }

  if (req.method !== 'POST') {
    const errorBody = { error: `Method ${req.method} not allowed. Only POST is accepted.` };
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
    return createCorsJsonResponse(errorBody, 405, headers);
  }

  try {
    console.log('[Login Handler] Attempting to parse request body as JSON...');
    const requestBody: LoginRequestBody = await req.json();
    console.log('[Login Handler] Request body parsed:', requestBody);
    const { identifier, password } = requestBody;

    if (!identifier || !password) {
      console.warn('[Login Handler] Missing identifier or password.');
      const errorBody = { error: 'Username/Email and password are required.' };
      const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
      return createCorsJsonResponse(errorBody, 400, headers);
    }

    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !supabaseServiceRoleKey) {
      console.error('[Login Handler] CRITICAL: Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY env vars.');
      const errorBody = { error: 'Server configuration error (missing essential env vars).' };
      const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
      return createCorsJsonResponse(errorBody, 500, headers);
    }

    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);
    let emailToAuth = identifier;

    if (!identifier.includes('@')) {
      console.log(`[Login Handler] Identifier "${identifier}" is username. Looking up email (case-insensitive).`);
      
      // First, let's check how many profiles match this username
      const { data: allProfiles, error: countError } = await supabaseAdmin
        .from('profiles')
        .select('id, username')
        .ilike('username', identifier);
      
      console.log(`[Login Handler] Found ${allProfiles?.length || 0} profiles matching username "${identifier}":`, allProfiles);
      
      if (countError) {
        console.error(`[Login Handler] Error checking profiles for "${identifier}": ${countError.message}`);
        const errorBody = { error: 'Invalid username or password (profile lookup).' };
        const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
        return createCorsJsonResponse(errorBody, 401, headers);
      }
      
      if (!allProfiles || allProfiles.length === 0) {
        console.warn(`[Login Handler] No profiles found for username "${identifier}"`);
        const errorBody = { error: 'Invalid username or password (profile lookup).' };
        const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
        return createCorsJsonResponse(errorBody, 401, headers);
      }
      
      if (allProfiles.length > 1) {
        console.error(`[Login Handler] Multiple profiles found for username "${identifier}" - data integrity issue!`);
        const errorBody = { error: 'Invalid username or password (profile lookup).' };
        const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
        return createCorsJsonResponse(errorBody, 401, headers);
      }
      
      // Now we know there's exactly one profile, use it
      const profileData = allProfiles[0];
      console.log(`[Login Handler] Using profile ID: ${profileData.id} for username "${identifier}"`);
      
      if (!profileData) {
        console.warn(`[Login Handler] Profile data is null for "${identifier}"`);
        const errorBody = { error: 'Invalid username or password (profile lookup).' };
        const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
        return createCorsJsonResponse(errorBody, 401, headers);
      }
      
      const { data: authUser, error: authUserError } = await supabaseAdmin.auth.admin.getUserById(profileData.id);

      if (authUserError || !authUser?.user?.email) {
        console.error(`[Login Handler] Email lookup for user ID "${profileData.id}" failed: ${authUserError?.message || 'User/email not in auth schema.'}`);
        const errorBody = { error: 'Invalid username or password (auth email lookup).' };
        const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
        return createCorsJsonResponse(errorBody, 401, headers);
      }
      emailToAuth = authUser.user.email;
      console.log(`[Login Handler] Found email "${emailToAuth}" for username "${identifier}".`);
    } else {
      console.log(`[Login Handler] Identifier "${identifier}" treated as email.`);
    }

    console.log(`[Login Handler] Attempting signInWithPassword for email: "${emailToAuth}"`);
    const { data: signInData, error: signInError } = await supabaseAdmin.auth.signInWithPassword({
      email: emailToAuth,
      password: password,
    });

    if (signInError) {
      console.warn(`[Login Handler] signInWithPassword error for "${emailToAuth}": ${signInError.message}`);
      const errorBody = { error: 'Invalid credentials provided.' }; // Generic for client
      const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
      return createCorsJsonResponse(errorBody, 401, headers);
    }

    if (!signInData.session || !signInData.user) {
      console.error('[Login Handler] signInWithPassword success but no session/user data returned.');
      const errorBody = { error: 'Login process failed unexpectedly. Please try again.' };
      const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
      return createCorsJsonResponse(errorBody, 500, headers);
    }
    
    console.log(`[Login Handler] User "${signInData.user.email}" logged in successfully. Session created.`);
    const successBody = { session: signInData.session, user: signInData.user };
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
    return createCorsJsonResponse(successBody, 200, headers);

  } catch (e: unknown) {
    console.error('[Login Handler] Unhandled exception in POST path:', e);
    let errorMessage = 'An unexpected server error occurred during login.';
    let statusCode = 500;
    if (e instanceof Error) {
        errorMessage = e.message;
        if (e instanceof SyntaxError && e.message.toLowerCase().includes("json")) {
            console.warn('[Login Handler] Error parsing request body as JSON in POST.');
            errorMessage = 'Invalid request body: Expected JSON.';
            statusCode = 400;
        }
    }
    const errorBody = { error: errorMessage };
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };
    return createCorsJsonResponse(errorBody, statusCode, headers);
  }
});

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/login-handler' \
    --header 'Content-Type: application/json' \
    --data '{"identifier":"<EMAIL>","password":"password123"}'

*/ 