const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://xmyxuvuimebjltnaamox.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function checkPlayersData() {
  console.log('Checking players_data table...\n');

  try {
    // Try to query the players_data table
    const { data, error, count } = await supabase
      .from('players_data')
      .select('*', { count: 'exact', head: false })
      .limit(5);

    if (error) {
      console.error('Error querying players_data:', error);
      console.log('\nPossible issues:');
      console.log('1. Table does not exist');
      console.log('2. RLS policies are blocking access');
      console.log('3. No permissions to read the table');
      return;
    }

    console.log(`Total records in players_data: ${count || 'unknown'}`);
    console.log('\nSample data (first 5 records):');
    
    if (data && data.length > 0) {
      data.forEach((player, index) => {
        console.log(`\n${index + 1}. ${player.player_name || 'Unknown'}`);
        console.log(`   Team: ${player.team_name || 'Unknown'}`);
        console.log(`   Image Path: ${player.local_image_path || 'No image'}`);
        console.log(`   Position: ${player.position || 'Unknown'}`);
      });
    } else {
      console.log('No data found in players_data table!');
    }

    // Check for players with images
    const { data: playersWithImages, error: imageError, count: imageCount } = await supabase
      .from('players_data')
      .select('id', { count: 'exact', head: true })
      .not('local_image_path', 'is', null);

    if (!imageError) {
      console.log(`\nPlayers with images: ${imageCount || 0}`);
    }

  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

checkPlayersData();