import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { SupabaseClient } from '@supabase/supabase-js';

// Mock Supabase
vi.mock('@/lib/supabaseClient', () => ({
  supabase: {
    auth: {
      getUser: vi.fn(),
    },
    functions: {
      invoke: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      insert: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    })),
    channel: vi.fn(() => ({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
    })),
  },
}));

describe('Multiplayer Next Question Progression', () => {
  let mockSupabase: any;
  let mockChannel: any;
  let realtimeCallbacks: Record<string, Function> = {};

  beforeEach(() => {
    vi.useFakeTimers();
    mockSupabase = (await import('@/lib/supabaseClient')).supabase;
    
    // Setup channel mock to capture realtime callbacks
    mockChannel = {
      on: vi.fn((event: string, filter: any, callback: Function) => {
        if (typeof filter === 'function') {
          realtimeCallbacks[event] = filter;
        } else {
          realtimeCallbacks[`${event}:${filter.event}`] = callback;
        }
        return mockChannel;
      }),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
    };
    
    mockSupabase.channel.mockReturnValue(mockChannel);
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    realtimeCallbacks = {};
  });

  describe('Automatic Progression After All Players Submit', () => {
    it('should automatically progress to next question 2.3 seconds after all players submit', async () => {
      // Setup: Mock room with 3 players
      const mockRoom = {
        id: 'room-123',
        host_id: 'user-1',
        status: 'in_progress',
        current_question_data: {
          questionId: 'q1',
          correctPlayerId: 123,
          choices: [
            { name: 'Player A', isCorrect: true },
            { name: 'Player B', isCorrect: false },
            { name: 'Player C', isCorrect: false },
            { name: 'Player D', isCorrect: false },
          ]
        },
        current_round_answers: []
      };

      const mockPlayers = [
        { user_id: 'user-1', display_name: 'Host' },
        { user_id: 'user-2', display_name: 'Player 2' },
        { user_id: 'user-3', display_name: 'Player 3' },
      ];

      // Mock next-question-handler
      const nextQuestionHandler = vi.fn().mockResolvedValue({ data: { success: true } });
      mockSupabase.functions.invoke.mockImplementation((fn: string) => {
        if (fn === 'next-question-handler') return nextQuestionHandler();
        return Promise.resolve({ data: null });
      });

      // Simulate all players submitting answers
      const allPlayersAnswered = [
        { userId: 'user-1', questionId: 'q1', choiceName: 'Player A', timestamp: Date.now() },
        { userId: 'user-2', questionId: 'q1', choiceName: 'Player B', timestamp: Date.now() },
        { userId: 'user-3', questionId: 'q1', choiceName: 'Player C', timestamp: Date.now() },
      ];

      // Update room state with all answers
      mockRoom.current_round_answers = allPlayersAnswered;

      // Trigger realtime update
      act(() => {
        realtimeCallbacks['postgres_changes:UPDATE']?.({
          new: mockRoom,
          old: mockRoom,
          eventType: 'UPDATE'
        });
      });

      // Fast-forward 2.2 seconds - should NOT trigger next question yet
      act(() => {
        vi.advanceTimersByTime(2200);
      });
      
      expect(nextQuestionHandler).not.toHaveBeenCalled();

      // Fast-forward to 2.3 seconds - should trigger next question
      act(() => {
        vi.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(nextQuestionHandler).toHaveBeenCalledTimes(1);
      });
    });

    it('should NOT auto-progress if not all players have submitted', async () => {
      const mockRoom = {
        id: 'room-123',
        host_id: 'user-1',
        status: 'in_progress',
        current_question_data: {
          questionId: 'q1',
          correctPlayerId: 123,
          choices: [
            { name: 'Player A', isCorrect: true },
            { name: 'Player B', isCorrect: false },
          ]
        },
        current_round_answers: [
          { userId: 'user-1', questionId: 'q1', choiceName: 'Player A' },
          { userId: 'user-2', questionId: 'q1', choiceName: 'Player B' },
          // user-3 has not submitted
        ]
      };

      const mockPlayers = [
        { user_id: 'user-1', display_name: 'Host' },
        { user_id: 'user-2', display_name: 'Player 2' },
        { user_id: 'user-3', display_name: 'Player 3' },
      ];

      const nextQuestionHandler = vi.fn();
      mockSupabase.functions.invoke.mockImplementation((fn: string) => {
        if (fn === 'next-question-handler') return nextQuestionHandler();
        return Promise.resolve({ data: null });
      });

      // Wait more than 2.3 seconds
      act(() => {
        vi.advanceTimersByTime(5000);
      });

      expect(nextQuestionHandler).not.toHaveBeenCalled();
    });

    it('should cancel auto-progression if host manually advances', async () => {
      const mockRoom = {
        id: 'room-123',
        host_id: 'user-1',
        status: 'in_progress',
        current_question_data: { questionId: 'q1' },
        current_round_answers: [
          { userId: 'user-1', questionId: 'q1' },
          { userId: 'user-2', questionId: 'q1' },
        ]
      };

      const nextQuestionHandler = vi.fn().mockResolvedValue({ data: { success: true } });
      mockSupabase.functions.invoke.mockImplementation((fn: string) => {
        if (fn === 'next-question-handler') return nextQuestionHandler();
        return Promise.resolve({ data: null });
      });

      // All players submit
      act(() => {
        realtimeCallbacks['postgres_changes:UPDATE']?.({
          new: mockRoom,
          eventType: 'UPDATE'
        });
      });

      // Host manually advances after 1 second
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      // Manual advance by host
      await act(async () => {
        await nextQuestionHandler();
      });

      expect(nextQuestionHandler).toHaveBeenCalledTimes(1);

      // Wait for the auto-advance timer (should be cancelled)
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      // Should still only be called once (manual call)
      expect(nextQuestionHandler).toHaveBeenCalledTimes(1);
    });

    it('should handle tab focus/blur correctly during auto-progression', async () => {
      const mockRoom = {
        id: 'room-123',
        host_id: 'user-1',
        status: 'in_progress',
        current_round_answers: [
          { userId: 'user-1', questionId: 'q1' },
          { userId: 'user-2', questionId: 'q1' },
        ]
      };

      const nextQuestionHandler = vi.fn().mockResolvedValue({ data: { success: true } });
      mockSupabase.functions.invoke.mockImplementation((fn: string) => {
        if (fn === 'next-question-handler') return nextQuestionHandler();
        return Promise.resolve({ data: null });
      });

      // All players submit
      act(() => {
        realtimeCallbacks['postgres_changes:UPDATE']?.({
          new: mockRoom,
          eventType: 'UPDATE'
        });
      });

      // Tab loses focus after 1 second
      act(() => {
        vi.advanceTimersByTime(1000);
        Object.defineProperty(document, 'visibilityState', {
          value: 'hidden',
          writable: true
        });
        document.dispatchEvent(new Event('visibilitychange'));
      });

      // Tab regains focus after another 1.5 seconds (total 2.5s)
      act(() => {
        vi.advanceTimersByTime(1500);
        Object.defineProperty(document, 'visibilityState', {
          value: 'visible',
          writable: true
        });
        document.dispatchEvent(new Event('visibilitychange'));
      });

      // Should still trigger auto-progression
      await waitFor(() => {
        expect(nextQuestionHandler).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Single Player Mode Compatibility', () => {
    it('should not affect single player timed mode auto-progression', async () => {
      // This test ensures our multiplayer changes don't break single player
      const { useGameStore } = await import('@/stores/gameStore');
      
      const { result } = renderHook(() => useGameStore());
      
      // Start timed mode
      act(() => {
        result.current.setMounted(true);
        result.current.setGameMode('timed');
      });

      // Wait for countdown
      act(() => {
        vi.advanceTimersByTime(4000); // 3s countdown + 1s
      });

      // Submit answer
      const currentQuestion = result.current.currentQuestion;
      const correctChoice = currentQuestion?.choices.find(c => c.isCorrect);
      
      act(() => {
        if (correctChoice) {
          result.current.submitAnswer(correctChoice.name);
        }
      });

      // Should auto-advance after 150ms
      act(() => {
        vi.advanceTimersByTime(150);
      });

      expect(result.current.totalAsked).toBe(2); // Should have moved to next question
    });
  });

  describe('Edge Cases', () => {
    it('should handle rapid answer submissions correctly', async () => {
      const mockRoom = {
        id: 'room-123',
        host_id: 'user-1',
        status: 'in_progress',
        current_question_data: { questionId: 'q1' },
        current_round_answers: []
      };

      const nextQuestionHandler = vi.fn().mockResolvedValue({ data: { success: true } });
      mockSupabase.functions.invoke.mockImplementation((fn: string) => {
        if (fn === 'next-question-handler') return nextQuestionHandler();
        return Promise.resolve({ data: null });
      });

      // All players submit within 100ms
      act(() => {
        mockRoom.current_round_answers.push({ userId: 'user-1', questionId: 'q1' });
        realtimeCallbacks['postgres_changes:UPDATE']?.({ new: mockRoom });
        
        vi.advanceTimersByTime(50);
        
        mockRoom.current_round_answers.push({ userId: 'user-2', questionId: 'q1' });
        realtimeCallbacks['postgres_changes:UPDATE']?.({ new: mockRoom });
        
        vi.advanceTimersByTime(50);
        
        mockRoom.current_round_answers.push({ userId: 'user-3', questionId: 'q1' });
        realtimeCallbacks['postgres_changes:UPDATE']?.({ new: mockRoom });
      });

      // Should start timer from last submission
      act(() => {
        vi.advanceTimersByTime(2200);
      });

      expect(nextQuestionHandler).not.toHaveBeenCalled();

      act(() => {
        vi.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(nextQuestionHandler).toHaveBeenCalledTimes(1);
      });
    });

    it('should handle player disconnection during auto-progression', async () => {
      const mockRoom = {
        id: 'room-123',
        host_id: 'user-1',
        status: 'in_progress',
        current_round_answers: [
          { userId: 'user-1', questionId: 'q1' },
          { userId: 'user-2', questionId: 'q1' },
          { userId: 'user-3', questionId: 'q1' },
        ]
      };

      const mockPlayers = [
        { user_id: 'user-1', display_name: 'Host', is_connected: true },
        { user_id: 'user-2', display_name: 'Player 2', is_connected: true },
        { user_id: 'user-3', display_name: 'Player 3', is_connected: false }, // disconnected
      ];

      const nextQuestionHandler = vi.fn().mockResolvedValue({ data: { success: true } });
      mockSupabase.functions.invoke.mockImplementation((fn: string) => {
        if (fn === 'next-question-handler') return nextQuestionHandler();
        return Promise.resolve({ data: null });
      });

      // Should still auto-progress even with disconnected player
      act(() => {
        vi.advanceTimersByTime(2300);
      });

      await waitFor(() => {
        expect(nextQuestionHandler).toHaveBeenCalledTimes(1);
      });
    });
  });
});