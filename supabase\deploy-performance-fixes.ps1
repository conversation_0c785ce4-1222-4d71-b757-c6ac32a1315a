# Deploy Supabase performance fixes migration
# This script applies the RLS performance optimizations and index improvements

Write-Host "Deploying Supabase performance fixes..." -ForegroundColor Green
Write-Host "Running migration: 20250625000000_fix_rls_performance_issues.sql" -ForegroundColor Cyan

# Push the migration to the remote database
supabase db push

Write-Host "`nMigration deployed successfully!" -ForegroundColor Green

# Run the linter again to verify fixes
Write-Host "`nRunning Supabase linter to verify fixes..." -ForegroundColor Cyan
supabase db lint

Write-Host "`nDeployment complete!" -ForegroundColor Green
Write-Host "The following performance improvements have been applied:" -ForegroundColor Yellow
Write-Host "  - Fixed auth RLS initialization plan issues (replaced auth.uid() with (select auth.uid()))" -ForegroundColor White
Write-Host "  - Consolidated multiple permissive policies on game_players table" -ForegroundColor White
Write-Host "  - Removed duplicate indexes on game_players and profiles tables" -ForegroundColor White
Write-Host "  - Added covering indexes for all unindexed foreign keys" -ForegroundColor White
Write-Host "  - Documented unused indexes for future review" -ForegroundColor White