// Test Edge Function using Supabase client
const { createClient } = require('@supabase/supabase-js');

async function testWithSupabaseClient() {
  console.log('=== Testing Edge Function with Supabase Client ===\n');
  
  const SUPABASE_URL = 'https://xmyxuvuimebjltnaamox.supabase.co';
  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjcyOTA2NjUsImV4cCI6MjA0Mjg2NjY2NX0.uI_gQzFkKlKMTb7hQItE6OtivgjLGByTNRi9Wvv18B4';
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  try {
    // First, sign in
    console.log('Signing in...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: 'fresh', // Try username as email field
      password: 'test123'
    });
    
    if (authError) {
      console.log('Auth error (trying username as email):', authError.message);
      
      // If that fails, we might need to get the actual email
      // For now, let's just test the Edge Function with anon key
      console.log('\nTesting Edge Function without user auth...');
    } else {
      console.log('Signed in successfully:', authData.user?.id);
    }
    
    // Try to invoke the Edge Function
    console.log('\nInvoking Edge Function...');
    const { data, error } = await supabase.functions.invoke('start-game-handler', {
      body: { roomId: 'test-room-123' }
    });
    
    if (error) {
      console.error('\nEdge Function Error:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
    } else {
      console.log('\nEdge Function Response:', data);
    }
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Check if @supabase/supabase-js is installed
try {
  require.resolve('@supabase/supabase-js');
  testWithSupabaseClient();
} catch(e) {
  console.log('Installing @supabase/supabase-js...');
  const { execSync } = require('child_process');
  execSync('npm install @supabase/supabase-js', { stdio: 'inherit' });
  console.log('\nPlease run the script again.');
}