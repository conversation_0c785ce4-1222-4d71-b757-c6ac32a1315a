const puppeteer = require('puppeteer-core');

async function testPuppeteer() {
  console.log('Testing Puppeteer setup...\n');
  
  try {
    // Try to launch with minimal configuration
    console.log('Attempting to launch browser...');
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe'
    });
    
    console.log('✓ Browser launched successfully!');
    
    const page = await browser.newPage();
    console.log('✓ New page created');
    
    await page.goto('https://example.com');
    console.log('✓ Navigation successful');
    
    const title = await page.title();
    console.log(`✓ Page title: ${title}`);
    
    await browser.close();
    console.log('✓ Browser closed\n');
    
    console.log('Puppeteer is working correctly!');
    
  } catch (error) {
    console.error('✗ Error:', error.message);
    console.log('\nTroubleshooting steps:');
    console.log('1. Make sure you have Chrome/Chromium installed');
    console.log('2. Try setting PUPPETEER_EXECUTABLE_PATH environment variable');
    console.log('3. On WSL, you might need to install Chrome in WSL:');
    console.log('   wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -');
    console.log('   sudo sh -c \'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list\'');
    console.log('   sudo apt update && sudo apt install google-chrome-stable');
  }
}

testPuppeteer();