import { useState, useCallback } from 'react';

export type LoadingStateKey = string;

interface LoadingState {
  [key: LoadingStateKey]: boolean;
}

interface LoadingStateActions {
  setLoading: (key: LoadingStateKey, isLoading: boolean) => void;
  startLoading: (key: LoadingStateKey) => void;
  stopLoading: (key: LoadingStateKey) => void;
  isLoading: (key: LoadingStateKey) => boolean;
  isAnyLoading: () => boolean;
  reset: () => void;
}

/**
 * Custom hook to manage multiple loading states in a component
 * Reduces the number of individual useState calls for loading states
 */
export function useLoadingState(initialKeys: LoadingStateKey[] = []): [LoadingState, LoadingStateActions] {
  const [loadingState, setLoadingState] = useState<LoadingState>(() => {
    const initial: LoadingState = {};
    initialKeys.forEach(key => {
      initial[key] = false;
    });
    return initial;
  });

  const setLoading = useCallback((key: LoadingStateKey, isLoading: boolean) => {
    setLoadingState(prev => ({
      ...prev,
      [key]: isLoading
    }));
  }, []);

  const startLoading = useCallback((key: LoadingStateKey) => {
    setLoading(key, true);
  }, [setLoading]);

  const stopLoading = useCallback((key: LoadingStateKey) => {
    setLoading(key, false);
  }, [setLoading]);

  const isLoading = useCallback((key: LoadingStateKey) => {
    return loadingState[key] || false;
  }, [loadingState]);

  const isAnyLoading = useCallback(() => {
    return Object.values(loadingState).some(isLoading => isLoading);
  }, [loadingState]);

  const reset = useCallback(() => {
    setLoadingState(prev => {
      const newState: LoadingState = {};
      Object.keys(prev).forEach(key => {
        newState[key] = false;
      });
      return newState;
    });
  }, []);

  const actions: LoadingStateActions = {
    setLoading,
    startLoading,
    stopLoading,
    isLoading,
    isAnyLoading,
    reset
  };

  return [loadingState, actions];
}