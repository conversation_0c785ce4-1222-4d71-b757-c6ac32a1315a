'use client';

import { useState, useEffect, FormEvent, useRef, forwardRef, useImperativeHandle } from 'react';
import { supabase } from '@/lib/supabaseClient';
import { Session, User } from '@supabase/supabase-js';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;

// Define the ref interface for imperative access
export interface AuthModalRef {
  openAuthModal: (context?: 'multiplayer' | 'general') => void;
}

interface AuthModalProps {
  openAuthModal?: boolean; // Optional prop to control opening from outside
  onModalStateChange?: (isOpen: boolean) => void; // Callback for modal state changes
  onAuthSuccess?: (context: 'multiplayer' | 'general') => void; // Callback for successful authentication
  context?: 'multiplayer' | 'general'; // Context for why the modal was opened
}

const AuthModal = forwardRef<AuthModalRef, AuthModalProps>(({ openAuthModal, onModalStateChange, onAuthSuccess, context = 'general' }, ref) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  
  // For Sign Up
  const [newUsername, setNewUsername] = useState('');
  const [newEmail, setNewEmail] = useState('');
  const [newPassword, setNewPassword] = useState('');

  // For Sign In
  const [identifier, setIdentifier] = useState(''); // Will hold username or email for login
  const [password, setPassword] = useState(''); // Password for login

  const [isSignUp, setIsSignUp] = useState(false); // Default to Sign In form
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const messageTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [showAuthForm, setShowAuthForm] = useState(false);
  const [modalContext, setModalContext] = useState<'multiplayer' | 'general'>('general');
  const authFormRef = useRef<HTMLDivElement>(null);

  // CRITICAL FIX: Track previous session state to differentiate initial sign-in from token refresh
  const previousSessionRef = useRef<Session | null>(null);

  // Expose imperative methods via ref
  useImperativeHandle(ref, () => ({
    openAuthModal: (modalContext: 'multiplayer' | 'general' = 'general') => {
      console.log('[AuthModal] Opening auth modal programmatically with context:', modalContext);
      setShowAuthForm(true);
      setModalContext(modalContext);
      setMessage(''); // Clear any previous messages
      setIsSignUp(false); // Default to sign in
      // Clear form fields
      setNewUsername(''); setNewEmail(''); setNewPassword('');
      setIdentifier(''); setPassword('');
    }
  }));

  // Handle external openAuthModal prop
  useEffect(() => {
    if (openAuthModal === true) {
      console.log('[AuthModal] Opening auth modal via prop with context:', context);
      setShowAuthForm(true);
      setModalContext(context);
      setMessage(''); // Clear any previous messages
      setIsSignUp(false); // Default to sign in
      // Clear form fields
      setNewUsername(''); setNewEmail(''); setNewPassword('');
      setIdentifier(''); setPassword('');
    }
  }, [openAuthModal, context]);

  // Notify parent of modal state changes
  useEffect(() => {
    if (onModalStateChange) {
      onModalStateChange(showAuthForm);
    }
  }, [showAuthForm, onModalStateChange]);

  // Click outside to close logic
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (authFormRef.current && !authFormRef.current.contains(event.target as Node)) {
        const toggleButton = document.getElementById('auth-toggle-button');
        if (toggleButton && !toggleButton.contains(event.target as Node)) {
          setShowAuthForm(false);
        } else if (!toggleButton && showAuthForm) {
          setShowAuthForm(false);
        }
      }
    }
    if (showAuthForm) document.addEventListener('mousedown', handleClickOutside);
    else document.removeEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showAuthForm]);

  useEffect(() => {
    console.log('[AuthModal] Setting up auth listener with tab focus race condition fix');
    
    // Initialize the previous session ref and local state
    supabase.auth.getSession().then(({ data: { session } }) => {
      previousSessionRef.current = session;
      setSession(session);
      setUser(session?.user ?? null);
    });
    
    const subscription = supabase.auth.onAuthStateChange(
      (_event, currentSession) => {
        console.log(`[AuthModal] Auth state change: ${_event}`, currentSession?.user?.id);
        
        // Always update local state
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
        
        if (_event === 'SIGNED_IN' && currentSession) {
          console.log(`[AuthModal] Authentication successful - event: ${_event}, context: ${modalContext}`);
          
          // CRITICAL LOGIC: Only trigger onAuthSuccess for TRUE initial sign-ins
          // A true sign-in is when the previous session was null and the new one is not
          if (!previousSessionRef.current && currentSession) {
            console.log(`[AuthModal] ✅ DETECTED TRUE INITIAL SIGN-IN: Calling onAuthSuccess callback with context: ${modalContext}`);
            setShowAuthForm(false); // Close auth form
            
            // Call onAuthSuccess callback if provided
            if (onAuthSuccess) {
              onAuthSuccess(modalContext);
            }
          } else {
            console.log(`[AuthModal] 🔄 DETECTED TOKEN REFRESH: Previous session existed, this is just a refresh on tab focus - NOT calling onAuthSuccess`);
            console.log(`[AuthModal] Previous session existed:`, !!previousSessionRef.current);
            console.log(`[AuthModal] Current session exists:`, !!currentSession);
            // This is a token refresh, not a new login - do not trigger the callback
          }
        }
        
        // Update the ref to the current session for the next auth event
        previousSessionRef.current = currentSession;
        
        if (_event !== 'INITIAL_SESSION' && _event !== 'USER_UPDATED') {
          setMessage(''); // Clear messages on auth changes, except for initial load or user updates
        }
      }
    );
    return () => {
      subscription.data.subscription.unsubscribe();
      if (messageTimeoutRef.current) clearTimeout(messageTimeoutRef.current);
    };
  }, [modalContext, onAuthSuccess]);

  useEffect(() => {
    if (message) {
      if (messageTimeoutRef.current) clearTimeout(messageTimeoutRef.current);
      messageTimeoutRef.current = setTimeout(() => setMessage(''), 5000);
    }
    return () => { if (messageTimeoutRef.current) clearTimeout(messageTimeoutRef.current); };
  }, [message]);

  const handleAuth = async (e: FormEvent) => {
    console.log('[AuthModal] ========== handleAuth TRIGGERED ==========');
    console.log('[AuthModal] Form event:', e);
    console.log('[AuthModal] Current isSignUp mode:', isSignUp);
    console.log('[AuthModal] Current loading state:', loading);

    e.preventDefault();

    // 💡 THE CRITICAL GUARD CLAUSE - Prevent React Strict Mode double-invocation
    if (loading) {
      console.warn('[AuthModal] Auth action already in progress. Ignoring duplicate call.');
      return;
    }

    setLoading(true);
    setMessage('');

    if (isSignUp) {
      console.log('[AuthModal] ===== SIGN UP FLOW =====');
      console.log('[AuthModal] Sign-up form values:', { 
        newUsername: newUsername, 
        newEmail: newEmail, 
        newPasswordLength: newPassword.length 
      });
      
      // Sign Up Logic (Client-Side via Supabase JS SDK)
      if (!newUsername.trim()) { 
        console.log('[AuthModal] VALIDATION FAILED: Username is required');
        setMessage('Error: Username is required.'); 
        setLoading(false); 
        return; 
      }
      if (!newEmail.trim()) { 
        console.log('[AuthModal] VALIDATION FAILED: Email is required');
        setMessage('Error: Email is required.'); 
        setLoading(false); 
        return; 
      }
      if (!newPassword) { 
        console.log('[AuthModal] VALIDATION FAILED: Password is required');
        setMessage('Error: Password is required.'); 
        setLoading(false); 
        return; 
      }

      try {
        console.log('[AuthModal] Calling supabase.auth.signUp...');
        setLoading(true); // Moved setLoading inside try, before await
        setMessage('');   // Clear previous messages

        const { data, error } = await supabase.auth.signUp({
          email: newEmail.trim(),
          password: newPassword,
          options: {
            data: { 
              username: newUsername.trim() // THIS IS THE CRUCIAL PART
            }
          }
        });

        console.log('[AuthModal] signUp response:', { data, error });
        setLoading(false); // Set loading false after await

        if (error) {
          console.error('[AuthModal] Sign-up error:', error);
          setMessage(`Error: ${error.message}`);
          return; // Exit if there's an error
        }

        // Check if email confirmation is pending (data.user exists but data.session is null)
        if (data.user && data.session === null) {
          console.log('[AuthModal] Email confirmation pending for user:', data.user.id);
          setMessage('Account created! Please check your email to verify your account before logging in.');
          // Optionally, clear form fields here
          setNewUsername('');
          setNewEmail('');
          setNewPassword('');
          // setIsSignUp(false); // Optionally switch back to sign-in form
        } else if (data.session && data.user) {
          // This case means auto-login after sign-up (if email confirmation is off or auto-confirmed)
          console.log('[AuthModal] Auto-login after sign-up successful, user:', data.user.id);
          setMessage('Signed up & logged in successfully!');
          // Session is automatically handled by onAuthStateChange listener, modal should close
        } else {
          // Should not happen if error is null, but as a fallback
          console.log('[AuthModal] Unexpected sign-up state - no error but unexpected data:', { data });
          setMessage('Sign-up initiated. Please check for a confirmation email if required.');
        }

      } catch (error) { // Catch any unexpected errors during the process
        const err = error instanceof Error ? error : new Error(String(error));
        setLoading(false);
        console.error('[AuthModal] CATCH BLOCK: Unexpected sign-up exception:', err);
        setMessage(`Error: ${err.message || 'An unexpected error occurred during sign-up.'}`);
      }
      // setLoading(false); // Already handled within try/catch
    } else {
      console.log('[AuthModal] ===== SIGN IN FLOW =====');
      console.log('[AuthModal] Sign-in form values:', { 
        identifier: identifier, 
        passwordLength: password.length 
      });
      
      // Sign In Logic (Server-Side via Edge Function)
      if (!identifier.trim()) { 
        console.log('[AuthModal] VALIDATION FAILED: Username or Email is required');
        setMessage('Error: Username or Email is required.'); 
        setLoading(false); 
        return; 
      }
      if (!password) { 
        console.log('[AuthModal] VALIDATION FAILED: Password is required');
        setMessage('Error: Password is required.'); 
        setLoading(false); 
        return; 
      }

      if (!NEXT_PUBLIC_SUPABASE_URL) {
        console.error('[AuthModal] CONFIGURATION ERROR: NEXT_PUBLIC_SUPABASE_URL is not defined');
        setMessage('Error: Client configuration missing for login.');
        setLoading(false);
        console.error("NEXT_PUBLIC_SUPABASE_URL is not defined.");
        return;
      }

      console.log('[AuthModal] NEXT_PUBLIC_SUPABASE_URL:', NEXT_PUBLIC_SUPABASE_URL);
      console.log('[AuthModal] Preparing to call Edge Function...');

      try {
        const loginPayload = { identifier: identifier.trim(), password };
        console.log('[AuthModal] Edge Function payload:', { 
          identifier: loginPayload.identifier, 
          passwordLength: loginPayload.password.length 
        });
        
        const edgeFunctionUrl = `${NEXT_PUBLIC_SUPABASE_URL}/functions/v1/login-handler`;
        console.log('[AuthModal] Calling Edge Function URL:', edgeFunctionUrl);

        const response = await fetch(edgeFunctionUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(loginPayload),
        });

        console.log('[AuthModal] Edge Function response status:', response.status);
        console.log('[AuthModal] Edge Function response ok:', response.ok);

        const result = await response.json();
        console.log('[AuthModal] Edge Function response body:', result);

        if (!response.ok) {
          console.error('[AuthModal] Edge Function returned error status:', response.status);
          throw new Error(result.error || `Login failed (Status: ${response.status})`);
        }
        
        if (result.user && result.session) {
          console.log('[AuthModal] Login successful, setting session...');
          console.log('[AuthModal] Session data:', { 
            user_id: result.user.id, 
            access_token_length: result.session.access_token?.length || 0,
            refresh_token_length: result.session.refresh_token?.length || 0
          });
          
          const { error: sessionError } = await supabase.auth.setSession({
            access_token: result.session.access_token,
            refresh_token: result.session.refresh_token,
          });
          
          if (sessionError) {
            console.error('[AuthModal] Client-side error setting session:', sessionError);
            throw new Error("Failed to establish session locally after login.");
          }
          console.log('[AuthModal] Session set successfully on client');
          
          // CRITICAL FIX: Trigger auth state change manually to ensure main component updates
          // Wait a brief moment for the session to be fully set, then trigger auth change event
          setTimeout(async () => {
            console.log('[AuthModal] Manually triggering auth state synchronization...');
            try {
              // Get the current session to verify it's set
              const { data: sessionCheck } = await supabase.auth.getSession();
              console.log('[AuthModal] Session verification after login:', { 
                hasSession: !!sessionCheck.session,
                userId: sessionCheck.session?.user?.id 
              });
              
              // Force trigger the onAuthStateChange event by refreshing the session
              if (sessionCheck.session) {
                await supabase.auth.setSession({
                  access_token: sessionCheck.session.access_token,
                  refresh_token: sessionCheck.session.refresh_token,
                });
                console.log('[AuthModal] Successfully re-triggered auth state change');
              }
            } catch (e) {
              console.error('[AuthModal] Error in manual auth state sync:', e);
              // Fallback: force page refresh if manual sync fails
              window.location.reload();
            }
          }, 100);
          
          setMessage('Signed in successfully!');
        } else {
          console.error('[AuthModal] Invalid response from Edge Function - missing user or session:', result);
          throw new Error(result.error || 'Login failed: Invalid response from server.');
        }
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error('[AuthModal] CATCH BLOCK: Login attempt failed:', err);
        setMessage(`Error: ${err.message}`);
      } finally {
        console.log('[AuthModal] Login attempt finished, setting loading to false');
        setLoading(false);
      }
    }
    console.log('[AuthModal] ========== handleAuth COMPLETED ==========');
  };

  const handleSignOut = async () => {
    console.log('[AuthModal] Enhanced sign-out process initiated', {
      hasUser: !!user,
      userId: user?.id,
      timestamp: new Date().toISOString()
    });

    setLoading(true);
    setMessage('');
    
    try {
      // Step 1: Check if user might be in a multiplayer room and attempt to leave
      // Note: This is a best-effort approach since AuthModal doesn't have direct access to room state
      if (user?.id) {
        console.log('[AuthModal] User is authenticated, checking for active rooms before sign-out');
        
        try {
          // Get all rooms where user might be a player
          const { data: userRooms, error: roomsError } = await supabase
            .from('game_players')
            .select('room_id, is_connected')
            .eq('user_id', user.id)
            .eq('is_connected', true);

          if (roomsError) {
            console.warn('[AuthModal] Could not check user rooms before sign-out:', roomsError);
          } else if (userRooms && userRooms.length > 0) {
            console.log('[AuthModal] User is connected to rooms, attempting to leave:', userRooms);
            
            // Attempt to leave each room
            for (const roomConnection of userRooms) {
              try {
                console.log(`[AuthModal] Calling leave-room-handler for room: ${roomConnection.room_id}`);
                const { error: leaveError } = await supabase.functions.invoke('leave-room-handler', {
                  body: { roomId: roomConnection.room_id },
                });
                
                if (leaveError) {
                  console.warn(`[AuthModal] Failed to leave room ${roomConnection.room_id}:`, leaveError);
                } else {
                  console.log(`[AuthModal] Successfully left room ${roomConnection.room_id}`);
                }
              } catch (roomLeaveError) {
                console.warn(`[AuthModal] Exception leaving room ${roomConnection.room_id}:`, roomLeaveError);
              }
            }
            
            // Give a brief moment for the leave operations to complete
            await new Promise(resolve => setTimeout(resolve, 300));
          } else {
            console.log('[AuthModal] User is not connected to any rooms, proceeding with sign-out');
          }
        } catch (roomCheckError) {
          console.warn('[AuthModal] Exception checking for user rooms before sign-out:', roomCheckError);
          // Continue with sign-out even if room check fails
        }
      }
      
      // Step 2: Clean up all Supabase Realtime channels BEFORE signing out
      // This prevents "WebSocket is closed" errors when new user signs in
      console.log('[AuthModal] Cleaning up all Realtime channels before sign-out');
      try {
        await supabase.removeAllChannels();
        console.log('[AuthModal] Successfully removed all Realtime channels');
      } catch (channelCleanupError) {
        console.warn('[AuthModal] Error cleaning up Realtime channels:', channelCleanupError);
        // Continue with sign-out even if channel cleanup fails
      }
      
      // Step 3: Perform Supabase sign-out
      console.log('[AuthModal] Proceeding with Supabase sign-out');
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('[AuthModal] Error during Supabase sign-out:', error);
        throw error;
      }
      
      console.log('[AuthModal] Enhanced sign-out completed successfully');
      setMessage('Signed out successfully.');
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      console.error('[AuthModal] Error during enhanced sign-out:', err);
      setMessage(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const toggleAuthMode = () => {
    setIsSignUp(!isSignUp);
    setMessage('');
    // Clear all input fields when toggling mode
    setNewUsername(''); setNewEmail(''); setNewPassword('');
    setIdentifier(''); setPassword('');
  };

  // Display for logged-in user
  if (session) {
    return (
      <div className="text-white p-2 bg-gray-900 bg-opacity-90 rounded-md shadow-lg">
        <div className="flex items-center justify-between space-x-2">
          <span className="text-xs sm:text-sm truncate">
            {user?.user_metadata?.username ? `User: ${user.user_metadata.username}` : (user?.email || 'Logged In')}
          </span>
          <Button 
            onClick={handleSignOut} 
            disabled={loading} 
            className="px-2 py-1 sm:px-3 sm:py-1.5 text-xs sm:text-sm bg-red-700 hover:bg-red-600 whitespace-nowrap"
          >
            {loading ? '...' : 'Sign Out'}
          </Button>
        </div>
        {message && (
          <p className="mt-1 text-xs text-center" style={{ color: message.startsWith('Error:') ? '#fca5a5' : '#86efac' }}>
            {message}
          </p>
        )}
      </div>
    );
  }

  // Auth form (Login/Sign Up)
  return (
    <div className="relative">
      <Button
        id="auth-toggle-button"
        onClick={() => {
          setShowAuthForm(!showAuthForm);
          if (showAuthForm) { // If was open, now closing
            setMessage(''); // Clear message
            // Reset fields and mode to default (Sign In)
            setIsSignUp(false);
            setModalContext('general'); // Reset context when closing
            setNewUsername(''); setNewEmail(''); setNewPassword('');
            setIdentifier(''); setPassword('');
          }
        }}
        className="px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm bg-slate-800 hover:bg-slate-700 text-white rounded-md shadow-lg"
      >
        {showAuthForm ? 'Close Auth' : 'Login / Sign Up'}
      </Button>

      {showAuthForm && (
        <div
          ref={authFormRef}
          className={cn(
            "absolute top-full right-0 z-20",
            "p-4 bg-white text-gray-800 rounded-lg shadow-2xl w-64 sm:w-72 border border-gray-300",
            "mt-2"
          )}
        >
          <h2 className="text-lg sm:text-xl font-bold mb-3 text-center text-gray-900">
            {isSignUp ? 'Create Account' : (modalContext === 'multiplayer' ? 'Sign in to play multiplayer' : 'Sign In')}
          </h2>
          <form onSubmit={handleAuth} className="space-y-3">
            {isSignUp ? (
              <>
                <div>
                  <input id="new-username-input" type="text" placeholder="Username" value={newUsername} onChange={(e) => {
                    console.log('[AuthModal] Username input changed:', e.target.value);
                    setNewUsername(e.target.value);
                  }}
                    className="block w-full px-2 py-1.5 bg-gray-50 border border-gray-300 rounded-md text-gray-900 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm placeholder-gray-400" />
                </div>
                <div>
                  <input id="new-email-input" type="email" placeholder="Email" value={newEmail} onChange={(e) => {
                    console.log('[AuthModal] Email input changed:', e.target.value);
                    setNewEmail(e.target.value);
                  }}
                    className="block w-full px-2 py-1.5 bg-gray-50 border border-gray-300 rounded-md text-gray-900 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm placeholder-gray-400" />
                </div>
                <div>
                  <input id="new-password-input-auth" type="password" placeholder="Password" value={newPassword} onChange={(e) => {
                    console.log('[AuthModal] Password input changed, length:', e.target.value.length);
                    setNewPassword(e.target.value);
                  }}
                    className="block w-full px-2 py-1.5 bg-gray-50 border border-gray-300 rounded-md text-gray-900 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm placeholder-gray-400" />
                </div>
              </>
            ) : ( // Sign In Form
              <>
                <div>
                  <input id="identifier-input" type="text" placeholder="Username or Email" value={identifier} onChange={(e) => {
                    console.log('[AuthModal] Identifier input changed:', e.target.value);
                    setIdentifier(e.target.value);
                  }}
                    className="block w-full px-2 py-1.5 bg-gray-50 border border-gray-300 rounded-md text-gray-900 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm placeholder-gray-400" />
                </div>
                <div>
                  <input id="password-input-auth" type="password" placeholder="Password" value={password} onChange={(e) => {
                    console.log('[AuthModal] Password input changed, length:', e.target.value.length);
                    setPassword(e.target.value);
                  }}
                    className="block w-full px-2 py-1.5 bg-gray-50 border border-gray-300 rounded-md text-gray-900 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm placeholder-gray-400" />
                </div>
              </>
            )}
            <Button 
              type="submit" 
              disabled={loading} 
              onClick={(e) => {
                console.log('[AuthModal] ===== SUBMIT BUTTON CLICKED =====');
                console.log('[AuthModal] Button click event:', e);
                console.log('[AuthModal] Form values at button click:', isSignUp ? 
                  { newUsername, newEmail, newPasswordLength: newPassword.length } : 
                  { identifier, passwordLength: password.length }
                );
                console.log('[AuthModal] Loading state:', loading);
                console.log('[AuthModal] IsSignUp mode:', isSignUp);
                // Don't preventDefault here - let the form submission happen naturally
              }}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-1.5 text-sm"
            >
              {loading ? 'Processing...' : (isSignUp ? 'Sign Up' : (modalContext === 'multiplayer' ? 'Sign in to play multiplayer' : 'Sign In'))}
            </Button>
          </form>
          <button
            onClick={toggleAuthMode}
            className="mt-3 text-xs text-blue-600 hover:text-blue-700 hover:underline w-full text-center"
          >
            {isSignUp ? (modalContext === 'multiplayer' ? 'Already have an account? Sign in to play multiplayer' : 'Already have an account? Sign In') : "Don't have an account? Sign Up"}
          </button>
          {message && (
            <p className="mt-2 text-xs text-center" style={{ color: message.startsWith('Error:') ? '#ef4444' : '#10b981' }}>
              {message}
            </p>
          )}
        </div>
      )}
    </div>
  );
});

AuthModal.displayName = 'AuthModal';

export default AuthModal; 