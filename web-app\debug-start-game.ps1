# Debug script for start-game-handler edge function

$supabaseUrl = "https://ktlncsmzllxmtykzrlcc.supabase.co"
$supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0bG5jc216bGx4bXR5a3pybGNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzAzMjAxNDIsImV4cCI6MjA0NTg5NjE0Mn0.o9W-NODzqE8gzZaWGP6lPmUJpdzf54aBMRdZdiqxiSI"

Write-Host "Starting debug of start-game-handler..." -ForegroundColor Cyan
Write-Host ""

# Step 1: Authenticate as test user
Write-Host "1. Authenticating as test user fresh..." -ForegroundColor Yellow

$authBody = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $authResponse = Invoke-RestMethod -Uri "$supabaseUrl/auth/v1/token?grant_type=password" `
        -Method POST `
        -ContentType "application/json" `
        -Headers @{
            "apikey" = $supabaseAnonKey
        } `
        -Body $authBody

    Write-Host "✓ Authentication successful" -ForegroundColor Green
    Write-Host "User ID: $($authResponse.user.id)"
    Write-Host "Access Token: $($authResponse.access_token.Substring(0, 20))..."
    Write-Host ""

    $accessToken = $authResponse.access_token
    $userId = $authResponse.user.id
}
catch {
    Write-Host "Authentication failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    exit 1
}

# Step 2: Find a waiting room hosted by this user
Write-Host "2. Looking for waiting rooms hosted by fresh..." -ForegroundColor Yellow

$headers = @{
    "apikey" = $supabaseAnonKey
    "Authorization" = "Bearer $accessToken"
    "Prefer" = "return=representation"
}

try {
    $roomsUrl = "$supabaseUrl/rest/v1/game_rooms?host_id=eq.$userId&status=eq.waiting&order=created_at.desc&limit=1"
    $rooms = Invoke-RestMethod -Uri $roomsUrl -Headers $headers -Method GET

    if ($rooms.Count -eq 0) {
        Write-Host "No waiting rooms found. Creating one..." -ForegroundColor Yellow
        
        $roomCode = -join ((65..90) | Get-Random -Count 6 | ForEach-Object {[char]$_})
        $newRoom = @{
            host_id = $userId
            status = "waiting"
            max_players = 5
            game_type = "multiplayer"
            room_code = $roomCode
        } | ConvertTo-Json

        $room = Invoke-RestMethod -Uri "$supabaseUrl/rest/v1/game_rooms" `
            -Method POST `
            -Headers $headers `
            -ContentType "application/json" `
            -Body $newRoom

        Write-Host "✓ Created new room: $($room.room_code)" -ForegroundColor Green
    }
    else {
        $room = $rooms[0]
        Write-Host "✓ Found existing room: $($room.room_code)" -ForegroundColor Green
    }

    Write-Host ""
    Write-Host "Room details:"
    Write-Host "- Room ID: $($room.id)"
    Write-Host "- Room Code: $($room.room_code)"
    Write-Host "- Status: $($room.status)"
    Write-Host "- Created at: $($room.created_at)"
}
catch {
    Write-Host "Failed to fetch/create room:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    exit 1
}

# Step 3: Call start-game-handler edge function
Write-Host ""
Write-Host "3. Calling start-game-handler edge function..." -ForegroundColor Yellow

$functionBody = @{
    roomId = $room.id
} | ConvertTo-Json

Write-Host "Request body: $functionBody"

try {
    $functionHeaders = @{
        "Authorization" = "Bearer $accessToken"
        "Content-Type" = "application/json"
    }

    $response = Invoke-WebRequest -Uri "$supabaseUrl/functions/v1/start-game-handler" `
        -Method POST `
        -Headers $functionHeaders `
        -Body $functionBody `
        -UseBasicParsing

    Write-Host ""
    Write-Host "4. Response details:" -ForegroundColor Yellow
    Write-Host "- Status: $($response.StatusCode) $($response.StatusDescription)"
    Write-Host "- Headers:"
    $response.Headers | ForEach-Object { Write-Host "  $_" }
    
    Write-Host ""
    Write-Host "- Raw response body:" -ForegroundColor Yellow
    Write-Host $response.Content

    # Try to parse as JSON
    try {
        $responseJson = $response.Content | ConvertFrom-Json
        Write-Host ""
        Write-Host "- Parsed JSON response:" -ForegroundColor Yellow
        Write-Host ($responseJson | ConvertTo-Json -Depth 10)

        if ($responseJson.error) {
            Write-Host ""
            Write-Host "⚠️  Error response received:" -ForegroundColor Red
            Write-Host "- Error: $($responseJson.error)"
            Write-Host "- Details: $($responseJson.details)"
        }
        elseif ($responseJson.success -eq $false) {
            Write-Host ""
            Write-Host "⚠️  Unsuccessful response:" -ForegroundColor Red
            Write-Host "- Message: $($responseJson.message)"
        }
        else {
            Write-Host ""
            Write-Host "✓ Success response received" -ForegroundColor Green
            Write-Host "- Success: $($responseJson.success)"
            Write-Host "- Message: $($responseJson.message)"
            if ($responseJson.gameState) {
                Write-Host "- Game state included: $($responseJson.gameState.PSObject.Properties.Name -join ', ')"
            }
        }
    }
    catch {
        Write-Host ""
        Write-Host "⚠️  Response is not valid JSON" -ForegroundColor Red
    }
}
catch {
    Write-Host "Function call failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}

# Step 5: Check if room status was updated
Write-Host ""
Write-Host "5. Checking if room status was updated..." -ForegroundColor Yellow

try {
    $checkUrl = "$supabaseUrl/rest/v1/game_rooms?id=eq.$($room.id)&select=*"
    $updatedRoom = (Invoke-RestMethod -Uri $checkUrl -Headers $headers -Method GET)[0]

    Write-Host "Room status after function call: $($updatedRoom.status)"
    Write-Host "Game state: $(if ($updatedRoom.game_state) { 'Present' } else { 'Not present' })"
    if ($updatedRoom.game_state) {
        Write-Host "Game state keys: $($updatedRoom.game_state.PSObject.Properties.Name -join ', ')"
    }
}
catch {
    Write-Host "Failed to check room status:" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

Write-Host ""
Write-Host "Debug complete!" -ForegroundColor Green