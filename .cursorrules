# Project Rules for Recognition-Combine

## Environment File Existence
- `web-app/.env.local` file DOES exist, stop assuming it does not. It is hidden for security purposes.
- `supabase/.env` file DOES exist, stop assuming it does not. It is hidden for security purposes.

## PowerShell Syntax
- Always use PowerShell appropriate syntax
- Don't use `&&` operators; use commands separated by lines
- Don't use the `cat` operator

## Development Protocol
- Always log anything and everything you can
- Begin with writing tests, then the code, and then ensure the code passes these tests
- Follow this iterative protocol to write the most robust and correct code possible
- Log extensively and comprehensively, so that debugging is straightforward

## CSS PROTECTION RULES - CRITICAL
**⚠️ THESE RULES MUST BE ENFORCED AT ALL TIMES - CSS IS WORKING, PRESERVE IT!**

### CRITICAL CSS PROTECTION - NEXT.JS INTEGRATION
**HIGHEST PRIORITY:** Ensure global CSS import is present and correct in the root layout file.
- **File:** `web-app/src/app/layout.tsx` (App Router - CURRENT SETUP)
- **Required Import:** `import "./globals.css";` (EXACT PATH - line 2)
- **Location:** Must be imported at the top level of the layout file
- **Action:** If missing, re-add immediately. If commented out, uncomment.
- **Warning:** This is the #1 cause of ALL styles disappearing in Next.js
- **Never:** Remove, comment, or modify this import path
- **Verification:** Check that line 2 contains exactly: `import "./globals.css";`

### CRITICAL CSS PROTECTION - TAILWIND CONFIG
**HIGH PRIORITY:** Verify Tailwind 'content' paths in `web-app/tailwind.config.ts` are comprehensive and correct.
- **Current Paths (VERIFIED WORKING):**
  ```typescript
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',      // Legacy Pages Router support
    './src/components/**/*.{js,ts,jsx,tsx,mdx}', // All components
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',        // App Router (CURRENT)
  ]
  ```
- **Action:** If paths seem incorrect, too restrictive, or missing directories, flag for review
- **Never:** Remove or narrow these paths without careful consideration
- **Warning:** Incorrect content paths cause Tailwind to purge styles, leading to missing CSS

### NEVER MODIFY WITHOUT BACKUP:
- `web-app/src/app/globals.css` - PRIMARY CSS FILE (6.9KB, 232 lines)
- `web-app/tailwind.config.ts` - Tailwind configuration
- `web-app/postcss.config.js` and `web-app/postcss.config.mjs` - PostCSS configs
- `web-app/components.json` - ShadCN UI configuration

### CRITICAL CSS IMPORTS - NEVER CHANGE:
```css
@import "tailwindcss";           // CRITICAL - Tailwind base
@import "tw-animate-css";        // CRITICAL - Animation library
```
- NEVER remove these imports
- NEVER change the import order
- ALWAYS keep quotes around import names

### CSS CUSTOM PROPERTIES - NEVER REMOVE:
- NEVER remove or rename CSS custom properties (--color-*, --radius-*)
- NEVER change OKLCH color format to hex/rgb/hsl
- ALWAYS maintain both light and dark theme variables
- PRESERVE the exact variable naming convention

### CSS LAYERS - PRESERVE STRUCTURE:
```css
@layer base { ... }              // CRITICAL - Base styles
@layer utilities { ... }         // CRITICAL - Utility classes
```
- PRESERVE layer structure
- MAINTAIN base layer button resets
- KEEP utility layer custom classes

### BACKGROUND SYSTEM - NEVER REMOVE:
- NEVER remove the `/images/field_background.png` reference
- ALWAYS maintain fallback `bg-gray-900`
- PRESERVE all background properties (cover, center, no-repeat, fixed)

### ANIMATIONS - NEVER MODIFY:
- NEVER modify `@keyframes footballBounce`
- PRESERVE all animation delays and timing
- MAINTAIN spinner component structure

### PROTECTED CSS CLASSES:
- `.jumbotron-title` (Main title styling)
- `.game-loading-container` (Game loading UI)
- `.loading-text` (Loading text styling)
- `.football-spinner` (Animation component)

### DEPENDENCY VERSION LOCK:
- **@tailwindcss/postcss**: `4.1.6` (CRITICAL - DO NOT CHANGE)
- **tailwindcss**: `3.4.1` (CRITICAL - DO NOT CHANGE)
- **postcss**: `8.4.35` (CRITICAL - DO NOT CHANGE)
- **autoprefixer**: `10.4.18` (CRITICAL - DO NOT CHANGE)
- **tw-animate-css**: `1.2.9` (CRITICAL - DO NOT CHANGE)
- **lightningcss**: `1.24.1` (CRITICAL - DO NOT CHANGE)
- **class-variance-authority**: `0.7.1` (CRITICAL - DO NOT CHANGE)
- **clsx**: `2.1.1` (CRITICAL - DO NOT CHANGE)
- **tailwind-merge**: `3.2.0` (CRITICAL - DO NOT CHANGE)

### CSS EMERGENCY RESTORATION:
If CSS breaks, immediately execute:
1. `git checkout HEAD -- web-app/src/app/globals.css`
2. `cd web-app`
3. `npm ci`
4. `npm run build`

### CSS MODIFICATION PROTOCOL:
Before ANY CSS changes:
1. BACKUP current CSS files
2. Test current build works
3. Document changes planned
4. Verify in development environment
5. After changes: Test build, verify animations, check light/dark modes

**🔒 REMEMBER: This CSS configuration is WORKING. Any changes risk breaking the entire styling system!**

## Agent Tips for Effective Development

### 1. Deployment Command Nuances
When a standard command fails, before trying many variations, explicitly state: "The standard deployment command `supabase functions deploy <function-name>` seems to be having an issue. I will now try with common flags like `--no-verify-jwt` or check the `config.toml` for project-specific settings. If these fail, I will assume manual deployment or a different CLI interaction is needed by the user." This manages expectations and shows a structured troubleshooting approach.

### 2. File Path Specificity (especially for `cd`)
When executing `cd` commands, use the most complete path known from previous successful interactions or from explicit user guidance. If unsure, ask for the full path to the target directory. Example: Use `cd C:\Projects\recognition-combine\supabase` instead of generic `cd supabase`.

### 3. State Variable Existence Checks
Before using a `set<StateVariable>` function, always perform a quick search in the relevant file(s) for the corresponding `useState(<stateVariable>)` declaration. If not found, add it, inferring its type if possible, or ask for clarification.

### 4. Schema First, Then Client Logic (for DB-dependent fields)
When client-side logic needs to interact with a database field (e.g., setting `is_host`), prioritize confirming the field's existence and location in the database schema *before* writing extensive client code that depends on it. If the schema is unknown, state the assumption and proceed, noting it as a point for verification.

### 5. Error Message Interpretation (Client-Side 409 vs. Edge Function Logic)
When a client receives an HTTP error (like 409 Conflict) from a Supabase REST endpoint or Edge Function, first analyze the error message and code (e.g., "23505" for unique violation) to understand the database-level cause. Then, trace back to the server-side logic (Edge Function or RLS) that might be leading to this database state or preventing the desired operation.

## Project Structure Notes
- Main project directory: `C:\Projects\recognition-combine`
- Supabase functions directory: `C:\Projects\recognition-combine\supabase\functions`
- Web application directory: `C:\Projects\recognition-combine\web-app`
- Use deployment script: `deploy-leave-handler.ps1` for Edge Function deployments when standard CLI fails

## CSS Protection Documentation
- **Complete CSS protection rules documented in:** `CSS_PROTECTION_RULES_ENHANCED.md`
- **Generated and maintained by:** `generate-css-protection-rules-enhanced.ps1` script
- **Reference this file for all CSS configuration details and procedures**

## Enhanced CSS Protection Features
- **Automated CSS Verification:** Build-time checks for CSS output integrity
- **Next.js Integration Protection:** Specific guards for layout.tsx CSS imports
- **Tailwind Content Path Validation:** Ensures Tailwind purging works correctly
- **Browser Diagnostic Guides:** Step-by-step debugging for missing styles
- **Emergency Restoration Protocols:** Multi-level recovery procedures
- **Performance Monitoring:** CSS health checks and benchmarks 