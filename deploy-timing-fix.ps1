# Deploy script for transition timing fix
# This script deploys the updated edge functions with question_started_at tracking

Write-Host "=== Deploying Transition Timing Fix ===" -ForegroundColor Green
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "./supabase/functions")) {
    Write-Host "Error: Must run from project root directory" -ForegroundColor Red
    exit 1
}

Write-Host "Step 1: Applying database migration..." -ForegroundColor Yellow
cd supabase
npx supabase db push
cd ..

Write-Host ""
Write-Host "Step 2: Deploying updated edge functions..." -ForegroundColor Yellow

# Deploy start-game-handler with question_started_at
Write-Host "  - Deploying start-game-handler..." -ForegroundColor Cyan
cd supabase
.\deploy-start-game-handler.ps1
cd ..

# Deploy next-question-handler with question_started_at  
Write-Host "  - Deploying next-question-handler..." -ForegroundColor Cyan
cd supabase
.\deploy-next-question-handler.ps1
cd ..

Write-Host ""
Write-Host "=== Deployment Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "Summary of changes:" -ForegroundColor Yellow
Write-Host "1. Added question_started_at field to track when each question begins"
Write-Host "2. Updated edge functions to set question_started_at"
Write-Host "3. Client now implements:"
Write-Host "   - 7-second hard cap from question start"
Write-Host "   - 3-second delay after all players answer"
Write-Host "   - Transitions use whichever fires first"
Write-Host ""
Write-Host "Test the timing by:" -ForegroundColor Cyan
Write-Host "1. Starting a multiplayer game"
Write-Host "2. Let a question sit for 7 seconds without answering"
Write-Host "3. Or have all players answer quickly to see 3-second delay"