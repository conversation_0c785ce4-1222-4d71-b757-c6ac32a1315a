# Force deployment without Docker checks

Write-Host "Forcing Edge Function deployment..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"

# Set environment to bypass local checks
$env:SUPABASE_WORKDIR = $null
$env:SUPABASE_PROJECT_REF = $projectRef

Write-Host "Deploying submit-answer-handler to $projectRef" -ForegroundColor Yellow

# Use npx with explicit parameters
$deployCmd = "npx supabase@latest functions deploy submit-answer-handler --project-ref $projectRef --no-verify-jwt"

Write-Host "Running: $deployCmd" -ForegroundColor Gray

# Execute with output redirection
& cmd /c $deployCmd 2>&1

Write-Host "`nDeployment command completed." -ForegroundColor Green