const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');

// Configuration
const BASE_URL = 'http://localhost:3001';

async function testMultiplayerWithDetailedLogs() {
  console.log('=== Starting Detailed Multiplayer Test ===');
  console.log(`Testing at: ${BASE_URL}`);
  
  // Detect environment and set appropriate browser path
  let executablePath;
  const platform = os.platform();
  const isWSL = platform === 'linux' && os.release().toLowerCase().includes('microsoft');
  
  if (isWSL) {
    console.log('Detected WSL environment, using system chromium-browser');
    executablePath = '/usr/bin/chromium-browser';
  }

  const browserHost = await puppeteer.launch({
    headless: true,
    executablePath,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-gpu']
  });

  try {
    const hostPage = await browserHost.newPage();
    
    // Enable console logging
    hostPage.on('console', msg => {
      const text = msg.text();
      if (text.includes('START_GAME') || text.includes('WORKAROUND') || text.includes('exception')) {
        console.log(`[HOST Browser] ${text}`);
      }
    });
    
    hostPage.on('pageerror', error => {
      console.error('[HOST Browser Error]', error.message);
    });

    // Navigate to app
    console.log('\n[HOST] Navigating to app...');
    await hostPage.goto(BASE_URL, { waitUntil: 'networkidle0', timeout: 60000 });
    
    // Wait for app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Click Multiplayer Mode
    console.log('\n[HOST] Clicking Multiplayer Mode...');
    await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const multiplayerBtn = buttons.find(btn => btn.textContent.includes('Multiplayer'));
      if (multiplayerBtn) multiplayerBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Fill auth
    console.log('[HOST] Filling auth form...');
    const inputs = await hostPage.$$('input:not([type="hidden"])');
    if (inputs.length >= 2) {
      await inputs[0].type('fresh');
      await inputs[1].type('test123');
    }
    
    // Submit
    await hostPage.evaluate(() => {
      const submitBtn = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent.includes('Sign In') || btn.type === 'submit'
      );
      if (submitBtn) submitBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Click Multiplayer again
    console.log('\n[HOST] Switching to multiplayer mode...');
    await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const multiplayerBtn = buttons.find(btn => btn.textContent.includes('Multiplayer'));
      if (multiplayerBtn) multiplayerBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Create room
    console.log('[HOST] Creating room...');
    await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const hostBtn = buttons.find(btn => btn.textContent.includes('Host Game') || btn.textContent.includes('Create Room'));
      if (hostBtn) hostBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Mark ready
    console.log('[HOST] Marking ready...');
    await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const readyBtn = buttons.find(btn => btn.textContent.includes('Ready') && !btn.textContent.includes('✓'));
      if (readyBtn) readyBtn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Start game with detailed logging
    console.log('\n[HOST] Starting game with detailed error logging...');
    const startResult = await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const startBtn = buttons.find(btn => btn.textContent.includes('Start Game'));
      if (startBtn) {
        console.log('[Client] Found Start Game button, clicking it');
        startBtn.click();
        return { success: true, message: 'Clicked Start Game button' };
      }
      return { success: false, message: 'Start Game button not found' };
    });
    
    console.log('[HOST] Start result:', startResult);
    
    // Wait and check for errors
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Check final state
    const finalState = await hostPage.evaluate(() => {
      return {
        hasError: document.body.textContent.includes('Error') || document.body.textContent.includes('exception'),
        errorText: Array.from(document.querySelectorAll('*')).find(el => 
          el.textContent.includes('exception') || el.textContent.includes('Error')
        )?.textContent,
        hasGameImage: !!document.querySelector('img[alt="Guess the player"]'),
        currentUrl: window.location.href,
        bodyText: document.body.textContent.substring(0, 500)
      };
    });
    
    console.log('\n[HOST] Final state:', JSON.stringify(finalState, null, 2));
    
    // Take screenshot
    await hostPage.screenshot({ path: 'test-detailed-final-state.png', fullPage: true });
    console.log('\nScreenshot saved: test-detailed-final-state.png');
    
  } catch (error) {
    console.error('\n=== TEST ERROR ===');
    console.error(error);
  } finally {
    await browserHost.close();
  }
}

testMultiplayerWithDetailedLogs().catch(console.error);