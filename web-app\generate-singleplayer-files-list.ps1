#!/usr/bin/env pwsh

# Script to generate a comprehensive list of files relevant to the singleplayer game
# and football emoji celebration animation for replication in multiplayer

Write-Host "Football Generating Singleplayer Game Files Documentation..." -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Yellow

$outputFile = "SINGLEPLAYER_GAME_FILES.md"
$currentDir = Get-Location

# Initialize the output file
@"
# Singleplayer Game Files Documentation
Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

This document contains all the relevant files for the singleplayer game implementation and the football emoji celebration animation system. These files should be used as reference for implementing similar functionality in the multiplayer experience.

## Overview

The singleplayer game consists of:
1. **Core Game Logic** - Game state management, question generation, scoring
2. **UI Components** - Player image display, choice buttons, score panels
3. **Animation System** - Football emoji celebrations, score popups, time change animations
4. **Data Management** - Player data loading, image handling
5. **Game Modes** - Normal mode and Timed mode implementations

## File Categories

### Core Game Files
- Game state management and logic
- Question generation and validation
- Score tracking and persistence

### UI Components
- Player image display
- Choice buttons and interactions
- Score and info panels

### Animation System
- Football emoji celebration effects
- Score change popups
- Time change animations
- Loading animations

### Data Management
- Player data loading and caching
- Image path handling
- Type definitions

---

"@ | Out-File -FilePath $outputFile -Encoding UTF8

Write-Host "Documenting Core Game Files..." -ForegroundColor Cyan

# Core Game Files
$coreFiles = @(
    @{
        Path = "src/stores/gameStore.ts"
        Category = "Core Game Logic"
        Description = "Main game state management using Zustand. Handles game modes (normal/timed), scoring, question flow, timer management, and game lifecycle."
        KeyFeatures = @(
            "Game mode switching (normal/timed)",
            "Score tracking and persistence",
            "Question generation and validation",
            "Timer management for timed mode",
            "Streak tracking",
            "Recent answers management",
            "Animation triggers"
        )
    },
    @{
        Path = "src/lib/playerData.ts"
        Category = "Data Management"
        Description = "Player data loading and question generation logic. Loads from players_game_data.json and generates random questions with choices."
        KeyFeatures = @(
            "Player data caching",
            "Question generation with 4 choices",
            "Image URL construction",
            "Distractor selection logic",
            "Data validation"
        )
    },
    @{
        Path = "src/types/index.ts"
        Category = "Data Types"
        Description = "TypeScript type definitions for game data structures."
        KeyFeatures = @(
            "PlayerData interface",
            "PlayerQuestion interface", 
            "PlayerChoice interface",
            "RecentAnswer interface",
            "GameModeType enum"
        )
    }
)

# UI Component Files
$uiFiles = @(
    @{
        Path = "src/components/game/PlayerImageDisplay.tsx"
        Category = "UI Components"
        Description = "Displays player images with loading states and optimized image handling."
        KeyFeatures = @(
            "Next.js Image optimization",
            "Loading state management",
            "Blur placeholder",
            "Responsive sizing",
            "Error handling"
        )
    },
    @{
        Path = "src/components/game/ChoiceButton.tsx"
        Category = "UI Components"
        Description = "Interactive choice buttons with correct/incorrect styling and disabled states."
        KeyFeatures = @(
            "Click handling",
            "Correct/incorrect visual feedback",
            "Disabled state styling",
            "Hover effects",
            "Accessibility features"
        )
    },
    @{
        Path = "src/components/game/PlayerInfoPanel.tsx"
        Category = "UI Components"
        Description = "Displays detailed player information in the right panel."
        KeyFeatures = @(
            "Player stats display",
            "Team information",
            "Position and physical stats",
            "College information",
            "Responsive layout"
        )
    },
    @{
        Path = "src/components/game/RecentAnswersList.tsx"
        Category = "UI Components"
        Description = "Shows list of recent answers with correct/incorrect indicators."
        KeyFeatures = @(
            "Answer history display",
            "Click to view player details",
            "Visual correct/incorrect indicators",
            "Avatar integration",
            "Scrollable list"
        )
    },
    @{
        Path = "src/components/game/ScorePanel.tsx"
        Category = "UI Components"
        Description = "Displays current score and game statistics."
        KeyFeatures = @(
            "Score display",
            "Best score tracking",
            "Streak information",
            "Game mode indicators"
        )
    }
)

# Animation System Files
$animationFiles = @(
    @{
        Path = "src/components/game/FootballFx.tsx"
        Category = "Football Celebration Animation"
        Description = "Main football emoji celebration effect. Creates burst of football emojis based on streak count."
        KeyFeatures = @(
            "Streak-based football count (max 5)",
            "Random trajectory generation",
            "Framer Motion animations",
            "GPU-optimized transforms",
            "Configurable timing and physics",
            "Burst effect on correct answers"
        )
    },
    @{
        Path = "src/components/game/ScorePopup.tsx"
        Category = "Score Animation"
        Description = "Animated popup showing score increases like +10, +20, etc."
        KeyFeatures = @(
            "Fade and scale animation",
            "Upward movement",
            "Score change display",
            "Trigger-based activation",
            "Positioned above game area"
        )
    },
    @{
        Path = "src/components/game/TimeChangePopup.tsx"
        Category = "Time Animation"
        Description = "Shows time bonus/penalty changes in timed mode like +1s, -1s."
        KeyFeatures = @(
            "Positive/negative time display",
            "Color-coded feedback (green/red)",
            "Coordinated with score popup",
            "Conditional positioning",
            "Timed mode integration"
        )
    },
    @{
        Path = "src/components/game/FootballLoader.tsx"
        Category = "Loading Animation"
        Description = "Loading screen with animated football emojis."
        KeyFeatures = @(
            "Bouncing football animation",
            "Staggered timing",
            "CSS keyframe animations",
            "Loading message display",
            "Game initialization feedback"
        )
    }
)

# Main Application File
$mainFiles = @(
    @{
        Path = "src/app/page.tsx"
        Category = "Main Application"
        Description = "Main page component containing both singleplayer and multiplayer game logic. Contains the complete singleplayer game implementation."
        KeyFeatures = @(
            "Game mode selection (single/multiplayer)",
            "Singleplayer game flow",
            "Animation integration",
            "State management",
            "UI layout and responsive design",
            "Game controls and interactions"
        )
    }
)

# Data Files - Special handling for large files
$dataFiles = @(
    @{
        Path = "public/data/players_game_data.json"
        Category = "Game Data"
        Description = "Main game data file containing all player information used for questions."
        KeyFeatures = @(
            "Player profiles with images",
            "Team information",
            "Physical stats",
            "Career information",
            "Image path references"
        )
        IsLargeFile = $true
    },
    @{
        Path = "public/players_images/"
        Category = "Game Assets"
        Description = "Directory containing all player images referenced in the game data."
        KeyFeatures = @(
            "Player headshot images",
            "Optimized for web display",
            "Referenced by local_image_path in JSON",
            "Used by PlayerImageDisplay component"
        )
        IsDirectory = $true
    }
)

# Style Files
$styleFiles = @(
    @{
        Path = "src/app/globals.css"
        Category = "Styling"
        Description = "Global CSS including football animation keyframes and game-specific styles."
        KeyFeatures = @(
            "Football bounce animation keyframes",
            "Loading spinner styles",
            "Game container styles",
            "Button focus overrides",
            "Responsive design utilities"
        )
    }
)

# Function to add file documentation
function Add-FileDocumentation {
    param(
        [array]$Files,
        [string]$SectionTitle
    )
    
    Add-Content -Path $outputFile -Value "`n## $SectionTitle`n"
    
    foreach ($file in $Files) {
        Write-Host "  Processing: $($file.Path)" -ForegroundColor White
        
        Add-Content -Path $outputFile -Value "### $($file.Category): $($file.Path)`n"
        Add-Content -Path $outputFile -Value "**Description:** $($file.Description)`n"
        Add-Content -Path $outputFile -Value "**Key Features:**"
        
        foreach ($feature in $file.KeyFeatures) {
            Add-Content -Path $outputFile -Value "- $feature"
        }
        
        # Handle different file types
        $fullPath = Join-Path $currentDir $file.Path
        
        if ($file.IsDirectory) {
            # Handle directory
            if (Test-Path $fullPath) {
                $imageCount = (Get-ChildItem $fullPath -File | Measure-Object).Count
                Add-Content -Path $outputFile -Value "`n**Directory Info:** Contains $imageCount image files"
                Add-Content -Path $outputFile -Value "`n**Sample Files:**"
                $sampleFiles = Get-ChildItem $fullPath -File | Select-Object -First 5 | ForEach-Object { $_.Name }
                foreach ($sample in $sampleFiles) {
                    Add-Content -Path $outputFile -Value "- $sample"
                }
                if ($imageCount -gt 5) {
                    Add-Content -Path $outputFile -Value "- ... and $($imageCount - 5) more files"
                }
            } else {
                Add-Content -Path $outputFile -Value "`n**Note:** Directory not found at $fullPath"
            }
        } elseif ($file.IsLargeFile) {
            # Handle large JSON file with summary
            if (Test-Path $fullPath) {
                $lineCount = (Get-Content $fullPath | Measure-Object -Line).Lines
                Add-Content -Path $outputFile -Value "`n**File Size:** $lineCount lines (large data file)"
                
                # Read first few lines to show structure
                $firstLines = Get-Content $fullPath -Head 10
                Add-Content -Path $outputFile -Value "`n**Data Structure Sample:**"
                Add-Content -Path $outputFile -Value '```json'
                foreach ($line in $firstLines) {
                    Add-Content -Path $outputFile -Value $line
                }
                Add-Content -Path $outputFile -Value "... (truncated - contains $lineCount total lines with player data)"
                Add-Content -Path $outputFile -Value '```'
                
                # Try to parse and show data summary
                try {
                    $jsonContent = Get-Content $fullPath -Raw | ConvertFrom-Json
                    $playerCount = $jsonContent.Count
                    $samplePlayer = $jsonContent[0]
                    
                    Add-Content -Path $outputFile -Value "`n**Data Summary:**"
                    Add-Content -Path $outputFile -Value "- Total players: $playerCount"
                    Add-Content -Path $outputFile -Value "- Sample player structure:"
                    Add-Content -Path $outputFile -Value '```json'
                    Add-Content -Path $outputFile -Value ($samplePlayer | ConvertTo-Json -Depth 2)
                    Add-Content -Path $outputFile -Value '```'
                } catch {
                    Add-Content -Path $outputFile -Value "`n**Note:** Could not parse JSON for summary"
                }
            } else {
                Add-Content -Path $outputFile -Value "`n**Note:** File not found at $fullPath"
            }
        } else {
            # Handle regular files
            if (Test-Path $fullPath) {
                $content = Get-Content $fullPath -Raw -Encoding UTF8
                $lineCount = (Get-Content $fullPath | Measure-Object -Line).Lines
                
                Add-Content -Path $outputFile -Value "`n**File Size:** $lineCount lines"
                Add-Content -Path $outputFile -Value "`n**File Content:**"
                Add-Content -Path $outputFile -Value '```typescript'
                Add-Content -Path $outputFile -Value $content
                Add-Content -Path $outputFile -Value '```'
            } else {
                Add-Content -Path $outputFile -Value "`n**Note:** File not found at $fullPath"
            }
        }
        
        Add-Content -Path $outputFile -Value "`n---`n"
    }
}

# Add all sections
Add-FileDocumentation -Files $coreFiles -SectionTitle "Core Game Logic Files"
Add-FileDocumentation -Files $uiFiles -SectionTitle "UI Component Files"
Add-FileDocumentation -Files $animationFiles -SectionTitle "Animation System Files"
Add-FileDocumentation -Files $mainFiles -SectionTitle "Main Application File"
Add-FileDocumentation -Files $dataFiles -SectionTitle "Data and Asset Files"
Add-FileDocumentation -Files $styleFiles -SectionTitle "Styling Files"

# Add implementation notes
$implementationNotes = @"

## Implementation Notes for Multiplayer

### Football Emoji Celebration System
The football celebration animation system consists of:

1. **FootballFx.tsx** - Main celebration component
   - Triggered by animationTrigger prop
   - Number of footballs based on streak count
   - Uses Framer Motion for smooth animations
   - Can be easily integrated into multiplayer by passing trigger and streak values

2. **Animation Integration** - In main page component
   ```typescript
   <FootballFx trigger={animationTrigger} streak={streak} />
   ```

3. **CSS Animations** - In globals.css
   - footballBounce keyframes for loading animation
   - Staggered timing for multiple footballs
   - GPU-optimized transforms

### Key Integration Points for Multiplayer

1. **Data Source** - Use same players_game_data.json and players_images/ directory
2. **Question Generation** - Reuse generateQuestion() function from playerData.ts
3. **Animation Triggers** - Implement similar trigger system for celebrations
4. **Image Loading** - Use same PlayerImageDisplay component
5. **Choice Buttons** - Reuse ChoiceButton component with multiplayer answer handling

### Recommended Replication Strategy

1. **Copy Animation Components** - FootballFx, ScorePopup, TimeChangePopup
2. **Reuse Data Layer** - playerData.ts functions and JSON data
3. **Adapt UI Components** - Modify existing components for multiplayer context
4. **Implement Trigger System** - Create multiplayer-specific animation triggers
5. **Maintain Styling** - Use same CSS classes and animations

## File Summary

**Total Files Documented:** $($coreFiles.Count + $uiFiles.Count + $animationFiles.Count + $mainFiles.Count + $dataFiles.Count + $styleFiles.Count)

**Categories:**
- Core Game Logic: $($coreFiles.Count) files
- UI Components: $($uiFiles.Count) files  
- Animation System: $($animationFiles.Count) files
- Main Application: $($mainFiles.Count) files
- Data & Assets: $($dataFiles.Count) files
- Styling: $($styleFiles.Count) files

This documentation provides a complete reference for implementing similar functionality in the multiplayer experience while maintaining consistency with the singleplayer game.
"@

Add-Content -Path $outputFile -Value $implementationNotes

Write-Host "`nDocumentation generated successfully!" -ForegroundColor Green
Write-Host "Output file: $outputFile" -ForegroundColor Yellow
Write-Host "Total files documented: $($coreFiles.Count + $uiFiles.Count + $animationFiles.Count + $mainFiles.Count + $dataFiles.Count + $styleFiles.Count)" -ForegroundColor Cyan

# Display file size
if (Test-Path $outputFile) {
    $fileSize = (Get-Item $outputFile).Length
    $fileSizeKB = [math]::Round($fileSize / 1024, 2)
    Write-Host "File size: $fileSizeKB KB" -ForegroundColor Magenta
}

Write-Host "`nUse this documentation to:" -ForegroundColor White
Write-Host "  • Replicate singleplayer game logic in multiplayer" -ForegroundColor Gray
Write-Host "  • Implement football emoji celebrations" -ForegroundColor Gray  
Write-Host "  • Maintain consistent data sources and UI components" -ForegroundColor Gray
Write-Host "  • Reference animation systems and triggers" -ForegroundColor Gray 