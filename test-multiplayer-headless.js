const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');

async function testMultiplayerGame() {
  console.log('Starting multiplayer game test in headless mode...');
  
  let browser;
  
  try {
    // Launch browser with minimal settings
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();
    
    // Set viewport
    await page.setViewport({ width: 1280, height: 800 });
    
    // Create screenshots directory
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }
    
    // Step 1: Navigate to the game
    console.log('\n1. Navigating to http://localhost:3000...');
    try {
      await page.goto('http://localhost:3000', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      });
      console.log('   ✓ Page loaded successfully');
    } catch (error) {
      console.log('   ✗ Failed to load page:', error.message);
      return;
    }
    
    // Step 2: Take initial screenshot
    console.log('\n2. Taking initial screenshot...');
    await page.screenshot({ 
      path: 'screenshots/01-initial-state.png',
      fullPage: true 
    });
    console.log('   ✓ Screenshot saved: screenshots/01-initial-state.png');
    
    // Get page content for analysis
    const pageContent = await page.content();
    console.log('   Page size:', pageContent.length, 'bytes');
    
    // Step 3: Analyze authentication state
    console.log('\n3. Analyzing authentication state...');
    
    // Check for common auth indicators
    const authIndicators = await page.evaluate(() => {
      const indicators = {
        hasSignInButton: false,
        hasSignOutButton: false,
        hasAuthModal: false,
        authButtonText: null,
        visibleButtons: []
      };
      
      // Find all buttons
      const buttons = document.querySelectorAll('button');
      buttons.forEach(button => {
        const text = button.textContent?.trim().toLowerCase() || '';
        if (text) {
          indicators.visibleButtons.push(button.textContent.trim());
          
          if (text.includes('sign in') || text.includes('login')) {
            indicators.hasSignInButton = true;
            indicators.authButtonText = button.textContent.trim();
          }
          if (text.includes('sign out') || text.includes('logout')) {
            indicators.hasSignOutButton = true;
          }
        }
      });
      
      // Check for modals
      const modals = document.querySelectorAll('[role="dialog"], .modal, [class*="modal"]');
      indicators.hasAuthModal = modals.length > 0;
      
      return indicators;
    });
    
    console.log('   Auth state:', JSON.stringify(authIndicators, null, 2));
    
    // Step 4: Try to authenticate if needed
    if (authIndicators.hasSignInButton) {
      console.log('\n4. Found sign in button, attempting authentication...');
      
      // Click sign in button
      try {
        await page.click(`button:has-text("${authIndicators.authButtonText}")`);
        await page.waitForTimeout(2000);
        console.log('   ✓ Clicked sign in button');
        
        // Look for form fields
        const formFields = await page.evaluate(() => {
          const fields = {
            hasEmailField: !!document.querySelector('input[type="email"], input[name="email"]'),
            hasPasswordField: !!document.querySelector('input[type="password"]'),
            hasUsernameField: !!document.querySelector('input[name="username"]')
          };
          return fields;
        });
        
        console.log('   Form fields found:', formFields);
        
        if (formFields.hasEmailField || formFields.hasUsernameField) {
          // Fill in credentials
          const emailSelector = formFields.hasEmailField ? 
            'input[type="email"], input[name="email"]' : 
            'input[name="username"]';
            
          await page.type(emailSelector, 'fresh');
          await page.type('input[type="password"]', 'test123');
          
          console.log('   ✓ Filled in credentials');
          
          // Take screenshot of filled form
          await page.screenshot({ 
            path: 'screenshots/02-login-form.png',
            fullPage: true 
          });
          console.log('   ✓ Screenshot saved: screenshots/02-login-form.png');
          
          // Submit form
          await page.keyboard.press('Enter');
          await page.waitForTimeout(3000);
          
          console.log('   ✓ Submitted login form');
        }
      } catch (error) {
        console.log('   ✗ Authentication error:', error.message);
      }
    }
    
    // Step 5: Take post-auth screenshot
    console.log('\n5. Taking post-authentication screenshot...');
    await page.screenshot({ 
      path: 'screenshots/03-after-auth.png',
      fullPage: true 
    });
    console.log('   ✓ Screenshot saved: screenshots/03-after-auth.png');
    
    // Step 6: Look for game options
    console.log('\n6. Looking for game mode options...');
    
    const gameOptions = await page.evaluate(() => {
      const options = {
        buttons: [],
        links: [],
        multiplayerFound: false
      };
      
      // Get all clickable elements
      const clickables = document.querySelectorAll('button, a[href], [role="button"]');
      clickables.forEach(el => {
        const text = el.textContent?.trim();
        if (text) {
          if (el.tagName === 'BUTTON') {
            options.buttons.push(text);
          } else {
            options.links.push(text);
          }
          
          if (text.toLowerCase().includes('multiplayer') || 
              text.toLowerCase().includes('room') ||
              text.toLowerCase().includes('friends')) {
            options.multiplayerFound = true;
          }
        }
      });
      
      return options;
    });
    
    console.log('   Game options found:');
    console.log('   Buttons:', gameOptions.buttons);
    console.log('   Links:', gameOptions.links);
    console.log('   Multiplayer found:', gameOptions.multiplayerFound);
    
    // Step 7: Try to access multiplayer
    if (gameOptions.multiplayerFound) {
      console.log('\n7. Attempting to access multiplayer mode...');
      
      const multiplayerSelectors = [
        'button:has-text("Multiplayer")',
        'button:has-text("Create Room")',
        'button:has-text("Join Room")',
        'a:has-text("Multiplayer")'
      ];
      
      for (const selector of multiplayerSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            await element.click();
            await page.waitForTimeout(2000);
            console.log(`   ✓ Clicked: ${selector}`);
            break;
          }
        } catch (e) {
          // Continue with next selector
        }
      }
      
      // Take screenshot after clicking
      await page.screenshot({ 
        path: 'screenshots/04-multiplayer-view.png',
        fullPage: true 
      });
      console.log('   ✓ Screenshot saved: screenshots/04-multiplayer-view.png');
    }
    
    // Step 8: Final analysis
    console.log('\n8. Final page analysis...');
    
    const finalState = await page.evaluate(() => {
      return {
        title: document.title,
        url: window.location.href,
        bodyText: document.body.innerText.substring(0, 500),
        hasCanvas: !!document.querySelector('canvas'),
        hasVideo: !!document.querySelector('video'),
        hasForm: !!document.querySelector('form')
      };
    });
    
    console.log('   Page title:', finalState.title);
    console.log('   Current URL:', finalState.url);
    console.log('   Has game canvas:', finalState.hasCanvas);
    console.log('   Has video:', finalState.hasVideo);
    console.log('   Has form:', finalState.hasForm);
    console.log('\n   Page preview:');
    console.log('   ' + finalState.bodyText.split('\n').slice(0, 10).join('\n   '));
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'screenshots/05-final-state.png',
      fullPage: true 
    });
    console.log('\n   ✓ Screenshot saved: screenshots/05-final-state.png');
    
    console.log('\nTest completed successfully!');
    console.log('Check the screenshots directory for visual results.');
    
  } catch (error) {
    console.error('\nTest failed with error:', error.message);
    console.error(error.stack);
  } finally {
    if (browser) {
      await browser.close();
      console.log('\nBrowser closed.');
    }
  }
}

// Run the test
testMultiplayerGame().catch(console.error);