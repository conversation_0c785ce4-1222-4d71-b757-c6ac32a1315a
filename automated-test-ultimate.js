/**
 * Ultimate Automated Multiplayer Test
 * Handles the two-step room joining process correctly
 */

const puppeteer = require('puppeteer');

// Test configuration
const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → Round at 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → Round at 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → Round at 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'Only P1 → Round at 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1 at 1s, P2 at 3s → Round at 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// Monitoring code to track round changes
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0,
    submissions: []
  };
  
  console.log('🔍 Monitoring active');
  
  let lastQuestion = null;
  let checkInterval = setInterval(() => {
    // Look for question text
    const questionEl = document.querySelector('h2');
    const question = questionEl?.textContent || '';
    
    // Check if it's a game question (contains "NFL PLAYER")
    if (question && question.includes('NFL PLAYER') && question !== lastQuestion) {
      const now = Date.now();
      
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('🎮 Game started at', new Date(now).toISOString());
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({
          round: window.testData.currentRound++,
          time: elapsed
        });
        console.log('📍 Round', window.testData.currentRound, 'advanced at', elapsed, 'ms');
      }
      
      lastQuestion = question;
    }
  }, 100);
  
  window.submitAnswerAt = function(targetMs) {
    return new Promise((resolve) => {
      const trySubmit = () => {
        if (!window.testData.gameStartTime) {
          setTimeout(trySubmit, 100);
          return;
        }
        
        const elapsed = Date.now() - window.testData.gameStartTime;
        const timeToWait = targetMs - elapsed;
        
        const submitAnswer = () => {
          // Find answer buttons (player name choices)
          const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
            const text = b.textContent || '';
            // Player names are typically 2-3 words, no special characters except . - '
            const isPlayerName = text.length > 0 && 
                               text.length < 30 &&
                               text.match(/^[A-Za-z\\s\\.\\-\\']+$/) &&
                               !['Ready', 'Start', 'Mode', 'Sign', 'Create', 'Join', 'Leave', 'Host', 'Back'].some(word => text.includes(word));
            const isVisible = b.offsetParent !== null;
            
            return isPlayerName && isVisible;
          });
          
          console.log('Found', buttons.length, 'answer buttons');
          
          if (buttons.length >= 4) {
            const chosen = buttons[Math.floor(Math.random() * 4)];
            const submitTime = Date.now() - window.testData.gameStartTime;
            console.log('✅ Submitting:', chosen.textContent, 'at', submitTime, 'ms');
            window.testData.submissions.push({
              time: submitTime,
              answer: chosen.textContent
            });
            chosen.click();
            resolve(true);
          } else {
            // Retry if buttons not found
            setTimeout(submitAnswer, 100);
          }
        };
        
        if (timeToWait > 0) {
          console.log('⏰ Waiting', timeToWait, 'ms to submit');
          setTimeout(submitAnswer, timeToWait);
        } else {
          submitAnswer();
        }
      };
      
      trySubmit();
    });
  };
  
  'Monitoring injected';
`;

async function signInPlayer(page, credentials, playerName) {
  console.log(`  ${playerName} signing in...`);
  
  // Click Login button
  await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const loginBtn = buttons.find(b => b.textContent && b.textContent.includes('Login'));
    if (loginBtn) loginBtn.click();
  });
  
  await delay(2000);
  
  // Fill credentials
  await page.type('input[placeholder="Username or Email"]', credentials.email);
  await page.type('input[placeholder="Password"]', credentials.password);
  
  // Submit
  await page.evaluate(() => document.querySelector('form').requestSubmit());
  
  await delay(5000);
  console.log(`  ✅ ${playerName} signed in`);
}

async function runTest() {
  console.log('🎯 Ultimate Automated Multiplayer Test');
  console.log('=====================================\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Browser configuration
    const browserOptions = {
      headless: false,
      executablePath: '/usr/bin/chromium-browser',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--window-size=1200,900'
      ],
      protocolTimeout: 120000
    };
    
    // Launch browsers
    console.log('🚀 Launching browsers...');
    browser1 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=0,0']
    });
    
    browser2 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=600,0']
    });
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    
    // Increase page timeouts
    page1.setDefaultTimeout(60000);
    page2.setDefaultTimeout(60000);
    
    // Load game
    console.log('📱 Loading game...');
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2' }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2' })
    ]);
    
    await delay(3000);
    
    // Inject monitoring
    await page1.evaluate(monitoringCode);
    await page2.evaluate(monitoringCode);
    console.log('✅ Monitoring code injected\n');
    
    // Sign in
    console.log('🔐 Authentication:');
    await signInPlayer(page1, CONFIG.player1, 'Player 1');
    await signInPlayer(page2, CONFIG.player2, 'Player 2');
    
    // Navigate to multiplayer
    console.log('\n🎮 Entering Multiplayer:');
    await page1.evaluate(() => {
      Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Multiplayer Mode'
      )?.click();
    });
    console.log('  Player 1 → Multiplayer');
    
    await delay(3000);
    
    await page2.evaluate(() => {
      Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Multiplayer Mode'
      )?.click();
    });
    console.log('  Player 2 → Multiplayer');
    
    await delay(3000);
    
    // Create room
    console.log('\n🏠 Room Management:');
    await page1.evaluate(() => {
      Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent.trim() === 'Host Game'
      )?.click();
    });
    await delay(3000);
    console.log('  ✅ Room created by Player 1');
    
    // Join room - Two step process
    console.log('  Player 2 joining room...');
    
    // Step 1: Click on the room in the lobby
    const roomClicked = await page2.evaluate(() => {
      // Find elements containing "fresh's Game"
      const elements = Array.from(document.querySelectorAll('*'));
      
      // Look for the room listing
      for (const el of elements) {
        const text = el.textContent || '';
        if (text.includes("fresh's Game") && 
            (text.includes("Host:") || text.includes("Players:")) &&
            el.children.length < 10) {
          console.log('Clicking on room listing');
          el.click();
          return true;
        }
      }
      
      // Fallback: click any element with room name
      const roomEl = elements.find(el => 
        el.textContent === "fresh's Game" && el.children.length === 0
      );
      if (roomEl && roomEl.parentElement) {
        roomEl.parentElement.click();
        return true;
      }
      
      return false;
    });
    
    if (!roomClicked) {
      throw new Error('Failed to click on room in lobby');
    }
    
    await delay(2000);
    
    // Step 2: Click "Join This Room" button
    const joined = await page2.evaluate(() => {
      const joinBtn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Join This Room'
      );
      if (joinBtn) {
        console.log('Clicking Join This Room button');
        joinBtn.click();
        return true;
      }
      return false;
    });
    
    if (!joined) {
      throw new Error('Failed to find Join This Room button');
    }
    
    await delay(3000);
    console.log('  ✅ Player 2 joined room');
    
    // Ready up
    console.log('\n🎮 Starting Game:');
    
    // Both players ready
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready' || b.textContent === 'Ready Up'
      );
      if (btn) btn.click();
    });
    console.log('  Player 1 ready ✓');
    
    await delay(1000);
    
    await page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready' || b.textContent === 'Ready Up'
      );
      if (btn) btn.click();
    });
    console.log('  Player 2 ready ✓');
    
    await delay(2000);
    
    // Start game
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Start Game'
      );
      if (btn) btn.click();
    });
    
    await delay(3000);
    console.log('  ✅ Game started!\n');
    
    // Run test scenarios
    console.log('📊 Test Scenarios:');
    console.log('=================');
    
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const scenario = CONFIG.scenarios[i];
      console.log(`\n${i + 1}. ${scenario.name}`);
      
      // Schedule answer submissions
      const submissions = [];
      
      if (scenario.p1 !== null) {
        submissions.push(
          page1.evaluate(`window.submitAnswerAt(${scenario.p1})`)
            .catch(err => console.error('P1 submit error:', err))
        );
        console.log(`   P1 → ${scenario.p1}ms`);
      }
      
      if (scenario.p2 !== null) {
        submissions.push(
          page2.evaluate(`window.submitAnswerAt(${scenario.p2})`)
            .catch(err => console.error('P2 submit error:', err))
        );
        console.log(`   P2 → ${scenario.p2}ms`);
      }
      
      // Wait for submissions
      await Promise.all(submissions);
      
      // Wait for round to complete
      console.log('   ⏳ Waiting for round...');
      await delay(8000);
      
      // Get timing results
      const [timing1, timing2] = await Promise.all([
        page1.evaluate((index) => {
          const changes = window.testData.roundChanges || [];
          return changes[index];
        }, i).catch(() => null),
        page2.evaluate((index) => {
          const changes = window.testData.roundChanges || [];
          return changes[index];
        }, i).catch(() => null)
      ]);
      
      const timing = timing1 || timing2;
      
      if (timing) {
        const diff = Math.abs(timing.time - scenario.expected);
        const passed = diff <= 500;
        
        console.log(`   Expected: ${scenario.expected}ms`);
        console.log(`   Actual: ${timing.time}ms`);
        console.log(`   ${passed ? '✅ PASS' : '❌ FAIL'} (±${diff}ms)`);
        
        results.push({ 
          ...scenario, 
          actual: timing.time, 
          difference: diff,
          passed 
        });
      } else {
        console.log('   ❌ No round change detected');
        results.push({ 
          ...scenario, 
          actual: null, 
          passed: false 
        });
      }
    }
    
    // Final summary
    console.log('\n\n' + '='.repeat(50));
    console.log('📈 FINAL TEST RESULTS');
    console.log('='.repeat(50));
    
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    const successRate = (passed / total * 100).toFixed(1);
    
    console.log(`\nSuccess Rate: ${successRate}% (${passed}/${total} tests passed)\n`);
    
    // Detailed results table
    console.log('Test Results Summary:');
    console.log('-'.repeat(70));
    console.log('# | Scenario                | Expected | Actual  | Diff  | Status');
    console.log('-'.repeat(70));
    
    results.forEach((r, i) => {
      const actualStr = r.actual ? `${r.actual}ms` : 'FAILED';
      const diffStr = r.difference !== null ? `±${r.difference}ms` : 'N/A';
      const status = r.passed ? '✅ PASS' : '❌ FAIL';
      
      console.log(
        `${i + 1} | ${r.name.padEnd(23)} | ${String(r.expected).padEnd(8)}ms | ${actualStr.padEnd(7)} | ${diffStr.padEnd(5)} | ${status}`
      );
    });
    
    console.log('-'.repeat(70));
    
    if (passed === total) {
      console.log('\n🎉 ALL TESTS PASSED! 🎉');
      console.log('\nMultiplayer round advance timing verified:');
      console.log('✓ 3-second transition after all players submit');
      console.log('✓ 7-second maximum round duration');
      console.log('✓ Timing accuracy within ±500ms tolerance');
    } else {
      console.log('\n⚠️  Some tests failed. Review the results above.');
    }
    
    console.log('\n✅ Test complete! Press Ctrl+C to exit.\n');
    
    // Keep browsers open
    process.on('SIGINT', async () => {
      console.log('\n🔚 Closing browsers...');
      if (browser1) await browser1.close();
      if (browser2) await browser2.close();
      process.exit(0);
    });
    
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    
    process.exit(1);
  }
}

// Run the test
console.log('Starting ultimate automated test...\n');
runTest().catch(console.error);