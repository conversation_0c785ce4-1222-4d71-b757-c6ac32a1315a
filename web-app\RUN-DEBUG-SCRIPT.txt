To debug the start-game-handler edge function:

1. Open PowerShell as Administrator

2. Navigate to the web-app directory:
   cd C:\Projects\recognition-combine\web-app

3. Run the debug script:
   .\debug-start-game.ps1

This script will:
- Authenticate as the test user "fresh"
- Find or create a waiting room
- Call the start-game-handler edge function
- Display the full response to help debug any issues

The script will show:
- Authentication details
- Room information
- The full HTTP response from the edge function
- Any error messages or data structure issues

If you see any errors or unexpected responses, they will help identify why the game isn't starting properly.