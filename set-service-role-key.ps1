# Set the service role key for Edge Functions
$serviceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjY4MzE1MCwiZXhwIjoyMDYyMjU5MTUwfQ.YjQP5MvOznin_3B9xb_y9E0G_kECbmBc12QL9kH4gD4"

Write-Host "Setting SUPABASE_SERVICE_ROLE_KEY secret..." -ForegroundColor Yellow

npx supabase secrets set SUPABASE_SERVICE_ROLE_KEY=$serviceRoleKey --project-ref xmyxuvuimebjltnaamox

Write-Host "`nSecret set command completed." -ForegroundColor Green
Write-Host "You may need to redeploy your Edge Functions for the changes to take effect." -ForegroundColor Cyan