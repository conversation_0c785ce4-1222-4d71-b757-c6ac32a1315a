const puppeteer = require('puppeteer-core');
const path = require('path');

async function testMultiplayerGame() {
  console.log('Starting multiplayer game test...');
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: 'new', // Use new headless mode
    defaultViewport: {
      width: 1280,
      height: 800
    },
    args: [
      '--no-sandbox', 
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--disable-web-security',
      '--disable-features=IsolateOrigins,site-per-process'
    ],
    // Skip the default Chrome download and use system Chrome if available
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || process.env.CHROME_BIN
  });

  const page = await browser.newPage();
  
  try {
    // Step 1: Navigate to the game
    console.log('1. Navigating to http://localhost:3000...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // Step 2: Take initial screenshot
    console.log('2. Taking initial screenshot...');
    await page.screenshot({ 
      path: 'screenshots/01-initial-state.png',
      fullPage: true 
    });
    console.log('   Screenshot saved: 01-initial-state.png');
    
    // Step 3: Look for Sign In button or auth modal
    console.log('3. Looking for authentication elements...');
    
    // Wait a bit for any modals to appear
    await page.waitForTimeout(2000);
    
    // Check for sign in button
    const signInButton = await page.$('button:has-text("Sign In"), button:has-text("Sign in"), button:has-text("Login"), button:has-text("Log in"), [data-testid="sign-in-button"]');
    
    if (signInButton) {
      console.log('   Found Sign In button, clicking...');
      await signInButton.click();
      await page.waitForTimeout(1000);
    }
    
    // Step 4: Look for auth modal or form
    console.log('4. Looking for authentication form...');
    
    // Try to find username/email input
    const usernameSelectors = [
      'input[type="email"]',
      'input[name="email"]',
      'input[name="username"]',
      'input[placeholder*="email" i]',
      'input[placeholder*="username" i]',
      '#email',
      '#username'
    ];
    
    let usernameInput = null;
    for (const selector of usernameSelectors) {
      usernameInput = await page.$(selector);
      if (usernameInput) {
        console.log(`   Found username input with selector: ${selector}`);
        break;
      }
    }
    
    if (usernameInput) {
      // Step 5: Fill in credentials
      console.log('5. Filling in credentials...');
      await usernameInput.type('fresh', { delay: 100 });
      
      // Find password input
      const passwordSelectors = [
        'input[type="password"]',
        'input[name="password"]',
        '#password'
      ];
      
      let passwordInput = null;
      for (const selector of passwordSelectors) {
        passwordInput = await page.$(selector);
        if (passwordInput) {
          console.log(`   Found password input with selector: ${selector}`);
          break;
        }
      }
      
      if (passwordInput) {
        await passwordInput.type('test123', { delay: 100 });
        
        // Take screenshot of filled form
        await page.screenshot({ 
          path: 'screenshots/02-login-form-filled.png',
          fullPage: true 
        });
        console.log('   Screenshot saved: 02-login-form-filled.png');
        
        // Step 6: Submit login form
        console.log('6. Submitting login form...');
        
        // Look for submit button
        const submitSelectors = [
          'button[type="submit"]',
          'button:has-text("Sign In")',
          'button:has-text("Sign in")',
          'button:has-text("Login")',
          'button:has-text("Log in")',
          'button:has-text("Submit")'
        ];
        
        let submitButton = null;
        for (const selector of submitSelectors) {
          try {
            submitButton = await page.$(selector);
            if (submitButton) {
              console.log(`   Found submit button with selector: ${selector}`);
              break;
            }
          } catch (e) {
            // Continue trying other selectors
          }
        }
        
        if (submitButton) {
          await submitButton.click();
        } else {
          // Try pressing Enter
          console.log('   No submit button found, pressing Enter...');
          await passwordInput.press('Enter');
        }
        
        // Wait for navigation or modal to close
        await page.waitForTimeout(3000);
      }
    } else {
      console.log('   No authentication form found on page');
    }
    
    // Step 7: Take screenshot after login attempt
    console.log('7. Taking screenshot after login...');
    await page.screenshot({ 
      path: 'screenshots/03-after-login.png',
      fullPage: true 
    });
    console.log('   Screenshot saved: 03-after-login.png');
    
    // Step 8: Look for multiplayer options
    console.log('8. Looking for multiplayer/room options...');
    
    const multiplayerSelectors = [
      'button:has-text("Multiplayer")',
      'button:has-text("Create Room")',
      'button:has-text("Join Room")',
      'button:has-text("Play with Friends")',
      '[data-testid="multiplayer-button"]',
      'a:has-text("Multiplayer")',
      'div:has-text("Multiplayer")'
    ];
    
    let multiplayerElement = null;
    for (const selector of multiplayerSelectors) {
      try {
        multiplayerElement = await page.$(selector);
        if (multiplayerElement) {
          console.log(`   Found multiplayer element with selector: ${selector}`);
          
          // Get element details
          const elementTag = await multiplayerElement.evaluate(el => el.tagName);
          const elementText = await multiplayerElement.evaluate(el => el.textContent);
          console.log(`   Element: <${elementTag}> with text: "${elementText}"`);
          
          break;
        }
      } catch (e) {
        // Continue trying other selectors
      }
    }
    
    if (multiplayerElement) {
      // Check if it's clickable
      const isClickable = await multiplayerElement.evaluate(el => {
        const tag = el.tagName.toLowerCase();
        return tag === 'button' || tag === 'a' || el.onclick !== null;
      });
      
      if (isClickable) {
        console.log('   Clicking multiplayer element...');
        await multiplayerElement.click();
        await page.waitForTimeout(2000);
        
        // Take screenshot after clicking
        await page.screenshot({ 
          path: 'screenshots/04-multiplayer-clicked.png',
          fullPage: true 
        });
        console.log('   Screenshot saved: 04-multiplayer-clicked.png');
      }
    }
    
    // Step 9: Final screenshot and page analysis
    console.log('9. Taking final screenshot and analyzing page...');
    await page.screenshot({ 
      path: 'screenshots/05-final-state.png',
      fullPage: true 
    });
    console.log('   Screenshot saved: 05-final-state.png');
    
    // Get page title and URL
    const title = await page.title();
    const url = page.url();
    console.log(`   Page title: ${title}`);
    console.log(`   Current URL: ${url}`);
    
    // Get all visible text on page for analysis
    const visibleText = await page.evaluate(() => {
      const elements = document.querySelectorAll('body *:not(script):not(style)');
      const texts = [];
      elements.forEach(el => {
        const text = el.textContent?.trim();
        if (text && text.length > 0 && text.length < 100) {
          texts.push(text);
        }
      });
      return [...new Set(texts)].slice(0, 20); // Get first 20 unique text elements
    });
    
    console.log('\n   Visible text elements on page:');
    visibleText.forEach(text => console.log(`   - ${text}`));
    
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('Error during test:', error);
    
    // Take error screenshot
    await page.screenshot({ 
      path: 'screenshots/error-state.png',
      fullPage: true 
    });
    console.log('Error screenshot saved: error-state.png');
    
  } finally {
    // Keep browser open for manual inspection
    console.log('\nBrowser will remain open for manual inspection.');
    console.log('Press Ctrl+C to close the browser and exit.');
    
    // Wait indefinitely
    await new Promise(() => {});
  }
}

// Create screenshots directory
const fs = require('fs');
if (!fs.existsSync('screenshots')) {
  fs.mkdirSync('screenshots');
}

// Run the test
testMultiplayerGame().catch(console.error);