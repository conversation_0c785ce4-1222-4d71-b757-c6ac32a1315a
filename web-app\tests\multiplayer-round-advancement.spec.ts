import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_URL = process.env.TEST_URL || 'http://localhost:3000';
const WAIT_TIMEOUT = 30000;
const SHORT_WAIT = 2000;

// Helper to wait for text to appear
async function waitForText(page: Page, text: string, timeout = WAIT_TIMEOUT) {
  await page.waitForFunction(
    (searchText) => {
      const elements = Array.from(document.querySelectorAll('*'));
      return elements.some(el => el.textContent?.includes(searchText));
    },
    text,
    { timeout }
  );
}

// Helper to click button by text
async function clickButtonByText(page: Page, text: string) {
  await page.locator('button', { hasText: text }).click();
}

// Helper to wait and click button
async function waitAndClickButton(page: Page, text: string, timeout = WAIT_TIMEOUT) {
  await page.waitForSelector(`button:has-text("${text}")`, { timeout });
  await clickButtonByText(page, text);
}

// Helper to get room code from page
async function getRoomCode(page: Page): Promise<string> {
  // Look for "Room: " text and extract the code
  const roomHeader = await page.locator('h2:has-text("Room:")').textContent();
  const match = roomHeader?.match(/Room: (.*)/);
  return match ? match[1].trim() : '';
}

// Helper to check if game is in specific state
async function isGameInState(page: Page, state: 'idle' | 'countdown' | 'playing' | 'finished'): Promise<boolean> {
  return await page.evaluate((expectedState) => {
    // Check various UI elements to determine state
    if (expectedState === 'countdown') {
      return !!document.querySelector('div:has-text("Get Ready!")');
    }
    if (expectedState === 'playing') {
      return !!document.querySelector('img[alt*="player"]'); // Player image is shown
    }
    if (expectedState === 'finished') {
      return !!document.querySelector('button:has-text("Play Again")');
    }
    return !document.querySelector('img[alt*="player"]') && !document.querySelector('button:has-text("Play Again")');
  }, state);
}

// Helper to wait for game state
async function waitForGameState(page: Page, state: 'idle' | 'countdown' | 'playing' | 'finished', timeout = WAIT_TIMEOUT) {
  await page.waitForFunction(
    async (expectedState) => {
      if (expectedState === 'countdown') {
        return !!document.querySelector('div:has-text("Get Ready!")');
      }
      if (expectedState === 'playing') {
        return !!document.querySelector('img[alt*="player"]');
      }
      if (expectedState === 'finished') {
        return !!document.querySelector('button:has-text("Play Again")');
      }
      return false;
    },
    state,
    { timeout }
  );
}

test.describe('Multiplayer Round Advancement - Comprehensive Testing', () => {
  test('Full multiplayer game flow - complete game with 10 questions', async ({ browser }) => {
    // Create two browser contexts for host and player
    const hostContext = await browser.newContext();
    const playerContext = await browser.newContext();
    
    const hostPage = await hostContext.newPage();
    const playerPage = await playerContext.newPage();
    
    // Enable console logging for debugging
    hostPage.on('console', msg => {
      if (msg.type() === 'error') console.log('[HOST ERROR]:', msg.text());
    });
    playerPage.on('console', msg => {
      if (msg.type() === 'error') console.log('[PLAYER ERROR]:', msg.text());
    });
    
    let roomCode = '';
    
    try {
      // Step 1: Host creates room
      console.log('[TEST] Step 1: Host creating room...');
      await hostPage.goto(TEST_URL);
      await hostPage.waitForLoadState('networkidle');
      
      // Click on Multiplayer mode
      await waitAndClickButton(hostPage, 'Multiplayer');
      await hostPage.waitForTimeout(SHORT_WAIT);
      
      // Click create room
      await waitAndClickButton(hostPage, 'Create a New Game');
      await hostPage.waitForTimeout(SHORT_WAIT);
      
      // Get the room code
      await waitForText(hostPage, 'Room:');
      roomCode = await getRoomCode(hostPage);
      console.log(`[TEST] ✓ Room created with code: ${roomCode}`);
      
      // Step 2: Player joins room
      console.log('[TEST] Step 2: Player joining room...');
      await playerPage.goto(TEST_URL);
      await playerPage.waitForLoadState('networkidle');
      
      // Click on Multiplayer mode
      await waitAndClickButton(playerPage, 'Multiplayer');
      await playerPage.waitForTimeout(SHORT_WAIT);
      
      // Find active room in the list
      await waitForText(playerPage, roomCode);
      
      // Click join button for this room
      const joinButton = playerPage.locator('button:has-text("Join This Room")').first();
      await joinButton.click();
      await playerPage.waitForTimeout(SHORT_WAIT);
      
      // Verify player joined
      await waitForText(playerPage, `Room: ${roomCode}`);
      console.log('[TEST] ✓ Player joined successfully');
      
      // Step 3: Both players ready up
      console.log('[TEST] Step 3: Players readying up...');
      
      // Wait for ready buttons to appear
      await hostPage.waitForSelector('button:has-text("Ready Up")', { timeout: WAIT_TIMEOUT });
      await playerPage.waitForSelector('button:has-text("Ready Up")', { timeout: WAIT_TIMEOUT });
      
      // Player readies up
      await clickButtonByText(playerPage, 'Ready Up');
      await playerPage.waitForTimeout(1000);
      
      // Wait for player to show as ready on host side
      await waitForText(hostPage, 'Ready ✓');
      
      // Host readies up
      await clickButtonByText(hostPage, 'Ready Up');
      await hostPage.waitForTimeout(1000);
      
      console.log('[TEST] ✓ Both players ready');
      
      // Step 4: Host starts game
      console.log('[TEST] Step 4: Starting game...');
      
      // Wait for start button to be enabled
      await hostPage.waitForSelector('button:has-text("Start Game"):not(:disabled)', { timeout: WAIT_TIMEOUT });
      await clickButtonByText(hostPage, 'Start Game');
      
      // Wait for countdown
      await waitForText(hostPage, 'Get Ready!');
      await waitForText(playerPage, 'Get Ready!');
      console.log('[TEST] ✓ Game countdown started');
      
      // Wait for game to start (player image appears)
      await hostPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
      await playerPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
      console.log('[TEST] ✓ Game started');
      
      // Step 5: Play through all questions
      let questionCount = 0;
      const maxQuestions = 10; // NFL games have 10 questions
      
      while (questionCount < maxQuestions) {
        questionCount++;
        console.log(`[TEST] Playing question ${questionCount}...`);
        
        // Wait for player image and choices to load
        await hostPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
        await playerPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
        
        // Get answer choices (buttons with player names)
        await hostPage.waitForSelector('button.bg-slate-700', { timeout: WAIT_TIMEOUT });
        await playerPage.waitForSelector('button.bg-slate-700', { timeout: WAIT_TIMEOUT });
        
        const hostChoices = await hostPage.locator('button.bg-slate-700').all();
        const playerChoices = await playerPage.locator('button.bg-slate-700').all();
        
        if (hostChoices.length < 4 || playerChoices.length < 4) {
          console.log(`[TEST] Warning: Expected 4 choices, got ${hostChoices.length} for host, ${playerChoices.length} for player`);
        }
        
        // Submit answers - host picks first choice, player picks second
        if (hostChoices.length > 0) {
          await hostChoices[0].click();
          console.log(`[TEST] Host submitted answer for question ${questionCount}`);
        }
        
        if (playerChoices.length > 1) {
          await playerChoices[1].click();
          console.log(`[TEST] Player submitted answer for question ${questionCount}`);
        }
        
        // Wait for transition or game end
        if (questionCount < maxQuestions) {
          // Wait for next question (new player image)
          await hostPage.waitForTimeout(3000); // Wait for transition
          
          // Check if game ended early
          const gameEnded = await hostPage.locator('button:has-text("Play Again")').isVisible().catch(() => false);
          if (gameEnded) {
            console.log('[TEST] Game ended early');
            break;
          }
        }
      }
      
      // Step 6: Verify game completion
      console.log('[TEST] Step 6: Verifying game completion...');
      
      // Wait for "Play Again" button
      await hostPage.waitForSelector('button:has-text("Play Again")', { timeout: WAIT_TIMEOUT });
      await playerPage.waitForSelector('button:has-text("Play Again")', { timeout: WAIT_TIMEOUT });
      
      // Check for final scores
      await waitForText(hostPage, 'Final Scores');
      await waitForText(playerPage, 'Final Scores');
      
      console.log('[TEST] ✓ Game completed successfully');
      
      // Get final scores if visible
      const scoresText = await hostPage.locator('text=/\\d+ points?/').allTextContents();
      console.log('[TEST] Final scores found:', scoresText);
      
      // Step 7: Test rematch functionality
      console.log('[TEST] Step 7: Testing rematch...');
      
      // Both players click play again
      await clickButtonByText(hostPage, 'Play Again');
      await clickButtonByText(playerPage, 'Play Again');
      
      // Wait for ready buttons to reappear
      await hostPage.waitForSelector('button:has-text("Ready Up")', { timeout: WAIT_TIMEOUT });
      await playerPage.waitForSelector('button:has-text("Ready Up")', { timeout: WAIT_TIMEOUT });
      
      console.log('[TEST] ✓ Rematch successful - back in lobby');
      
      // Leave room
      await clickButtonByText(hostPage, 'Leave Room');
      await clickButtonByText(playerPage, 'Leave Room');
      
    } catch (error) {
      // Take screenshots on failure
      await hostPage.screenshot({ path: 'test-screenshots/host-error.png', fullPage: true });
      await playerPage.screenshot({ path: 'test-screenshots/player-error.png', fullPage: true });
      
      console.error('[TEST] Test failed:', error);
      throw error;
    } finally {
      // Cleanup
      await hostContext.close();
      await playerContext.close();
    }
  });
  
  test('Multiplayer edge cases - player disconnect and rejoin', async ({ browser }) => {
    const hostContext = await browser.newContext();
    let playerContext = await browser.newContext();
    
    const hostPage = await hostContext.newPage();
    let playerPage = await playerContext.newPage();
    
    let roomCode = '';
    
    try {
      // Create room
      console.log('[TEST] Creating room for disconnect test...');
      await hostPage.goto(TEST_URL);
      await waitAndClickButton(hostPage, 'Multiplayer');
      await waitAndClickButton(hostPage, 'Create a New Game');
      await waitForText(hostPage, 'Room:');
      roomCode = await getRoomCode(hostPage);
      
      // Player joins
      await playerPage.goto(TEST_URL);
      await waitAndClickButton(playerPage, 'Multiplayer');
      await waitForText(playerPage, roomCode);
      const joinButton = playerPage.locator('button:has-text("Join This Room")').first();
      await joinButton.click();
      await waitForText(playerPage, `Room: ${roomCode}`);
      
      // Ready up and start game
      await clickButtonByText(playerPage, 'Ready Up');
      await waitForText(hostPage, 'Ready ✓');
      await clickButtonByText(hostPage, 'Ready Up');
      await hostPage.waitForSelector('button:has-text("Start Game"):not(:disabled)', { timeout: WAIT_TIMEOUT });
      await clickButtonByText(hostPage, 'Start Game');
      
      // Wait for game to start
      await hostPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
      await playerPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
      
      // Player disconnects mid-game
      console.log('[TEST] Simulating player disconnect...');
      await playerPage.close();
      await playerContext.close();
      
      // Wait a bit for disconnect to register
      await hostPage.waitForTimeout(5000);
      
      // Check if host sees player as disconnected (look for offline status)
      const offlineVisible = await hostPage.locator('.bg-red-900').isVisible().catch(() => false);
      if (offlineVisible) {
        console.log('[TEST] ✓ Host sees player disconnected');
      }
      
      // Player rejoins
      console.log('[TEST] Player attempting to rejoin...');
      playerContext = await browser.newContext();
      playerPage = await playerContext.newPage();
      
      await playerPage.goto(TEST_URL);
      await waitAndClickButton(playerPage, 'Multiplayer');
      
      // Look for rejoin button
      await waitForText(playerPage, roomCode);
      const rejoinButton = playerPage.locator('button:has-text("Rejoin Active Game"), button:has-text("Re-enter Your Game")').first();
      if (await rejoinButton.isVisible()) {
        await rejoinButton.click();
        console.log('[TEST] ✓ Player clicked rejoin button');
      } else {
        // Try regular join
        const regularJoinButton = playerPage.locator('button:has-text("Join This Room")').first();
        await regularJoinButton.click();
      }
      
      // Verify player rejoined active game
      await playerPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
      console.log('[TEST] ✓ Player successfully rejoined active game');
      
      // Check if host sees player as reconnected (look for online status)
      const onlineVisible = await hostPage.locator('.bg-green-900').isVisible().catch(() => false);
      if (onlineVisible) {
        console.log('[TEST] ✓ Host sees player reconnected');
      }
      
    } catch (error) {
      await hostPage.screenshot({ path: 'test-screenshots/disconnect-host-error.png', fullPage: true });
      if (playerPage) await playerPage.screenshot({ path: 'test-screenshots/disconnect-player-error.png', fullPage: true });
      throw error;
    } finally {
      await hostContext.close();
      if (playerContext) await playerContext.close();
    }
  });
  
  test('Multiplayer answer synchronization and scoring', async ({ browser }) => {
    const hostContext = await browser.newContext();
    const playerContext = await browser.newContext();
    
    const hostPage = await hostContext.newPage();
    const playerPage = await playerContext.newPage();
    
    let roomCode = '';
    
    try {
      // Setup game quickly
      await hostPage.goto(TEST_URL);
      await waitAndClickButton(hostPage, 'Multiplayer');
      await waitAndClickButton(hostPage, 'Create a New Game');
      await waitForText(hostPage, 'Room:');
      roomCode = await getRoomCode(hostPage);
      
      await playerPage.goto(TEST_URL);
      await waitAndClickButton(playerPage, 'Multiplayer');
      await waitForText(playerPage, roomCode);
      const joinButton = playerPage.locator('button:has-text("Join This Room")').first();
      await joinButton.click();
      
      await clickButtonByText(playerPage, 'Ready Up');
      await waitForText(hostPage, 'Ready ✓');
      await clickButtonByText(hostPage, 'Ready Up');
      await hostPage.waitForSelector('button:has-text("Start Game"):not(:disabled)');
      await clickButtonByText(hostPage, 'Start Game');
      
      await hostPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
      await playerPage.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT });
      
      console.log('[TEST] Testing answer synchronization...');
      
      // Play 3 questions to test scoring
      for (let i = 0; i < 3; i++) {
        console.log(`[TEST] Question ${i + 1}...`);
        
        // Get the correct answer from the image alt text if possible
        const imgAlt = await hostPage.locator('img[alt*="player"]').getAttribute('alt');
        console.log(`[TEST] Player image alt: ${imgAlt}`);
        
        // Get all choices
        const hostChoices = await hostPage.locator('button.bg-slate-700').all();
        const playerChoices = await playerPage.locator('button.bg-slate-700').all();
        
        // Both players submit different answers
        if (hostChoices.length > 0) {
          await hostChoices[0].click();
          console.log('[TEST] Host submitted first choice');
        }
        
        if (playerChoices.length > 2) {
          await playerChoices[2].click();
          console.log('[TEST] Player submitted third choice');
        }
        
        // Wait for next question or game end
        await hostPage.waitForTimeout(4000);
        
        // Check if scores are visible
        const scoreElements = await hostPage.locator('span:has-text("points"), span:has-text("Points")').all();
        if (scoreElements.length > 0) {
          const scores = await Promise.all(scoreElements.map(el => el.textContent()));
          console.log('[TEST] Current scores:', scores);
        }
      }
      
      console.log('[TEST] ✓ Answer synchronization test complete');
      
    } catch (error) {
      await hostPage.screenshot({ path: 'test-screenshots/sync-host-error.png', fullPage: true });
      await playerPage.screenshot({ path: 'test-screenshots/sync-player-error.png', fullPage: true });
      throw error;
    } finally {
      await hostContext.close();
      await playerContext.close();
    }
  });
});