"use client";

import { useEffect, useState } from "react";

interface MultiplayerTimerProps {
  timeRemaining: number;
  maxTime: number;
  isTransition: boolean;
}

export function MultiplayerTimer({ 
  timeRemaining, 
  maxTime, 
  isTransition 
}: MultiplayerTimerProps) {
  const [displayTime, setDisplayTime] = useState(timeRemaining);
  const [lastUpdate, setLastUpdate] = useState(Date.now());

  useEffect(() => {
    setDisplayTime(timeRemaining);
    setLastUpdate(Date.now());
  }, [timeRemaining]);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const elapsed = (now - lastUpdate) / 1000;
      const newTime = Math.max(0, displayTime - elapsed);
      setDisplayTime(newTime);
      setLastUpdate(now);
    }, 100);

    return () => clearInterval(interval);
  }, [displayTime, lastUpdate]);

  const percentage = (displayTime / maxTime) * 100;
  const displayValue = displayTime.toFixed(1);

  return (
    <div className="absolute bottom-4 right-4 bg-gray-900/80 rounded-lg p-3 backdrop-blur-sm">
      <div className="flex flex-col items-center">
        <div className="text-2xl font-bold text-white tabular-nums">
          {displayValue}s
        </div>
        <div className="text-xs text-gray-400 mt-1">
          {isTransition ? "Next round in" : "Time remaining"}
        </div>
        <div className="w-24 h-2 bg-gray-700 rounded-full mt-2 overflow-hidden">
          <div 
            className={`h-full transition-all duration-100 ${
              isTransition ? "bg-blue-500" : "bg-green-500"
            }`}
            style={{ 
              width: `${percentage}%`,
              background: isTransition 
                ? "linear-gradient(90deg, #3B82F6 0%, #60A5FA 100%)"
                : percentage > 50 
                  ? "linear-gradient(90deg, #10B981 0%, #34D399 100%)"
                  : percentage > 25
                    ? "linear-gradient(90deg, #F59E0B 0%, #FCD34D 100%)"
                    : "linear-gradient(90deg, #EF4444 0%, #F87171 100%)"
            }}
          />
        </div>
      </div>
    </div>
  );
}