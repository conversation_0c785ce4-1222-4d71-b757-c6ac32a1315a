import { useState, useCallback, useEffect, useRef } from 'react';
import { supabase } from '@/lib/supabaseClient';
import { User } from '@supabase/supabase-js';
import { useLoadingState } from './useLoadingState';

interface GameRoom {
  id: string;
  created_at: string;
  status: 'waiting' | 'active' | 'finished';
  host_id: string;
  multiplayer_mode: 'competitive' | 'cooperative' | null;
  title: string | null;
  room_code: string | null;
  max_players: number;
  profiles: { username: string | null } | null;
  game_players: any[];
  player_count: number;
  connected_players: number;
  question_started_at: string | null;
  transition_until: string | null;
  next_question_data: any | null;
}

interface MultiplayerRoomState {
  rooms: GameRoom[];
  activeRoomId: string | null;
  currentRoom: GameRoom | null;
  playersInRoom: any[];
  error: string | null;
}

interface MultiplayerRoomActions {
  fetchRooms: () => Promise<void>;
  createRoom: (title: string, maxPlayers: number, mode: 'competitive' | 'cooperative') => Promise<string | null>;
  joinRoom: (roomId: string) => Promise<boolean>;
  leaveRoom: () => Promise<void>;
  toggleReady: () => Promise<void>;
  refreshRoomState: () => Promise<void>;
}

/**
 * Custom hook to manage multiplayer room functionality
 * Extracts room management logic from the main component
 */
export function useMultiplayerRoom(user: User | null) {
  const [state, setState] = useState<MultiplayerRoomState>({
    rooms: [],
    activeRoomId: null,
    currentRoom: null,
    playersInRoom: [],
    error: null
  });
  
  const [loadingState, loadingActions] = useLoadingState([
    'fetchingRooms',
    'creatingRoom',
    'joiningRoom',
    'leavingRoom',
    'togglingReady'
  ]);
  
  const isLoadingRoomsRef = useRef(false);

  const fetchRooms = useCallback(async () => {
    if (!user || loadingActions.isLoading('fetchingRooms') || isLoadingRoomsRef.current) {
      return;
    }

    isLoadingRoomsRef.current = true;
    loadingActions.startLoading('fetchingRooms');
    
    try {
      const { data, error } = await supabase
        .from('game_rooms')
        .select(`
          *,
          profiles!game_rooms_host_id_fkey(username),
          game_players(count)
        `)
        .in('status', ['waiting', 'active'])
        .order('created_at', { ascending: false });

      if (error) throw error;

      const processedRooms = data?.map(room => ({
        ...room,
        player_count: room.game_players?.[0]?.count || 0,
        connected_players: 0 // This would need real-time updates
      })) || [];

      setState(prev => ({ ...prev, rooms: processedRooms, error: null }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch rooms';
      setState(prev => ({ ...prev, error: errorMessage }));
    } finally {
      loadingActions.stopLoading('fetchingRooms');
      isLoadingRoomsRef.current = false;
    }
  }, [user, loadingActions]);

  const createRoom = useCallback(async (title: string, maxPlayers: number, mode: 'competitive' | 'cooperative') => {
    if (!user || loadingActions.isLoading('creatingRoom')) {
      return null;
    }

    loadingActions.startLoading('creatingRoom');
    
    try {
      const roomCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      
      const { data: roomData, error: roomError } = await supabase
        .from('game_rooms')
        .insert({
          title,
          max_players: maxPlayers,
          room_code: roomCode,
          host_id: user.id,
          status: 'waiting',
          multiplayer_mode: mode
        })
        .select()
        .single();

      if (roomError) throw roomError;

      const { error: playerError } = await supabase
        .from('game_players')
        .insert({
          room_id: roomData.id,
          user_id: user.id,
          is_host: true,
          is_ready: false,
          is_connected: true
        });

      if (playerError) throw playerError;

      setState(prev => ({ 
        ...prev, 
        activeRoomId: roomData.id,
        currentRoom: roomData,
        error: null
      }));

      return roomData.id;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create room';
      setState(prev => ({ ...prev, error: errorMessage }));
      return null;
    } finally {
      loadingActions.stopLoading('creatingRoom');
    }
  }, [user, loadingActions]);

  const joinRoom = useCallback(async (roomId: string) => {
    if (!user || loadingActions.isLoading('joiningRoom')) {
      return false;
    }

    loadingActions.startLoading('joiningRoom');
    
    try {
      // Check if already in room
      const { data: existingPlayer } = await supabase
        .from('game_players')
        .select('*')
        .eq('room_id', roomId)
        .eq('user_id', user.id)
        .single();

      if (existingPlayer) {
        // Update connection status
        await supabase
          .from('game_players')
          .update({ is_connected: true })
          .eq('room_id', roomId)
          .eq('user_id', user.id);
      } else {
        // Join as new player
        const { error } = await supabase
          .from('game_players')
          .insert({
            room_id: roomId,
            user_id: user.id,
            is_host: false,
            is_ready: false,
            is_connected: true
          });

        if (error) throw error;
      }

      setState(prev => ({ 
        ...prev, 
        activeRoomId: roomId,
        error: null
      }));

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to join room';
      setState(prev => ({ ...prev, error: errorMessage }));
      return false;
    } finally {
      loadingActions.stopLoading('joiningRoom');
    }
  }, [user, loadingActions]);

  const leaveRoom = useCallback(async () => {
    if (!user || !state.activeRoomId || loadingActions.isLoading('leavingRoom')) {
      return;
    }

    loadingActions.startLoading('leavingRoom');
    
    try {
      const { error } = await supabase.functions.invoke('leave-room-handler', {
        body: { roomId: state.activeRoomId }
      });

      if (error) throw error;

      setState(prev => ({ 
        ...prev, 
        activeRoomId: null,
        currentRoom: null,
        playersInRoom: [],
        error: null
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to leave room';
      setState(prev => ({ ...prev, error: errorMessage }));
    } finally {
      loadingActions.stopLoading('leavingRoom');
    }
  }, [user, state.activeRoomId, loadingActions]);

  const toggleReady = useCallback(async () => {
    if (!user || !state.activeRoomId || loadingActions.isLoading('togglingReady')) {
      return;
    }

    const currentPlayer = state.playersInRoom.find(p => p.user_id === user.id);
    if (!currentPlayer) return;

    loadingActions.startLoading('togglingReady');
    
    try {
      const { error } = await supabase
        .from('game_players')
        .update({ is_ready: !currentPlayer.is_ready })
        .eq('room_id', state.activeRoomId)
        .eq('user_id', user.id);

      if (error) throw error;

      // Update local state optimistically
      setState(prev => ({
        ...prev,
        playersInRoom: prev.playersInRoom.map(p => 
          p.user_id === user.id ? { ...p, is_ready: !p.is_ready } : p
        )
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to toggle ready status';
      setState(prev => ({ ...prev, error: errorMessage }));
    } finally {
      loadingActions.stopLoading('togglingReady');
    }
  }, [user, state.activeRoomId, state.playersInRoom, loadingActions]);

  const refreshRoomState = useCallback(async () => {
    if (!state.activeRoomId) return;

    try {
      const { data: roomData, error: roomError } = await supabase
        .from('game_rooms')
        .select('*')
        .eq('id', state.activeRoomId)
        .single();

      if (roomError) throw roomError;

      const { data: playersData, error: playersError } = await supabase
        .from('game_players')
        .select(`
          *,
          profile:profiles!game_players_user_id_fkey(username)
        `)
        .eq('room_id', state.activeRoomId)
        .order('joined_at', { ascending: true });

      if (playersError) throw playersError;

      setState(prev => ({
        ...prev,
        currentRoom: roomData,
        playersInRoom: playersData || [],
        error: null
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh room state';
      setState(prev => ({ ...prev, error: errorMessage }));
    }
  }, [state.activeRoomId]);

  // Auto-refresh rooms when user changes
  useEffect(() => {
    if (user) {
      fetchRooms();
    } else {
      setState({
        rooms: [],
        activeRoomId: null,
        currentRoom: null,
        playersInRoom: [],
        error: null
      });
    }
  }, [user, fetchRooms]);

  const actions: MultiplayerRoomActions = {
    fetchRooms,
    createRoom,
    joinRoom,
    leaveRoom,
    toggleReady,
    refreshRoomState
  };

  return {
    ...state,
    ...actions,
    loadingState
  };
}