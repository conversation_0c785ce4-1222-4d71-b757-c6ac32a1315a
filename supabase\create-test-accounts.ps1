# Script to help create test accounts for automated testing

Write-Host "CREATE TEST ACCOUNTS FOR AUTOMATED TESTING" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "This script will help you create test accounts for the automated tests." -ForegroundColor Yellow
Write-Host ""

Write-Host "Option 1: Create via Supabase Dashboard (Recommended)" -ForegroundColor Green
Write-Host "-----------------------------------------------------" -ForegroundColor Green
Write-Host "1. Opening Supabase Auth dashboard..." -ForegroundColor White
Start-Process "https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/auth/users"
Write-Host ""
Write-Host "2. Click 'Add user' → 'Create new user'" -ForegroundColor White
Write-Host "3. Create these two accounts:" -ForegroundColor White
Write-Host "   • Email: <EMAIL>, Password: Test123!@#" -ForegroundColor Gray
Write-Host "   • Email: <EMAIL>, Password: Test123!@#" -ForegroundColor Gray
Write-Host ""

Write-Host "Option 2: Create via SQL (Alternative)" -ForegroundColor Blue
Write-Host "--------------------------------------" -ForegroundColor Blue
Write-Host "Run this SQL in the Supabase SQL editor:" -ForegroundColor White
Write-Host ""

$sqlCode = @'
-- Create test users for automated testing
-- Note: You may need to adjust based on your auth setup

-- First, ensure test users don't exist
DELETE FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Create test player 1
INSERT INTO auth.users (
  id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_user_meta_data
) VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  crypt('Test123!@#', gen_salt('bf')),
  now(),
  now(),
  now(),
  '{"name": "Test Player 1"}'::jsonb
);

-- Create test player 2  
INSERT INTO auth.users (
  id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_user_meta_data
) VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  crypt('Test123!@#', gen_salt('bf')),
  now(),
  now(),
  now(),
  '{"name": "Test Player 2"}'::jsonb
);

-- Verify creation
SELECT id, email, created_at FROM auth.users 
WHERE email IN ('<EMAIL>', '<EMAIL>');
'@

Write-Host $sqlCode -ForegroundColor Gray
Write-Host ""

Write-Host "Option 3: Quick Setup Script" -ForegroundColor Magenta
Write-Host "----------------------------" -ForegroundColor Magenta
Write-Host "Press Enter to save test credentials to a config file..." -ForegroundColor White
Read-Host

# Create test config
$testConfig = @{
  player1 = @{
    email = "<EMAIL>"
    password = "Test123!@#"
  }
  player2 = @{
    email = "<EMAIL>"
    password = "Test123!@#"
  }
} | ConvertTo-Json -Depth 3

$testConfig | Out-File -FilePath "test-config.json" -Encoding UTF8

Write-Host ""
Write-Host "✅ Test config saved to test-config.json" -ForegroundColor Green
Write-Host ""
Write-Host "After creating accounts, run:" -ForegroundColor Yellow
Write-Host "node test-multiplayer-automated.js" -ForegroundColor White
Write-Host ""
Write-Host "Or for the simple version:" -ForegroundColor Yellow
Write-Host "node test-multiplayer-simple.js" -ForegroundColor White