import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/providers/AuthProvider'
// Temporarily disabled to debug SSR issues
// import { ImagePreloader } from "@/components/game/ImagePreloader";

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Recognition Combine',
  description: 'The premier platform for athletic recognition',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          {children}
        </AuthProvider>
        {/* Portal container for global animations */}
        <div 
          id="global-animation-portal" 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            zIndex: 9999
          }}
        />
      </body>
    </html>
  )
}
