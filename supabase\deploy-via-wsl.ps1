# Deploy Edge Function via WSL or provide manual instructions
Write-Host "Attempting Edge Function deployment..." -ForegroundColor Yellow

# Check if WSL is available
$wslAvailable = $false
try {
    $wslTest = wsl --list 2>$null
    if ($LASTEXITCODE -eq 0) {
        $wslAvailable = $true
    }
} catch {
    $wslAvailable = $false
}

if ($wslAvailable) {
    Write-Host "WSL detected! Deploying via WSL..." -ForegroundColor Green
    
    # Deploy using WSL
    $projectPath = "/mnt/c/Projects/recognition-combine/supabase"
    $deployCommand = @"
cd $projectPath && npx supabase functions deploy start-game-handler --project-ref xmyxuvuimebjltnaamox
"@
    
    Write-Host "Running deployment command in WSL..." -ForegroundColor Cyan
    wsl -e bash -c $deployCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nDeployment successful!" -ForegroundColor Green
    } else {
        Write-Host "`nDeployment via WSL failed. See manual instructions below." -ForegroundColor Red
    }
} else {
    Write-Host "WSL not available. Please use one of these alternatives:" -ForegroundColor Yellow
}

Write-Host "`n=== MANUAL DEPLOYMENT INSTRUCTIONS ===" -ForegroundColor Cyan
Write-Host "Since the Windows CLI is stuck, you can deploy manually:" -ForegroundColor White
Write-Host ""
Write-Host "Option 1: Supabase Dashboard (Recommended)" -ForegroundColor Green
Write-Host "1. Go to: https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/functions" -ForegroundColor White
Write-Host "2. Click on 'start-game-handler'" -ForegroundColor White
Write-Host "3. Click 'Deploy function'" -ForegroundColor White
Write-Host "4. Paste the contents of: functions\start-game-handler\index.ts" -ForegroundColor White
Write-Host ""
Write-Host "Option 2: Install WSL and deploy from there" -ForegroundColor Green
Write-Host "1. Install WSL: wsl --install" -ForegroundColor White
Write-Host "2. Open WSL terminal" -ForegroundColor White
Write-Host "3. Run: cd /mnt/c/Projects/recognition-combine/supabase" -ForegroundColor White
Write-Host "4. Run: npx supabase functions deploy start-game-handler --project-ref xmyxuvuimebjltnaamox" -ForegroundColor White
Write-Host ""
Write-Host "Option 3: Use GitHub Actions or CI/CD" -ForegroundColor Green
Write-Host "Set up a GitHub Action to deploy your Edge Functions automatically" -ForegroundColor White

Write-Host "`n=== IMPORTANT ===" -ForegroundColor Yellow
Write-Host "The Edge Function has been fixed to work without service role key issues." -ForegroundColor White
Write-Host "It will now use the user's auth token for querying players_data." -ForegroundColor White