// Test to capture the actual Edge Function error
const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;

const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };

async function captureEdgeFunctionError() {
  console.log('=== Capturing Edge Function Error ===\n');
  
  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: false,
      executablePath: process.platform === 'linux' ? '/usr/bin/chromium-browser' : undefined,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Capture console logs
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('Edge Function') || text.includes('ERROR') || text.includes('start-game')) {
        console.log('[Brows<PERSON> Console]', text);
      }
    });
    
    // Capture network responses
    page.on('response', response => {
      const url = response.url();
      if (url.includes('start-game-handler')) {
        console.log('\n=== Edge Function Response ===');
        console.log('URL:', url);
        console.log('Status:', response.status());
        console.log('Headers:', response.headers());
        
        response.text().then(body => {
          console.log('Body:', body);
          try {
            const json = JSON.parse(body);
            console.log('Parsed:', JSON.stringify(json, null, 2));
          } catch (e) {
            // Not JSON
          }
        }).catch(err => {
          console.log('Error reading body:', err);
        });
      }
    });
    
    // Navigate to app
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Login flow
    const multiplayerButton = await page.evaluateHandle(() => 
      Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Multiplayer Mode'))
    );
    await multiplayerButton.click();
    await page.waitForSelector('input[type="text"]', { visible: true });
    
    await page.type('input[type="text"]', HOST_CREDENTIALS.username);
    await page.type('input[type="password"]', HOST_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForFunction(() => 
      Array.from(document.querySelectorAll('button')).some(b => b.textContent.includes('Create New Room')),
      { timeout: 10000 }
    );
    
    // Create room
    const createButton = await page.evaluateHandle(() => 
      Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Create New Room'))
    );
    await createButton.click();
    
    await page.waitForFunction(() => 
      Array.from(document.querySelectorAll('button')).some(b => b.textContent.includes('Ready'))
    );
    
    // Mark ready
    const readyButton = await page.evaluateHandle(() => 
      Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Ready') && !b.textContent.includes('✓'))
    );
    await readyButton.click();
    
    await page.waitForFunction(() => 
      Array.from(document.querySelectorAll('button')).some(b => b.textContent.includes('Start Game'))
    );
    
    console.log('\nClicking Start Game button - watch for Edge Function response above...\n');
    
    // Click start game and wait for response
    const startButton = await page.evaluateHandle(() => 
      Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Start Game'))
    );
    await startButton.click();
    
    // Wait a bit for the response to be captured
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\nTest complete. Check the Edge Function response above for error details.');
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    if (browser) {
      console.log('\nClosing browser in 10 seconds...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      await browser.close();
    }
  }
}

captureEdgeFunctionError();