const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://xmyxuvuimebjltnaamox.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkPlayersTable() {
  console.log('=== Checking players_data table ===\n');
  
  try {
    // Check if we can query the table
    const { data, error, count } = await supabase
      .from('players_data')
      .select('*', { count: 'exact', head: true });
    
    if (error) {
      console.error('Error querying players_data table:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      return;
    }
    
    console.log(`✓ Table exists with ${count} total rows`);
    
    // Get a sample of players with images
    const { data: samplePlayers, error: sampleError } = await supabase
      .from('players_data')
      .select('id, player_name, team_name, local_image_path')
      .not('local_image_path', 'is', null)
      .limit(5);
    
    if (sampleError) {
      console.error('Error getting sample players:', sampleError);
    } else {
      console.log('\nSample players with images:');
      if (samplePlayers && samplePlayers.length > 0) {
        samplePlayers.forEach(p => {
          console.log(`- ${p.player_name} (${p.team_name}) - Image: ${p.local_image_path}`);
        });
      } else {
        console.log('No players found with images');
      }
    }
    
  } catch (err) {
    console.error('Exception:', err);
  }
}

checkPlayersTable();