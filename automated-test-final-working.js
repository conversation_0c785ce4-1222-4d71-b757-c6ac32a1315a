/**
 * Final Working Automated Multiplayer Test
 * Properly handles room joining by clicking the game container
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Test configuration
const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → Round at 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → Round at 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → Round at 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'Only P1 → Round at 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1 at 1s, P2 at 3s → Round at 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

// Create screenshots directory
const screenshotsDir = path.join(__dirname, 'test-screenshots-final');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

let screenshotCounter = 0;
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

async function takeScreenshot(page, name) {
  const filename = `${String(screenshotCounter++).padStart(3, '0')}-${name}.png`;
  const filepath = path.join(screenshotsDir, filename);
  await page.screenshot({ path: filepath, fullPage: true });
  console.log(`  📸 ${filename}`);
}

async function signIn(page, credentials, playerName) {
  console.log(`\n🔐 Signing in ${playerName}...`);
  
  // Click Login button
  await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const loginBtn = buttons.find(b => b.textContent && b.textContent.includes('Login'));
    if (loginBtn) loginBtn.click();
  });
  
  await delay(2000);
  
  // Fill credentials
  await page.type('input[placeholder="Username or Email"]', credentials.email);
  await page.type('input[placeholder="Password"]', credentials.password);
  
  // Submit form
  await page.evaluate(() => {
    const form = document.querySelector('form');
    if (form) form.requestSubmit();
  });
  
  await delay(5000);
  console.log(`  ✅ ${playerName} signed in`);
}

async function navigateToMultiplayer(page, playerName) {
  console.log(`  ${playerName} → Multiplayer Mode`);
  
  await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const mpBtn = buttons.find(b => b.textContent === 'Multiplayer Mode');
    if (mpBtn) mpBtn.click();
  });
  
  await delay(3000);
}

async function createRoom(page) {
  console.log('\n🏠 Creating room...');
  
  await takeScreenshot(page, 'before-create-room');
  
  // Click Host Game
  await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('button'));
    const hostBtn = buttons.find(b => b.textContent && b.textContent.trim() === 'Host Game');
    if (hostBtn) hostBtn.click();
  });
  
  await delay(3000);
  await takeScreenshot(page, 'room-created');
  
  return "fresh's Game";
}

async function joinRoom(page, roomName) {
  console.log(`\n🏠 Player 2 joining "${roomName}"...`);
  
  await takeScreenshot(page, 'p2-before-join');
  
  // More robust room joining - find and click the game room container
  const joined = await page.evaluate((targetRoomName) => {
    console.log('Looking for room:', targetRoomName);
    
    // Strategy 1: Find elements containing the room name and host info
    const elements = Array.from(document.querySelectorAll('*'));
    
    // Look for elements that contain both the room name and "Players:" text
    const roomElements = elements.filter(el => {
      const text = el.textContent || '';
      const hasRoomName = text.includes(targetRoomName);
      const hasPlayerCount = text.includes('Players:') || text.includes('/4');
      const hasHost = text.includes('Host:');
      const isNotTooLarge = el.children.length < 20; // Not a huge container
      
      return hasRoomName && (hasPlayerCount || hasHost) && isNotTooLarge;
    });
    
    console.log('Found', roomElements.length, 'potential room elements');
    
    // Find the most specific element (smallest container)
    let bestElement = null;
    let smallestSize = Infinity;
    
    for (const el of roomElements) {
      const rect = el.getBoundingClientRect();
      const size = rect.width * rect.height;
      
      if (size > 0 && size < smallestSize) {
        smallestSize = size;
        bestElement = el;
      }
    }
    
    if (bestElement) {
      console.log('Clicking on room element:', bestElement.tagName, bestElement.className);
      bestElement.click();
      return true;
    }
    
    // Strategy 2: Look for clickable parent of room name
    const roomNameEl = elements.find(el => 
      el.textContent === targetRoomName && el.children.length === 0
    );
    
    if (roomNameEl) {
      let parent = roomNameEl.parentElement;
      let attempts = 0;
      
      while (parent && attempts < 5) {
        const style = window.getComputedStyle(parent);
        const isClickable = style.cursor === 'pointer' || 
                          parent.onclick !== null ||
                          parent.tagName === 'BUTTON' ||
                          parent.hasAttribute('role');
        
        if (isClickable || parent.textContent.includes('Players:')) {
          console.log('Clicking parent element:', parent.tagName);
          parent.click();
          return true;
        }
        
        parent = parent.parentElement;
        attempts++;
      }
    }
    
    // Strategy 3: Try any div/button containing the room name
    const fallbackElements = Array.from(document.querySelectorAll('div, button, [role="button"]'));
    for (const el of fallbackElements) {
      if (el.textContent && el.textContent.includes(targetRoomName)) {
        console.log('Fallback click on:', el.tagName);
        el.click();
        return true;
      }
    }
    
    return false;
  }, roomName);
  
  if (!joined) {
    console.log('  ⚠️  First join attempt failed, trying alternative method...');
    
    // Alternative: simulate a click at coordinates
    await page.evaluate((targetRoomName) => {
      const elements = Array.from(document.querySelectorAll('*'));
      const roomEl = elements.find(el => 
        el.textContent && 
        el.textContent.includes(targetRoomName) && 
        el.textContent.includes('Players:')
      );
      
      if (roomEl) {
        const rect = roomEl.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top + rect.height / 2;
        
        const clickEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true,
          clientX: x,
          clientY: y
        });
        
        roomEl.dispatchEvent(clickEvent);
        console.log('Dispatched click event at', x, y);
      }
    }, roomName);
  }
  
  await delay(3000);
  await takeScreenshot(page, 'p2-after-join-attempt');
  
  // Verify join was successful by checking if we're in the room
  const inRoom = await page.evaluate(() => {
    const pageText = document.body.textContent || '';
    return pageText.includes('Ready') || pageText.includes('Waiting for game');
  });
  
  if (inRoom) {
    console.log('  ✅ Player 2 successfully joined room');
  } else {
    console.log('  ⚠️  Player 2 may not have joined successfully');
  }
  
  return inRoom;
}

// Monitoring code
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0,
    submissions: []
  };
  
  console.log('🔍 Monitoring active');
  
  let lastQuestion = null;
  setInterval(() => {
    const questionEl = document.querySelector('h2');
    const question = questionEl?.textContent || '';
    
    if (question && question.includes('NFL PLAYER') && question !== lastQuestion) {
      const now = Date.now();
      
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('🎮 Game started at', new Date(now).toISOString());
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({
          round: window.testData.currentRound++,
          time: elapsed
        });
        console.log('📍 Round', window.testData.currentRound, 'advanced at', elapsed, 'ms');
      }
      
      lastQuestion = question;
    }
  }, 100);
  
  window.submitAnswerAt = function(targetMs) {
    return new Promise((resolve) => {
      const trySubmit = () => {
        if (!window.testData.gameStartTime) {
          setTimeout(trySubmit, 100);
          return;
        }
        
        const elapsed = Date.now() - window.testData.gameStartTime;
        const delay = targetMs - elapsed;
        
        const submit = () => {
          // Look for answer buttons (4 player name choices)
          const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
            const text = b.textContent || '';
            const isPlayerName = text.length > 0 && 
                               text.length < 30 &&
                               text.match(/^[A-Za-z\\s\\.\\-\\']+$/);
            const isNotSystemButton = !['Ready', 'Start', 'Mode', 'Sign', 'Create', 'Join', 'Leave', 'Host'].some(word => text.includes(word));
            const isVisible = b.offsetParent !== null;
            const isEnabled = !b.disabled;
            
            return isPlayerName && isNotSystemButton && isVisible && isEnabled;
          });
          
          console.log('Found', buttons.length, 'answer buttons');
          
          if (buttons.length >= 4) {
            const chosen = buttons[Math.floor(Math.random() * 4)];
            const submitTime = Date.now() - window.testData.gameStartTime;
            console.log('✅ Submitting:', chosen.textContent, 'at', submitTime, 'ms');
            window.testData.submissions.push({
              time: submitTime,
              answer: chosen.textContent
            });
            chosen.click();
            resolve(true);
          } else {
            setTimeout(submit, 100);
          }
        };
        
        if (delay > 0) {
          setTimeout(submit, delay);
        } else {
          submit();
        }
      };
      
      trySubmit();
    });
  };
`;

async function runTest() {
  console.log('🎯 Final Working Automated Multiplayer Test');
  console.log('==========================================\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Browser options
    const browserOptions = {
      headless: false,
      executablePath: '/usr/bin/chromium-browser',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--window-size=1200,900'
      ],
      defaultViewport: null,
      protocolTimeout: 60000
    };
    
    // Launch browsers
    console.log('🚀 Launching browsers...');
    browser1 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=0,0']
    });
    
    browser2 = await puppeteer.launch({
      ...browserOptions,
      args: [...browserOptions.args, '--window-position=600,0']
    });
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    
    // Set longer timeouts
    page1.setDefaultTimeout(30000);
    page2.setDefaultTimeout(30000);
    
    // Enable console logging
    page1.on('console', msg => {
      if (msg.type() === 'log') console.log('  [P1]', msg.text());
    });
    
    page2.on('console', msg => {
      if (msg.type() === 'log') console.log('  [P2]', msg.text());
    });
    
    // Load game
    console.log('\n📱 Loading game...');
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2' }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2' })
    ]);
    await delay(3000);
    
    // Inject monitoring
    await page1.evaluate(monitoringCode);
    await page2.evaluate(monitoringCode);
    console.log('  ✅ Monitoring code injected');
    
    // Sign in both players
    await signIn(page1, CONFIG.player1, 'Player 1');
    await signIn(page2, CONFIG.player2, 'Player 2');
    
    // Navigate to multiplayer
    console.log('\n🎮 Entering Multiplayer Mode...');
    await navigateToMultiplayer(page1, 'Player 1');
    await navigateToMultiplayer(page2, 'Player 2');
    
    // Create room with Player 1
    const roomName = await createRoom(page1);
    console.log(`  ✅ Room created: "${roomName}"`);
    
    // Join room with Player 2
    const joined = await joinRoom(page2, roomName);
    
    if (!joined) {
      throw new Error('Player 2 failed to join room');
    }
    
    // Ready up
    console.log('\n🎮 Getting ready...');
    await delay(2000);
    
    // Both players ready
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready' || b.textContent === 'Ready Up'
      );
      if (btn) {
        console.log('Player 1 clicking Ready');
        btn.click();
      }
    });
    
    await delay(1000);
    
    await page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready' || b.textContent === 'Ready Up'
      );
      if (btn) {
        console.log('Player 2 clicking Ready');
        btn.click();
      }
    });
    
    await delay(2000);
    await takeScreenshot(page1, 'both-ready');
    
    // Start game
    console.log('  Starting game...');
    await page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Start Game'
      );
      if (btn) {
        console.log('Host clicking Start Game');
        btn.click();
      }
    });
    
    await delay(3000);
    await takeScreenshot(page1, 'game-started');
    console.log('  ✅ Game started!\n');
    
    // Run test scenarios
    console.log('📊 Running test scenarios:');
    console.log('========================');
    
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const scenario = CONFIG.scenarios[i];
      console.log(`\nTest ${i + 1}: ${scenario.name}`);
      
      // Take screenshot before each test
      await takeScreenshot(page1, `test-${i + 1}-start`);
      
      // Schedule submissions
      const submissions = [];
      
      if (scenario.p1 !== null) {
        submissions.push(page1.evaluate(`window.submitAnswerAt(${scenario.p1})`));
        console.log(`  P1 will answer at ${scenario.p1}ms`);
      }
      
      if (scenario.p2 !== null) {
        submissions.push(page2.evaluate(`window.submitAnswerAt(${scenario.p2})`));
        console.log(`  P2 will answer at ${scenario.p2}ms`);
      }
      
      // Wait for submissions
      await Promise.all(submissions);
      
      console.log('  ⏳ Waiting for round to advance...');
      await delay(8000);
      
      // Get results from both players
      const [timing1, timing2] = await Promise.all([
        page1.evaluate((index) => {
          const changes = window.testData.roundChanges || [];
          return changes[index];
        }, i),
        page2.evaluate((index) => {
          const changes = window.testData.roundChanges || [];
          return changes[index];
        }, i)
      ]);
      
      const timing = timing1 || timing2;
      
      if (timing) {
        const diff = Math.abs(timing.time - scenario.expected);
        const passed = diff <= 500;
        
        console.log(`  Expected: ${scenario.expected}ms`);
        console.log(`  Actual: ${timing.time}ms`);
        console.log(`  Difference: ${diff}ms`);
        console.log(`  Result: ${passed ? '✅ PASS' : '❌ FAIL'}`);
        
        results.push({ 
          ...scenario, 
          actual: timing.time, 
          difference: diff,
          passed 
        });
      } else {
        console.log('  ❌ No round change detected');
        results.push({ 
          ...scenario, 
          actual: null, 
          difference: null,
          passed: false 
        });
      }
      
      // Take screenshot after test
      await takeScreenshot(page1, `test-${i + 1}-complete`);
    }
    
    // Final summary
    console.log('\n\n📈 FINAL RESULTS');
    console.log('================');
    
    const passed = results.filter(r => r.passed).length;
    const failed = results.filter(r => !r.passed).length;
    const total = results.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed} ✅`);
    console.log(`Failed: ${failed} ❌`);
    console.log(`Success Rate: ${((passed/total) * 100).toFixed(1)}%\n`);
    
    console.log('Detailed Results:');
    results.forEach((r, i) => {
      console.log(`${i + 1}. ${r.name}`);
      if (r.actual !== null) {
        console.log(`   Expected: ${r.expected}ms, Actual: ${r.actual}ms, Diff: ${r.difference}ms`);
      }
      console.log(`   Status: ${r.passed ? '✅ PASS' : '❌ FAIL'}`);
    });
    
    if (passed === total) {
      console.log('\n🎉 ALL TESTS PASSED! 🎉');
      console.log('\nMultiplayer round advance timing verified:');
      console.log('• 3-second transition after all players submit');
      console.log('• 7-second maximum round duration');
      console.log('• Timing accuracy within ±500ms tolerance');
    }
    
    console.log(`\n📸 Screenshots saved to: ${screenshotsDir}`);
    console.log('\n✅ Test complete! Press Ctrl+C to exit.\n');
    
    // Keep alive
    process.on('SIGINT', async () => {
      console.log('\n🔚 Closing browsers...');
      if (browser1) await browser1.close();
      if (browser2) await browser2.close();
      process.exit(0);
    });
    
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    
    // Take error screenshots
    try {
      if (page1) await takeScreenshot(page1, 'error-p1');
      if (page2) await takeScreenshot(page2, 'error-p2');
    } catch (e) {
      console.error('Failed to take error screenshots:', e.message);
    }
    
    // Clean up
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    
    process.exit(1);
  }
}

// Run test
console.log('Starting automated multiplayer test...\n');
runTest().catch(console.error);