const fetch = require('node-fetch');

// Replace with your actual values
const SUPABASE_URL = 'https://xmyxuvuimebjltnaamox.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY';

async function testStartGameHandler() {
  try {
    // First, we need to authenticate
    console.log('1. Authenticating...');
    const authResponse = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'test123'
      })
    });

    if (!authResponse.ok) {
      console.error('Auth failed:', await authResponse.text());
      return;
    }

    const authData = await authResponse.json();
    console.log('✓ Authenticated successfully');

    // Get a test room ID
    console.log('\n2. Finding a test room...');
    const roomsResponse = await fetch(`${SUPABASE_URL}/rest/v1/game_rooms?status=eq.waiting&select=id,host_id`, {
      headers: {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${authData.access_token}`
      }
    });

    const rooms = await roomsResponse.json();
    
    if (!rooms || rooms.length === 0) {
      console.error('No waiting rooms found. Please create a room first.');
      return;
    }

    const testRoom = rooms[0];
    console.log(`✓ Found room: ${testRoom.id} (host: ${testRoom.host_id})`);

    // Call the edge function
    console.log('\n3. Calling start-game-handler edge function...');
    const edgeResponse = await fetch(`${SUPABASE_URL}/functions/v1/start-game-handler`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.access_token}`,
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        roomId: testRoom.id
      })
    });

    console.log('Response status:', edgeResponse.status);
    console.log('Response headers:', edgeResponse.headers.raw());
    
    const responseText = await edgeResponse.text();
    console.log('\nResponse body:');
    console.log(responseText);

    // Try to parse as JSON
    try {
      const responseData = JSON.parse(responseText);
      console.log('\nParsed response:');
      console.log(JSON.stringify(responseData, null, 2));
      
      if (responseData.firstQuestion) {
        console.log('\n✓ Edge function returned question data!');
        console.log('Question structure:', {
          hasQuestionId: !!responseData.firstQuestion.questionId,
          hasCorrectPlayerId: !!responseData.firstQuestion.correctPlayerId,
          hasImageUrl: !!responseData.firstQuestion.imageUrl,
          hasChoices: !!responseData.firstQuestion.choices,
          hasCorrectChoiceName: !!responseData.firstQuestion.correctChoiceName,
          choicesCount: responseData.firstQuestion.choices?.length
        });
      }
    } catch (e) {
      console.log('Could not parse as JSON:', e.message);
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

testStartGameHandler();