# Apply Migrations Using Environment Variable
# This script sets the database password as an environment variable before running migrations

Write-Host "Setting up environment and applying migrations..." -ForegroundColor Cyan
Write-Host ""

# Set the database password as environment variable
$env:PGPASSWORD = '9ACv!PEKEN5$@Mh'

Write-Host "Attempting to apply migrations with --include-all flag..." -ForegroundColor Yellow
Write-Host ""

# Run the migration command
$result = supabase db push --include-all 2>&1

# Clear the password from environment
Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue

# Display result
Write-Host $result

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "Migrations applied successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Deploy the edge functions using individual deployment scripts"
    Write-Host "2. Test the multiplayer transition feature"
} else {
    Write-Host ""
    Write-Host "Migration failed. Let's try a different approach..." -ForegroundColor Red
    Write-Host ""
    Write-Host "Alternative approach:" -ForegroundColor Yellow
    Write-Host "1. Manually run the safe migration SQL in Supabase Dashboard"
    Write-Host "2. Use the SQL Editor in: https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/editor"
    Write-Host "3. Copy and paste the content from: supabase/migrations/20250629091952_safe_add_missing_columns.sql"
}