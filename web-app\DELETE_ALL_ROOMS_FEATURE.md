# Delete All Rooms Feature

## Overview

The "Delete All Rooms" feature allows users to quickly delete all of their waiting game rooms in the multiplayer lobby. This is a convenience feature for users who have created multiple test rooms or want to clean up their hosted games.

## Feature Details

### Location
- **UI Location**: Center panel header in the multiplayer lobby
- **Position**: Next to the "Refresh List" button
- **Visibility**: Only visible to authenticated users

### Functionality

#### What It Does
- Deletes ALL waiting rooms hosted by the current user
- Automatically handles the deletion of associated players (respects foreign key constraints)
- Refreshes the lobby list after successful deletion
- Shows confirmation dialog before proceeding

#### What It Doesn't Do
- **Does NOT delete active games** - Only waiting rooms are affected
- **Does NOT delete other users' rooms** - Only rooms hosted by the current user
- **Does NOT delete finished games** - Only waiting status rooms

### Security & Permissions

The feature respects the existing Row Level Security (RLS) policies:

```sql
-- Only allows deletion of user's own waiting rooms
create policy "Allow host to delete a waiting room"
on "public"."game_rooms"
as permissive
for delete
to authenticated
using (((auth.uid() = host_id) AND (status = 'waiting'::text)));
```

### User Experience

#### Button Appearance
- **Color**: Red background (`bg-red-600 hover:bg-red-700`)
- **Size**: Small (`text-xs px-3 py-1`)
- **Text**: "Delete All Rooms" (changes to "Deleting..." when in progress)
- **Tooltip**: "Delete all your waiting rooms"

#### Confirmation Flow
1. User clicks "Delete All Rooms" button
2. Browser shows confirmation dialog with warning message
3. If confirmed, deletion process begins
4. Success notification shows deleted room names
5. Lobby list automatically refreshes

#### Error Handling
- Shows error messages in the lobby error area
- Handles network failures gracefully
- Provides specific error messages for different failure types

### Implementation Details

#### Function: `handleDeleteAllRooms`
**Location**: `web-app/src/app/page.tsx`

**Process**:
1. **Authentication Check**: Verifies user is logged in
2. **Confirmation Dialog**: Shows browser confirmation with warning
3. **Room Fetching**: Queries for user's waiting rooms
4. **Player Cleanup**: Deletes associated players first (foreign key constraints)
5. **Room Deletion**: Deletes the rooms themselves
6. **UI Update**: Refreshes lobby and shows success message

**Error Scenarios Handled**:
- User not authenticated
- No rooms to delete
- Database errors during fetching
- Database errors during player deletion
- Database errors during room deletion
- Network connectivity issues

#### Database Operations
```typescript
// 1. Fetch user's waiting rooms
const { data: userRooms } = await supabase
  .from('game_rooms')
  .select('id, title, status, host_id')
  .eq('host_id', user.id)
  .eq('status', 'waiting');

// 2. Delete players first (foreign key constraints)
await supabase
  .from('game_players')
  .delete()
  .in('room_id', roomIds);

// 3. Delete rooms
await supabase
  .from('game_rooms')
  .delete()
  .in('id', roomIds);
```

### Testing Scenarios

#### Positive Tests
1. **Single Room Deletion**: Create one waiting room, delete all
2. **Multiple Room Deletion**: Create several waiting rooms, delete all
3. **Mixed Room Types**: Create waiting + active rooms, verify only waiting rooms deleted
4. **No Rooms**: Click button when no rooms exist, verify appropriate message

#### Negative Tests
1. **Unauthenticated User**: Button should not be visible
2. **Network Failure**: Should show error message and not crash
3. **Permission Denied**: Should respect RLS policies
4. **Cancellation**: User cancels confirmation dialog, no deletion occurs

#### Edge Cases
1. **Rooms Created by Others**: Should not delete rooms hosted by other users
2. **Active Games**: Should not delete rooms with status 'active'
3. **Concurrent Deletion**: Handle race conditions if rooms are deleted by other means

### Future Enhancements

#### Potential Improvements
1. **Selective Deletion**: Allow users to select specific rooms to delete
2. **Undo Functionality**: Temporary recovery option for accidental deletions
3. **Toast Notifications**: Replace alert() with proper toast notifications
4. **Batch Size Limits**: Handle very large numbers of rooms more efficiently
5. **Progress Indicator**: Show progress for large deletion operations

#### Admin Features (Future)
- Could be extended to allow admin users to delete any rooms
- Bulk cleanup of stale rooms across all users
- Room management dashboard for administrators

### Related Files

- **Main Implementation**: `web-app/src/app/page.tsx`
- **Database Policies**: `supabase/migrations/` (RLS policies)
- **Existing Cleanup**: `supabase/functions/stale-room-janitor/` (automated cleanup)

### Dependencies

- **Supabase Client**: For database operations
- **React Hooks**: `useCallback` for function memoization
- **Existing Functions**: `fetchAndSetGameRooms` for lobby refresh
- **UI Components**: Button component from the UI library

---

## Usage Instructions

1. **Navigate** to the multiplayer lobby
2. **Ensure** you are signed in (button only visible to authenticated users)
3. **Click** the red "Delete All Rooms" button in the center panel header
4. **Confirm** the deletion in the browser dialog
5. **Wait** for the success notification and lobby refresh

**Warning**: This action cannot be undone. Only your own waiting rooms will be deleted.
