import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CheckCircle, XCircle } from 'lucide-react';
import type { RecentAnswer, PlayerData } from '@/types';

interface Props {
  answers: RecentAnswer[];
  onSelect: (player: PlayerData) => void;
}

export function RecentAnswersList({ answers, onSelect }: Props) {
  return (
    <div className="space-y-2 text-base text-center">
      {answers.length === 0 && (
        <p className="text-gray-400 text-center mt-4">No answers yet.</p>
      )}
      {answers.slice().reverse().map((answer) => (
        <div
          key={answer.timestamp}
          className="flex items-center space-x-2 p-1 bg-gray-800 bg-opacity-50 rounded cursor-pointer hover:bg-gray-700 transition-colors justify-center"
          onClick={() => onSelect(answer.player)}
        >
          <Avatar className="h-6 w-6 flex-shrink-0">
            <AvatarImage src={answer.player.local_image_path ? `/players_images/${answer.player.local_image_path}` : undefined} alt={answer.player.player_name} />
            <AvatarFallback className="text-[8px]">?</AvatarFallback>
          </Avatar>
          <span className={`flex-1 truncate text-lg font-extrabold text-center ${answer.isCorrect ? 'text-green-400' : 'text-red-400'}`}>{answer.player.player_name}</span>
          {answer.isCorrect
            ? <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
            : <XCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
          }
        </div>
      ))}
    </div>
  );
} 