import { useEffect, useRef } from 'react';

/**
 * Hook to ensure multiplayer room state stays synchronized
 * This addresses the issue where hosts don't see new players joining
 */
export function useMultiplayerSync(
  isHost: boolean,
  isInWaitingRoom: boolean,
  activeRoomId: string | null,
  fetchPlayersInActiveRoom: ((roomId: string, caller?: string) => Promise<void>) | null
) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    // Only run for hosts in waiting rooms
    if (!isHost || !isInWaitingRoom || !activeRoomId || !fetchPlayersInActiveRoom) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }
    
    console.log('[MultiplayerSync] Starting periodic sync for host in waiting room');
    
    // Fetch immediately
    fetchPlayersInActiveRoom(activeRoomId, 'multiplayer_sync_initial');
    
    // Then fetch every 3 seconds while in waiting room
    intervalRef.current = setInterval(() => {
      console.log('[MultiplayerSync] Periodic player fetch for host');
      fetchPlayersInActiveRoom(activeRoomId, 'multiplayer_sync_periodic');
    }, 3000);
    
    return () => {
      if (intervalRef.current) {
        console.log('[MultiplayerSync] Stopping periodic sync');
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isHost, isInWaitingRoom, activeRoomId, fetchPlayersInActiveRoom]);
}