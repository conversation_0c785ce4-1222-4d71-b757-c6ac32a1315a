const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };
const SCREENSHOT_DIR = path.join(__dirname, 'test-screenshots-working');

// Helper to take screenshots
async function takeScreenshot(page, name, prefix) {
  try {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${prefix}-${name}-${timestamp}.png`;
    const filepath = path.join(SCREENSHOT_DIR, filename);
    await page.screenshot({ path: filepath, fullPage: true });
    console.log(`[${prefix}] Screenshot: ${filename}`);
  } catch (error) {
    console.error(`[${prefix}] Screenshot failed:`, error.message);
  }
}

// Main test function
async function testMultiplayerComplete() {
  console.log('=== MULTIPLAYER COMPLETE FLOW TEST ===\n');
  console.log('Testing full multiplayer game flow with timing verification');
  console.log('---\n');

  const browserOptions = {
    headless: true,
    defaultViewport: { width: 1200, height: 900 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  };

  // Find browser
  let executablePath = '/usr/bin/chromium-browser';
  try {
    await fs.access(executablePath);
    browserOptions.executablePath = executablePath;
  } catch (e) {
    // Use bundled browser
  }

  let hostBrowser, guestBrowser;
  let hostPage, guestPage;

  try {
    // === PHASE 1: Launch and Navigate ===
    console.log('=== PHASE 1: Launching browsers ===');
    hostBrowser = await puppeteer.launch(browserOptions);
    guestBrowser = await puppeteer.launch(browserOptions);
    
    hostPage = await hostBrowser.newPage();
    guestPage = await guestBrowser.newPage();
    
    await Promise.all([
      hostPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' }),
      guestPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' })
    ]);
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    console.log('✓ Browsers launched and navigated');
    
    // === PHASE 2: Authentication ===
    console.log('\n=== PHASE 2: Authenticating users ===');
    
    // Helper to login quickly
    async function quickAuth(page, credentials, name) {
      // Click Multiplayer Mode to trigger auth
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Fill and submit
      await page.type('input[type="email"], input[type="text"]:not([type="password"])', credentials.username);
      await page.type('input[type="password"]', credentials.password);
      await page.keyboard.press('Enter');
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Click Multiplayer Mode again to enter MP mode
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log(`[${name}] ✓ Authenticated and in multiplayer mode`);
    }
    
    await quickAuth(hostPage, HOST_CREDENTIALS, 'HOST');
    await quickAuth(guestPage, GUEST_CREDENTIALS, 'GUEST');
    
    // === PHASE 3: Create Room ===
    console.log('\n=== PHASE 3: Host creating room ===');
    
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Host Game');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verify room created
    const hostRoomState = await hostPage.evaluate(() => {
      const playerCount = document.body.textContent.match(/Players.*\((\d+)\/\d+\)/);
      return {
        inRoom: document.body.textContent.includes('Room:'),
        playerCount: playerCount ? playerCount[0] : null
      };
    });
    
    console.log('[HOST] Room state:', hostRoomState);
    if (!hostRoomState.inRoom) throw new Error('Host failed to create room');
    
    // === PHASE 4: Guest Joins ===
    console.log('\n=== PHASE 4: Guest joining room ===');
    
    // Refresh room list
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent.includes('Refresh'));
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Click on room
    await guestPage.evaluate(() => {
      const room = Array.from(document.querySelectorAll('div, tr'))
        .find(el => el.textContent.includes('fresh') && el.textContent.includes('Game'));
      if (room) room.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Click Join
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Join');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verify guest joined
    const guestInRoom = await guestPage.evaluate(() => {
      return document.body.textContent.includes('Room:') || 
             document.body.textContent.includes('Ready');
    });
    
    if (!guestInRoom) throw new Error('Guest failed to join room');
    console.log('[GUEST] ✓ Successfully joined room');
    
    // === PHASE 5: Both Ready Up ===
    console.log('\n=== PHASE 5: Players readying up ===');
    
    // Wait for room state to stabilize
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Host clicks ready
    const hostReadyClicked = await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent.includes('Ready') && !b.textContent.includes('Refresh'));
      if (btn && !btn.disabled) {
        btn.click();
        return true;
      }
      return false;
    });
    
    console.log('[HOST] Ready clicked:', hostReadyClicked);
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Guest clicks ready
    const guestReadyClicked = await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent.includes('Ready') && !b.textContent.includes('Refresh'));
      if (btn && !btn.disabled) {
        btn.click();
        return true;
      }
      return false;
    });
    
    console.log('[GUEST] Ready clicked:', guestReadyClicked);
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await takeScreenshot(hostPage, 'both-ready', 'host');
    await takeScreenshot(guestPage, 'both-ready', 'guest');
    
    // === PHASE 6: Start Game ===
    console.log('\n=== PHASE 6: Starting game ===');
    
    // Wait for Start Game button to appear
    let startButtonFound = false;
    for (let i = 0; i < 5; i++) {
      const hasStartButton = await hostPage.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent.includes('Start Game'));
        return btn && !btn.disabled;
      });
      
      if (hasStartButton) {
        startButtonFound = true;
        break;
      }
      
      console.log(`[HOST] Waiting for Start Game button... (${i + 1}/5)`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    if (!startButtonFound) {
      // Debug why Start Game isn't appearing
      const debugInfo = await hostPage.evaluate(() => {
        const players = Array.from(document.querySelectorAll('*'))
          .filter(el => el.textContent.includes('Ready'))
          .map(el => el.textContent.substring(0, 50));
        
        return {
          playersList: players,
          hasNeedMorePlayers: document.body.textContent.includes('Need') && 
                             document.body.textContent.includes('more player'),
          playerCount: document.body.textContent.match(/Players.*\((\d+)\/\d+\)/)?.[0]
        };
      });
      
      console.log('[HOST] Debug info:', debugInfo);
      await takeScreenshot(hostPage, 'no-start-button', 'host');
      throw new Error('Start Game button not found');
    }
    
    // Click Start Game
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent.includes('Start Game'));
      if (btn) btn.click();
    });
    
    console.log('[HOST] ✓ Game started');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === PHASE 7: Play Rounds with Timing ===
    console.log('\n=== PHASE 7: Playing game rounds ===');
    
    const roundTimings = [];
    
    for (let round = 1; round <= 3; round++) {
      console.log(`\n--- Round ${round} ---`);
      const roundStart = Date.now();
      
      // Wait for question
      let questionAppeared = false;
      for (let i = 0; i < 10; i++) {
        const hasQuestion = await Promise.all([
          hostPage.evaluate(() => !!document.querySelector('img[alt*="player"], img[alt*="Player"], .player-image')),
          guestPage.evaluate(() => !!document.querySelector('img[alt*="player"], img[alt*="Player"], .player-image'))
        ]);
        
        if (hasQuestion[0] && hasQuestion[1]) {
          questionAppeared = true;
          break;
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      if (!questionAppeared) {
        console.log(`Round ${round}: Question did not appear`);
        continue;
      }
      
      await takeScreenshot(hostPage, `round${round}-question`, 'host');
      await takeScreenshot(guestPage, `round${round}-question`, 'guest');
      
      // Answer based on round to test different timing scenarios
      if (round === 1) {
        // Both answer quickly
        console.log('Testing: Both answer quickly');
        
        await Promise.all([
          hostPage.evaluate(() => {
            const btn = document.querySelector('button.choice-button, button[class*="choice"]');
            if (btn) btn.click();
          }),
          guestPage.evaluate(() => {
            const btn = document.querySelector('button.choice-button, button[class*="choice"]');
            if (btn) btn.click();
          })
        ]);
        
      } else if (round === 2) {
        // Only host answers
        console.log('Testing: Only host answers');
        
        await hostPage.evaluate(() => {
          const btn = document.querySelector('button.choice-button, button[class*="choice"]');
          if (btn) btn.click();
        });
        
      } else {
        // Wait for timeout
        console.log('Testing: Timeout (no answers)');
      }
      
      // Wait for round to advance
      const advanceStart = Date.now();
      let roundAdvanced = false;
      
      for (let i = 0; i < 10; i++) {
        const inTransition = await hostPage.evaluate(() => {
          return document.body.textContent.includes('Next question in') ||
                 document.body.textContent.includes('Game Over');
        });
        
        if (inTransition) {
          roundAdvanced = true;
          break;
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      const roundDuration = Date.now() - roundStart;
      const advanceDuration = Date.now() - advanceStart;
      
      roundTimings.push({
        round,
        duration: roundDuration,
        advanceTime: advanceDuration,
        advanced: roundAdvanced
      });
      
      console.log(`Round ${round} complete: ${roundDuration}ms total, ${advanceDuration}ms to advance`);
      
      if (!roundAdvanced) break;
      
      // Wait for next round
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    // === PHASE 8: Results ===
    console.log('\n=== PHASE 8: Game Results ===');
    
    const gameComplete = await hostPage.evaluate(() => {
      return document.body.textContent.includes('Game Over') ||
             document.body.textContent.includes('Final Score');
    });
    
    if (gameComplete) {
      console.log('✓ Game completed successfully');
      await takeScreenshot(hostPage, 'game-complete', 'host');
      await takeScreenshot(guestPage, 'game-complete', 'guest');
    }
    
    // Analyze timing
    console.log('\n=== Timing Analysis ===');
    roundTimings.forEach(timing => {
      console.log(`Round ${timing.round}: ${timing.duration}ms total, advanced in ${timing.advanceTime}ms`);
      
      if (timing.round === 1 && timing.advanceTime > 6000) {
        console.log(`  ⚠️  Expected ~5s for all answers + 3s, got ${timing.advanceTime}ms`);
      } else if (timing.round >= 2 && timing.advanceTime > 8000) {
        console.log(`  ⚠️  Expected max 7s, got ${timing.advanceTime}ms`);
      } else {
        console.log('  ✓ Timing within expected range');
      }
    });
    
    console.log('\n✅ MULTIPLAYER TEST COMPLETE!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    await takeScreenshot(hostPage, 'error', 'host');
    await takeScreenshot(guestPage, 'error', 'guest');
  } finally {
    if (hostBrowser) await hostBrowser.close();
    if (guestBrowser) await guestBrowser.close();
  }
}

// Run test
testMultiplayerComplete().catch(console.error);