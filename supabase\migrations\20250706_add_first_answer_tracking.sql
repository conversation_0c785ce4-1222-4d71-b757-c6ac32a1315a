-- Add field to track when the first answer is submitted for each round
-- This is needed to implement dynamic round advance timing based on player response speed

ALTER TABLE game_rooms
ADD COLUMN first_answer_at timestamptz;

COMMENT ON COLUMN game_rooms.first_answer_at IS 'Timestamp when the first player submitted an answer for the current question';

-- Add field to track the response window duration for dynamic timing
ALTER TABLE game_rooms
ADD COLUMN all_answers_window_seconds numeric DEFAULT 2.0;

COMMENT ON COLUMN game_rooms.all_answers_window_seconds IS 'If all players answer within this many seconds, round advances after this duration + 3 seconds';