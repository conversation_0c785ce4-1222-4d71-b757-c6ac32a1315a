#!/usr/bin/env pwsh

Write-Host "🎯 Running Fully Automated Multiplayer Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if node is available
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Run the automated test
Write-Host "🚀 Starting automated test with MCP Puppeteer..." -ForegroundColor Yellow
Write-Host ""

try {
    # Execute the test
    node automated-multiplayer-test.js
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Automated test completed successfully!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "❌ Automated test failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "❌ Error running automated test: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "📊 Test execution finished" -ForegroundColor Cyan
Write-Host "Check the screenshots for visual verification" -ForegroundColor Gray