const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };
const SCREENSHOT_DIR = path.join(__dirname, 'test-screenshots-final');

// Helper to take screenshots
async function takeScreenshot(page, name, prefix) {
  try {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    const filename = `${prefix}-${name}.png`;
    const filepath = path.join(SCREENSHOT_DIR, filename);
    await page.screenshot({ path: filepath, fullPage: true });
    console.log(`[${prefix}] Screenshot saved: ${filename}`);
  } catch (error) {
    console.error(`[${prefix}] Screenshot failed:`, error.message);
  }
}

// Helper to wait for condition
async function waitFor(fn, description, timeout = 30000) {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    try {
      const result = await fn();
      if (result) return result;
    } catch (e) {
      // Continue
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  throw new Error(`Timeout waiting for ${description}`);
}

// Main test
async function testMultiplayerFinal() {
  console.log('=== FINAL MULTIPLAYER TEST ===\n');
  
  const browserOptions = {
    headless: true,
    defaultViewport: { width: 1200, height: 900 },
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    executablePath: '/usr/bin/chromium-browser'
  };

  let hostBrowser, guestBrowser;
  let hostPage, guestPage;

  try {
    // Launch browsers
    console.log('Launching browsers...');
    hostBrowser = await puppeteer.launch(browserOptions);
    guestBrowser = await puppeteer.launch(browserOptions);
    
    hostPage = await hostBrowser.newPage();
    guestPage = await guestBrowser.newPage();
    
    // Navigate
    await Promise.all([
      hostPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' }),
      guestPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' })
    ]);
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === Quick Authentication ===
    console.log('\nAuthenticating users...');
    
    async function authenticate(page, creds, name) {
      // Click Multiplayer Mode
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Fill credentials
      await page.type('input[type="email"], input[type="text"]:not([type="password"])', creds.username);
      await page.type('input[type="password"]', creds.password);
      await page.keyboard.press('Enter');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Click Multiplayer Mode again
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log(`${name} authenticated`);
    }
    
    await authenticate(hostPage, HOST_CREDENTIALS, 'HOST');
    await authenticate(guestPage, GUEST_CREDENTIALS, 'GUEST');
    
    // === Host Creates Room ===
    console.log('\nHost creating room...');
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Host Game');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Get room code
    const roomInfo = await hostPage.evaluate(() => {
      const roomTitle = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.includes("Room:"))?.textContent;
      return { roomTitle };
    });
    
    console.log('Room created:', roomInfo.roomTitle);
    await takeScreenshot(hostPage, '01-room-created', 'host');
    
    // === Guest Joins Room ===
    console.log('\nGuest joining room...');
    
    // First refresh the room list
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Refresh List');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    await takeScreenshot(guestPage, '02-room-list', 'guest');
    
    // Click on the room (try multiple selectors)
    const roomClicked = await guestPage.evaluate(() => {
      // Try different ways to find the room
      const selectors = [
        'tr:has-text("fresh")',
        'div:has-text("fresh\'s Game")',
        '[class*="room"]:has-text("fresh")',
        'button:has-text("fresh\'s Game")'
      ];
      
      for (const selector of selectors) {
        try {
          const element = document.querySelector(selector);
          if (element) {
            element.click();
            return true;
          }
        } catch (e) {
          // Continue
        }
      }
      
      // Fallback: find any element with fresh's game
      const elements = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes("fresh's Game") && 
                     !el.textContent.includes('Default:'));
      
      if (elements.length > 0) {
        // Click the smallest element (most specific)
        elements.sort((a, b) => a.textContent.length - b.textContent.length);
        elements[0].click();
        return true;
      }
      
      return false;
    });
    
    console.log('Room clicked:', roomClicked);
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Now click Join button
    const joinClicked = await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Join' && !b.disabled);
      if (btn) {
        btn.click();
        return true;
      }
      return false;
    });
    
    console.log('Join clicked:', joinClicked);
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verify guest joined
    const guestState = await guestPage.evaluate(() => {
      const inRoom = document.body.textContent.includes('Leave Game');
      const hasReadyButton = !!Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent.includes('Ready') && !b.textContent.includes('Refresh'));
      
      return { inRoom, hasReadyButton };
    });
    
    console.log('Guest state:', guestState);
    await takeScreenshot(guestPage, '03-guest-in-room', 'guest');
    
    if (!guestState.inRoom) {
      throw new Error('Guest failed to join room');
    }
    
    // === Both Players Ready Up ===
    console.log('\nPlayers readying up...');
    
    // Wait a bit for sync
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Host ready
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Ready Up' || b.textContent === 'Ready');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Guest ready
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Ready Up' || b.textContent === 'Ready');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check player count
    const playerStatus = await hostPage.evaluate(() => {
      const playerCount = document.body.textContent.match(/Players.*\((\d+)\/\d+\)/);
      const readyPlayers = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('✓'))
        .length;
      
      return { 
        playerCount: playerCount ? playerCount[0] : null,
        readyPlayers 
      };
    });
    
    console.log('Player status:', playerStatus);
    await takeScreenshot(hostPage, '04-players-ready', 'host');
    await takeScreenshot(guestPage, '04-players-ready', 'guest');
    
    // === Start Game ===
    console.log('\nStarting game...');
    
    // Wait for Start Game button
    await waitFor(async () => {
      return await hostPage.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Start Game');
        return btn && !btn.disabled;
      });
    }, 'Start Game button', 10000);
    
    // Click Start Game
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Start Game');
      if (btn) btn.click();
    });
    
    console.log('Game started!');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === Play One Round ===
    console.log('\nPlaying round 1...');
    
    // Wait for question
    await waitFor(async () => {
      const hasQuestion = await Promise.all([
        hostPage.evaluate(() => !!document.querySelector('img')),
        guestPage.evaluate(() => !!document.querySelector('img'))
      ]);
      return hasQuestion[0] && hasQuestion[1];
    }, 'question to appear', 10000);
    
    await takeScreenshot(hostPage, '05-question', 'host');
    await takeScreenshot(guestPage, '05-question', 'guest');
    
    // Both answer
    await Promise.all([
      hostPage.evaluate(() => {
        const choices = Array.from(document.querySelectorAll('button'))
          .filter(b => b.className.includes('choice') || 
                      b.textContent.match(/^[A-Z]\./));
        if (choices[0]) choices[0].click();
      }),
      guestPage.evaluate(() => {
        const choices = Array.from(document.querySelectorAll('button'))
          .filter(b => b.className.includes('choice') || 
                      b.textContent.match(/^[A-Z]\./));
        if (choices[1]) choices[1].click();
      })
    ]);
    
    console.log('Both players answered');
    
    // Wait for round advance
    const startTime = Date.now();
    await waitFor(async () => {
      return await hostPage.evaluate(() => {
        return document.body.textContent.includes('Next question in') ||
               document.body.textContent.includes('Game Over');
      });
    }, 'round to advance', 10000);
    
    const advanceTime = Date.now() - startTime;
    console.log(`Round advanced in ${advanceTime}ms`);
    
    await takeScreenshot(hostPage, '06-round-complete', 'host');
    await takeScreenshot(guestPage, '06-round-complete', 'guest');
    
    console.log('\n✅ MULTIPLAYER TEST SUCCESSFUL!');
    console.log(`- Guest successfully joined room`);
    console.log(`- Both players readied up`);
    console.log(`- Game started successfully`);
    console.log(`- Round played and advanced in ${advanceTime}ms`);
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    await takeScreenshot(hostPage, 'error-host', 'host');
    await takeScreenshot(guestPage, 'error-guest', 'guest');
    
    // Additional debugging
    const hostDebug = await hostPage?.evaluate(() => ({
      url: window.location.href,
      buttons: Array.from(document.querySelectorAll('button')).map(b => b.textContent),
      bodyText: document.body.innerText.substring(0, 500)
    }));
    
    const guestDebug = await guestPage?.evaluate(() => ({
      url: window.location.href,
      buttons: Array.from(document.querySelectorAll('button')).map(b => b.textContent),
      bodyText: document.body.innerText.substring(0, 500)
    }));
    
    console.log('\nHost debug:', hostDebug);
    console.log('\nGuest debug:', guestDebug);
    
  } finally {
    if (hostBrowser) await hostBrowser.close();
    if (guestBrowser) await guestBrowser.close();
  }
}

// Run test
testMultiplayerFinal().catch(console.error);