# Automated Multiplayer Test Plan

## Overview
This document outlines the fully automated test for multiplayer round advance timing in Recognition Combine.

## Test Credentials
- **Player 1**: Email: `fresh`, Password: `test123`
- **Player 2**: Email: `fresh2`, Password: `test123`

## Test Scenarios

### 1. Both Players Answer at 1s → Round Advances at 4s
- Both players submit their answers 1 second into the round
- Expected: Round advances at 4 seconds (3s transition after both answered)

### 2. Both Players Answer at 2s → Round Advances at 5s
- Both players submit their answers 2 seconds into the round
- Expected: Round advances at 5 seconds (3s transition after both answered)

### 3. Both Players Answer at 5s → Round Advances at 7s (Cap)
- Both players submit their answers 5 seconds into the round
- Expected: Round advances at 7 seconds (capped at max 7s)

### 4. Only Player 1 Answers → Round Advances at 7s
- Only Player 1 submits an answer at 1 second
- Player 2 doesn't answer
- Expected: Round advances at 7 seconds (timeout)

### 5. Staggered Answers (P1 at 1s, P2 at 3s) → Round at 6s
- Player 1 answers at 1 second
- Player 2 answers at 3 seconds
- Expected: Round advances at 6 seconds (3s after last submission)

## Automated Test Flow

### Phase 1: Setup
1. Launch two browser windows side by side
2. Navigate both to https://recognition-combine.vercel.app/
3. Inject monitoring code to track round timings

### Phase 2: Authentication
1. Player 1 window: Click "Sign In" → Enter credentials → Submit
2. Player 2 window: Click "Sign In" → Enter credentials → Submit
3. Wait for authentication to complete

### Phase 3: Room Creation and Joining
1. Player 1: Click "Create Room"
2. Extract room code from UI
3. Player 2: Click "Join Room" → Enter room code → Submit
4. Verify both players are in the room

### Phase 4: Game Start
1. Both players: Click "Ready" button
2. Player 1 (Host): Click "Start Game"
3. Verify game has started (question appears)

### Phase 5: Test Execution
For each test scenario:
1. Schedule answer submissions at specified times
2. Monitor for round changes
3. Record actual transition time
4. Compare with expected time (±500ms tolerance)

### Phase 6: Results Analysis
1. Compile all test results
2. Display pass/fail status for each scenario
3. Show timing accuracy metrics
4. Generate summary report

## Success Criteria
- All 5 test scenarios must pass with timing within ±500ms tolerance
- No manual intervention required during test execution
- Full test completes in under 3 minutes

## Implementation Files

### 1. `automated-test-simple.js`
Main test implementation using Puppeteer directly:
- Launches two browser instances
- Handles all authentication and room setup
- Executes test scenarios
- Reports results

### 2. `test-automated.ps1`
PowerShell wrapper to run the test:
- Checks prerequisites
- Executes Node.js test
- Formats output

### 3. Test Helpers
Injected JavaScript code that:
- Monitors game state changes
- Tracks round transition timing
- Provides answer submission scheduling
- Records test data

## Running the Test

### Prerequisites
```bash
# Install Puppeteer and Chrome
npm install puppeteer
npx puppeteer browsers install chrome
```

### Execute Test
```bash
# From supabase directory
node automated-test-simple.js

# Or using PowerShell
./test-automated.ps1
```

## Expected Output
```
🎯 Fully Automated Multiplayer Round Advance Test
================================================

🚀 Launching browsers...
✅ Browsers ready
✅ Monitoring code injected

🔐 Signing in players...
✅ Both players signed in

🏠 Creating room...
✅ Room created: ABC123

🏠 Player 2 joining room...
✅ Player 2 joined

🎮 Players getting ready...
🎮 Starting game...
✅ Game started!

📊 Running test scenarios...
========================

Test 1: Both at 1s → Round at 4s
Expected: 4000ms, Actual: 3985ms
Difference: 15ms - ✅ PASS

Test 2: Both at 2s → Round at 5s
Expected: 5000ms, Actual: 5021ms
Difference: 21ms - ✅ PASS

[... remaining tests ...]

📈 Test Summary
==============

Passed: 5/5 tests

✅ Test complete!
```

## Troubleshooting

### Common Issues
1. **Chrome not found**: Run `npx puppeteer browsers install chrome`
2. **Permission errors**: Add `--no-sandbox` flag to launch options
3. **Timing failures**: Check network latency, increase tolerance
4. **Authentication fails**: Verify test accounts exist and credentials are correct

### Debug Mode
Add `PUPPETEER_HEADLESS=false` environment variable to see browser actions:
```bash
PUPPETEER_HEADLESS=false node automated-test-simple.js
```