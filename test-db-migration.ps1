# Test script to verify the database migration worked correctly
# This script checks that current_round_answers defaults to [] instead of {}

Write-Host "Testing database migration for current_round_answers..." -ForegroundColor Green

# Check if supabase is running
$supabaseStatus = supabase status 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Supabase is not running. Starting supabase..." -ForegroundColor Yellow
    supabase start
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to start supabase. Please check your setup." -ForegroundColor Red
        exit 1
    }
}

Write-Host "Supabase is running. Testing migration..." -ForegroundColor Green

# Test 1: Check the default value for new rows
Write-Host "`nTest 1: Checking default value for new game_rooms..." -ForegroundColor Cyan

$testQuery = @"
SELECT 
    current_round_answers,
    jsonb_typeof(current_round_answers) as type_check
FROM game_rooms 
WHERE current_round_answers IS NOT NULL
LIMIT 5;
"@

Write-Host "Query: $testQuery" -ForegroundColor Gray

# Test 2: Check if any rows still have {} instead of []
Write-Host "`nTest 2: Checking for any remaining {} values..." -ForegroundColor Cyan

$checkEmptyObjectQuery = @"
SELECT 
    id,
    current_round_answers,
    jsonb_typeof(current_round_answers) as type_check
FROM game_rooms 
WHERE current_round_answers = '{}'::jsonb;
"@

Write-Host "Query: $checkEmptyObjectQuery" -ForegroundColor Gray

Write-Host "`nTo manually test these queries, run:" -ForegroundColor Yellow
Write-Host "supabase db reset" -ForegroundColor White
Write-Host "Then connect to your database and run the queries above." -ForegroundColor White

Write-Host "`nMigration test script completed." -ForegroundColor Green
Write-Host "The fixes implemented should resolve the 'current_round_answers is defined but not an array' warning." -ForegroundColor Green 