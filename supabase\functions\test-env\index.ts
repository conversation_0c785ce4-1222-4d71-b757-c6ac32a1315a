import { serve } from "https://deno.land/std@0.177.0/http/server.ts"
import { corsHeaders } from "../_shared/cors.ts"

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get all environment variables that start with SUPABASE
    const envVars: Record<string, string> = {};
    const allEnvVars = Deno.env.toObject();
    
    for (const [key, value] of Object.entries(allEnvVars)) {
      if (key.startsWith('SUPABASE')) {
        // Mask the actual values for security
        envVars[key] = value ? `Present (${value.length} chars)` : 'Missing';
      }
    }
    
    // Specifically check for service role key
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    const hasServiceRoleKey = !!serviceRoleKey;
    
    return new Response(JSON.stringify({
      message: 'Environment check',
      supabaseEnvVars: envVars,
      serviceRoleKeyStatus: hasServiceRoleKey ? 'Present' : 'Missing',
      timestamp: new Date().toISOString()
    }, null, 2), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: error.message || 'An error occurred'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});