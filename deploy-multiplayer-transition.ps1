# Deploy Multiplayer Transition Feature
# All database columns are already in place

Write-Host "Deploying Multiplayer Transition Feature" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Database columns verified:" -ForegroundColor Green
Write-Host "✓ player_bonus_levels (jsonb)" -ForegroundColor Green
Write-Host "✓ transition_until (timestamptz)" -ForegroundColor Green
Write-Host "✓ next_question_data (jsonb)" -ForegroundColor Green
Write-Host ""

Write-Host "Deploying Edge Functions..." -ForegroundColor Yellow
Write-Host ""

# Navigate to supabase directory
Set-Location -Path "supabase"

# Deploy submit-answer-handler (handles transitions)
Write-Host "1. Deploying submit-answer-handler..." -ForegroundColor Yellow
.\deploy-submit-answer-handler.ps1
Write-Host ""

# Deploy other related functions if needed
Write-Host "2. Deploying start-game-handler..." -ForegroundColor Yellow
.\deploy-start-game-handler.ps1
Write-Host ""

# Return to root directory
Set-Location -Path ".."

Write-Host "Deployment Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "The multiplayer transition feature is now active with:" -ForegroundColor Cyan
Write-Host "- 3-second transition period between questions"
Write-Host "- Bonus points for consecutive correct answers"
Write-Host "- Smooth question progression"
Write-Host ""
Write-Host "Test it by starting a multiplayer game!" -ForegroundColor Yellow