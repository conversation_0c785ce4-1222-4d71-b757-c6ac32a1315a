Write-Host "Deploying Simplified 3-Second Transition (V3)" -ForegroundColor Cyan
Write-Host ""

Set-Location -Path "supabase"

Write-Host "Deploying submit-answer-handler..." -ForegroundColor Yellow
.\deploy-submit-answer-handler.ps1

Set-Location -Path ".."

Write-Host ""
Write-Host "Deployment Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Simplified transition system:" -ForegroundColor Cyan
Write-Host "- Frontend detects when all players have answered"
Write-Host "- Waits exactly 3 seconds"
Write-Host "- Advances to next question"
Write-Host "- Works even when tab loses focus"
Write-Host "- No complex state management"
Write-Host ""
Write-Host "Test by having all players answer a question!" -ForegroundColor Yellow