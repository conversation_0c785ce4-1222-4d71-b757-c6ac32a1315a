const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

const APP_URL = process.env.APP_URL || 'http://localhost:3000';
const SCREENSHOT_DIR = 'test-screenshots-debug-edge';

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function saveScreenshot(page, filename) {
  try {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    const filepath = path.join(SCREENSHOT_DIR, filename);
    await page.screenshot({ path: filepath, fullPage: true });
    console.log(`Screenshot saved: ${filepath}`);
  } catch (error) {
    console.error(`Failed to save screenshot ${filename}:`, error.message);
  }
}

async function testEdgeFunctionError() {
  console.log('=== Testing Edge Function Error Details ===');
  console.log(`Testing at: ${APP_URL}`);

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Capture all console logs
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('error') || text.includes('Error') || 
          text.includes('edge') || text.includes('Edge') ||
          text.includes('start-game') || text.includes('invoke')) {
        console.log(`[Browser Console] ${text}`);
      }
    });

    // Capture network responses
    page.on('response', response => {
      const url = response.url();
      if (url.includes('start-game-handler')) {
        console.log(`[Network] Edge function response:`, {
          url: url,
          status: response.status(),
          statusText: response.statusText(),
          headers: response.headers()
        });
        
        // Try to get response body
        response.text().then(body => {
          console.log(`[Network] Edge function response body:`, body);
        }).catch(err => {
          console.log(`[Network] Could not read response body:`, err.message);
        });
      }
    });

    await page.goto(APP_URL);
    await delay(2000);

    // Click multiplayer mode
    await page.click('button:has-text("Multiplayer Mode")');
    await delay(1000);

    // Sign in
    await page.type('input[type="text"]', '<EMAIL>');
    await page.type('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await delay(3000);

    // Click multiplayer mode again
    await page.click('button:has-text("Multiplayer Mode")');
    await delay(3000);

    // Create room
    await page.click('button:has-text("Create Game")');
    await delay(2000);

    // Mark ready
    await page.click('button:has-text("Not Ready")');
    await delay(1000);

    // Now try to start game and capture the error
    console.log('\n=== Attempting to start game ===');
    await saveScreenshot(page, 'before-start-game.png');
    
    // Click start game and wait for network response
    const startGamePromise = page.waitForResponse(response => 
      response.url().includes('start-game-handler'), 
      { timeout: 10000 }
    ).catch(err => {
      console.log('No edge function response detected:', err.message);
      return null;
    });

    await page.click('button:has-text("Start Game")');
    
    const edgeResponse = await startGamePromise;
    if (edgeResponse) {
      console.log('\n=== Edge Function Response Details ===');
      console.log('Status:', edgeResponse.status());
      console.log('Status Text:', edgeResponse.statusText());
      console.log('Headers:', edgeResponse.headers());
      
      try {
        const responseBody = await edgeResponse.text();
        console.log('Response Body:', responseBody);
        
        // Try to parse as JSON
        try {
          const jsonBody = JSON.parse(responseBody);
          console.log('Parsed JSON:', JSON.stringify(jsonBody, null, 2));
        } catch (e) {
          console.log('Response is not JSON');
        }
      } catch (e) {
        console.log('Could not read response body:', e.message);
      }
    }

    await delay(3000);
    await saveScreenshot(page, 'after-start-game-attempt.png');

  } finally {
    await browser.close();
  }
}

testEdgeFunctionError().catch(console.error);