# Real-time monitoring script for server-authoritative transitions
param(
    [int]$RefreshSeconds = 2,
    [string]$SupabaseUrl = $env:SUPABASE_URL,
    [string]$SupabaseKey = $env:SUPABASE_ANON_KEY
)

Write-Host "Server Transition Monitor" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host "Refresh Rate: $RefreshSeconds seconds" -ForegroundColor Gray
Write-Host "Press Ctrl+C to exit" -ForegroundColor Gray
Write-Host ""

# Function to format timestamp
function Format-Timestamp {
    param($timestamp)
    if ($null -eq $timestamp) { return "N/A" }
    $dt = [DateTime]::Parse($timestamp)
    return $dt.ToString("HH:mm:ss")
}

# Function to calculate remaining time
function Get-RemainingTime {
    param($deadline)
    if ($null -eq $deadline) { return "N/A" }
    $now = [DateTime]::UtcNow
    $dl = [DateTime]::Parse($deadline)
    $remaining = ($dl - $now).TotalSeconds
    if ($remaining -lt 0) { return "EXPIRED" }
    return [Math]::Round($remaining, 1).ToString() + "s"
}

# Main monitoring loop
while ($true) {
    Clear-Host
    Write-Host "Server Transition Monitor - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
    Write-Host "================================================================" -ForegroundColor Cyan
    
    # Note: This is a placeholder for the actual monitoring logic
    # In a real implementation, you would query Supabase directly
    Write-Host "`nActive Games:" -ForegroundColor Yellow
    Write-Host "Room ID                              | Status | Round | Deadline      | Remaining | Players"
    Write-Host "------------------------------------ | ------ | ----- | ------------- | --------- | -------"
    
    # Example output format (would be populated with real data)
    # Write-Host "12345678-1234-1234-1234-123456789012 | active |   3   | 14:30:45     | 2.5s      | 2/3"
    
    Write-Host "`nGames Needing Transition:" -ForegroundColor Red
    Write-Host "(Games where deadline has passed but still active)"
    Write-Host "Room ID                              | Deadline      | Overdue By"
    Write-Host "------------------------------------ | ------------- | ----------"
    
    Write-Host "`nRecent Transitions:" -ForegroundColor Green
    Write-Host "Room ID                              | Round | Transitioned At"
    Write-Host "------------------------------------ | ----- | ---------------"
    
    Write-Host "`nStats:" -ForegroundColor Magenta
    Write-Host "Total Active Games: 0"
    Write-Host "Games in Transition: 0"
    Write-Host "Overdue Transitions: 0"
    
    Write-Host "`n[Refreshing in $RefreshSeconds seconds...]" -ForegroundColor DarkGray
    
    Start-Sleep -Seconds $RefreshSeconds
}

# Note: To make this script functional, you would need to:
# 1. Install a PowerShell module for HTTP requests (like Invoke-RestMethod)
# 2. Query Supabase REST API for game_rooms data
# 3. Parse and display the results
# 4. Handle authentication with Supabase
# 
# Example query structure:
# $headers = @{
#     "apikey" = $SupabaseKey
#     "Authorization" = "Bearer $SupabaseKey"
# }
# $response = Invoke-RestMethod -Uri "$SupabaseUrl/rest/v1/game_rooms?status=eq.active" -Headers $headers