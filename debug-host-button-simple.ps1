Write-Host "HOST GAME BUTTON DEBUG TOOL" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan
Write-Host ""

Write-Host "The Host Game button is disabled when ANY of these conditions are true:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. User is not authenticated (!user)" -ForegroundColor White
Write-Host "2. Rooms are loading (isLoadingRooms)" -ForegroundColor White  
Write-Host "3. Currently creating a room (isCreatingRoom)" -ForegroundColor White
Write-Host "4. User is offline (!isOnline)" -ForegroundColor White
Write-Host ""

Write-Host "TO DEBUG:" -ForegroundColor Green
Write-Host "1. Open http://localhost:3000 in your browser" -ForegroundColor White
Write-Host "2. Press F12 to open Developer Tools" -ForegroundColor White
Write-Host "3. Go to Console tab" -ForegroundColor White
Write-Host "4. Look for '[HOST_GAME_BUTTON_DEBUG]' logs" -ForegroundColor White
Write-Host ""

Write-Host "QUICK FIXES:" -ForegroundColor Green
Write-Host "- If not signed in: Click user icon and sign in" -ForegroundColor White
Write-Host "- If loading stuck: Refresh the page" -ForegroundColor White
Write-Host "- If offline: Check internet connection" -ForegroundColor White
Write-Host ""

Write-Host "First, make sure your dev server is running:" -ForegroundColor Yellow
Write-Host "cd web-app" -ForegroundColor Cyan
Write-Host "npm run dev" -ForegroundColor Cyan 