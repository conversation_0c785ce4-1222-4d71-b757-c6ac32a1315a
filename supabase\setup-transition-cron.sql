-- Set up a cron job to call the transition-monitor edge function every second
-- This requires the pg_cron extension to be enabled in Supabase

-- First, create a function that calls the edge function
CREATE OR REPLACE FUNCTION public.trigger_transition_monitor()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  supabase_url text;
  anon_key text;
BEGIN
  -- Get the Supabase URL and anon key from the vault
  -- Note: You'll need to set these values in your Supabase vault
  SELECT decrypted_secret INTO supabase_url FROM vault.decrypted_secrets WHERE name = 'supabase_url';
  SELECT decrypted_secret INTO anon_key FROM vault.decrypted_secrets WHERE name = 'anon_key';
  
  -- Call the edge function using http extension
  PERFORM http_post(
    supabase_url || '/functions/v1/transition-monitor',
    '{}',
    'application/json',
    headers => jsonb_build_object(
      'Authorization', 'Bearer ' || anon_key,
      'Content-Type', 'application/json'
    )
  );
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail - we want the cron to keep running
    RAISE WARNING 'Transition monitor call failed: %', SQLERRM;
END;
$$;

-- Create the cron job to run every second
-- Note: pg_cron minimum interval is 1 minute, so we'll use a different approach
-- Instead, we'll use a recursive function with pg_sleep

-- Alternative approach: Use a database trigger on game_rooms updates
-- This is more efficient than polling
CREATE OR REPLACE FUNCTION public.check_transition_on_update()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- If transition_deadline is set and it's in the past, trigger transition
  IF NEW.transition_deadline IS NOT NULL AND NEW.transition_deadline <= NOW() THEN
    -- Call the transition monitor asynchronously
    PERFORM pg_notify('transition_needed', json_build_object(
      'room_id', NEW.id,
      'deadline', NEW.transition_deadline
    )::text);
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger on game_rooms table
DROP TRIGGER IF EXISTS check_game_transitions ON game_rooms;
CREATE TRIGGER check_game_transitions
  AFTER INSERT OR UPDATE ON game_rooms
  FOR EACH ROW
  EXECUTE FUNCTION check_transition_on_update();

-- Alternative: Create a scheduled job that runs every minute (pg_cron limitation)
-- and checks all games that need transitions
SELECT cron.schedule(
  'check-game-transitions',
  '* * * * *', -- Every minute
  $$
  SELECT public.trigger_transition_monitor();
  $$
);

-- To monitor the job:
-- SELECT * FROM cron.job;
-- SELECT * FROM cron.job_run_details ORDER BY start_time DESC LIMIT 10;