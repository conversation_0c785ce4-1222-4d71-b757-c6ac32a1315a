-- Fix RLS performance issues identified by Supabase linter
-- This migration addresses auth RLS initialization plan warnings, duplicate indexes,
-- consolidates multiple permissive policies, and adds missing foreign key indexes

-- 1. Fix auth RLS initialization plan issues
-- Replace auth.uid() with (select auth.uid()) for better performance

-- Drop and recreate policies for profiles table
DROP POLICY IF EXISTS "Allow users to update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;

CREATE POLICY "Allow users to update their own profile" ON public.profiles
    FOR UPDATE TO authenticated
    USING (id = (select auth.uid()))
    WITH CHECK (id = (select auth.uid()));

CREATE POLICY "Users can view their own profile" ON public.profiles
    FOR SELECT TO authenticated
    USING (id = (select auth.uid()));

-- Drop and recreate policies for game_rooms table
DROP POLICY IF EXISTS "Allow authenticated users to create rooms" ON public.game_rooms;
DROP POLICY IF EXISTS "Allow host to update their room" ON public.game_rooms;
DROP POLICY IF EXISTS "Allow host to delete a waiting room" ON public.game_rooms;

CREATE POLICY "Allow authenticated users to create rooms" ON public.game_rooms
    FOR INSERT TO authenticated
    WITH CHECK (host_id = (select auth.uid()));

CREATE POLICY "Allow host to update their room" ON public.game_rooms
    FOR UPDATE TO authenticated
    USING (host_id = (select auth.uid()));

CREATE POLICY "Allow host to delete a waiting room" ON public.game_rooms
    FOR DELETE TO authenticated
    USING (host_id = (select auth.uid()) AND status = 'waiting');

-- Drop and recreate policies for game_players table
DROP POLICY IF EXISTS "Allow player to update their own ready status/score" ON public.game_players;
DROP POLICY IF EXISTS "Allow players to update their own connection status" ON public.game_players;
DROP POLICY IF EXISTS "Allow players to leave rooms they are in" ON public.game_players;
DROP POLICY IF EXISTS "Allow players to read their own records and others in their roo" ON public.game_players;
DROP POLICY IF EXISTS "Allow authenticated users to join rooms" ON public.game_players;

-- Consolidate the two UPDATE policies into one
CREATE POLICY "Allow player to update their own record" ON public.game_players
    FOR UPDATE TO authenticated
    USING (user_id = (select auth.uid()));

CREATE POLICY "Allow players to leave rooms they are in" ON public.game_players
    FOR DELETE TO authenticated
    USING (user_id = (select auth.uid()));

CREATE POLICY "Allow players to read their own records and others in their room" ON public.game_players
    FOR SELECT TO authenticated
    USING (
        user_id = (select auth.uid()) OR 
        room_id IN (
            SELECT room_id FROM public.game_players 
            WHERE user_id = (select auth.uid())
        )
    );

CREATE POLICY "Allow authenticated users to join rooms" ON public.game_players
    FOR INSERT TO authenticated
    WITH CHECK (
        user_id = (select auth.uid()) AND
        EXISTS (
            SELECT 1 FROM public.game_rooms 
            WHERE id = room_id AND status = 'waiting'
        )
    );

-- Drop and recreate policies for player_game_stats table
DROP POLICY IF EXISTS "Users can read their own game stats" ON public.player_game_stats;

CREATE POLICY "Users can read their own game stats" ON public.player_game_stats
    FOR SELECT TO authenticated
    USING (user_id = (select auth.uid()));

-- 2. Consolidate multiple permissive policies for profiles table
-- Keep the more general "Authenticated users can read public profile info" policy
-- and drop the redundant "Users can view their own profile" which we already recreated above
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;

-- 3. Remove duplicate indexes
-- Check and drop duplicate indexes on game_players table
DROP INDEX IF EXISTS public.idx_game_players_room_connected;
-- Keep idx_game_players_room_id_is_connected as it's more descriptively named

-- Check and drop duplicate indexes on profiles table
DROP INDEX IF EXISTS public.profiles_id_key;
-- Keep profiles_pkey as it's the primary key

-- 4. Add covering indexes for unindexed foreign keys
CREATE INDEX IF NOT EXISTS idx_game_players_user_id ON public.game_players(user_id);
CREATE INDEX IF NOT EXISTS idx_game_rooms_current_turn_user_id ON public.game_rooms(current_turn_user_id);
CREATE INDEX IF NOT EXISTS idx_game_rooms_host_id ON public.game_rooms(host_id);
CREATE INDEX IF NOT EXISTS idx_player_game_stats_game_room_id ON public.player_game_stats(game_room_id);
CREATE INDEX IF NOT EXISTS idx_player_game_stats_user_id ON public.player_game_stats(user_id);

-- 5. Note on unused indexes:
-- The following indexes have been identified as unused but we'll keep them for now
-- as they may be used in the future or for specific query patterns:
-- - idx_players_data_player_name
-- - idx_players_data_local_image_path
-- - idx_game_players_last_seen_at
-- These can be reviewed and dropped later if confirmed to be unnecessary

-- Add comment to track this migration
COMMENT ON SCHEMA public IS 'Fixed RLS performance issues, consolidated policies, removed duplicate indexes, and added foreign key indexes';