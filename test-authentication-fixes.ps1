# Test Authentication State and Database Schema Fixes
# Tests for the three core problems identified:
# 1. Database Schema Mismatch (last_seen_at in profiles vs game_players)
# 2. Authentication State Desynchronization 
# 3. WebSocket connection issues

Write-Host "=========================================" -ForegroundColor Green
Write-Host "  AUTHENTICATION FIXES TEST SCRIPT" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host ""

# Get current directory and setup paths
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$supabaseDir = Join-Path $scriptDir "supabase"
$webAppDir = Join-Path $scriptDir "web-app"

Write-Host "📁 Working Directory: $scriptDir" -ForegroundColor Cyan
Write-Host "📁 Supabase Directory: $supabaseDir" -ForegroundColor Cyan  
Write-Host "📁 Web App Directory: $webAppDir" -ForegroundColor Cyan
Write-Host ""

# Test 1: Database Schema Investigation
Write-Host "🔍 TEST 1: Database Schema Investigation" -ForegroundColor Yellow
Write-Host "Checking if last_seen_at column exists in profiles table vs game_players table..."

# Check migration files to understand schema
$migrationFiles = Get-ChildItem -Path "$supabaseDir\migrations" -Filter "*.sql" | Sort-Object Name
Write-Host "Found $($migrationFiles.Count) migration files:" -ForegroundColor Cyan

foreach ($migration in $migrationFiles) {
    $content = Get-Content $migration.FullName -Raw
    if ($content -match "last_seen_at") {
        Write-Host "  ✅ $($migration.Name) contains last_seen_at references" -ForegroundColor Green
        # Extract the table context
        $lines = Get-Content $migration.FullName
        for ($i = 0; $i -lt $lines.Count; $i++) {
            if ($lines[$i] -match "last_seen_at") {
                $context = $lines[($i-2)..($i+2)] -join "`n"
                Write-Host "    Context: $context" -ForegroundColor Gray
            }
        }
    }
}

# Test 2: Code Analysis - Find all last_seen_at usage
Write-Host ""
Write-Host "🔍 TEST 2: Code Analysis - last_seen_at Usage" -ForegroundColor Yellow
Write-Host "Scanning web-app code for last_seen_at usage patterns..."

$pageFile = Join-Path $webAppDir "src\app\page.tsx"
if (Test-Path $pageFile) {
    $pageContent = Get-Content $pageFile -Raw
    $lastSeenMatches = [regex]::Matches($pageContent, "last_seen_at")
    
    Write-Host "Found $($lastSeenMatches.Count) references to last_seen_at in page.tsx" -ForegroundColor Cyan
    
    # Find the problematic profiles.last_seen_at usage
    if ($pageContent -match "profiles.*last_seen_at") {
        Write-Host "  ❌ FOUND ISSUE: Code tries to update profiles.last_seen_at" -ForegroundColor Red
    }
    
    # Find correct game_players.last_seen_at usage
    if ($pageContent -match "game_players.*last_seen_at") {
        Write-Host "  ✅ CORRECT: Code references game_players.last_seen_at" -ForegroundColor Green
    }
} else {
    Write-Host "  ❌ Could not find page.tsx file" -ForegroundColor Red
}

# Test 3: Authentication State Pattern Analysis  
Write-Host ""
Write-Host "🔍 TEST 3: Authentication State Pattern Analysis" -ForegroundColor Yellow
Write-Host "Analyzing handleVisibilityChange for auth state issues..."

if (Test-Path $pageFile) {
    $pageContent = Get-Content $pageFile -Raw
    
    # Check if handleVisibilityChange gets fresh session
    if ($pageContent -match "handleVisibilityChange.*getSession") {
        Write-Host "  ✅ GOOD: handleVisibilityChange calls getSession" -ForegroundColor Green
    } else {
        Write-Host "  ❌ ISSUE: handleVisibilityChange may use stale user state" -ForegroundColor Red
    }
    
    # Check for proper error handling in visibility change
    if ($pageContent -match "handleVisibilityChange.*try.*catch") {
        Write-Host "  ✅ GOOD: handleVisibilityChange has error handling" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  WARNING: handleVisibilityChange may lack error handling" -ForegroundColor Yellow
    }
}

# Test 4: Realtime Channel Cleanup Analysis
Write-Host ""
Write-Host "🔍 TEST 4: Realtime Channel Cleanup Analysis" -ForegroundColor Yellow
Write-Host "Checking AuthModal.tsx for proper realtime cleanup..."

$authModalFile = Join-Path $webAppDir "src\components\auth\AuthModal.tsx"
if (Test-Path $authModalFile) {
    $authContent = Get-Content $authModalFile -Raw
    
    # Check if sign-out cleans up realtime channels
    if ($authContent -match "removeAllChannels" -or $authContent -match "realtime.*disconnect") {
        Write-Host "  ✅ GOOD: handleSignOut cleans up realtime channels" -ForegroundColor Green
    } else {
        Write-Host "  ❌ ISSUE: handleSignOut may not clean up realtime channels" -ForegroundColor Red
    }
    
    # Check for proper room leaving logic
    if ($authContent -match "leave-room-handler") {
        Write-Host "  ✅ GOOD: handleSignOut attempts to leave rooms" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  WARNING: handleSignOut may not leave rooms properly" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ❌ Could not find AuthModal.tsx file" -ForegroundColor Red
}

# Test 5: Environment File Validation
Write-Host ""
Write-Host "🔍 TEST 5: Environment File Validation" -ForegroundColor Yellow
Write-Host "Validating that required environment files exist..."

$envLocalFile = Join-Path $webAppDir ".env.local"
$supabaseEnvFile = Join-Path $supabaseDir ".env"

if (Test-Path $envLocalFile) {
    Write-Host "  ✅ web-app/.env.local exists" -ForegroundColor Green
} else {
    Write-Host "  ❌ web-app/.env.local missing" -ForegroundColor Red
}

if (Test-Path $supabaseEnvFile) {
    Write-Host "  ✅ supabase/.env exists" -ForegroundColor Green
} else {
    Write-Host "  ❌ supabase/.env missing" -ForegroundColor Red
}

# Test Summary
Write-Host ""
Write-Host "=========================================" -ForegroundColor Green
Write-Host "  TEST SUMMARY & RECOMMENDATIONS" -ForegroundColor Green  
Write-Host "=========================================" -ForegroundColor Green

Write-Host ""
Write-Host "🔧 FIXES TO IMPLEMENT:" -ForegroundColor Cyan
Write-Host "1. Fix Database Schema Mismatch: Update handleVisibilityChange to use correct table" -ForegroundColor White
Write-Host "2. Fix Auth State Sync: Ensure fresh session in visibility handler" -ForegroundColor White  
Write-Host "3. Fix Realtime Cleanup: Add removeAllChannels to sign-out process" -ForegroundColor White
Write-Host "4. Add Error Handling: Improve error handling in all auth-related functions" -ForegroundColor White

Write-Host ""
Write-Host "📋 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Run this test script to baseline current issues" -ForegroundColor White
Write-Host "2. Implement the three core fixes step by step" -ForegroundColor White
Write-Host "3. Re-run this test script to validate fixes work" -ForegroundColor White
Write-Host "4. Test in browser to ensure WebSocket errors are resolved" -ForegroundColor White

Write-Host ""
Write-Host "✅ Test script completed!" -ForegroundColor Green 