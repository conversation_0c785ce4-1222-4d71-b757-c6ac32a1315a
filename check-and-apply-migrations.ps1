# PowerShell script to check existing migrations and apply only new ones
Write-Host "`nChecking Migration Status" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow

# First, let's check the current migration status
Write-Host "`nChecking which migrations have already been applied..." -ForegroundColor Cyan

cd $PSScriptRoot

# List remote migrations
Write-Host "`nRemote migrations:" -ForegroundColor Gray
npx supabase migration list

Write-Host "`nPress any key to continue with applying only the transition migration..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Since the player_bonus_levels already exists, let's create a new migration 
# that only adds the transition columns if they don't exist
$migrationContent = @"
-- Add transition_until and next_question_data columns to game_rooms table if they don't exist
-- These columns support the 3-second transition period between questions

DO `$`$ 
BEGIN
    -- Add transition_until column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'transition_until') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN transition_until timestamptz DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.transition_until IS 'Timestamp indicating when the transition period ends and the next question should be shown. NULL when not in transition.';
    END IF;

    -- Add next_question_data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'next_question_data') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN next_question_data jsonb DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.next_question_data IS 'The next question data that will become active after the transition period ends.';
    END IF;
END`$`$;
"@

# Create a new migration file with conditional logic
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$migrationFile = "supabase\migrations\${timestamp}_add_transition_columns_if_not_exists.sql"
$migrationContent | Out-File -FilePath $migrationFile -Encoding UTF8

Write-Host "`nCreated new migration file: $migrationFile" -ForegroundColor Green
Write-Host "`nApplying the transition columns migration..." -ForegroundColor Cyan

# Apply just this migration
npx supabase db push

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nMigration applied successfully!" -ForegroundColor Green
    
    # Now deploy the edge function
    Write-Host "`nStep 2: Deploying submit-answer-handler edge function..." -ForegroundColor Cyan
    cd supabase
    .\deploy-submit-answer-handler.ps1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nDeployment complete! The 3-second transition delay is now active." -ForegroundColor Green
    }
} else {
    Write-Host "`nMigration failed. Please check the error above." -ForegroundColor Red
}