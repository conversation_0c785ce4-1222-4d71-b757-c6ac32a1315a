# Complete multiplayer timing test suite

Clear-Host

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║        MULTIPLAYER ROUND ADVANCE COMPREHENSIVE TEST          ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Cyan

Write-Host @"

This test will verify the round advance logic:
• 7-second maximum from round start
• All players answer + 3s (if total < 7s)
• Whichever happens FIRST

"@ -ForegroundColor Yellow

# Create monitoring code file
$monitorCode = @'
// Round Advance Timing Monitor v2
(() => {
    const style = 'font-weight: bold; font-size: 14px;';
    console.log('%c🟢 TIMING MONITOR ACTIVE', style + 'color: #00ff00;');
    
    let state = {
        roundStart: null,
        roundNum: 1,
        myAnswerTime: null,
        allAnswered: false,
        transitionStarted: null
    };
    
    // Helper to get elapsed time
    const elapsed = () => state.roundStart ? ((Date.now() - state.roundStart) / 1000).toFixed(1) : '?';
    
    // Override fetch to track API calls
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
        const [url, options] = args;
        
        if (url?.includes('submit-answer-handler')) {
            state.myAnswerTime = elapsed();
            console.log(`%c🔵 ANSWER SUBMITTED: ${state.myAnswerTime}s`, style + 'color: #00ffff;');
        }
        
        if (url?.includes('start-game-handler')) {
            console.log('%c🎮 GAME STARTING...', style + 'color: #ffff00;');
            state.roundStart = Date.now();
            state.roundNum = 1;
        }
        
        const response = await originalFetch.apply(this, args);
        
        // Check response for transition info
        if (url?.includes('submit-answer-handler')) {
            try {
                const data = await response.clone().json();
                if (data.inTransition) {
                    state.transitionStarted = elapsed();
                    console.log(`%c🟣 TRANSITION TRIGGERED AT: ${state.transitionStarted}s`, style + 'color: #ff00ff;');
                    console.log('%c   → Expecting round advance in 3 seconds', 'color: #ff00ff;');
                }
            } catch (e) {}
        }
        
        return response;
    };
    
    // Monitor DOM changes
    let lastQuestion = '';
    setInterval(() => {
        const question = document.querySelector('h2')?.textContent || '';
        
        // Detect round advance
        if (question && question !== lastQuestion && lastQuestion) {
            const advanceTime = elapsed();
            console.log(`%c✅ ROUND ${state.roundNum} ADVANCED AT: ${advanceTime}s`, style + 'color: #00ff00; font-size: 18px;');
            
            // Analysis
            if (state.myAnswerTime) {
                console.log(`%c   Your answer: ${state.myAnswerTime}s`, 'color: #888;');
                if (state.transitionStarted) {
                    const transitionDuration = (parseFloat(advanceTime) - parseFloat(state.transitionStarted)).toFixed(1);
                    console.log(`%c   Transition duration: ${transitionDuration}s`, 'color: #888;');
                }
            }
            
            // Reset for next round
            state.roundNum++;
            state.roundStart = Date.now();
            state.myAnswerTime = null;
            state.transitionStarted = null;
        }
        lastQuestion = question;
        
        // Detect game active
        if (!state.roundStart && document.querySelector('.score-value, [class*="score"]') && !document.querySelector('button:has-text("Start Game")')) {
            state.roundStart = Date.now();
            console.log('%c🎯 Round detected, timer started', style + 'color: #00ff00;');
        }
    }, 100);
    
    // Add summary command
    window.timingSummary = () => {
        console.log('%c📊 EXPECTED TIMINGS:', style + 'color: #00ff00;');
        console.table([
            { Scenario: 'Both at 1s', Expected: '4.0s', Reason: '1 + 3' },
            { Scenario: 'Both at 2s', Expected: '5.0s', Reason: '2 + 3' },
            { Scenario: 'Both at 5s', Expected: '7.0s', Reason: 'Cap (not 8s)' },
            { Scenario: 'One player only', Expected: '7.0s', Reason: 'Cap' },
            { Scenario: 'P1@1s, P2@3s', Expected: '6.0s', Reason: '3 + 3' }
        ]);
    };
    
    console.log('%c📌 Type timingSummary() to see expected behaviors', 'color: #888;');
})();
'@

# Save monitor code
$monitorPath = "$env:TEMP\timing-monitor.js"
$monitorCode | Out-File -FilePath $monitorPath -Encoding UTF8

Write-Host "SETUP INSTRUCTIONS:" -ForegroundColor Green
Write-Host "1. Opening game in 2 windows..." -ForegroundColor White
Start-Process "https://recognition-combine.vercel.app/"
Start-Sleep -Seconds 2
Start-Process "https://recognition-combine.vercel.app/"

Write-Host "2. Sign in with different accounts in each window" -ForegroundColor White
Write-Host "3. Create room in Window 1, join in Window 2" -ForegroundColor White
Write-Host "4. Open F12 console in BOTH windows" -ForegroundColor White
Write-Host "5. Paste this code in BOTH consoles:" -ForegroundColor White
Write-Host ""
Write-Host "=== COPY THIS CODE ===" -ForegroundColor Cyan -BackgroundColor DarkGray
Write-Host $monitorCode -ForegroundColor Gray
Write-Host "=== END OF CODE ===" -ForegroundColor Cyan -BackgroundColor DarkGray
Write-Host ""
Write-Host "6. Start the game and test these scenarios:" -ForegroundColor White
Write-Host ""

# Display test scenarios
$scenarios = @(
    @{
        Name = "FAST (1s)"
        Desc = "Both answer at 1 second"
        Expected = "4.0s"
        Color = "Green"
    },
    @{
        Name = "MEDIUM (2s)"
        Desc = "Both answer at 2 seconds"
        Expected = "5.0s"
        Color = "Blue"
    },
    @{
        Name = "LATE (5s)"
        Desc = "Both answer at 5 seconds"
        Expected = "7.0s (cap)"
        Color = "Red"
    },
    @{
        Name = "SOLO"
        Desc = "Only one player answers"
        Expected = "7.0s"
        Color = "Yellow"
    },
    @{
        Name = "STAGGER"
        Desc = "P1 at 1s, P2 at 3s"
        Expected = "6.0s"
        Color = "Magenta"
    }
)

foreach ($scenario in $scenarios) {
    Write-Host ""
    Write-Host "TEST: $($scenario.Name)" -ForegroundColor $scenario.Color -BackgroundColor Black
    Write-Host "Action: $($scenario.Desc)" -ForegroundColor White
    Write-Host "Expected: Round advances at $($scenario.Expected)" -ForegroundColor White
}

Write-Host ""
Write-Host "WHAT TO VERIFY:" -ForegroundColor Yellow
Write-Host "• 🔵 Blue = Answer submission time" -ForegroundColor White
Write-Host "• 🟣 Purple = 3-second transition starts" -ForegroundColor White
Write-Host "• ✅ Green = Round advance time" -ForegroundColor White
Write-Host ""
Write-Host "Monitor code saved to: $monitorPath" -ForegroundColor Gray
Write-Host ""
Write-Host "Press Enter to open the monitor code in notepad..." -ForegroundColor Green
Read-Host
notepad $monitorPath