const puppeteer = require('puppeteer');
const path = require('path');
const os = require('os');

// Configuration
const BASE_URL = 'http://localhost:3000';

// Detect browser based on platform
function getBrowserPath() {
  const platform = os.platform();
  
  if (platform === 'linux') {
    // WSL/Linux - check for chromium-browser first
    const fs = require('fs');
    if (fs.existsSync('/usr/bin/chromium-browser')) {
      console.log('Detected WSL environment, using system chromium-browser');
      return '/usr/bin/chromium-browser';
    }
  }
  
  // Let Puppeteer find Chrome/Chromium
  return undefined;
}

async function testMultiplayerJoin() {
  const browserPath = getBrowserPath();
  const launchOptions = {
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-gpu'],
    defaultViewport: { width: 1200, height: 800 }
  };
  
  if (browserPath) {
    launchOptions.executablePath = browserPath;
  }
  
  // Set display for WSL
  if (os.platform() === 'linux' && !process.env.DISPLAY) {
    process.env.DISPLAY = ':0';
  }

  const hostBrowser = await puppeteer.launch(launchOptions);
  const guestBrowser = await puppeteer.launch(launchOptions);
  
  const hostPage = await hostBrowser.newPage();
  const guestPage = await guestBrowser.newPage();
  
  try {
    console.log('=== Testing Multiplayer Join Issue ===\n');
    
    // Navigate both browsers
    await Promise.all([
      hostPage.goto(BASE_URL, { waitUntil: 'networkidle0' }),
      guestPage.goto(BASE_URL, { waitUntil: 'networkidle0' })
    ]);
    
    // Quick login for both
    console.log('Logging in both players...');
    
    // Host login
    await hostPage.click('button:has-text("Login")');
    await hostPage.waitForSelector('input[type="text"]', { visible: true });
    await hostPage.type('input[type="text"]', '<EMAIL>');
    await hostPage.type('input[type="password"]', 'fresh123');
    await hostPage.click('button[type="submit"]');
    await hostPage.waitForFunction(() => !document.querySelector('[role="dialog"]'), { timeout: 10000 });
    
    // Guest login
    await guestPage.click('button:has-text("Login")');
    await guestPage.waitForSelector('input[type="text"]', { visible: true });
    await guestPage.type('input[type="text"]', '<EMAIL>');
    await guestPage.type('input[type="password"]', 'guest123');
    await guestPage.click('button[type="submit"]');
    await guestPage.waitForFunction(() => !document.querySelector('[role="dialog"]'), { timeout: 10000 });
    
    console.log('✓ Both players logged in\n');
    
    // Switch to multiplayer
    await hostPage.click('button:has-text("Multiplayer Mode")');
    await hostPage.waitForTimeout(3000); // Wait for auth
    
    // Wait for Host Game button
    await hostPage.waitForFunction(
      () => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.some(btn => btn.textContent?.includes('Host Game'));
      },
      { timeout: 10000 }
    );
    
    // Create room
    console.log('HOST: Creating room...');
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent?.includes('Host Game'));
      btn?.click();
    });
    
    // Wait for room creation
    await hostPage.waitForFunction(
      () => document.body.textContent?.includes('Players (1/4)'),
      { timeout: 10000 }
    );
    
    // Get room code
    const roomCode = await hostPage.evaluate(() => {
      const match = document.body.textContent?.match(/Room: (.+?)\s/);
      return match?.[1];
    });
    
    console.log(`HOST: Created room with code: ${roomCode}`);
    
    // Guest joins
    await guestPage.click('button:has-text("Multiplayer Mode")');
    await guestPage.waitForTimeout(3000);
    
    console.log('GUEST: Looking for room to join...');
    
    // Try to join the room
    const joined = await guestPage.evaluate((code) => {
      const roomDivs = Array.from(document.querySelectorAll('div'));
      const roomDiv = roomDivs.find(div => div.textContent?.includes(code));
      if (roomDiv) {
        const joinBtn = roomDiv.querySelector('button:has-text("Join")');
        if (joinBtn) {
          joinBtn.click();
          return true;
        }
      }
      return false;
    }, roomCode);
    
    if (!joined) {
      throw new Error('Guest could not find room to join');
    }
    
    // Wait for guest to be in room
    await guestPage.waitForFunction(
      () => document.body.textContent?.includes('Ready'),
      { timeout: 10000 }
    );
    
    console.log('GUEST: Joined room successfully\n');
    
    // Now check player counts
    console.log('=== Checking Player Counts ===');
    
    // Initial check
    const checkPlayerCounts = async (label) => {
      const hostCount = await hostPage.evaluate(() => {
        const match = document.body.innerText.match(/Players \((\d+)\/\d+\)/);
        return match?.[1] || '0';
      });
      
      const guestCount = await guestPage.evaluate(() => {
        const match = document.body.innerText.match(/Players \((\d+)\/\d+\)/);
        return match?.[1] || '0';
      });
      
      console.log(`${label}:`);
      console.log(`  HOST sees: ${hostCount} player(s)`);
      console.log(`  GUEST sees: ${guestCount} player(s)`);
      console.log('');
      
      return { hostCount, guestCount };
    };
    
    // Check immediately
    await checkPlayerCounts('Immediately after join');
    
    // Wait and check again
    await new Promise(resolve => setTimeout(resolve, 3000));
    await checkPlayerCounts('After 3 seconds');
    
    // Check realtime logs
    console.log('=== Checking Console Logs ===');
    
    const hostLogs = await hostPage.evaluate(() => {
      const logs = [];
      // Capture any realtime-related logs
      return logs;
    });
    
    // Try manual actions to trigger update
    console.log('=== Trying Manual Refresh ===');
    
    // Look for refresh button
    const hasRefresh = await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      return buttons.some(btn => btn.textContent?.includes('Refresh') || btn.textContent?.includes('🔄'));
    });
    
    console.log(`Refresh button available: ${hasRefresh}`);
    
    // Final check
    await new Promise(resolve => setTimeout(resolve, 2000));
    const final = await checkPlayerCounts('Final check');
    
    if (final.hostCount !== '2') {
      console.log('❌ ISSUE CONFIRMED: Host does not see guest player');
      console.log('This indicates a Realtime synchronization problem');
    } else {
      console.log('✅ Both players see each other correctly');
    }
    
  } catch (error) {
    console.error('Test error:', error.message);
  } finally {
    console.log('\nKeeping browsers open for manual inspection...');
    // Don't close browsers - keep them open for debugging
  }
}

// Run the test
testMultiplayerJoin().catch(console.error);