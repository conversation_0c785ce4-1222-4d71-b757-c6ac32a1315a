import React, { useEffect, useState } from 'react';

interface TransitionCountdownProps {
  transitionDeadline: string | null;
  currentRoundAnswers: any[];
  totalPlayers: number;
}

export function TransitionCountdown({ 
  transitionDeadline, 
  currentRoundAnswers,
  totalPlayers 
}: TransitionCountdownProps) {
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);

  useEffect(() => {
    if (!transitionDeadline) {
      setTimeRemaining(null);
      return;
    }

    const updateCountdown = () => {
      const now = Date.now();
      const deadline = new Date(transitionDeadline).getTime();
      const remaining = Math.max(0, deadline - now);
      setTimeRemaining(Math.ceil(remaining / 1000));

      if (remaining <= 0) {
        setTimeRemaining(0);
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 100); // Update frequently for smooth countdown

    return () => clearInterval(interval);
  }, [transitionDeadline]);

  if (!transitionDeadline || timeRemaining === null) {
    return null;
  }

  const allPlayersAnswered = currentRoundAnswers.length === totalPlayers;

  return (
    <div className="text-center text-sm text-gray-400 mt-2">
      {allPlayersAnswered ? (
        <span>Next question in {timeRemaining}s...</span>
      ) : (
        <span>Time remaining: {timeRemaining}s</span>
      )}
    </div>
  );
}