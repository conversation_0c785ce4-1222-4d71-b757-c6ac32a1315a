-- HYBR<PERSON> APPROACH: Best of both worlds
-- Combines triggers for instant response + periodic checks for reliability

-- 1. Create a lightweight monitoring table to track last check
CREATE TABLE IF NOT EXISTS transition_monitor_state (
  id INT PRIMARY KEY DEFAULT 1,
  last_check TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT single_row CHECK (id = 1)
);

-- 2. Function to process transitions directly in database
CREATE OR REPLACE FUNCTION process_game_transitions()
RETURNS TABLE(room_id uuid, transitioned boolean) AS $$
DECLARE
  game_record RECORD;
  next_question_data JSONB;
  all_players JSONB;
BEGIN
  -- Process each game that needs transition
  FOR game_record IN 
    SELECT id, current_round_number, current_question_data, 
           original_player_ids, next_question_data as existing_next_question
    FROM game_rooms
    WHERE status = 'active' 
      AND transition_deadline IS NOT NULL
      AND transition_deadline <= NOW()
    FOR UPDATE SKIP LOCKED  -- Prevent concurrent processing
  LOOP
    -- Generate next question if needed
    IF game_record.existing_next_question IS NULL THEN
      -- For now, return empty question (would need proper generation logic)
      next_question_data := jsonb_build_object(
        'questionId', gen_random_uuid(),
        'choices', '[]'::jsonb
      );
    ELSE
      next_question_data := game_record.existing_next_question;
    END IF;
    
    -- Update the game room
    UPDATE game_rooms SET
      current_round_number = current_round_number + 1,
      current_question_data = next_question_data,
      current_round_answers = '[]'::jsonb,
      question_started_at = NOW(),
      transition_deadline = NOW() + INTERVAL '7 seconds',
      next_question_data = NULL,
      last_activity_timestamp = NOW()
    WHERE id = game_record.id;
    
    room_id := game_record.id;
    transitioned := true;
    RETURN NEXT;
  END LOOP;
  
  -- Update monitor state
  INSERT INTO transition_monitor_state (id, last_check) 
  VALUES (1, NOW()) 
  ON CONFLICT (id) 
  DO UPDATE SET last_check = NOW();
END;
$$ LANGUAGE plpgsql;

-- 3. Trigger for immediate response to answers
CREATE OR REPLACE FUNCTION check_transition_on_answer()
RETURNS TRIGGER AS $$
BEGIN
  -- When all players have answered, process immediately
  IF NEW.status = 'active' AND 
     NEW.transition_deadline IS NOT NULL AND 
     NEW.transition_deadline <= NOW() THEN
    -- Process transitions for this game
    PERFORM process_game_transitions();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS instant_transition_check ON game_rooms;
CREATE TRIGGER instant_transition_check
  AFTER UPDATE OF current_round_answers ON game_rooms
  FOR EACH ROW
  EXECUTE FUNCTION check_transition_on_answer();

-- 4. Periodic safety check (using pg_cron if available)
-- This catches any games that got "stuck"
CREATE OR REPLACE FUNCTION periodic_transition_check()
RETURNS void AS $$
BEGIN
  PERFORM process_game_transitions();
END;
$$ LANGUAGE plpgsql;

-- If pg_cron is available (1-minute minimum interval):
-- SELECT cron.schedule('game-transitions', '* * * * *', 'SELECT periodic_transition_check();');

-- 5. Manual check function for testing
CREATE OR REPLACE FUNCTION check_transitions_now()
RETURNS TABLE(room_id uuid, transitioned boolean) AS $$
BEGIN
  RETURN QUERY SELECT * FROM process_game_transitions();
END;
$$ LANGUAGE plpgsql;

-- Usage:
-- SELECT * FROM check_transitions_now();  -- Run manually to test
-- SELECT * FROM transition_monitor_state; -- Check last run time