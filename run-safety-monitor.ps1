# Manual Safety Monitor Runner
# Run this script whenever you want to check for abandoned games

Write-Host "Running transition safety monitor..." -ForegroundColor Yellow

# Load environment variables
$envPath = Join-Path $PSScriptRoot "supabase\.env"
if (Test-Path $envPath) {
    Get-Content $envPath | ForEach-Object {
        if ($_ -match '^([^=]+)=(.*)$') {
            [System.Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
}

$supabaseUrl = $env:SUPABASE_URL
$supabaseAnonKey = $env:SUPABASE_ANON_KEY

if (-not $supabaseUrl -or -not $supabaseAnonKey) {
    Write-Host "Error: Missing SUPABASE_URL or SUPABASE_ANON_KEY" -ForegroundColor Red
    Write-Host "Please ensure supabase/.env file exists with these values" -ForegroundColor Red
    exit 1
}

try {
    $response = Invoke-RestMethod `
        -Uri "$supabaseUrl/functions/v1/transition-safety-monitor" `
        -Method POST `
        -Headers @{
            "Authorization" = "Bearer $supabaseAnonKey"
            "Content-Type" = "application/json"
        }

    Write-Host "`nSafety Monitor Results:" -ForegroundColor Green
    Write-Host "Games Checked: $($response.gamesChecked)" -ForegroundColor Cyan
    Write-Host "Truly Abandoned: $($response.trulyAbandoned)" -ForegroundColor Cyan
    Write-Host "Transitions Processed: $($response.transitionsProcessed)" -ForegroundColor Cyan
    
    if ($response.transitionsProcessed -gt 0) {
        Write-Host "`n✅ Successfully transitioned $($response.transitionsProcessed) abandoned game(s)!" -ForegroundColor Green
    } else {
        Write-Host "`n✅ No abandoned games found - all games are healthy!" -ForegroundColor Green
    }
} catch {
    Write-Host "`nError calling safety monitor:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
    }
}