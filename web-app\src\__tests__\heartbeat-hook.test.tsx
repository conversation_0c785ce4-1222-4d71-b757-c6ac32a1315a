import { renderHook, act, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useHeartbeat } from '../hooks/useHeartbeat';
import { AuthProvider } from '../providers/AuthProvider';
import React from 'react';

// Mock dependencies
vi.mock('../lib/supabaseClient', () => ({
  default: {
    auth: {
      getSession: vi.fn(),
      refreshSession: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: { subscription: { unsubscribe: vi.fn() } }
      }))
    },
    functions: {
      invoke: vi.fn()
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn()
    }))
  }
}));

vi.mock('../providers/AuthProvider', () => ({
  useAuth: vi.fn(),
  AuthProvider: ({ children }: { children: React.ReactNode }) => children
}));

import supabase from '../lib/supabaseClient';
import { useAuth } from '../providers/AuthProvider';

describe('useHeartbeat Hook Direct Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should not send heartbeat when user is not authenticated', async () => {
    (useAuth as any).mockReturnValue({
      user: null,
      loading: false
    });

    const { result } = renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    // Advance timers
    act(() => {
      vi.advanceTimersByTime(6000);
    });

    expect(supabase.functions.invoke).not.toHaveBeenCalled();
  });

  it('should send heartbeat when authenticated with valid session', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'valid-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    (supabase.functions.invoke as any).mockResolvedValue({
      data: { success: true },
      error: null
    });

    const { result } = renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    // Wait for initial heartbeat
    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(supabase.functions.invoke).toHaveBeenCalledWith('heartbeat-handler', {
      body: { roomId: 'test-room-id', action: 'ping' }
    });
  });

  it('should handle 401 error and call onSessionExpired', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'valid-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    const error401 = new Error('Unauthorized');
    (error401 as any).status = 401;

    (supabase.functions.invoke as any).mockResolvedValue({
      data: null,
      error: error401
    });

    const onSessionExpired = vi.fn();

    const { result } = renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000,
        onSessionExpired
      })
    );

    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(onSessionExpired).toHaveBeenCalled();
  });

  it('should refresh session when token is close to expiry', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    // Session expires in 4 minutes
    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'expiring-token',
          expires_at: Math.floor(Date.now() / 1000) + 240
        }
      },
      error: null
    });

    (supabase.auth.refreshSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'new-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    (supabase.functions.invoke as any).mockResolvedValue({
      data: { success: true },
      error: null
    });

    const { result } = renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(supabase.auth.refreshSession).toHaveBeenCalled();
  });

  it('should stop heartbeat on unmount', async () => {
    (useAuth as any).mockReturnValue({
      user: { id: 'user-123' },
      loading: false
    });

    (supabase.auth.getSession as any).mockResolvedValue({
      data: {
        session: {
          access_token: 'valid-token',
          expires_at: Math.floor(Date.now() / 1000) + 3600
        }
      },
      error: null
    });

    (supabase.functions.invoke as any).mockResolvedValue({
      data: { success: true },
      error: null
    });

    const { unmount } = renderHook(() => 
      useHeartbeat({
        roomId: 'test-room-id',
        interval: 5000
      })
    );

    await act(async () => {
      vi.advanceTimersByTime(1100);
      await Promise.resolve();
    });

    expect(supabase.functions.invoke).toHaveBeenCalledTimes(1);

    unmount();

    act(() => {
      vi.advanceTimersByTime(10000);
    });

    // Should still be 1 call after unmount
    expect(supabase.functions.invoke).toHaveBeenCalledTimes(1);
  });
});