const { spawn } = require('child_process');
const path = require('path');

console.log('=== Starting Multiplayer Test Environment ===\n');

// Check if dev server is already running
const checkDevServer = async () => {
  try {
    const response = await fetch('http://localhost:3000');
    return response.ok;
  } catch (error) {
    return false;
  }
};

const runTest = async () => {
  const serverRunning = await checkDevServer();
  
  if (serverRunning) {
    console.log('✓ Dev server is already running on port 3000');
    console.log('Starting multiplayer test...\n');
    
    // Run the test
    const test = spawn('node', ['test-multiplayer-game-flow.js'], {
      cwd: __dirname,
      stdio: 'inherit'
    });
    
    test.on('close', (code) => {
      process.exit(code);
    });
  } else {
    console.log('Dev server not detected. Please start it with:');
    console.log('  npm run dev\n');
    console.log('Then run this test again.');
    process.exit(1);
  }
};

// Add delay to ensure everything is loaded
setTimeout(runTest, 1000);