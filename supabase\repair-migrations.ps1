# Repair migration history to match local files
Write-Host "Repairing migration history..." -ForegroundColor Yellow

# First, mark the mismatched remote migration as reverted
supabase migration repair --status reverted 20250129000000

# Then mark all the local migrations as applied in the correct order
$migrations = @(
    "20241201000003",
    "20250128000000",
    "20250128000001", 
    "20250519003910",
    "20250519012600",
    "20250519022730",
    "20250526002702",
    "20250527000601",
    "20250527003717",
    "20250604043815",
    "20250615000001",
    "20250617000000",
    "20250625000000",
    "20250629091720",
    "20250629091952"
)

foreach ($migration in $migrations) {
    Write-Host "Marking $migration as applied..." -ForegroundColor Green
    supabase migration repair --status applied $migration
}

Write-Host "Migration history repaired!" -ForegroundColor Green
Write-Host "Now you can push the new migration." -ForegroundColor Yellow