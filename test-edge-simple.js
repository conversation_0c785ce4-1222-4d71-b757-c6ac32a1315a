const fetch = require('node-fetch');

const SUPABASE_URL = 'https://xmyxuvuimebjltnaamox.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY';

async function testEdgeFunction() {
  try {
    // Test OPTIONS request first
    console.log('Testing edge function availability...\n');
    
    console.log('1. Testing OPTIONS request (CORS)...');
    const optionsResponse = await fetch(`${SUPABASE_URL}/functions/v1/start-game-handler`, {
      method: 'OPTIONS',
      headers: {
        'apikey': SUPABASE_ANON_KEY
      }
    });
    
    console.log('OPTIONS Response status:', optionsResponse.status);
    console.log('CORS Headers:', {
      'Access-Control-Allow-Origin': optionsResponse.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Headers': optionsResponse.headers.get('Access-Control-Allow-Headers')
    });

    // Test POST without auth (should get 401)
    console.log('\n2. Testing POST without auth (expecting 401)...');
    const noAuthResponse = await fetch(`${SUPABASE_URL}/functions/v1/start-game-handler`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        roomId: 'test-room-id'
      })
    });
    
    console.log('No-auth Response status:', noAuthResponse.status);
    const noAuthText = await noAuthResponse.text();
    console.log('No-auth Response:', noAuthText);
    
    // Test with a dummy auth token (should get specific error)
    console.log('\n3. Testing POST with dummy auth token...');
    const dummyAuthResponse = await fetch(`${SUPABASE_URL}/functions/v1/start-game-handler`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer dummy-token',
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        roomId: 'test-room-id'
      })
    });
    
    console.log('Dummy auth Response status:', dummyAuthResponse.status);
    const dummyAuthText = await dummyAuthResponse.text();
    console.log('Dummy auth Response:', dummyAuthText);
    
    console.log('\n✓ Edge function is accessible and responding!');

  } catch (error) {
    console.error('Error:', error);
  }
}

testEdgeFunction();