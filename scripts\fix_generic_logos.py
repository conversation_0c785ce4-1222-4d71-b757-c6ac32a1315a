#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to identify and fix players with generic team logos.
1. Identifies players with generic logos based on file size
2. Removes them from players_game_data.json
3. Prepares a list for re-fetching
"""

import os
import json
import shutil
from datetime import datetime
import logging

# Configuration
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)

# File paths
GAME_DATA_FILE = os.path.join(PROJECT_ROOT, "web-app", "public", "data", "players_game_data.json")
PLAYERS_IMAGES_DIR = os.path.join(PROJECT_ROOT, "web-app", "public", "players_images")
BACKUP_DIR = os.path.join(SCRIPT_DIR, "backups", datetime.now().strftime("%Y%m%d_%H%M%S"))
LOG_FILE = os.path.join(SCRIPT_DIR, "fix_generic_logos.log")

# HARDCODED list of problematic player image paths (relative to PLAYERS_IMAGES_DIR)
GENERIC_LOGO_PATHS = [
    # Jacksonville Jaguars
    "jacksonville-jaguars/b.j.-green-ii.jpg",
    "jacksonville-jaguars/bhayhul-tuten.jpg",
    "jacksonville-jaguars/cam-camper.jpg",
    "jacksonville-jaguars/camron-silmon-craig.jpg",
    "jacksonville-jaguars/darius-lassiter.jpg",
    "jacksonville-jaguars/dorian-singer.jpg",
    "jacksonville-jaguars/jaquinden-jackson.jpg",
    "jacksonville-jaguars/jonah-monheim.jpg",
    "jacksonville-jaguars/rayuan-lane-iii.jpg",
    "jacksonville-jaguars/travis-hunter.jpg",
    "jacksonville-jaguars/wyatt-milum.jpg",
    # Miami Dolphins
    "miami-dolphins/aj-henning.jpg",
    "miami-dolphins/jalin-conyers.jpg",
    "miami-dolphins/trevonn-rybka.jpg",
    "miami-dolphins/willie-gay-jr.jpg",
]

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, mode='w', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def backup_file(file_path):
    """Create a backup of a file before modifying it."""
    if not os.path.exists(file_path):
        logging.error(f"File not found for backup: {file_path}")
        return False
    
    os.makedirs(BACKUP_DIR, exist_ok=True)
    backup_path = os.path.join(BACKUP_DIR, os.path.basename(file_path))
    shutil.copy2(file_path, backup_path)
    logging.info(f"Created backup at: {backup_path}")
    return True

def find_generic_logos_hardcoded():
    """Find all players in the game data whose local_image_path matches the hardcoded list."""
    # Load the game data
    with open(GAME_DATA_FILE, 'r', encoding='utf-8') as f:
        game_data = json.load(f)
    
    generic_logo_players = []
    for player in game_data:
        if player.get('local_image_path') in GENERIC_LOGO_PATHS:
            generic_logo_players.append({
                'team': player.get('team_name'),
                'player_name': player.get('player_name'),
                'image_path': player.get('local_image_path'),
                'file_size': None  # Not used here
            })
            logging.info(f"Will remove: {player.get('local_image_path')} ({player.get('player_name')})")
    return generic_logo_players

def update_game_data(generic_logo_players):
    """Remove players with generic logos from the game data file."""
    if not backup_file(GAME_DATA_FILE):
        return False
        
    try:
        with open(GAME_DATA_FILE, 'r', encoding='utf-8') as f:
            game_data = json.load(f)
            
        # Create a set of player names to remove
        players_to_remove = {p['player_name'] for p in generic_logo_players}
        
        # Filter out players with generic logos
        original_count = len(game_data)
        game_data = [p for p in game_data if p['player_name'] not in players_to_remove]
        removed_count = original_count - len(game_data)
        
        # Save updated game data
        with open(GAME_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(game_data, f, indent=4)
            
        logging.info(f"Removed {removed_count} players with generic logos from game data")
        return True
        
    except Exception as e:
        logging.error(f"Error updating game data: {e}")
        return False

def generate_refetch_list(generic_logo_players):
    """Generate a list of players that need to be re-fetched."""
    refetch_file = os.path.join(SCRIPT_DIR, "players_to_refetch.json")
    
    try:
        # Format the data for the fetch script
        refetch_data = {
            'players': [
                {
                    'player_name': p['player_name'],
                    'team_slug': p['team'],
                    'current_image_path': p['image_path']
                }
                for p in generic_logo_players
            ],
            'generated_at': datetime.now().isoformat(),
            'total_players': len(generic_logo_players)
        }
        
        with open(refetch_file, 'w', encoding='utf-8') as f:
            json.dump(refetch_data, f, indent=4)
            
        logging.info(f"Generated refetch list with {len(generic_logo_players)} players at: {refetch_file}")
        return True
        
    except Exception as e:
        logging.error(f"Error generating refetch list: {e}")
        return False

def main():
    logging.info("=== Starting Generic Logo Cleanup (HARDCODED LIST) ===")
    
    # Find all generic logos (from hardcoded list)
    generic_logo_players = find_generic_logos_hardcoded()
    logging.info(f"Found {len(generic_logo_players)} players with generic logos (from hardcoded list)")
    
    if not generic_logo_players:
        logging.info("No generic logos found. Nothing to do.")
        return
        
    # Update game data
    if not update_game_data(generic_logo_players):
        logging.error("Failed to update game data. Aborting.")
        return
        
    # Generate refetch list
    if not generate_refetch_list(generic_logo_players):
        logging.error("Failed to generate refetch list.")
        return
        
    logging.info("=== Generic Logo Cleanup Complete ===")
    logging.info(f"Summary:")
    logging.info(f"- Found {len(generic_logo_players)} players with generic logos (from hardcoded list)")
    logging.info(f"- Removed them from {GAME_DATA_FILE}")
    logging.info(f"- Generated refetch list at scripts/players_to_refetch.json")
    logging.info(f"- Full backup created at {BACKUP_DIR}")

if __name__ == "__main__":
    main() 