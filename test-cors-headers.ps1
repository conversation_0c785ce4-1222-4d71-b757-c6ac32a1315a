# Test CORS headers for login handler function
Write-Host "Testing CORS headers for login-handler function..." -ForegroundColor Green

# Test OPTIONS request
Write-Host "`nTesting OPTIONS request..." -ForegroundColor Yellow
try {
    $optionsResponse = Invoke-WebRequest -Uri "http://127.0.0.1:54321/functions/v1/login-handler" -Method OPTIONS -Headers @{"Origin"="http://localhost:3000"} -UseBasicParsing
    Write-Host "OPTIONS Response Status: $($optionsResponse.StatusCode)" -ForegroundColor Cyan
    Write-Host "OPTIONS Response Headers:" -ForegroundColor Cyan
    $optionsResponse.Headers | Format-Table -AutoSize
} catch {
    Write-Host "OPTIONS request failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test POST request with invalid credentials
Write-Host "`nTesting POST request with invalid credentials..." -ForegroundColor Yellow
try {
    $body = @{
        identifier = "<EMAIL>"
        password = "wrongpassword"
    } | ConvertTo-Json

    $postResponse = Invoke-WebRequest -Uri "http://127.0.0.1:54321/functions/v1/login-handler" -Method POST -Body $body -ContentType "application/json" -Headers @{"Origin"="http://localhost:3000"} -UseBasicParsing
    Write-Host "POST Response Status: $($postResponse.StatusCode)" -ForegroundColor Cyan
    Write-Host "POST Response Headers:" -ForegroundColor Cyan
    $postResponse.Headers | Format-Table -AutoSize
    Write-Host "POST Response Body:" -ForegroundColor Cyan
    $responseContent = $postResponse.Content | ConvertFrom-Json
    Write-Host "Original Status: $($responseContent._status)" -ForegroundColor Yellow
    Write-Host "Success: $($responseContent._success)" -ForegroundColor Yellow
    Write-Host "Error: $($responseContent.error)" -ForegroundColor Yellow
    
    # Check if CORS headers are present
    if ($postResponse.Headers.ContainsKey("access-control-allow-origin")) {
        Write-Host "✅ CORS headers are present in POST response!" -ForegroundColor Green
    } else {
        Write-Host "❌ CORS headers are missing in POST response!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "POST request failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nCORS test completed." -ForegroundColor Green 