{"name": "recognition-combine", "version": "1.0.0", "private": true, "scripts": {"dev": "cd web-app && npm run dev", "build": "cd web-app && npm run build", "start": "cd web-app && npm run start", "lint": "cd web-app && npm run lint"}, "dependencies": {"@modelcontextprotocol/server-puppeteer": "^2025.5.12", "@supabase/supabase-js": "^2.50.3", "autoprefixer": "^10.4.17", "eslint": "^9.29.0", "framer-motion": "^11.0.8", "next": "^15.3.3", "postcss": "^8.4.35", "puppeteer": "^24.11.2", "puppeteer-core": "^24.11.2", "tailwindcss": "^3.4.1"}, "workspaces": ["web-app"], "devDependencies": {"brace-expansion": "^4.0.0", "lightningcss": "^1.29.3", "minimatch": "^10.0.1"}, "engines": {"node": "20.x"}}