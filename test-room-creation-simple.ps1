# Simple Room Creation Test
# Isolate the room creation issue

$baseUrl = "https://xmyxuvuimebjltnaamox.supabase.co"
$anonKey = $env:SUPABASE_ANON_KEY
$authToken = $env:SUPABASE_AUTH_TOKEN

Write-Host "[TEST] Simple Room Creation Test" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

if (-not $anonKey -or -not $authToken) {
    Write-Host "[ERROR] Missing environment variables" -ForegroundColor Red
    exit 1
}

# Get user ID from token
$tokenParts = $authToken.Split('.')
$payload = $tokenParts[1]
while ($payload.Length % 4 -ne 0) { $payload += "=" }
$tokenData = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($payload)) | ConvertFrom-Json
$userId = $tokenData.sub

Write-Host "[INFO] User ID: $userId" -ForegroundColor Green

$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $authToken"
    "apikey" = $anonKey
    "Prefer" = "return=representation"
}

# Minimal room data
$roomData = @{
    host_id = $userId
    title = "Test Room $(Get-Random)"
    status = "waiting"
} | ConvertTo-Json

Write-Host "[TEST] Creating room with minimal data..." -ForegroundColor Yellow
Write-Host "[DATA] $roomData" -ForegroundColor Gray

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/rest/v1/game_rooms" `
        -Method POST `
        -Headers $headers `
        -Body $roomData
    
    Write-Host "[SUCCESS] Room created!" -ForegroundColor Green
    Write-Host "[RESPONSE] $($response | ConvertTo-Json -Depth 2)" -ForegroundColor White
    
} catch {
    Write-Host "[ERROR] Room creation failed: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "[STATUS] HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorContent = $reader.ReadToEnd()
            Write-Host "[DETAILS] Error content: $errorContent" -ForegroundColor Red
        } catch {
            Write-Host "[DEBUG] Could not read error details" -ForegroundColor Yellow
        }
    }
} 