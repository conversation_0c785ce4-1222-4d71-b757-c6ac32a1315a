# Deploy submit-answer-handler Edge Function with verbose output

Write-Host "Starting deployment process..." -ForegroundColor Cyan
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow

# Check if supabase CLI is available
Write-Host "Checking Supabase CLI..." -ForegroundColor Yellow
npx supabase --version

# Get project status
Write-Host "`nGetting project status..." -ForegroundColor Yellow
npx supabase status

Write-Host "`nDeploying submit-answer-handler Edge Function..." -ForegroundColor Cyan

# Deploy the function with verbose output
npx supabase functions deploy submit-answer-handler --no-verify-jwt --debug

if ($LASTEXITCODE -ne 0) {
    Write-Host "Deployment failed with exit code: $LASTEXITCODE" -ForegroundColor Red
    exit 1
}

Write-Host "submit-answer-handler deployed successfully!" -ForegroundColor Green