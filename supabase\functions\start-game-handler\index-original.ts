﻿// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
/// <reference types="https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts" />

import { serve } from "https://deno.land/std@0.177.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { corsHeaders } from "../_shared/cors.ts"

// --- Helper: Simulate fetching all player data (adapt to your actual playerData source) ---
interface PlayerData { 
  id: number; 
  player_name: string; 
  team_name: string;
  local_image_path: string | null; // This will be the path relative to /public/players_images/
  jersey_number: string | number | null;
  position: string | null;
  height: string | null;
  weight: string | number | null;
  age_or_dob: string | number | null;
  experience: string | number | null;
  college: string | null;
}

interface PlayerChoice { 
  name: string; 
  isCorrect: boolean; 
}

// Match the frontend's expected structure for multiplayer
interface PlayerQuestion {
  questionId: string;
  correctPlayerId: number;
  imageUrl: string;
  choices: PlayerChoice[];
  correctChoiceName: string;
}

async function generateNewQuestionForRoom(supabaseAdmin: ReturnType<typeof createClient>, userClient: ReturnType<typeof createClient>, roomId: string): Promise<PlayerQuestion | null> {
  console.log('[EDGE_START_GAME] generateNewQuestionForRoom called for room:', roomId);
  
  try {
    // Query real player data from the players_data table
    console.log('[EDGE_START_GAME] Fetching players from players_data table...');
    const { data: playersData, error: fetchError } = await userClient
      .from('players_data')
      .select('id, player_name, team_name, local_image_path, jersey_number, position, height, weight, age_or_dob, experience, college')
      .not('local_image_path', 'is', null)
      .limit(100); // Get a reasonable sample of players with images

    if (fetchError) {
      console.error('[EDGE_START_GAME] Error fetching players from database:', {
        message: fetchError.message,
        code: fetchError.code,
        details: fetchError.details,
        hint: fetchError.hint,
        // Log full error object for debugging
        fullError: JSON.stringify(fetchError)
      });
      // Fallback to sample data if database query fails
      const samplePlayers: PlayerData[] = [
        { id: 1001, player_name: 'Sample Player Alpha', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '99', position: 'QB', height: '6-2', weight: 210, age_or_dob: 25, experience: '3', college: 'Sample U' },
        { id: 1002, player_name: 'Sample Player Bravo', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '88', position: 'WR', height: '6-0', weight: 195, age_or_dob: 23, experience: 'R', college: 'Sample State' },
        { id: 1003, player_name: 'Sample Player Charlie', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '77', position: 'LB', height: '6-3', weight: 245, age_or_dob: 28, experience: '6', college: 'Sample Tech' },
        { id: 1004, player_name: 'Sample Player Delta', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '22', position: 'RB', height: '5-11', weight: 215, age_or_dob: 24, experience: '2', college: 'Sample College' },
        { id: 1005, player_name: 'Sample Player Echo', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '55', position: 'DE', height: '6-5', weight: 275, age_or_dob: 26, experience: '4', college: 'Sample University' },
      ];
      console.log('[EDGE_START_GAME] Using fallback sample players due to database error');
      const shuffledPlayers = [...samplePlayers].sort(() => 0.5 - Math.random());
      const correctPlayer = shuffledPlayers[0];
      const distractors = shuffledPlayers.slice(1, 4);
      
      const choices: PlayerChoice[] = [
        { name: correctPlayer.player_name, isCorrect: true },
        ...distractors.map((p: PlayerData) => ({ name: p.player_name, isCorrect: false }))
      ].sort(() => 0.5 - Math.random());

      return {
        questionId: globalThis.crypto.randomUUID(),
        correctPlayerId: correctPlayer.id,
        imageUrl: `/players_images/${correctPlayer.local_image_path}`,
        choices: choices,
        correctChoiceName: correctPlayer.player_name
      };
    }

    if (!playersData || playersData.length < 4) {
      console.error(`[EDGE_START_GAME] Not enough players in database. Found: ${playersData?.length || 0}`);
      return null;
    }

    console.log(`[EDGE_START_GAME] Successfully fetched ${playersData.length} players from database`);

    // Shuffle players to pick a correct answer and distractors
    console.log('[EDGE_START_GAME] Shuffling players and selecting correct answer...');
    const shuffledPlayers = [...playersData].sort(() => 0.5 - Math.random());
    const correctPlayer = shuffledPlayers[0];
    const distractors = shuffledPlayers.slice(1, 4); // Need 3 distractors

    console.log(`[EDGE_START_GAME] Correct player selected: ${correctPlayer.player_name} (ID: ${correctPlayer.id})`);
    console.log(`[EDGE_START_GAME] Distractors selected: ${distractors.map((p: PlayerData) => p.player_name).join(', ')}`);

    if (distractors.length < 3) {
      console.error('[EDGE_START_GAME] Could not get enough distractors from players');
      return null;
    }

    const choices: PlayerChoice[] = [
      { name: correctPlayer.player_name, isCorrect: true },
      ...distractors.map((p: PlayerData) => ({ name: p.player_name, isCorrect: false }))
    ].sort(() => 0.5 - Math.random()); // Shuffle choices

    console.log(`[EDGE_START_GAME] Choices created and shuffled: ${choices.map(c => `${c.name}(${c.isCorrect ? 'CORRECT' : 'wrong'})`).join(', ')}`);

    const question: PlayerQuestion = {
      questionId: globalThis.crypto.randomUUID(),
      correctPlayerId: correctPlayer.id,
      // Construct the full image URL for the client
      imageUrl: `/players_images/${correctPlayer.local_image_path}`,
      choices: choices,
      correctChoiceName: correctPlayer.player_name // Store the correct choice name
    };
    
    console.log('[EDGE_START_GAME] Generated question successfully:', {
      questionId: question.questionId,
      correctPlayerId: question.correctPlayerId,
      imageUrl: question.imageUrl,
      correctChoiceName: question.correctChoiceName,
      totalChoices: question.choices.length
    });
    return question;
  } catch (error) {
    console.error('[EDGE_START_GAME] Unexpected error in generateNewQuestionForRoom:', error);
    return null;
  }
}

console.log('[EDGE_FN_LOAD] start-game-handler function script loaded.'); // Log when script itself is loaded

serve(async (req: Request) => {
  console.log(`[EDGE_START_GAME] Request received. Method: ${req.method}, URL: ${req.url}`);
  
  if (req.method === 'OPTIONS') {
    console.log('[EDGE_START_GAME] OPTIONS request, responding with CORS.');
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[EDGE_START_GAME] Processing POST request for start-game-handler');
    
    interface StartGameRequestBody {
      roomId?: string;
    }
    
    // Environment variable checks
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    
    // Fallback: If service role key is missing, use anon key for admin client
    // This is safe for our use case as we're only reading public data
    const effectiveServiceRoleKey = supabaseServiceRoleKey || supabaseAnonKey

    if ((!supabaseUrl || !supabaseAnonKey)) {
      console.error('[EDGE_START_GAME] CRITICAL: Missing environment variables');
      console.error('[EDGE_START_GAME] SUPABASE_URL:', supabaseUrl ? 'Present' : 'MISSING');
      console.error('[EDGE_START_GAME] SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Present' : 'MISSING');
      console.error('[EDGE_START_GAME] SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceRoleKey ? 'Present' : 'MISSING');
      throw new Error('Server configuration error: Missing Supabase credentials.')
    }
    console.log('[EDGE_START_GAME] Environment variables loaded successfully');
    
    // Parse request body
    console.log('[EDGE_START_GAME] Parsing request body...');
    const requestBody: StartGameRequestBody = await req.json()
    console.log('[EDGE_START_GAME] Parsed request body:', requestBody);
    const { roomId } = requestBody;

    if (!roomId) {
      console.error('[EDGE_START_GAME] Missing roomId in request body');
      throw new Error('Room ID is required.')
    }
    console.log(`[EDGE_START_GAME] Processing start game for room ID: ${roomId}`);
    
    // Authentication
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('[EDGE_START_GAME] Missing Authorization header');
      throw new Error('User not authenticated: Missing Authorization header.')
    }
    console.log('[EDGE_START_GAME] Authorization header present, verifying user...');

    const userClient = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } },
    })
    console.log('[EDGE_START_GAME] User client created, getting user...');
    
    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError) {
      console.error('[EDGE_START_GAME] User authentication error:', userError);
      throw new Error('User not authenticated: ' + userError.message)
    }
    if (!user) {
      console.error('[EDGE_START_GAME] No user found in session');
      throw new Error('User not authenticated or not found.')
    }
    console.log(`[EDGE_START_GAME] User authenticated successfully. User ID: ${user.id}`);
    const requesterId = user.id

    // Create admin client
    console.log('[EDGE_START_GAME] Creating admin client...');
    const supabaseAdmin = createClient(supabaseUrl, effectiveServiceRoleKey)
    console.log('[EDGE_START_GAME] Admin client created successfully');

    console.log(`[EDGE_START_GAME] Processing start game request for room ${roomId} by user ${requesterId}`)

    // CRITICAL: Fetch current room state first for optimistic locking validation
    console.log(`[EDGE_START_GAME] [OPTIMISTIC_LOCK_SETUP] Fetching current room state for validation and race condition detection...`);
    const { data: roomData, error: roomFetchError } = await supabaseAdmin
      .from('game_rooms')
      .select('*, game_players(user_id, is_ready)') // Fetch nested players
      .eq('id', roomId)
      .single()

    if (roomFetchError) {
      console.error(`[EDGE_START_GAME] Database error fetching room ${roomId}:`, roomFetchError);
      throw new Error('Database error: ' + roomFetchError.message)
    }
    if (!roomData) {
      console.error(`[EDGE_START_GAME] Room ${roomId} not found in database`);
      throw new Error('Room not found.')
    }
    console.log(`[EDGE_START_GAME] [ROOM_STATE_SNAPSHOT] Current room state:`, {
      id: roomData.id,
      host_id: roomData.host_id,
      status: roomData.status,
      playersCount: roomData.game_players?.length || 0,
      created_at: roomData.created_at,
      game_start_timestamp: roomData.game_start_timestamp,
      last_activity_timestamp: roomData.last_activity_timestamp
    });

    // 1. Verify caller is the host
    console.log(`[EDGE_START_GAME] Verifying host permissions. Room host: ${roomData.host_id}, Requester: ${requesterId}`);
    if (roomData.host_id !== requesterId) {
      console.error(`[EDGE_START_GAME] Permission denied. User ${requesterId} is not host of room ${roomId} (host: ${roomData.host_id})`);
      return new Response(JSON.stringify({ 
        error: 'Only the host can start the game.',
        details: `User ${requesterId} is not the host of room ${roomId}. Host is: ${roomData.host_id}`
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 403, // 403 Forbidden for permission issues
      });
    }
    console.log('[EDGE_START_GAME] Host verification passed');

    // 2. CRITICAL: Verify game is in 'waiting' status with enhanced race condition detection
    console.log(`[EDGE_START_GAME] [STATUS_VALIDATION] Checking room status. Current status: ${roomData.status}`);
    if (roomData.status !== 'waiting') {
      const errorMessage = `Invalid room status. Expected: 'waiting', Actual: '${roomData.status}'. Game may have already started or is finished.`;
      console.error(`[EDGE_START_GAME] ${errorMessage}`);
      
      // Enhanced 409 response with comprehensive race condition analysis
      const timeElapsedSinceCreation = roomData.created_at ? Date.now() - new Date(roomData.created_at).getTime() : null;
      const gameStartTime = roomData.game_start_timestamp ? new Date(roomData.game_start_timestamp).toISOString() : null;
      const timeSinceGameStart = gameStartTime ? Date.now() - new Date(gameStartTime).getTime() : null;
      
      console.error(`[EDGE_START_GAME] [RACE_CONDITION_ANALYSIS] Room status conflict analysis:`, {
        roomId,
        currentStatus: roomData.status,
        expectedStatus: 'waiting',
        timeElapsedSinceCreation: timeElapsedSinceCreation ? `${Math.round(timeElapsedSinceCreation / 1000)}s` : 'unknown',
        gameStartTimestamp: gameStartTime,
        timeSinceGameStart: timeSinceGameStart ? `${Math.round(timeSinceGameStart / 1000)}s ago` : 'N/A',
        requesterId,
        hostId: roomData.host_id,
        timestamp: new Date().toISOString(),
        isRecentStart: timeSinceGameStart ? timeSinceGameStart < 30000 : false // Within last 30 seconds
      });
      
      // Determine conflict type for better client handling
      let conflictType = 'INVALID_STATUS';
      let suggestion = 'Room is in an invalid state for starting a game.';
      
      if (roomData.status === 'active') {
        conflictType = timeSinceGameStart && timeSinceGameStart < 30000 ? 'RACE_CONDITION_ALREADY_STARTED' : 'GAME_ALREADY_ACTIVE';
        suggestion = timeSinceGameStart && timeSinceGameStart < 30000 
          ? 'The game has already been started, possibly by a concurrent request.' 
          : 'The game was started earlier and is currently active.';
      }
      
      return new Response(JSON.stringify({ 
        error: 'Game has already started or cannot be started in current state.',
        details: errorMessage,
        currentStatus: roomData.status,
        gameStartTime: gameStartTime,
        conflictType: conflictType,
        suggestion: suggestion,
        timestamp: new Date().toISOString()
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 409, // 409 Conflict is appropriate for "already started"
      });
    }
    console.log('[EDGE_START_GAME] Room status verification passed');

    // 3. Verify player conditions (readiness, minimum count)
    const playersInRoom = roomData.game_players || [];
    console.log(`[EDGE_START_GAME] [PLAYER_VALIDATION] Checking player readiness. Players in room: ${playersInRoom.length}`);
    console.log(`[EDGE_START_GAME] Player details:`, playersInRoom.map((p: {user_id: string, is_ready: boolean}) => ({ user_id: p.user_id, is_ready: p.is_ready })));
    
    const allPlayersReady = playersInRoom.length > 0 && playersInRoom.every((p: {is_ready: boolean}) => p.is_ready);
    const minPlayersMet = playersInRoom.length >= 2; // Changed back to 2 for production
    const readyPlayerCount = playersInRoom.filter((p: {is_ready: boolean}) => p.is_ready).length;
    const notReadyPlayerCount = playersInRoom.length - readyPlayerCount;

    console.log(`[EDGE_START_GAME] Player readiness analysis:`, {
      totalPlayers: playersInRoom.length,
      readyPlayers: readyPlayerCount,
      notReadyPlayers: notReadyPlayerCount,
      allPlayersReady,
      minPlayersMet,
      requiredMinimum: 2
    });

    if (!allPlayersReady) {
      console.error('[EDGE_START_GAME] Not all players are ready');
      return new Response(JSON.stringify({ 
        error: 'Not all players are ready.',
        details: `${notReadyPlayerCount} out of ${playersInRoom.length} players are not ready.`,
        readyCount: readyPlayerCount,
        totalCount: playersInRoom.length
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400, // 400 Bad Request for validation failures
      });
    }
    if (!minPlayersMet) {
      console.error('[EDGE_START_GAME] Minimum players not met');
      return new Response(JSON.stringify({ 
        error: 'Minimum players not met.',
        details: `Need at least 2 players, but only ${playersInRoom.length} found.`,
        currentCount: playersInRoom.length,
        requiredCount: 2
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400, // 400 Bad Request for validation failures
      });
    }
    console.log('[EDGE_START_GAME] All player requirements satisfied');

    // 4. Generate the first question
    console.log('[EDGE_START_GAME] Generating first question...');
    const firstQuestion = await generateNewQuestionForRoom(supabaseAdmin, userClient, roomId);
    if (!firstQuestion) {
      console.error(`[EDGE_START_GAME] Failed to generate first question for room ${roomId}`);
      throw new Error('Failed to generate the first question for the game.');
    }
    console.log(`[EDGE_START_GAME] First question generated successfully:`, {
      questionId: firstQuestion.questionId,
      correctPlayerName: firstQuestion.correctChoiceName,
      correctPlayerId: firstQuestion.correctPlayerId,
      imageUrl: firstQuestion.imageUrl,
      choicesCount: firstQuestion.choices.length
    });

    // 5. CRITICAL: Prepare original_player_ids for rejoin logic
    console.log('[EDGE_START_GAME] [ORIGINAL_PLAYERS] Preparing original_player_ids for rejoin logic...');
    const allOriginalPlayerIds = playersInRoom.map((p: {user_id: string}) => p.user_id);
    console.log('[EDGE_START_GAME] [ORIGINAL_PLAYERS] Current players at game start:', JSON.stringify(allOriginalPlayerIds));
    console.log('[EDGE_START_GAME] [ORIGINAL_PLAYERS] Player details:', playersInRoom.map((p: {user_id: string, is_ready: boolean}) => ({ 
      user_id: p.user_id, 
      is_ready: p.is_ready 
    })));

    // CRITICAL: Validate allOriginalPlayerIds is a proper array
    if (!Array.isArray(allOriginalPlayerIds)) {
      console.error('[EDGE_START_GAME] CRITICAL ERROR: allOriginalPlayerIds is not an array:', allOriginalPlayerIds);
      throw new Error('Invalid player data: Unable to extract player IDs for game start');
    }
    
    if (allOriginalPlayerIds.length === 0) {
      console.error('[EDGE_START_GAME] CRITICAL ERROR: No player IDs found for game start');
      throw new Error('Invalid game state: No players found to start game');
    }
    
    // Validate all player IDs are strings
    const invalidPlayerIds = allOriginalPlayerIds.filter(id => typeof id !== 'string' || !id.trim());
    if (invalidPlayerIds.length > 0) {
      console.error('[EDGE_START_GAME] CRITICAL ERROR: Invalid player IDs found:', invalidPlayerIds);
      throw new Error('Invalid player data: Some player IDs are not valid strings');
    }
    
    console.log('[EDGE_START_GAME] [VALIDATION_SUCCESS] allOriginalPlayerIds validated successfully:', {
      count: allOriginalPlayerIds.length,
      playerIds: allOriginalPlayerIds
    });

    // 6. CRITICAL: Atomic update with optimistic locking
    console.log('[EDGE_START_GAME] [ATOMIC_UPDATE] Preparing game start update...');
    const roundDurationSeconds = 30; 
    const now = new Date();
    const roundEndsAt = new Date(now.getTime() + roundDurationSeconds * 1000).toISOString();

    // CRITICAL: Validate the first question data before using it
    if (!firstQuestion || !firstQuestion.questionId || !firstQuestion.correctPlayerId || !firstQuestion.imageUrl || !Array.isArray(firstQuestion.choices) || !firstQuestion.correctChoiceName) {
      console.error('[EDGE_START_GAME] CRITICAL ERROR: Invalid first question data:', firstQuestion);
      throw new Error('Invalid question data: Generated question is incomplete or malformed');
    }
    
    console.log('[EDGE_START_GAME] [QUESTION_VALIDATION] First question validated successfully:', {
      questionId: firstQuestion.questionId,
      correctPlayerName: firstQuestion.correctChoiceName,
      correctPlayerId: firstQuestion.correctPlayerId,
      imageUrl: firstQuestion.imageUrl,
      choicesCount: firstQuestion.choices.length
    });

    const updateData = {
      status: 'active' as const,
      game_start_timestamp: now.toISOString(),
      question_started_at: now.toISOString(), // Track when this question starts
      current_round_number: 1,
      current_question_data: firstQuestion,
      player_scores: {}, 
      current_round_answers: [], 
      current_round_ends_at: roundEndsAt,
      transition_deadline: new Date(now.getTime() + 7000).toISOString(), // 7-second hard cap for first question
      last_activity_timestamp: now.toISOString(),
      original_player_ids: allOriginalPlayerIds, // CRITICAL: Set this for rejoin logic!
      // Timer state for visual countdown
      timer_type: 'round' as const,
      timer_started_at: now.toISOString(),
      timer_duration_seconds: 7.0,
      // Initialize answer timing fields
      first_answer_at: null,
      all_answers_window_seconds: 2.0
    };
    
    // CRITICAL: Validate the update data structure before attempting database update
    console.log('[EDGE_START_GAME] [UPDATE_DATA_VALIDATION] Validating update data structure...');
    
    // Validate required fields are present and correct types
    const validationErrors: string[] = [];
    
    if (updateData.status !== 'active') validationErrors.push('status must be "active"');
    if (!updateData.game_start_timestamp || typeof updateData.game_start_timestamp !== 'string') validationErrors.push('game_start_timestamp must be a valid ISO string');
    if (typeof updateData.current_round_number !== 'number' || updateData.current_round_number < 1) validationErrors.push('current_round_number must be a positive number');
    if (!updateData.current_question_data) validationErrors.push('current_question_data cannot be null/undefined');
    if (typeof updateData.player_scores !== 'object' || updateData.player_scores === null) validationErrors.push('player_scores must be an object');
    if (!Array.isArray(updateData.current_round_answers)) validationErrors.push('current_round_answers must be an array');
    if (!updateData.current_round_ends_at || typeof updateData.current_round_ends_at !== 'string') validationErrors.push('current_round_ends_at must be a valid ISO string');
    if (!updateData.last_activity_timestamp || typeof updateData.last_activity_timestamp !== 'string') validationErrors.push('last_activity_timestamp must be a valid ISO string');
    if (!Array.isArray(updateData.original_player_ids) || updateData.original_player_ids.length === 0) validationErrors.push('original_player_ids must be a non-empty array');
    
    if (validationErrors.length > 0) {
      console.error('[EDGE_START_GAME] CRITICAL ERROR: Update data validation failed:', {
        errors: validationErrors,
        updateData: JSON.stringify(updateData, null, 2)
      });
      throw new Error(`Invalid update data: ${validationErrors.join(', ')}`);
    }
    
    console.log('[EDGE_START_GAME] [UPDATE_DATA_VALIDATION] Update data validated successfully');
    console.log('[EDGE_START_GAME] [ATOMIC_UPDATE] Update data prepared:', {
      status: updateData.status,
      game_start_timestamp: updateData.game_start_timestamp,
      current_round_number: updateData.current_round_number,
      current_question_data: `[Question with ${firstQuestion.choices.length} choices]`, // Abbreviated for logging
      player_scores: JSON.stringify(updateData.player_scores),
      current_round_answers_length: updateData.current_round_answers.length,
      current_round_ends_at: updateData.current_round_ends_at,
      last_activity_timestamp: updateData.last_activity_timestamp,
      original_player_ids: JSON.stringify(updateData.original_player_ids) // Log the critical field
    });

    // CRITICAL: Use optimistic locking to prevent race conditions - only update if status is still 'waiting'
    console.log(`[EDGE_START_GAME] [OPTIMISTIC_LOCK] Attempting atomic update - ensuring room ${roomId} status is still 'waiting'...`);
    
    try {
      const { data: updateResult, error: updateRoomError } = await supabaseAdmin
        .from('game_rooms')
        .update(updateData)
        .eq('id', roomId)
        .eq('status', 'waiting') // CRITICAL: Only update if status is still 'waiting'
        .select('id, status, game_start_timestamp, current_round_number, original_player_ids') // Return updated data to verify

      if (updateRoomError) {
        console.error(`[EDGE_START_GAME] Database error updating room ${roomId}:`, {
          error: updateRoomError,
          message: updateRoomError.message,
          code: updateRoomError.code,
          details: updateRoomError.details,
          hint: updateRoomError.hint
        });
        throw new Error(`Failed to start game - database update error: ${updateRoomError.message}`)
      }

      // CRITICAL: Check if the update actually affected any rows (optimistic lock check)
      if (!updateResult || updateResult.length === 0) {
        console.error(`[EDGE_START_GAME] [OPTIMISTIC_LOCK_FAILED] Room ${roomId} was not updated - status may have changed concurrently`);
        
        // Fetch current room state to understand what happened
        const { data: currentRoomState } = await supabaseAdmin
          .from('game_rooms')
          .select('id, status, game_start_timestamp, host_id, last_activity_timestamp')
          .eq('id', roomId)
          .single();
          
        console.error(`[EDGE_START_GAME] [CONCURRENT_MODIFICATION] Current room state after failed update:`, currentRoomState);
        
        // Enhanced conflict response for better client handling
        const gameStartTime = currentRoomState?.game_start_timestamp ? new Date(currentRoomState.game_start_timestamp).toISOString() : null;
        const timeSinceStart = gameStartTime ? Date.now() - new Date(gameStartTime).getTime() : null;
        
        return new Response(JSON.stringify({ 
          error: 'Game has already started or cannot be started in current state.',
          details: `Room status changed during update attempt. Current status: ${currentRoomState?.status || 'unknown'}`,
          currentStatus: currentRoomState?.status,
          gameStartTime: gameStartTime,
          conflictType: 'CONCURRENT_START_ATTEMPT',
          suggestion: 'Another request may have started the game simultaneously.',
          timeSinceStart: timeSinceStart ? `${Math.round(timeSinceStart / 1000)}s ago` : 'unknown',
          timestamp: new Date().toISOString()
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 409, // 409 Conflict for concurrent modification
        });
      }

      console.log(`[EDGE_START_GAME] [OPTIMISTIC_LOCK_SUCCESS] Room ${roomId} successfully updated:`, updateResult[0]);
      console.log(`[EDGE_START_GAME] [ORIGINAL_PLAYERS_VERIFICATION] original_player_ids set to:`, JSON.stringify(updateResult[0]?.original_player_ids));
      console.log(`[EDGE_START_GAME] SUCCESS: Game started successfully in room ${roomId}. Image: ${firstQuestion.imageUrl}`)
      
      // Return success with comprehensive data
      return new Response(JSON.stringify({ 
        message: 'Game started successfully!', 
        firstQuestion,
        gameStartTime: now.toISOString(),
        roomId: roomId,
        roundEndsAt: roundEndsAt,
        roundNumber: 1
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
      
    } catch (dbError) {
      console.error('[EDGE_START_GAME] CRITICAL DATABASE ERROR during room update:', {
        error: dbError,
        message: dbError instanceof Error ? dbError.message : String(dbError),
        stack: dbError instanceof Error ? dbError.stack : undefined,
        roomId,
        updateData: JSON.stringify(updateData, null, 2)
      });
      throw new Error(`Critical database error during game start: ${dbError instanceof Error ? dbError.message : String(dbError)}`);
    }

  } catch (error) {
    console.error('[EDGE_START_GAME] UNHANDLED EXCEPTION in start-game-handler:', error);
    if (error instanceof Error) {
      console.error('[EDGE_START_GAME] Exception Name:', error.name);
      console.error('[EDGE_START_GAME] Exception Message:', error.message);
      console.error('[EDGE_START_GAME] Exception Stack:', error.stack);
    }
    
    // Determine appropriate status code based on error type/message
    let statusCode = 500; // Default to internal server error
    let errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    if (errorMessage.includes('Room ID is required') || errorMessage.includes('Missing roomId')) {
      statusCode = 400; // Bad Request for missing required fields
    } else if (errorMessage.includes('not authenticated') || errorMessage.includes('Missing Authorization')) {
      statusCode = 401; // Unauthorized for authentication issues
    } else if (errorMessage.includes('Room not found')) {
      statusCode = 404; // Not Found for missing resources
    } else if (errorMessage.includes('Database error')) {
      statusCode = 500; // Keep as 500 for actual database issues
      errorMessage = 'Database error occurred while processing request';
    } else if (errorMessage.includes('Server configuration error')) {
      statusCode = 503; // Service Unavailable for configuration issues
      errorMessage = 'Service temporarily unavailable due to configuration issues';
    }
    
    console.error(`[EDGE_START_GAME] Returning ${statusCode} status with message: ${errorMessage}`);
    
    return new Response(JSON.stringify({ 
      error: errorMessage, 
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    })
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/start-game-handler' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
