#!/bin/bash
# <PERSON><PERSON><PERSON> to temporarily use mock Edge Function for testing

echo "Switching to mock Edge Function for testing..."

# Backup original
if [ -f "supabase/functions/start-game-handler/index.ts" ] && [ ! -f "supabase/functions/start-game-handler/index-original.ts" ]; then
  echo "Backing up original Edge Function..."
  mv supabase/functions/start-game-handler/index.ts supabase/functions/start-game-handler/index-original.ts
fi

# Use mock version
if [ -f "supabase/functions/start-game-handler/index-mock.ts" ]; then
  echo "Activating mock Edge Function..."
  cp supabase/functions/start-game-handler/index-mock.ts supabase/functions/start-game-handler/index.ts
  echo "Done! Mock Edge Function is now active."
  echo ""
  echo "To restore original:"
  echo "  mv supabase/functions/start-game-handler/index-original.ts supabase/functions/start-game-handler/index.ts"
else
  echo "Error: Mock Edge Function not found!"
  exit 1
fi