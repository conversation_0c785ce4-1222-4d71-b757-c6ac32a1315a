/**
 * Automated Multiplayer Test Runner
 * Tests round advance timing with two players
 */

const puppeteer = require('puppeteer');

// Test configuration
const CONFIG = {
  url: 'http://localhost:3001',
  player1: { email: 'fresh', password: 'test123' },
  player2: { email: 'fresh2', password: 'test123' },
  scenarios: [
    { name: 'Both at 1s → Round at 4s', p1: 1000, p2: 1000, expected: 4000 },
    { name: 'Both at 2s → Round at 5s', p1: 2000, p2: 2000, expected: 5000 },
    { name: 'Both at 5s → Round at 7s', p1: 5000, p2: 5000, expected: 7000 },
    { name: 'Only P1 → Round at 7s', p1: 1000, p2: null, expected: 7000 },
    { name: 'P1 at 1s, P2 at 3s → Round at 6s', p1: 1000, p2: 3000, expected: 6000 }
  ]
};

// Helper functions
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

async function signIn(page, credentials) {
  // Click Login button
  await page.evaluate(() => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent.includes('Login')
    );
    if (btn) btn.click();
  });
  
  await delay(2000);
  
  // Fill credentials
  await page.type('input[placeholder="Username or Email"]', credentials.email);
  await page.type('input[placeholder="Password"]', credentials.password);
  
  // Submit
  await page.evaluate(() => {
    const form = document.querySelector('form');
    if (form) form.requestSubmit();
  });
  
  await delay(3000);
}

async function navigateToMultiplayer(page) {
  await page.evaluate(() => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent === 'Multiplayer Mode'
    );
    if (btn) btn.click();
  });
  await delay(2000);
}

async function createRoom(page) {
  // Click Host Game
  const roomCode = await page.evaluate(() => {
    const hostBtn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent.trim() === 'Host Game'
    );
    if (hostBtn) {
      hostBtn.click();
      return new Promise(resolve => {
        setTimeout(() => {
          // Get room code after creation
          const codeEl = Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.match(/^[A-Z0-9]{6}$/)
          );
          resolve(codeEl ? codeEl.textContent : null);
        }, 2000);
      });
    }
    return null;
  });
  
  return roomCode;
}

async function joinRoom(page, roomCode) {
  // Click Join Room
  await page.evaluate(() => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent === 'Join Room'
    );
    if (btn) btn.click();
  });
  
  await delay(1000);
  
  // Enter room code
  await page.type('input[placeholder*="room code" i]', roomCode);
  
  // Submit
  await page.evaluate(() => {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) submitBtn.click();
  });
  
  await delay(2000);
}

async function startGame(page1, page2) {
  // Both players ready up
  await Promise.all([
    page1.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready'
      );
      if (btn) btn.click();
    }),
    page2.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button')).find(b => 
        b.textContent === 'Ready'
      );
      if (btn) btn.click();
    })
  ]);
  
  await delay(2000);
  
  // Host starts game
  await page1.evaluate(() => {
    const btn = Array.from(document.querySelectorAll('button')).find(b => 
      b.textContent === 'Start Game'
    );
    if (btn) btn.click();
  });
  
  await delay(2000);
}

// Monitoring injection
const monitoringCode = `
  window.testData = {
    gameStartTime: null,
    roundChanges: [],
    currentRound: 0
  };
  
  let lastQuestion = null;
  setInterval(() => {
    const questionEl = document.querySelector('h2');
    const question = questionEl?.textContent || '';
    
    if (question && question.includes('?') && question !== lastQuestion) {
      const now = Date.now();
      
      if (!window.testData.gameStartTime) {
        window.testData.gameStartTime = now;
        console.log('Game started');
      } else {
        const elapsed = now - window.testData.gameStartTime;
        window.testData.roundChanges.push({
          round: window.testData.currentRound++,
          time: elapsed
        });
        console.log('Round advanced at ' + elapsed + 'ms');
      }
      
      lastQuestion = question;
    }
  }, 100);
  
  window.submitAt = function(targetMs) {
    if (!window.testData.gameStartTime) return;
    
    const elapsed = Date.now() - window.testData.gameStartTime;
    const delay = targetMs - elapsed;
    
    const submit = () => {
      const buttons = Array.from(document.querySelectorAll('button')).filter(b => {
        const text = b.textContent || '';
        return text.match(/^[A-Za-z\\s]+$/) && 
               !text.includes('Ready') && 
               !text.includes('Start');
      });
      
      if (buttons.length >= 4) {
        buttons[Math.floor(Math.random() * 4)].click();
      }
    };
    
    if (delay > 0) {
      setTimeout(submit, delay);
    } else {
      submit();
    }
  };
`;

async function runTest() {
  console.log('🎯 Automated Multiplayer Test');
  console.log('============================\n');
  
  let browser1, browser2;
  
  try {
    // Launch browsers
    console.log('🚀 Launching browsers...');
    browser1 = await puppeteer.launch({ 
      headless: false,
      args: ['--window-size=1000,800', '--window-position=0,0']
    });
    browser2 = await puppeteer.launch({ 
      headless: false,
      args: ['--window-size=1000,800', '--window-position=1000,0']
    });
    
    const page1 = await browser1.newPage();
    const page2 = await browser2.newPage();
    
    // Navigate to game
    await Promise.all([
      page1.goto(CONFIG.url, { waitUntil: 'networkidle2' }),
      page2.goto(CONFIG.url, { waitUntil: 'networkidle2' })
    ]);
    
    await delay(3000);
    
    // Inject monitoring
    await Promise.all([
      page1.evaluate(monitoringCode),
      page2.evaluate(monitoringCode)
    ]);
    
    // Sign in both players
    console.log('🔐 Signing in players...');
    await signIn(page1, CONFIG.player1);
    await signIn(page2, CONFIG.player2);
    
    // Navigate to multiplayer
    console.log('🎮 Navigating to multiplayer...');
    await navigateToMultiplayer(page1);
    await navigateToMultiplayer(page2);
    
    // Create and join room
    console.log('🏠 Creating room...');
    const roomCode = await createRoom(page1);
    console.log(`Room created: ${roomCode}`);
    
    console.log('🏠 Joining room...');
    await joinRoom(page2, roomCode);
    
    // Start game
    console.log('🎮 Starting game...');
    await startGame(page1, page2);
    
    // Run test scenarios
    console.log('\n📊 Running test scenarios...\n');
    const results = [];
    
    for (let i = 0; i < CONFIG.scenarios.length; i++) {
      const scenario = CONFIG.scenarios[i];
      console.log(`Test ${i + 1}: ${scenario.name}`);
      
      // Schedule submissions
      if (scenario.p1 !== null) {
        await page1.evaluate(`window.submitAt(${scenario.p1})`);
      }
      if (scenario.p2 !== null) {
        await page2.evaluate(`window.submitAt(${scenario.p2})`);
      }
      
      // Wait for round to complete
      await delay(8000);
      
      // Get results
      const timing = await page1.evaluate((index) => {
        const changes = window.testData.roundChanges || [];
        return changes[index];
      }, i);
      
      if (timing) {
        const diff = Math.abs(timing.time - scenario.expected);
        const passed = diff <= 500;
        console.log(`Expected: ${scenario.expected}ms, Actual: ${timing.time}ms`);
        console.log(`Difference: ${diff}ms - ${passed ? '✅ PASS' : '❌ FAIL'}\n`);
        results.push({ ...scenario, actual: timing.time, passed });
      } else {
        console.log('❌ No round change detected\n');
        results.push({ ...scenario, actual: null, passed: false });
      }
    }
    
    // Summary
    console.log('\n📈 Test Summary');
    console.log('==============');
    const passed = results.filter(r => r.passed).length;
    console.log(`Passed: ${passed}/${results.length} tests\n`);
    
    console.log('✅ Test complete! Browsers remain open for inspection.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
  }
}

// Run the test
runTest();