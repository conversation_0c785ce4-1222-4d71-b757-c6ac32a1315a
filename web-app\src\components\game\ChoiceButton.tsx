import { memo, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils'; // shadcn utility

interface Props {
  choiceText: string;
  onClick: () => void;
  disabled?: boolean;
  isCorrect?: boolean | null; // null = unanswered, true = correct, false = incorrect
  wasChosen?: boolean; // Was this specific button clicked by the user?
}

export const ChoiceButton = memo(function ChoiceButton({ choiceText, onClick, disabled, isCorrect, wasChosen }: Props) {
  // Memoize color classes to prevent recalculation
  const colorClasses = useMemo(() => {
    if (disabled && isCorrect === true) return 'bg-green-600 hover:bg-green-700 border-green-700 text-white';
    if (disabled && wasChosen && isCorrect === false) return 'bg-red-600 hover:bg-red-700 border-red-700 text-white';
    // Default or non-chosen incorrect
    if (disabled && isCorrect === false) return 'bg-gray-600 hover:bg-gray-700 border-gray-700 text-gray-300 opacity-70';
    return 'bg-blue-600 hover:bg-blue-700 border-blue-700'; // Default active state
  }, [disabled, isCorrect, wasChosen]);

  const buttonClassName = useMemo(() => cn(
    "w-full text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out text-sm md:text-base h-auto min-h-[50px] whitespace-normal", // Allow text wrapping
    colorClasses,
    disabled ? 'opacity-60 cursor-not-allowed pointer-events-none' : ''
  ), [colorClasses, disabled]);

  return (
    <Button
      onClick={() => {
        if (!disabled) {
          onClick();
        }
      }}
      disabled={disabled}
      className={buttonClassName}
    >
      {choiceText}
    </Button>
  );
}); 