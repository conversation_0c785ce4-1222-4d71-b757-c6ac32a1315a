# Fix Edge Function to use anon key for players_data query
Write-Host "Fixing Edge Function authentication approach..." -ForegroundColor Yellow

# Read the current Edge Function
$content = Get-Content "functions/start-game-handler/index.ts" -Raw

# Replace service role key usage for players_data query
# Change line where admin client queries players_data to use anon client
$newContent = $content -replace `
  '(const { data: playersData, error: fetchError } = await )supabaseAdmin', `
  '${1}userClient'

# Write the updated content
$newContent | Out-File "functions/start-game-handler/index.ts" -Encoding UTF8

Write-Host "Updated Edge Function to use userClient for players_data query" -ForegroundColor Green
Write-Host "Now deploy with: .\deploy-start-game-handler-direct.ps1" -ForegroundColor Cyan