# PowerShell Script: Export All SQL Migration Files to Text
# Generated on: $(Get-Date)

Write-Host "🔍 Scanning for SQL migration files..." -ForegroundColor Cyan

# Define paths
$migrationsPath = ".\migrations"
$outputFile = "all-migrations-export.txt"

# Check if migrations directory exists
if (-not (Test-Path $migrationsPath)) {
    Write-Host "❌ Migrations directory not found at: $migrationsPath" -ForegroundColor Red
    Write-Host "🔍 Looking for migrations in parent directories..." -ForegroundColor Yellow
    
    # Try common locations
    $possiblePaths = @(
        "..\migrations",
        ".\supabase\migrations",
        "..\supabase\migrations"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $migrationsPath = $path
            Write-Host "✅ Found migrations at: $migrationsPath" -ForegroundColor Green
            break
        }
    }
    
    if (-not (Test-Path $migrationsPath)) {
        Write-Host "❌ Could not find migrations directory!" -ForegroundColor Red
        exit 1
    }
}

# Get all .sql files
$sqlFiles = Get-ChildItem -Path $migrationsPath -Filter "*.sql" | Sort-Object Name

if ($sqlFiles.Count -eq 0) {
    Write-Host "❌ No .sql files found in: $migrationsPath" -ForegroundColor Red
    exit 1
}

Write-Host "📁 Found $($sqlFiles.Count) SQL migration files" -ForegroundColor Green

# Create output content
$outputContent = @()
$outputContent += "=" * 80
$outputContent += "SQL MIGRATIONS EXPORT - Generated on $(Get-Date)"
$outputContent += "Source Directory: $(Resolve-Path $migrationsPath)"
$outputContent += "Total Files: $($sqlFiles.Count)"
$outputContent += "=" * 80
$outputContent += ""

# Process each SQL file
foreach ($file in $sqlFiles) {
    Write-Host "📄 Processing: $($file.Name)" -ForegroundColor Yellow
    
    $outputContent += ""
    $outputContent += "╔" + ("═" * 78) + "╗"
    $fileName = $file.Name.PadRight(72)
    $outputContent += "║ FILE: $fileName ║"
    $fileSize = "$([math]::Round($file.Length/1KB, 2)) KB"
    $fileSizePadded = $fileSize.PadRight(77)
    $outputContent += "║ SIZE: $fileSizePadded ║"
    $fileModified = $file.LastWriteTime.ToString('yyyy-MM-dd HH:mm:ss')
    $fileModifiedPadded = "MODIFIED: $fileModified".PadRight(77)
    $outputContent += "║ $fileModifiedPadded ║"
    $outputContent += "╚" + ("═" * 78) + "╝"
    $outputContent += ""
    
    try {
        $fileContent = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        if ([string]::IsNullOrWhiteSpace($fileContent)) {
            $outputContent += "-- [EMPTY FILE] --"
        } else {
            $outputContent += $fileContent
        }
    }
    catch {
        $outputContent += "-- [ERROR READING FILE: $($_.Exception.Message)] --"
        Write-Host "⚠️  Error reading $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
    
    $outputContent += ""
    $outputContent += "-- END OF FILE: $($file.Name) --"
    $outputContent += ""
}

# Add footer
$outputContent += ""
$outputContent += "=" * 80
$outputContent += "EXPORT COMPLETED - $(Get-Date)"
$outputContent += "Total Files Processed: $($sqlFiles.Count)"
$outputContent += "Output File: $outputFile"
$outputContent += "=" * 80

# Write to output file
try {
    $outputContent | Out-File -FilePath $outputFile -Encoding UTF8
    Write-Host "✅ Export completed successfully!" -ForegroundColor Green
    Write-Host "📄 Output file: $(Resolve-Path $outputFile)" -ForegroundColor Cyan
    Write-Host "📊 Total lines exported: $($outputContent.Count)" -ForegroundColor Cyan
    
    # Display file summary
    Write-Host ""
    Write-Host "📋 FILES PROCESSED:" -ForegroundColor Magenta
    foreach ($file in $sqlFiles) {
        $fileSizeKB = [math]::Round($file.Length/1KB, 2)
        Write-Host "   • $($file.Name) ($fileSizeKB KB)" -ForegroundColor White
    }
}
catch {
    Write-Host "❌ Error writing output file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Migration export script completed!" -ForegroundColor Green 