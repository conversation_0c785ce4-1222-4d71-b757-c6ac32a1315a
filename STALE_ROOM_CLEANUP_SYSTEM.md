# Stale Room Cleanup System

## Overview

This document describes the comprehensive stale room cleanup system implemented to eliminate "ghost" game rooms that appear in the lobby but are no longer joinable. The system uses a multi-layered approach to prevent and handle stale rooms.

## The Problem

**Stale rooms** occur when:
1. A host creates a room but abruptly leaves (browser crash, network disconnect, etc.)
2. The room remains in `waiting` status in the database
3. Users see the room in the lobby and try to join
4. Jo<PERSON> attempts fail with validation errors because the host is gone

## The Solution: Multi-Layered Defense

### Layer 1: Server-Side Janitor (Proactive Cleanup)

**File**: `supabase/functions/stale-room-janitor/index.ts`

- **Purpose**: Automatically detect and delete abandoned rooms
- **Schedule**: Runs every 5 minutes via cron job
- **Logic**: 
  - Finds rooms with `status='waiting'` 
  - Checks `last_activity_timestamp` older than 5 minutes
  - Deletes associated players first (foreign key constraints)
  - Deletes the stale rooms
- **Configuration**: Defined in `supabase/config.toml`

```toml
[functions.stale-room-janitor]
enabled = true
verify_jwt = false
schedule = "*/5 * * * *"
entrypoint = "./functions/stale-room-janitor/index.ts"
```

### Layer 2: Client-Side Host Cleanup (Existing)

**File**: `web-app/src/app/page.tsx` - `handleCreateRoom` function

- **Purpose**: Clean up a user's own old rooms when creating new ones
- **Logic**: Before creating a new room, delete any existing rooms hosted by the same user
- **Status**: ✅ Already implemented and working correctly

### Layer 3: Client-Side Graceful Error Handling (Enhanced)

**File**: `web-app/src/app/page.tsx` - `handleJoinRoom` function

- **Purpose**: Handle stale room errors gracefully with good UX
- **Enhancements**:
  - Catch validation errors (room not found, wrong status)
  - Show user-friendly message: "Oops! That game is no longer available. The lobby has been refreshed."
  - Auto-refresh lobby list to remove stale entries
  - Clear error message after 3 seconds

## Database Schema

### Key Columns

**`game_rooms` table**:
- `last_activity_timestamp` - Tracks when room was last active
- `status` - Room status (`waiting`, `active`, `finished`)
- `host_id` - Room host user ID

**Indexes for Performance**:
```sql
-- Composite index for efficient stale room queries
CREATE INDEX idx_game_rooms_stale_cleanup 
ON game_rooms (status, last_activity_timestamp);
```

### Automatic Timestamp Updates

**Trigger**: `trigger_update_room_activity_timestamp`
- Automatically updates `last_activity_timestamp` on room modifications
- Ensures accurate activity tracking for janitor function

## Deployment

### 1. Deploy the Janitor Function

```bash
# Deploy the function
supabase functions deploy stale-room-janitor

# Or use the PowerShell script
./supabase/deploy-stale-room-janitor.ps1
```

### 2. Apply Database Migration

```bash
# Apply the index and trigger migration
supabase db push
```

### 3. Test the System

```bash
# Test the janitor function manually
./supabase/test-stale-room-janitor.ps1
```

## Monitoring

### Function Logs
- **Dashboard**: https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/functions/stale-room-janitor
- **Logs**: Check function invocations and cleanup activity

### Expected Log Output
```
[EDGE_JANITOR] Looking for 'waiting' rooms with last_activity_timestamp before 2025-01-28T10:30:00.000Z
[EDGE_JANITOR] Found 2 stale rooms to clean up
[EDGE_JANITOR] ✅ Successfully deleted players from stale rooms
[EDGE_JANITOR] ✅ Successfully deleted 2 stale rooms
```

## Configuration

### Stale Threshold
- **Current**: 5 minutes of inactivity
- **Location**: `STALE_THRESHOLD_MINUTES` in janitor function
- **Adjustable**: Modify based on your needs

### Cron Schedule
- **Current**: Every 5 minutes (`*/5 * * * *`)
- **Location**: `supabase/config.toml`
- **Adjustable**: Change frequency as needed

## Error Handling

### Client-Side
- **Room Not Found**: Auto-refresh lobby, show friendly message
- **Wrong Status**: Auto-refresh lobby, show friendly message
- **Network Errors**: Standard error handling with retry suggestions

### Server-Side
- **Database Errors**: Logged with full context
- **Foreign Key Violations**: Handled by deleting players first
- **Function Failures**: Logged and monitored via dashboard

## Benefits

1. **Proactive Cleanup**: Stale rooms are removed before users encounter them
2. **Graceful Degradation**: When users do encounter stale rooms, the experience is smooth
3. **Automatic Recovery**: Lobby refreshes automatically to show current state
4. **Performance**: Indexed queries ensure fast cleanup operations
5. **Monitoring**: Full visibility into cleanup activity

## Testing

### Manual Testing
1. Create a room
2. Manually update `last_activity_timestamp` to be older than 5 minutes
3. Wait for next janitor run (or invoke manually)
4. Verify room is deleted

### Automated Testing
- The janitor function runs every 5 minutes automatically
- Monitor logs to verify it's working correctly
- No stale rooms should persist longer than 5 minutes

## Troubleshooting

### Janitor Not Running
- Check function deployment status
- Verify cron schedule in config.toml
- Check function logs for errors

### Rooms Not Being Cleaned
- Verify `last_activity_timestamp` is being updated
- Check if rooms meet the stale criteria
- Review janitor function logs

### Client Errors Persist
- Ensure lobby refresh is working
- Check error message display logic
- Verify fetchAndSetGameRooms function

## Future Enhancements

1. **Configurable Thresholds**: Make stale threshold configurable per room type
2. **Host Notifications**: Notify hosts before their rooms are cleaned up
3. **Analytics**: Track stale room frequency and patterns
4. **Smart Cleanup**: Consider room activity patterns for smarter cleanup timing
