// Test Edge Function with proper authentication
const puppeteer = require('puppeteer');

async function testEdgeWithAuth() {
  console.log('=== Testing Edge Function with Authentication ===\n');
  
  const browser = await puppeteer.launch({
    headless: true,
    executablePath: process.platform === 'linux' ? '/usr/bin/chromium-browser' : undefined,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable request interception to capture the Edge Function call
    await page.setRequestInterception(true);
    
    let edgeFunctionRequest = null;
    let edgeFunctionResponse = null;
    
    page.on('request', request => {
      if (request.url().includes('start-game-handler')) {
        edgeFunctionRequest = {
          url: request.url(),
          method: request.method(),
          headers: request.headers(),
          postData: request.postData()
        };
        console.log('\n=== Edge Function Request ===');
        console.log('URL:', edgeFunctionRequest.url);
        console.log('Method:', edgeFunctionRequest.method);
        console.log('Auth Header:', edgeFunctionRequest.headers.authorization ? 'Present' : 'Missing');
        console.log('Body:', edgeFunctionRequest.postData);
      }
      request.continue();
    });
    
    page.on('response', async response => {
      if (response.url().includes('start-game-handler')) {
        edgeFunctionResponse = {
          status: response.status(),
          statusText: response.statusText(),
          headers: response.headers()
        };
        
        try {
          edgeFunctionResponse.body = await response.text();
        } catch (e) {
          edgeFunctionResponse.body = 'Could not read body';
        }
        
        console.log('\n=== Edge Function Response ===');
        console.log('Status:', edgeFunctionResponse.status, edgeFunctionResponse.statusText);
        console.log('Body:', edgeFunctionResponse.body);
        
        if (edgeFunctionResponse.body) {
          try {
            const parsed = JSON.parse(edgeFunctionResponse.body);
            console.log('Parsed Response:', JSON.stringify(parsed, null, 2));
          } catch (e) {
            // Not JSON
          }
        }
      }
    });
    
    // Navigate and login
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Quick login flow
    const multiplayerBtn = await page.evaluateHandle(() => 
      Array.from(document.querySelectorAll('button')).find(b => b.textContent.includes('Multiplayer Mode'))
    );
    await multiplayerBtn.click();
    
    await page.waitForSelector('input[type="text"]', { visible: true });
    await page.type('input[type="text"]', 'fresh');
    await page.type('input[type="password"]', 'test123');
    await page.click('button[type="submit"]');
    
    // Wait for lobby
    await new Promise(r => setTimeout(r, 3000));
    
    // Create room
    const createBtn = await page.evaluateHandle(() => 
      Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Create New Room')
    );
    if (createBtn) {
      await createBtn.asElement().click();
      await new Promise(r => setTimeout(r, 2000));
      
      // Ready up
      const readyBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Ready')
      );
      if (readyBtn) {
        await readyBtn.click();
        await new Promise(r => setTimeout(r, 2000));
        
        // Start game
        const startBtn = await page.evaluateHandle(() => 
          Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Start Game')
        );
        if (startBtn) {
          console.log('\nClicking Start Game...');
          await startBtn.click();
          await new Promise(r => setTimeout(r, 5000));
        }
      }
    }
    
    console.log('\n=== Test Complete ===');
    
  } finally {
    await browser.close();
  }
}

testEdgeWithAuth().catch(console.error);