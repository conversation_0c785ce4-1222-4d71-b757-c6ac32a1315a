# Simple deployment script for start-game-handler
param(
    [string]$FunctionName = "start-game-handler"
)

Write-Host "Deploying $FunctionName via Supabase Management API..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"

# Get access token
Write-Host ""
Write-Host "STEP 1: Getting access token" -ForegroundColor Yellow
Write-Host "Go to: https://supabase.com/dashboard/account/tokens" -ForegroundColor Cyan
Write-Host "Create a new access token and paste it below:" -ForegroundColor Yellow
$accessToken = Read-Host -Prompt "Access Token"

if (-not $accessToken) {
    Write-Host "No token provided. Exiting." -ForegroundColor Red
    exit 1
}

# Read function code
Write-Host ""
Write-Host "STEP 2: Reading function code" -ForegroundColor Yellow
$functionPath = Join-Path $PSScriptRoot "functions\$FunctionName\index.ts"
if (-not (Test-Path $functionPath)) {
    Write-Host "Function file not found at: $functionPath" -ForegroundColor Red
    exit 1
}

$functionCode = Get-Content $functionPath -Raw
Write-Host "Function code loaded: $($functionCode.Length) characters" -ForegroundColor Gray

# Read CORS code
$corsPath = Join-Path $PSScriptRoot "functions\_shared\cors.ts"
if (Test-Path $corsPath) {
    $corsCode = Get-Content $corsPath -Raw
    # Replace the import with inline code
    $functionCode = $functionCode.Replace("import { corsHeaders } from '../_shared/cors.ts'", $corsCode)
}

# Deploy
Write-Host ""
Write-Host "STEP 3: Deploying function" -ForegroundColor Yellow

$headers = @{
    Authorization = "Bearer $accessToken"
}

$uri = "https://api.supabase.com/v1/projects/$projectRef/functions/$FunctionName"

# Create request body
$bodyObj = @{
    name = $FunctionName
    slug = $FunctionName
    verify_jwt = $false
    body = $functionCode
}

$bodyJson = $bodyObj | ConvertTo-Json -Depth 10 -Compress

try {
    $response = Invoke-WebRequest -Uri $uri -Method Put -Headers $headers -Body $bodyJson -ContentType "application/json"
    
    Write-Host ""
    Write-Host "Function deployed successfully!" -ForegroundColor Green
    Write-Host "Function URL: https://$projectRef.supabase.co/functions/v1/$FunctionName" -ForegroundColor Blue
    Write-Host ""
    Write-Host "You can now run: node test-multiplayer-game-flow.js" -ForegroundColor Cyan
}
catch {
    Write-Host ""
    Write-Host "Deployment failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Details: $responseBody" -ForegroundColor Yellow
    }
}