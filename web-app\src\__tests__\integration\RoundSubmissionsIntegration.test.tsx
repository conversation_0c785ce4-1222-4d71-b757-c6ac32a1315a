import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { act } from 'react';

// Mock CSS animation durations for testing
const ANIMATION_DURATIONS = {
  original: 500, // 0.5s
  expected: 750, // 0.75s (50% slower)
};

// Simplified mock of the round submissions component
const RoundSubmissionsDisplay = ({ 
  submissions,
  animatingAnswers,
  onAnimationEnd 
}: {
  submissions: Array<{ username: string; id: string }>;
  animatingAnswers: Set<string>;
  onAnimationEnd: (id: string) => void;
}) => {
  return (
    <div className="submissions-container">
      {submissions.map((submission) => (
        <div
          key={submission.id}
          data-testid={`submission-${submission.id}`}
          className={`submission-card ${
            animatingAnswers.has(submission.id) ? 'animate-slide-up' : ''
          }`}
          onAnimationEnd={() => onAnimationEnd(submission.id)}
        >
          {submission.username}
        </div>
      ))}
    </div>
  );
};

describe('Round Submissions Integration', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  it('should animate new submissions with correct timing', async () => {
    const animatingAnswers = new Set<string>();
    const handleAnimationEnd = vi.fn();
    
    const initialSubmissions = [
      { username: 'Player1', id: '1' },
      { username: 'Player2', id: '2' }
    ];
    
    // Add IDs to animating set
    animatingAnswers.add('1');
    animatingAnswers.add('2');
    
    const { rerender } = render(
      <RoundSubmissionsDisplay
        submissions={initialSubmissions}
        animatingAnswers={animatingAnswers}
        onAnimationEnd={handleAnimationEnd}
      />
    );
    
    // Check both cards are animating
    expect(screen.getByTestId('submission-1')).toHaveClass('animate-slide-up');
    expect(screen.getByTestId('submission-2')).toHaveClass('animate-slide-up');
    
    // Simulate time passing (new duration)
    act(() => {
      vi.advanceTimersByTime(ANIMATION_DURATIONS.expected);
    });
    
    // Simulate animation end events
    act(() => {
      const card1 = screen.getByTestId('submission-1');
      const card2 = screen.getByTestId('submission-2');
      
      card1.dispatchEvent(new Event('animationend', { bubbles: true }));
      card2.dispatchEvent(new Event('animationend', { bubbles: true }));
    });
    
    expect(handleAnimationEnd).toHaveBeenCalledWith('1');
    expect(handleAnimationEnd).toHaveBeenCalledWith('2');
  });

  it('should handle staggered submissions', async () => {
    const animatingAnswers = new Set<string>();
    const handleAnimationEnd = vi.fn();
    
    // First submission
    const submissions = [{ username: 'Player1', id: '1' }];
    animatingAnswers.add('1');
    
    const { rerender } = render(
      <RoundSubmissionsDisplay
        submissions={submissions}
        animatingAnswers={animatingAnswers}
        onAnimationEnd={handleAnimationEnd}
      />
    );
    
    expect(screen.getByTestId('submission-1')).toHaveClass('animate-slide-up');
    
    // Advance time partially
    act(() => {
      vi.advanceTimersByTime(ANIMATION_DURATIONS.expected / 2);
    });
    
    // Add second submission while first is still animating
    submissions.push({ username: 'Player2', id: '2' });
    animatingAnswers.add('2');
    
    rerender(
      <RoundSubmissionsDisplay
        submissions={submissions}
        animatingAnswers={animatingAnswers}
        onAnimationEnd={handleAnimationEnd}
      />
    );
    
    // Both should be animating
    expect(screen.getByTestId('submission-1')).toHaveClass('animate-slide-up');
    expect(screen.getByTestId('submission-2')).toHaveClass('animate-slide-up');
    
    // First animation completes
    act(() => {
      vi.advanceTimersByTime(ANIMATION_DURATIONS.expected / 2);
      screen.getByTestId('submission-1').dispatchEvent(
        new Event('animationend', { bubbles: true })
      );
    });
    
    animatingAnswers.delete('1');
    rerender(
      <RoundSubmissionsDisplay
        submissions={submissions}
        animatingAnswers={animatingAnswers}
        onAnimationEnd={handleAnimationEnd}
      />
    );
    
    // First should no longer be animating
    expect(screen.getByTestId('submission-1')).not.toHaveClass('animate-slide-up');
    expect(screen.getByTestId('submission-2')).toHaveClass('animate-slide-up');
  });

  it('should not re-animate existing submissions', () => {
    const animatingAnswers = new Set<string>();
    const landedAnswers = new Set<string>(['1']); // Already animated
    
    const submissions = [
      { username: 'Player1', id: '1' },
      { username: 'Player2', id: '2' }
    ];
    
    // Only new submission should animate
    animatingAnswers.add('2');
    
    render(
      <RoundSubmissionsDisplay
        submissions={submissions}
        animatingAnswers={animatingAnswers}
        onAnimationEnd={vi.fn()}
      />
    );
    
    expect(screen.getByTestId('submission-1')).not.toHaveClass('animate-slide-up');
    expect(screen.getByTestId('submission-2')).toHaveClass('animate-slide-up');
  });
});

describe('Animation Performance', () => {
  it('should use GPU-accelerated properties', () => {
    // This test verifies that the animation uses transform and opacity
    // which are GPU-accelerated, rather than properties like top/left
    const cssRules = `
      @keyframes slide-up {
        0% {
          transform: translateY(100vh) translateZ(0) scale(0.8);
          opacity: 0;
        }
        100% {
          transform: translateY(0) translateZ(0) scale(1);
          opacity: 1;
        }
      }
    `;
    
    // Check that animation uses transform, not position properties
    expect(cssRules).toContain('transform');
    expect(cssRules).toContain('translateZ(0)'); // GPU acceleration hint
    expect(cssRules).not.toContain('top:');
    expect(cssRules).not.toContain('left:');
  });
});