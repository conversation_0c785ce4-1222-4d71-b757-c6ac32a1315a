import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { SupabaseClient } from '@supabase/supabase-js';

describe('Heartbeat Integration Tests', () => {
  let supabaseClient: SupabaseClient;
  let mockInvoke: any;

  beforeEach(() => {
    // Mock Supabase client
    mockInvoke = vi.fn();
    supabaseClient = {
      functions: {
        invoke: mockInvoke
      },
      auth: {
        getSession: vi.fn(),
        getUser: vi.fn()
      }
    } as any;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should include authentication headers when session exists', async () => {
    const mockSession = {
      access_token: 'test-token',
      user: { id: 'user-123' }
    };

    (supabaseClient.auth.getSession as any).mockResolvedValue({
      data: { session: mockSession },
      error: null
    });

    mockInvoke.mockResolvedValue({
      data: { success: true },
      error: null
    });

    // Call heartbeat
    await supabaseClient.functions.invoke('heartbeat-handler', {
      body: { roomId: 'room-123', action: 'ping' }
    });

    expect(mockInvoke).toHaveBeenCalledWith('heartbeat-handler', {
      body: { roomId: 'room-123', action: 'ping' }
    });
  });

  it('should handle network errors gracefully', async () => {
    const networkError = new Error('Network request failed');
    mockInvoke.mockRejectedValue(networkError);

    try {
      await supabaseClient.functions.invoke('heartbeat-handler', {
        body: { roomId: 'room-123', action: 'ping' }
      });
    } catch (error) {
      expect(error).toEqual(networkError);
    }
  });

  it('should handle malformed responses', async () => {
    mockInvoke.mockResolvedValue({
      data: null,
      error: { message: 'Invalid response format' }
    });

    const result = await supabaseClient.functions.invoke('heartbeat-handler', {
      body: { roomId: 'room-123', action: 'ping' }
    });

    expect(result.error).toBeDefined();
    expect(result.data).toBeNull();
  });
});

describe('Heartbeat Error Recovery', () => {
  it('should implement exponential backoff on repeated failures', async () => {
    vi.useFakeTimers();
    
    const attemptHeartbeat = async (retryCount: number = 0): Promise<boolean> => {
      const maxRetries = 3;
      const baseDelay = 1000;
      
      try {
        // Simulate heartbeat call
        if (retryCount < 2) {
          throw new Error('Network error');
        }
        return true;
      } catch (error) {
        if (retryCount >= maxRetries) {
          return false;
        }
        
        const delay = baseDelay * Math.pow(2, retryCount);
        await new Promise(resolve => setTimeout(resolve, delay));
        return attemptHeartbeat(retryCount + 1);
      }
    };

    const resultPromise = attemptHeartbeat();
    
    // Fast forward through retries
    await vi.advanceTimersByTimeAsync(1000); // First retry
    await vi.advanceTimersByTimeAsync(2000); // Second retry
    
    const result = await resultPromise;
    expect(result).toBe(true);
    
    vi.useRealTimers();
  });

  it('should validate room existence before sending heartbeat', async () => {
    const isValidRoom = async (roomId: string): Promise<boolean> => {
      // Validate room ID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      return uuidRegex.test(roomId);
    };

    expect(await isValidRoom('45eb4d01-a5df-4d6e-a58d-b03c51a69b3b')).toBe(true);
    expect(await isValidRoom('invalid-room-id')).toBe(false);
    expect(await isValidRoom('')).toBe(false);
  });
});

describe('Heartbeat Session Management', () => {
  it('should check session validity before sending heartbeat', async () => {
    const checkSessionValidity = async (session: any): Promise<boolean> => {
      if (!session || !session.access_token) {
        return false;
      }

      // Check if token is expired (mock implementation)
      const tokenPayload = session.access_token.split('.')[1];
      if (!tokenPayload) return false;

      try {
        const decoded = JSON.parse(atob(tokenPayload));
        const now = Math.floor(Date.now() / 1000);
        return decoded.exp > now;
      } catch {
        return false;
      }
    };

    // Valid session
    const validSession = {
      access_token: 'header.' + btoa(JSON.stringify({ exp: Math.floor(Date.now() / 1000) + 3600 })) + '.signature'
    };
    expect(await checkSessionValidity(validSession)).toBe(true);

    // Expired session
    const expiredSession = {
      access_token: 'header.' + btoa(JSON.stringify({ exp: Math.floor(Date.now() / 1000) - 3600 })) + '.signature'
    };
    expect(await checkSessionValidity(expiredSession)).toBe(false);

    // No session
    expect(await checkSessionValidity(null)).toBe(false);
  });

  it('should refresh token when approaching expiry', async () => {
    const shouldRefreshToken = (session: any): boolean => {
      if (!session || !session.expires_at) return true;
      
      const now = Math.floor(Date.now() / 1000);
      const expiresAt = session.expires_at;
      const bufferTime = 300; // 5 minutes buffer
      
      return (expiresAt - now) < bufferTime;
    };

    // Token expiring soon
    const soonToExpire = {
      expires_at: Math.floor(Date.now() / 1000) + 200 // 3.3 minutes
    };
    expect(shouldRefreshToken(soonToExpire)).toBe(true);

    // Token still valid
    const stillValid = {
      expires_at: Math.floor(Date.now() / 1000) + 3600 // 1 hour
    };
    expect(shouldRefreshToken(stillValid)).toBe(false);
  });
});