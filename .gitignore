# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage

# Build artifacts / Next.js output
/.next/
/out/
/build/
/dist/

# Production (already covered by /build if that's your prod dir)
# /build

# Misc
.DS_Store
*.pem

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local environment files
# This will ignore .env, .env.local, .env.development, .env.development.local, etc.
# at any level in the project, covering both web-app/.env.local and supabase/.env
.env*

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
# next-env.d.ts is typically tracked, so removed from ignore

# Temporary or non-essential project-specific directories/files
# (Adjust if any of these should be tracked)
/scripts/ # If scripts are for one-off tasks and not part of the build/repo
/.venv/   # Python virtual environments
/test_image_outputs/

# Backups and generated summaries (if not intended for version control)
web-app/public/data/*_backup_*.json
project_summary.txt
project_structure.txt
project_related_code.txt
generate_project_summary.py # If this is a local utility script not for the repo

# IDE configuration files
.vscode/
.idea/

# OS-specific files
Thumbs.db

# Explicitly ignore node_modules and .next folders at any depth,
# ensuring they are not accidentally committed if they appear in sub-projects.
# These are somewhat redundant if the top-level /node_modules and /.next are sufficient,
# but provide extra safety for monorepo-like structures or nested projects.
**/node_modules/
**/.next/

# If you want to ignore the entire content of /players_images/ at the root
# and also web-app/public/players_images/, but allow specific essential images
# that can be more complex.
# For now, assuming /players_images/ at the root is to be ignored.
# And web-app/public/players_images/ contains images that ARE part of the app.
# If web-app/public/players_images/ should also be ignored (e.g., very large, generated),
# add it explicitly. Otherwise, it's tracked by default.
/players_images/

# If you have a root /data/ directory to ignore, but your main app data
# (e.g., web-app/public/data/players_game_data.json) is inside web-app/public/data/
# and SHOULD be tracked, this is fine.
# This /data/ only ignores a top-level "data" folder.
/data/