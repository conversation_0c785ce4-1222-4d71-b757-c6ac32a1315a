=== Starting Multiplayer Game Flow Test ===
Testing at: http://localhost:3000
✓ Dev server is running
Detected WSL environment, using system chromium-browser
Running in headless mode
Using browser at: /usr/bin/chromium-browser

[HOST] Navigating to app...
[host] Screenshot saved: test-screenshots-game-flow/host-01-initial-load-2025-07-08T00-36-48-819Z.png

[GUEST] Navigating to app...
[guest] Screenshot saved: test-screenshots-game-flow/guest-01-initial-load-2025-07-08T00-36-53-410Z.png
Waiting for app to fully initialize...

=== PHASE 1: Authentication ===
[HOST] Checking authentication status...
[HOST] Warning: Loading check timed out, continuing anyway...
[host] Screenshot saved: test-screenshots-game-flow/host-02-loading-timeout-2025-07-08T00-37-11-528Z.png
[HOST] Not logged in, looking for Multiplayer Mode button...
[HOST] Page info: {
  bodyText: 'Loading...',
  buttonTexts: [],
  hasLoadingText: true,
  visibleElements: 15
}
[HOST] Page still loading, waiting more...
[host] Screenshot saved: test-screenshots-game-flow/host-02-no-multiplayer-button-2025-07-08T00-37-16-668Z.png

=== TEST FAILED ===
Error: [HOST] Could not find Multiplayer Mode button
    at testMultiplayerGameFlow (/mnt/c/Projects/recognition-combine/test-multiplayer-game-flow.js:265:15)

Keeping browsers open for observation...

❌ MULTIPLAYER GAME FLOW TEST FAILED
