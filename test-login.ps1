$body = '{"identifier":"test","password":"wrong"}'
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:54321/functions/v1/login-handler" -Method POST -Headers @{"Content-Type"="application/json"} -Body $body -UseBasicParsing
    Write-Host "Success Response:"
    Write-Host "Status:" $response.StatusCode
    Write-Host "Raw Content:" $response.RawContent
} catch {
    Write-Host "Error Response:"
    Write-Host "Status:" $_.Exception.Response.StatusCode
    Write-Host "Raw Content:"
    $stream = $_.Exception.Response.GetResponseStream()
    $reader = New-Object System.IO.StreamReader($stream)
    $responseBody = $reader.ReadToEnd()
    Write-Host $responseBody
    Write-Host "Response Headers:"
    Write-Host $_.Exception.Response.Headers
} 