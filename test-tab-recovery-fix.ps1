#!/usr/bin/env pwsh

# Test script to verify the "tab-back-in" real-time subscription fix
# This script checks that the aggressive cleanup strategy is properly implemented

Write-Host "🔍 Testing Tab-Recovery Real-time Subscription Fix" -ForegroundColor Cyan
Write-Host "=" * 60

function Test-RealtimeCleanupStrategy {
    param(
        [string]$FilePath,
        [string]$TestName
    )
    
    Write-Host "`n📋 Testing: $TestName" -ForegroundColor Yellow
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "❌ File not found: $FilePath" -ForegroundColor Red
        return $false
    }
    
    $content = Get-Content $FilePath -Raw
    
    # Test 1: Check for aggressive cleanup comments
    $hasAggressiveCleanupComment = $content -match "aggressive.*cleanup|nuke.*pave|tab.*recovery"
    Write-Host "✓ Has aggressive cleanup documentation: $hasAggressiveCleanupComment" -ForegroundColor $(if($hasAggressiveCleanupComment) { "Green" } else { "Red" })
    
    # Test 2: Check for removeAllChannels() usage
    $hasRemoveAllChannels = $content -match "removeAllChannels\(\)"
    Write-Host "✓ Uses removeAllChannels(): $hasRemoveAllChannels" -ForegroundColor $(if($hasRemoveAllChannels) { "Green" } else { "Red" })
    
    # Test 3: Count occurrences of removeAllChannels vs individual removeChannel
    $removeAllChannelsCount = ([regex]::Matches($content, "removeAllChannels\(\)")).Count
    $removeChannelCount = ([regex]::Matches($content, "removeChannel\([^)]+\)")).Count
    
    Write-Host "✓ removeAllChannels() calls: $removeAllChannelsCount" -ForegroundColor Green
    Write-Host "✓ Individual removeChannel() calls: $removeChannelCount" -ForegroundColor $(if($removeChannelCount -eq 0) { "Green" } else { "Yellow" })
    
    # Test 4: Check for tab-recovery protection comments
    $hasTabRecoveryProtection = $content -match "TAB.*RECOVERY.*PROTECTION|tab.*back.*in"
    Write-Host "✓ Has tab-recovery protection documentation: $hasTabRecoveryProtection" -ForegroundColor $(if($hasTabRecoveryProtection) { "Green" } else { "Red" })
    
    # Test 5: Check for proper cleanup function structure
    $hasProperCleanupStructure = $content -match "return\s*\(\s*\)\s*=>\s*{[^}]*removeAllChannels"
    Write-Host "✓ Has proper cleanup function structure: $hasProperCleanupStructure" -ForegroundColor $(if($hasProperCleanupStructure) { "Green" } else { "Red" })
    
    $allTestsPassed = $hasAggressiveCleanupComment -and $hasRemoveAllChannels -and $hasTabRecoveryProtection -and $hasProperCleanupStructure
    
    Write-Host "`n📊 Overall Result: $(if($allTestsPassed) { "✅ PASSED" } else { "❌ FAILED" })" -ForegroundColor $(if($allTestsPassed) { "Green" } else { "Red" })
    
    return $allTestsPassed
}

function Test-SubscriptionPatterns {
    param([string]$FilePath)
    
    Write-Host "`n🔍 Analyzing Subscription Patterns" -ForegroundColor Yellow
    
    $content = Get-Content $FilePath -Raw
    
    # Find all useEffect hooks that contain subscription logic
    $useEffectMatches = [regex]::Matches($content, "useEffect\s*\(\s*\(\s*\)\s*=>\s*{[^}]*subscribe[^}]*}", [System.Text.RegularExpressions.RegexOptions]::Singleline)
    
    Write-Host "✓ Found $($useEffectMatches.Count) useEffect hooks with subscriptions" -ForegroundColor Green
    
    # Check each useEffect for proper cleanup
    $properCleanupCount = 0
    foreach ($match in $useEffectMatches) {
        if ($match.Value -match "removeAllChannels") {
            $properCleanupCount++
        }
    }
    
    Write-Host "✓ useEffect hooks with aggressive cleanup: $properCleanupCount / $($useEffectMatches.Count)" -ForegroundColor $(if($properCleanupCount -eq $useEffectMatches.Count) { "Green" } else { "Yellow" })
    
    return $properCleanupCount -eq $useEffectMatches.Count
}

function Show-FixSummary {
    Write-Host "`n📋 Fix Summary" -ForegroundColor Cyan
    Write-Host "=" * 40
    
    Write-Host "🎯 Problem Solved:" -ForegroundColor Yellow
    Write-Host "   • Tab-back-in WebSocket recovery race conditions"
    Write-Host "   • Duplicate subscription channel conflicts"
    Write-Host "   • CHANNEL_ERROR and timeout loops"
    
    Write-Host "`n🔧 Solution Implemented:" -ForegroundColor Yellow
    Write-Host "   • Aggressive 'nuke and pave' cleanup strategy"
    Write-Host "   • removeAllChannels() instead of individual removal"
    Write-Host "   • Prevents subscription conflicts during recovery"
    
    Write-Host "`n🧪 Testing Instructions:" -ForegroundColor Yellow
    Write-Host "   1. Load your app and join a multiplayer room"
    Write-Host "   2. Switch to another browser tab for 4+ minutes"
    Write-Host "   3. Tab back to your app"
    Write-Host "   4. Check console - should see clean reconnection"
    Write-Host "   5. No more CHANNEL_ERROR or timeout loops!"
}

# Run the tests
$pageJsPath = "web-app/src/app/page.tsx"

Write-Host "🚀 Starting Tab-Recovery Fix Verification..." -ForegroundColor Green

$test1Passed = Test-RealtimeCleanupStrategy -FilePath $pageJsPath -TestName "Room-Specific Subscriptions"
$test2Passed = Test-SubscriptionPatterns -FilePath $pageJsPath

Write-Host "`n" + "=" * 60
Write-Host "🏁 FINAL RESULT" -ForegroundColor Cyan

if ($test1Passed -and $test2Passed) {
    Write-Host "✅ ALL TESTS PASSED - Tab-Recovery Fix Successfully Implemented!" -ForegroundColor Green
    Write-Host "   Your app should now handle tab-back-in scenarios gracefully." -ForegroundColor Green
} else {
    Write-Host "❌ SOME TESTS FAILED - Fix may be incomplete" -ForegroundColor Red
    Write-Host "   Please review the failed tests above." -ForegroundColor Red
}

Show-FixSummary

Write-Host "`n🎉 Ready to test! Try the tab-switching scenario described above." -ForegroundColor Magenta
