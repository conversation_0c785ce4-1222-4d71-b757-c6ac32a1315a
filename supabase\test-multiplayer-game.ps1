# Test multiplayer game flow

Write-Host @"

MULTIPLAYER GAME TEST INSTRUCTIONS
==================================

To verify the submit-answer-handler is working correctly in a real game:

1. Open two browser windows/tabs
2. Navigate to your game: https://recognition-combine.vercel.app/
3. In both windows:
   - Sign in (or create accounts)
   - Click "Multiplayer"
   - Create/Join the same room
   
4. Once both players are in:
   - Host clicks "Start Game"
   - Both players should see the countdown
   - When the game starts, both submit answers
   
5. Check for:
   ✓ Answers are recorded for both players
   ✓ Scores update correctly
   ✓ Game advances to next question after both answer
   ✓ 3-second transition timer shows results
   ✓ Bonus levels (BQ1, BQ2, BQ3) work on streaks
   
6. Monitor browser console for errors

Press Enter to open the game...
"@ -ForegroundColor Cyan

Read-Host

Start-Process "https://recognition-combine.vercel.app/"

Write-Host "`nGame opened. Follow the test instructions above." -ForegroundColor Green
Write-Host "`nKey things to verify:" -ForegroundColor Yellow
Write-Host "- Submit answer works for both players" -ForegroundColor White
Write-Host "- Game advances after both players answer" -ForegroundColor White
Write-Host "- 3-second transition timer appears" -ForegroundColor White
Write-Host "- Scores and bonus levels update correctly" -ForegroundColor White