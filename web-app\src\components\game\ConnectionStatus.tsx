'use client';

import React from 'react';
import { AlertCircle, WifiOff, Wifi } from 'lucide-react';

interface ConnectionStatusProps {
  isConnected: boolean;
  isRetrying: boolean;
  retryCount: number;
  maxRetries: number;
  error?: string | null;
}

export function ConnectionStatus({ 
  isConnected, 
  isRetrying, 
  retryCount, 
  maxRetries,
  error 
}: ConnectionStatusProps) {
  if (isConnected && !isRetrying && !error) {
    return null; // Don't show anything when connection is healthy
  }

  return (
    <div className="fixed top-4 right-4 z-50 animate-in fade-in slide-in-from-top-2">
      <div 
        className={`
          flex items-center gap-2 px-4 py-2 rounded-lg shadow-lg
          ${error 
            ? 'bg-red-100 border border-red-300 text-red-800' 
            : isRetrying 
              ? 'bg-yellow-100 border border-yellow-300 text-yellow-800'
              : 'bg-gray-100 border border-gray-300 text-gray-800'
          }
        `}
      >
        {error ? (
          <>
            <AlertCircle className="w-5 h-5" />
            <span className="text-sm font-medium">{error}</span>
          </>
        ) : isRetrying ? (
          <>
            <WifiOff className="w-5 h-5 animate-pulse" />
            <span className="text-sm font-medium">
              Reconnecting... (Attempt {retryCount}/{maxRetries})
            </span>
          </>
        ) : !isConnected ? (
          <>
            <WifiOff className="w-5 h-5" />
            <span className="text-sm font-medium">Connection lost</span>
          </>
        ) : null}
      </div>
    </div>
  );
}