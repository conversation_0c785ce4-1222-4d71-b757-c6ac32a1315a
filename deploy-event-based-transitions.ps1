# Deploy the event-based transition system
Write-Host "Deploying event-based transition system..." -ForegroundColor Green

# Change to supabase directory
Set-Location supabase

# Deploy the check-game-transition function
Write-Host "`n1. Deploying check-game-transition function..." -ForegroundColor Yellow
supabase functions deploy check-game-transition

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to deploy check-game-transition" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Deploy the updated transition-monitor function
Write-Host "`n2. Deploying updated transition-monitor function..." -ForegroundColor Yellow
supabase functions deploy transition-monitor

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to deploy transition-monitor" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Return to root directory
Set-Location ..

Write-Host "`n✅ Event-based transition system deployed successfully!" -ForegroundColor Green
Write-Host "`nThe system now works as follows:" -ForegroundColor Cyan
Write-Host "1. When the last player answers, a 3-second transition deadline is set" -ForegroundColor Gray
Write-Host "2. Each client watches this deadline with a local timer" -ForegroundColor Gray
Write-Host "3. When the deadline passes, the client asks the server to check/transition" -ForegroundColor Gray
Write-Host "4. The server validates and advances the game if appropriate" -ForegroundColor Gray
Write-Host "`nNo external cron job needed! 🎉" -ForegroundColor Green