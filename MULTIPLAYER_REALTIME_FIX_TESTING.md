# Multiplayer Realtime Fix Testing Guide

## Summary of Issues Fixed

1. **Realtime subscription timeout errors** - Fixed by removing duplicate subscription attempts
2. **Multiple subscription attempts** - Fixed by checking channel state before creating new channels
3. **Tab visibility disconnections** - Fixed by properly removing and recreating channels on reconnect
4. **Host not seeing joining players** - Fixed by ensuring RLS policies and realtime publications are correct

## Changes Made

### 1. Fixed Duplicate Subscription Prevention
- Removed automatic resubscription after timeout (was causing "tried to subscribe multiple times" error)
- Added channel state checking before creating new channels
- Only reuse existing channels if they're in 'joined' state

### 2. Improved Visibility Change Handling
- Remove all channels when tab becomes visible
- Trigger channel recreation through state update
- Fetch latest player data after reconnection

### 3. Enhanced Logging
- Added detailed logging for player JOIN events
- Track channel state in realtime events
- Log subscription setup completion

### 4. Database Migrations
- `20250617000000_fix_realtime_publications.sql` - Enables realtime for game_players table
- `20250701000000_fix_realtime_rls_policy.sql` - Fixes RLS policies for realtime access

## Testing Steps

### Step 1: Apply Migrations
```powershell
# Run from project root
.\apply-realtime-migrations.ps1
```

### Step 2: Build and Run
```bash
# From project root
npm run build
npm run dev
```

### Step 3: Test Multiplayer Flow

1. **Open Browser Console** (F12) to monitor logs

2. **Create Room as Host**:
   - Sign in as User 1
   - Switch to Multiplayer mode
   - Click "Host a Game"
   - Note the room ID in console logs
   - Watch for: `[Realtime] Player subscription setup complete`

3. **Join as Second Player**:
   - Open new incognito/private browser window
   - Sign in as User 2
   - Switch to Multiplayer mode
   - Look for the hosted room in the lobby
   - Click "Join"

4. **Verify Host Sees Player**:
   - Switch back to Host's browser
   - Look for console logs:
     ```
     [Realtime] *** NEW PLAYER JOINED ***
     [Realtime] Triggering player fetch due to realtime event (INSERT)
     [RoomView HOST] [HOST CRITICAL] *** HOST SETTING STATE WITH MULTIPLE PLAYERS
     ```
   - Host should see "2/4 players" in the UI
   - Both players should appear in the player list

### Step 4: Test Tab Switching

1. **Switch tabs** while in a room
2. **Return to tab** and check console for:
   ```
   [DISCONNECT_DETECTION] Page became visible - reconnecting real-time
   [DISCONNECT_DETECTION] Removing existing channels
   [DISCONNECT_DETECTION] Channels removed, realtime reconnected
   ```
3. Verify players still show correctly

### Step 5: Test Player Ready States

1. Have Player 2 click "Ready"
2. Host should see the ready state update in real-time
3. Check for: `[Realtime] *** PLAYER UPDATE EVENT ***` with eventType: 'UPDATE'

## Expected Console Output

### Successful Host Room Creation:
```
[Client] [CREATE_ROOM] *** ROOM CREATED SUCCESSFULLY ***
[Realtime] ✅ Setting up NEW subscriptions for room: [room-id]
[Realtime] Player subscription setup complete for room [room-id]
[Realtime] ✅ Successfully subscribed to room channel
```

### Successful Player Join (Host's Console):
```
[Realtime] *** PLAYER UPDATE EVENT *** for room [room-id]
[Realtime] *** NEW PLAYER JOINED *** Room [room-id]
[RoomView HOST] [HOST CRITICAL] *** HOST SETTING STATE WITH MULTIPLE PLAYERS
```

### Successful Reconnection After Tab Switch:
```
[DISCONNECT_DETECTION] Page became visible - reconnecting real-time
[DISCONNECT_DETECTION] Removing existing channels: ["room-[id]"]
[DISCONNECT_DETECTION] Channels removed, realtime reconnected
[Realtime] ✅ Setting up NEW subscriptions for room
```

## Troubleshooting

### If Host Still Doesn't See Players:

1. **Check Migration Status**:
   ```sql
   -- In Supabase SQL Editor
   SELECT * FROM supabase_realtime.schema_migrations;
   ```
   Should show both realtime migrations applied.

2. **Verify Realtime is Enabled**:
   ```sql
   -- Check if game_players is in realtime publication
   SELECT * FROM pg_publication_tables 
   WHERE pubname = 'supabase_realtime' 
   AND tablename = 'game_players';
   ```

3. **Check RLS Policies**:
   ```sql
   -- Verify the helper function exists
   SELECT proname FROM pg_proc 
   WHERE proname = 'user_can_see_game_player';
   ```

4. **Force Database Reset** (if needed):
   ```powershell
   cd supabase
   supabase db reset
   ```

### Common Issues:

1. **"tried to subscribe multiple times" error**
   - Should be fixed by the channel state checking
   - If persists, refresh the page

2. **Timeout errors**
   - Normal if tab is inactive for long periods
   - Should auto-recover when tab becomes active

3. **Players not appearing**
   - Check browser console for any RLS policy errors
   - Ensure migrations are applied
   - Try refreshing both browsers

## Success Criteria

✅ Host can create a room
✅ Other players can join the room
✅ Host sees joining players immediately (within 1-2 seconds)
✅ Ready states update in real-time for all players
✅ Tab switching doesn't break realtime connections
✅ No "tried to subscribe multiple times" errors
✅ No infinite timeout/reconnection loops