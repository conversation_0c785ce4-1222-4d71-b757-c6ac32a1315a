const puppeteer = require('puppeteer');
const readline = require('readline');

// Simple automated test that uses existing accounts

class SimpleMultiplayerTest {
  constructor() {
    this.gameUrl = 'https://recognition-combine.vercel.app/';
    this.results = [];
  }

  async promptForCredentials() {
    // Use predefined test credentials
    console.log('\n📝 Using test account credentials:');
    console.log('Player 1: fresh');
    console.log('Player 2: fresh2\n');

    return {
      player1: { email: 'fresh', password: 'test123' },
      player2: { email: 'fresh2', password: 'test123' }
    };
  }

  async run() {
    console.log('\n🎯 Multiplayer Round Advance Test (Simple Version)');
    console.log('================================================\n');

    // Get credentials
    const credentials = await this.promptForCredentials();

    console.log('\n🚀 Launching browsers...');
    
    const browser1 = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox'],
      defaultViewport: { width: 1200, height: 800 }
    });
    
    const browser2 = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox'],
      defaultViewport: { width: 1200, height: 800 }
    });

    const page1 = await browser1.newPage();
    const page2 = await browser2.newPage();

    try {
      // Navigate to game
      await Promise.all([
        page1.goto(this.gameUrl),
        page2.goto(this.gameUrl)
      ]);

      console.log('\n📋 Test will verify these scenarios:');
      console.log('1. Both answer at 1s → Round at 4s');
      console.log('2. Both answer at 2s → Round at 5s');
      console.log('3. Both answer at 5s → Round at 7s (cap)');
      console.log('4. One player only → Round at 7s');
      console.log('5. P1 at 1s, P2 at 3s → Round at 6s');

      console.log('\n⚠️  MANUAL STEPS REQUIRED:');
      console.log('1. Sign in both windows with the provided credentials');
      console.log('2. Create room in window 1, join in window 2');
      console.log('3. Start the game');
      console.log('4. The test will then run automatically\n');

      // Inject monitoring code in both pages
      const monitorCode = `
        console.log('🔍 Round timing monitor active');
        
        window.timingTest = {
          roundStart: null,
          results: []
        };
        
        // Monitor for game start
        const checkGameStart = setInterval(() => {
          const inGame = document.querySelector('.score, [class*="Score"]');
          const question = document.querySelector('h2');
          if (inGame && question && !window.timingTest.roundStart) {
            window.timingTest.roundStart = Date.now();
            console.log('🎮 Round started!');
            clearInterval(checkGameStart);
          }
        }, 100);
        
        // Monitor round advances
        let lastQuestion = '';
        setInterval(() => {
          const question = document.querySelector('h2')?.textContent || '';
          if (question && question !== lastQuestion && lastQuestion) {
            const elapsed = (Date.now() - window.timingTest.roundStart) / 1000;
            console.log(\`✅ Round advanced at: \${elapsed.toFixed(1)}s\`);
            window.timingTest.results.push(elapsed);
            window.timingTest.roundStart = Date.now();
          }
          lastQuestion = question;
        }, 50);
        
        // Helper to submit answer at specific time
        window.submitAt = (seconds) => {
          const target = seconds * 1000;
          const elapsed = Date.now() - window.timingTest.roundStart;
          const delay = Math.max(0, target - elapsed);
          
          setTimeout(() => {
            const button = document.querySelector('.choice-button, button[class*="choice"]');
            if (button) {
              button.click();
              console.log(\`📝 Submitted at \${((Date.now() - window.timingTest.roundStart) / 1000).toFixed(1)}s\`);
            }
          }, delay);
        };
      `;

      await page1.evaluate(monitorCode);
      await page2.evaluate(monitorCode);

      console.log('✅ Monitoring code injected');
      console.log('\n⏳ Waiting for game to start...');
      console.log('(Complete the manual steps above)\n');

      // Wait for user to set up the game
      await page1.waitForFunction(() => {
        return window.timingTest && window.timingTest.roundStart;
      }, { timeout: 300000 }); // 5 minute timeout

      console.log('🎮 Game detected! Running automated tests...\n');

      // Run test scenarios
      const scenarios = [
        { name: 'Both at 1s', p1: 1, p2: 1, expected: 4 },
        { name: 'Both at 2s', p1: 2, p2: 2, expected: 5 },
        { name: 'Both at 5s', p1: 5, p2: 5, expected: 7 },
        { name: 'Only P1', p1: 1, p2: null, expected: 7 },
        { name: 'P1@1s P2@3s', p1: 1, p2: 3, expected: 6 }
      ];

      for (let i = 0; i < scenarios.length; i++) {
        const scenario = scenarios[i];
        console.log(`\nTest ${i + 1}: ${scenario.name}`);
        console.log(`Expected: ${scenario.expected}s`);

        // Submit answers
        if (scenario.p1 !== null) {
          await page1.evaluate((time) => window.submitAt(time), scenario.p1);
        }
        if (scenario.p2 !== null) {
          await page2.evaluate((time) => window.submitAt(time), scenario.p2);
        }

        // Wait for round to advance
        await new Promise(resolve => setTimeout(resolve, 8000));

        // Get result from page1
        const result = await page1.evaluate(() => {
          const results = window.timingTest.results;
          return results[results.length - 1];
        });

        console.log(`Actual: ${result?.toFixed(1)}s`);
        const diff = Math.abs(result - scenario.expected);
        console.log(diff < 0.5 ? '✅ PASS' : '❌ FAIL');
      }

      console.log('\n📊 Test complete!');

    } catch (error) {
      console.error('❌ Test error:', error);
    } finally {
      console.log('\nPress Ctrl+C to close browsers and exit');
      // Keep browsers open for inspection
      await new Promise(() => {});
    }
  }
}

// Run the test
const test = new SimpleMultiplayerTest();
test.run().catch(console.error);