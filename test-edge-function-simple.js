// Direct test of the start-game-handler <PERSON> Function using built-in https module
const https = require('https');

async function testEdgeFunction() {
  console.log('=== Testing Edge Function Directly ===\n');
  
  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAwNDAzMDksImV4cCI6MjAzNTYxNjMwOX0.8uZ-s-ZfHPUIHcJlJmYz1TC-TnE-xe_o-ccR-UK55es';
  
  // Test room ID
  const roomId = 'test-room-' + Date.now();
  const data = JSON.stringify({ roomId });
  
  const options = {
    hostname: 'xmyxuvuimebjltnaamox.supabase.co',
    port: 443,
    path: '/functions/v1/start-game-handler',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      'apikey': SUPABASE_ANON_KEY
    }
  };
  
  console.log('Testing Edge Function at: https://' + options.hostname + options.path);
  console.log('With room ID:', roomId);
  
  const req = https.request(options, (res) => {
    console.log('\nResponse status:', res.statusCode);
    console.log('Response headers:', res.headers);
    
    let responseData = '';
    
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      console.log('\nResponse body:', responseData);
      
      if (res.statusCode !== 200) {
        console.log('\n❌ Edge Function returned error status:', res.statusCode);
        
        // Try to parse as JSON for error details
        try {
          const errorData = JSON.parse(responseData);
          console.log('Error details:', JSON.stringify(errorData, null, 2));
        } catch (e) {
          console.log('Response is not JSON');
        }
      } else {
        console.log('\n✅ Edge Function called successfully');
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('\n❌ Failed to call Edge Function:', error);
  });
  
  req.write(data);
  req.end();
}

testEdgeFunction();