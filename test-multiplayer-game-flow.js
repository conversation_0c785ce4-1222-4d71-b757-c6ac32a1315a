const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');

// Configuration
const BASE_URL = 'http://localhost:3002';
const SCREENSHOT_DIR = 'test-screenshots-game-flow';
const TIMEOUT_MS = 90000; // 90 second timeout for full game flow
const ROUND_ADVANCE_TIMEOUT_MS = 10000; // 10 seconds for round advancement

// Test credentials
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };

// Ensure screenshot directory exists
async function ensureScreenshotDir() {
  try {
    await fs.access(SCREENSHOT_DIR);
  } catch {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
  }
}

// Helper to take screenshot with timestamp
async function takeScreenshot(page, label, playerName) {
  try {
    // Check if page has dimensions
    const dimensions = await page.evaluate(() => {
      return {
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
      };
    });
    
    if (dimensions.width === 0 || dimensions.height === 0) {
      console.log(`[${playerName}] Warning: Page has 0 dimensions, skipping screenshot for ${label}`);
      return;
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${SCREENSHOT_DIR}/${playerName}-${label}-${timestamp}.png`;
    await page.screenshot({ path: filename, fullPage: true });
    console.log(`[${playerName}] Screenshot saved: ${filename}`);
  } catch (error) {
    console.log(`[${playerName}] Failed to take screenshot for ${label}: ${error.message}`);
  }
}

// Helper to wait for selector with custom timeout and detailed logging
async function waitForSelectorWithLogging(page, selector, playerName, description, timeout = 30000) {
  console.log(`[${playerName}] Waiting for ${description}: ${selector}`);
  try {
    await page.waitForSelector(selector, { visible: true, timeout });
    console.log(`[${playerName}] ✓ Found ${description}`);
    return true;
  } catch (error) {
    console.error(`[${playerName}] ✗ Timeout waiting for ${description}: ${selector}`);
    await takeScreenshot(page, `error-${description.replace(/\s+/g, '-')}`, playerName);
    throw error;
  }
}

// Helper to log console messages from the browser
function setupConsoleLogging(page, playerName) {
  page.on('console', msg => {
    const text = msg.text();
    // Filter out noisy logs
    if (text.includes('[Realtime]') || 
        text.includes('[SYNC_STATE]') || 
        text.includes('[Client]') ||
        text.includes('[EDGE_START_GAME]') ||
        text.includes('active game state') ||
        text.includes('Game Starting') ||
        text.includes('current_question_data')) {
      console.log(`[${playerName} Browser] ${text}`);
    }
  });
}

// Helper to check if dev server is running
async function checkDevServer() {
  try {
    const response = await fetch(BASE_URL);
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Main test function
async function testMultiplayerGameFlow() {
  console.log('=== Starting Multiplayer Game Flow Test ===');
  console.log(`Testing at: ${BASE_URL}`);
  
  // Check if dev server is running
  // Temporarily bypassing the check since fetch may not be available
  // const serverRunning = await checkDevServer();
  // if (!serverRunning) {
  //   console.error('\n❌ Development server is not running!');
  //   console.error('Please start the dev server with: npm run dev');
  //   process.exit(1);
  // }
  console.log('✓ Testing on port 3002');
  
  await ensureScreenshotDir();
  
  // Detect environment and set appropriate browser path
  let executablePath;
  const platform = os.platform();
  
  // Check if we're in WSL
  const isWSL = platform === 'linux' && os.release().toLowerCase().includes('microsoft');
  
  if (isWSL) {
    // In WSL, use the system chromium-browser
    executablePath = '/usr/bin/chromium-browser';
    console.log('Detected WSL environment, using system chromium-browser');
  } else if (platform === 'win32') {
    // On Windows, let Puppeteer find Chrome/Edge
    const possiblePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
    ];
    
    for (const chromePath of possiblePaths) {
      try {
        await fs.access(chromePath);
        executablePath = chromePath;
        console.log(`Found browser at: ${chromePath}`);
        break;
      } catch (e) {
        // Continue checking other paths
      }
    }
  }
  
  // Launch two browsers (host and guest) - set headless based on environment
  const isHeadless = process.env.HEADLESS !== 'false';
  const launchOptions = {
    headless: isHeadless,
    ...(executablePath && { executablePath }), // Only include if we found a path
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--disable-web-security',
      '--disable-features=IsolateOrigins,site-per-process'
    ]
  };
  
  console.log(`Running in ${isHeadless ? 'headless' : 'headed'} mode`);
  
  if (executablePath) {
    console.log(`Using browser at: ${executablePath}`);
  } else {
    console.log('Using Puppeteer bundled browser');
  }
  
  const browserHost = await puppeteer.launch(launchOptions);
  const browserGuest = await puppeteer.launch(launchOptions);
  
  let testPassed = false;
  
  try {
    // Create pages
    const hostPage = await browserHost.newPage();
    const guestPage = await browserGuest.newPage();
    
    await hostPage.setViewport({ width: 1200, height: 800 });
    await guestPage.setViewport({ width: 1200, height: 800 });
    
    // Setup error handling
    hostPage.on('error', err => {
      console.error('[HOST] Page crashed:', err);
    });
    hostPage.on('pageerror', err => {
      console.error('[HOST] Page error:', err.message);
    });
    
    guestPage.on('error', err => {
      console.error('[GUEST] Page crashed:', err);
    });
    guestPage.on('pageerror', err => {
      console.error('[GUEST] Page error:', err.message);
    });
    
    // Monitor failed requests
    hostPage.on('requestfailed', request => {
      console.error('[HOST] Request failed:', request.url(), request.failure().errorText);
    });
    
    guestPage.on('requestfailed', request => {
      console.error('[GUEST] Request failed:', request.url(), request.failure().errorText);
    });
    
    // Setup console logging
    setupConsoleLogging(hostPage, 'HOST');
    setupConsoleLogging(guestPage, 'GUEST');
    
    // Navigate to the app
    console.log('\n[HOST] Navigating to app...');
    await hostPage.goto(BASE_URL, { waitUntil: 'networkidle2', timeout: 30000 });
    await new Promise(resolve => setTimeout(resolve, 2000)); // Give page time to render
    await takeScreenshot(hostPage, '01-initial-load', 'host');
    
    console.log('\n[GUEST] Navigating to app...');
    await guestPage.goto(BASE_URL, { waitUntil: 'networkidle2', timeout: 30000 });
    await new Promise(resolve => setTimeout(resolve, 2000)); // Give page time to render
    await takeScreenshot(guestPage, '01-initial-load', 'guest');
    
    // Give the app more time to fully load and initialize
    console.log('Waiting for app to fully initialize...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // === PHASE 1: Authentication ===
    console.log('\n=== PHASE 1: Authentication ===');
    
    // Host login
    console.log('[HOST] Checking authentication status...');
    
    // Wait for auth loading to complete or for game elements to appear
    try {
      console.log('[HOST] Waiting for page to finish loading...');
      await hostPage.waitForFunction(
        () => {
          const hasLoading = document.body.textContent.includes('Loading...');
          const hasGameButtons = document.querySelector('button');
          const hasMultiplayerButton = Array.from(document.querySelectorAll('button'))
            .some(btn => btn.textContent.includes('Multiplayer Mode'));
          // Page is ready if no loading text OR if we have game buttons
          return !hasLoading || hasGameButtons || hasMultiplayerButton;
        },
        { timeout: 30000 } // Increased timeout to 30 seconds
      );
      console.log('[HOST] Page loaded, checking auth state');
    } catch (e) {
      console.log('[HOST] Warning: Loading check timed out after 30s, continuing anyway...');
      // Check what's on the page
      const pageState = await hostPage.evaluate(() => {
        return {
          bodyText: document.body.textContent.substring(0, 200),
          hasLoading: document.body.textContent.includes('Loading...'),
          buttonCount: document.querySelectorAll('button').length,
          errorText: document.querySelector('.error')?.textContent || null
        };
      });
      console.log('[HOST] Page state after timeout:', pageState);
      await takeScreenshot(hostPage, '02-loading-timeout', 'host');
    }
    
    // Check if already logged in
    const isLoggedInHost = await hostPage.evaluate(() => {
      return document.body.textContent.includes('fresh') || 
             document.body.textContent.includes('Sign Out') ||
             document.body.textContent.includes('Logout');
    });
    
    if (isLoggedInHost) {
      console.log('[HOST] Already logged in as fresh');
      await takeScreenshot(hostPage, '02-already-logged-in', 'host');
    } else {
      console.log('[HOST] Not logged in, looking for Multiplayer Mode button...');
      
      // Debug: Check what's on the page
      const pageInfo = await hostPage.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return {
          bodyText: document.body.innerText.substring(0, 500),
          buttonTexts: buttons.map(btn => btn.textContent.trim()),
          hasLoadingText: document.body.textContent.includes('Loading...'),
          visibleElements: document.body.children.length
        };
      });
      console.log('[HOST] Page info:', pageInfo);
      
      // If still loading, wait more
      if (pageInfo.hasLoadingText) {
        console.log('[HOST] Page still loading, waiting more...');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
      
      // Click on Multiplayer Mode button to trigger auth modal
      const multiplayerClicked = await hostPage.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const mpBtn = buttons.find(btn => 
          btn.textContent.includes('Multiplayer Mode') || 
          btn.textContent.includes('Multiplayer')
        );
        if (mpBtn) {
          mpBtn.click();
          return true;
        }
        return false;
      });
      
      if (!multiplayerClicked) {
        await takeScreenshot(hostPage, '02-no-multiplayer-button', 'host');
        throw new Error('[HOST] Could not find Multiplayer Mode button');
      }
      
      console.log('[HOST] Clicked Multiplayer Mode, waiting for auth modal...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      await takeScreenshot(hostPage, '02-after-multiplayer-click', 'host');
      
      // Check if modal opened and has auth form
      const hasModal = await hostPage.evaluate(() => {
        const modal = document.querySelector('[role="dialog"], .modal, [data-testid="auth-modal"]');
        const inputs = document.querySelectorAll('input');
        return {
          hasModal: !!modal,
          inputCount: inputs.length,
          inputNames: Array.from(inputs).map(i => i.name || i.type || i.placeholder)
        };
      });
      console.log('[HOST] Modal check:', hasModal);
      
      // Wait for auth form to be visible
      try {
        await hostPage.waitForSelector('input[type="email"], input[type="text"], input[placeholder*="email" i], input[placeholder*="username" i]', 
          { visible: true, timeout: 5000 });
        console.log('[HOST] Auth form is visible');
      } catch (e) {
        console.log('[HOST] Could not find auth form inputs, taking debug screenshot');
        await takeScreenshot(hostPage, '02-debug-no-auth-form', 'host');
        throw new Error('[HOST] Auth form did not appear after clicking Multiplayer Mode');
      }
      
      // Fill in credentials with delay between typing
      console.log('[HOST] Filling in credentials...');
      
      // Find email/username input more specifically
      const emailInput = await hostPage.$('input[type="email"], input[type="text"]:not([type="password"]), input[placeholder*="email" i], input[placeholder*="username" i]');
      if (!emailInput) {
        // Debug: list all inputs
        const inputInfo = await hostPage.evaluate(() => {
          return Array.from(document.querySelectorAll('input')).map(i => ({
            type: i.type,
            placeholder: i.placeholder,
            name: i.name,
            visible: i.offsetParent !== null
          }));
        });
        console.log('[HOST] Available inputs:', inputInfo);
        throw new Error('[HOST] Could not find email/username input');
      }
      
      // Find password input
      const passwordInput = await hostPage.$('input[type="password"]');
      if (!passwordInput) {
        throw new Error('[HOST] Could not find password input');
      }
      
      // Type credentials
      await emailInput.click();
      await emailInput.type(HOST_CREDENTIALS.username, { delay: 50 });
      
      await new Promise(resolve => setTimeout(resolve, 200));
      
      await passwordInput.click();
      await passwordInput.type(HOST_CREDENTIALS.password, { delay: 50 });
      
      console.log('[HOST] Credentials entered');
      await takeScreenshot(hostPage, '03-credentials-entered', 'host');
      
      // Submit the form
      console.log('[HOST] Looking for submit button...');
      const submitClicked = await hostPage.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const submitBtn = buttons.find(btn => 
          btn.type === 'submit' || 
          btn.textContent.toLowerCase().includes('sign in') ||
          btn.textContent.toLowerCase().includes('login') ||
          btn.textContent.toLowerCase().includes('continue')
        );
        if (submitBtn) {
          console.log('[Browser] Found submit button:', submitBtn.textContent);
          submitBtn.click();
          return true;
        }
        console.log('[Browser] Could not find submit button');
        return false;
      });
      
      if (!submitClicked) {
        console.log('[HOST] Could not find submit button, trying form submit...');
        await hostPage.keyboard.press('Enter');
      } else {
        console.log('[HOST] Clicked submit button');
      }
      
      // Wait for auth modal to close
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Take screenshot after submit
      await takeScreenshot(hostPage, '02-after-submit', 'host');
      
      // Check if logged in by verifying auth modal closed and no login form visible
      try {
        const loginSuccess = await hostPage.waitForFunction(
          () => {
            // Check if auth form inputs are gone (modal closed)
            const authInputsGone = !document.querySelector('input[type="text"]') || 
                                 document.querySelector('input[type="text"]').offsetParent === null;
            
            // Check for any error messages
            const hasError = document.body.textContent.includes('Invalid') || 
                           document.body.textContent.includes('Error') ||
                           document.body.textContent.includes('Wrong') ||
                           document.body.textContent.includes('incorrect');
            
            // Check if we're back on the game page
            const hasGameElements = !!document.querySelector('img[alt="Guess the player"]') ||
                                  !!document.querySelector('button.choice-button') ||
                                  document.body.textContent.includes("WHO'S THIS ACTIVE NFL PLAYER");
            
            return authInputsGone && !hasError && hasGameElements;
          },
          { timeout: 10000 }
        );
        console.log('[HOST] ✓ Logged in successfully - auth modal closed and game visible');
      } catch (error) {
        console.error('[HOST] Login failed - checking page state...');
        
        // Get current page state for debugging
        const pageState = await hostPage.evaluate(() => {
          return {
            hasAuthModal: !!document.querySelector('[role="dialog"], .modal'),
            hasAuthInputs: !!document.querySelector('input[type="text"]'),
            hasGameElements: !!document.querySelector('img[alt="Guess the player"]'),
            visibleText: document.body.innerText.substring(0, 300)
          };
        });
        console.log('[HOST] Page state:', pageState);
        throw error;
      }
      await takeScreenshot(hostPage, '02-logged-in', 'host');
    }
    
    // Guest login
    console.log('\n[GUEST] Checking authentication status...');
    
    // Wait for auth loading to complete or for game elements to appear
    try {
      await guestPage.waitForFunction(
        () => {
          const hasLoading = document.body.textContent.includes('Loading...');
          const hasGameButtons = document.querySelector('button');
          const hasMultiplayerButton = Array.from(document.querySelectorAll('button'))
            .some(btn => btn.textContent.includes('Multiplayer Mode'));
          // Page is ready if no loading text OR if we have game buttons
          return !hasLoading || hasGameButtons || hasMultiplayerButton;
        },
        { timeout: 15000 }
      );
      console.log('[GUEST] Page loaded, checking auth state');
    } catch (e) {
      console.log('[GUEST] Warning: Loading check timed out, continuing anyway...');
      await takeScreenshot(guestPage, '02-loading-timeout', 'guest');
    }
    
    // Check if already logged in
    const isLoggedInGuest = await guestPage.evaluate(() => {
      return document.body.textContent.includes('fresh2') || 
             document.body.textContent.includes('Sign Out') ||
             document.body.textContent.includes('Logout');
    });
    
    if (isLoggedInGuest) {
      console.log('[GUEST] Already logged in as fresh2');
      await takeScreenshot(guestPage, '02-already-logged-in', 'guest');
    } else {
      console.log('[GUEST] Not logged in, clicking Multiplayer Mode to trigger auth modal...');
      
      // Click on Multiplayer Mode button to trigger auth modal
      const multiplayerClicked = await guestPage.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const mpBtn = buttons.find(btn => btn.textContent.includes('Multiplayer Mode'));
        if (mpBtn) {
          mpBtn.click();
          return true;
        }
        return false;
      });
      
      if (!multiplayerClicked) {
        throw new Error('[GUEST] Could not find Multiplayer Mode button');
      }
      
      console.log('[GUEST] Clicked Multiplayer Mode, waiting for auth modal...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Wait for auth form to be visible
      try {
        await guestPage.waitForSelector('input[type="email"], input[type="text"], input[placeholder*="email" i], input[placeholder*="username" i]', 
          { visible: true, timeout: 5000 });
        console.log('[GUEST] Auth form is visible');
      } catch (e) {
        console.log('[GUEST] Could not find auth form inputs, taking debug screenshot');
        await takeScreenshot(guestPage, '02-debug-no-auth-form', 'guest');
        throw new Error('[GUEST] Auth form did not appear after clicking Multiplayer Mode');
      }
      
      // Fill in credentials with delay between typing
      console.log('[GUEST] Filling in credentials...');
      
      // Find email/username input more specifically
      const emailInput = await guestPage.$('input[type="email"], input[type="text"]:not([type="password"]), input[placeholder*="email" i], input[placeholder*="username" i]');
      if (!emailInput) {
        throw new Error('[GUEST] Could not find email/username input');
      }
      
      // Find password input
      const passwordInput = await guestPage.$('input[type="password"]');
      if (!passwordInput) {
        throw new Error('[GUEST] Could not find password input');
      }
      
      // Type credentials
      await emailInput.click();
      await emailInput.type(GUEST_CREDENTIALS.username, { delay: 50 });
      
      await new Promise(resolve => setTimeout(resolve, 200));
      
      await passwordInput.click();
      await passwordInput.type(GUEST_CREDENTIALS.password, { delay: 50 });
      
      console.log('[GUEST] Credentials entered');
      await takeScreenshot(guestPage, '03-credentials-entered', 'guest');
      
      // Submit the form
      console.log('[GUEST] Looking for submit button...');
      const submitClicked = await guestPage.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const submitBtn = buttons.find(btn => 
          btn.type === 'submit' || 
          btn.textContent.toLowerCase().includes('sign in') ||
          btn.textContent.toLowerCase().includes('login') ||
          btn.textContent.toLowerCase().includes('continue')
        );
        if (submitBtn) {
          console.log('[Browser] Found submit button:', submitBtn.textContent);
          submitBtn.click();
          return true;
        }
        console.log('[Browser] Could not find submit button');
        return false;
      });
      
      if (!submitClicked) {
        console.log('[GUEST] Could not find submit button, trying form submit...');
        await guestPage.keyboard.press('Enter');
      }
      
      // Wait for auth modal to close
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check if logged in by verifying auth modal closed
      try {
        await guestPage.waitForFunction(
          () => {
            // Check if auth form inputs are gone (modal closed)
            const authInputsGone = !document.querySelector('input[type="text"]') || 
                                 document.querySelector('input[type="text"]').offsetParent === null;
            
            // Check for any error messages
            const hasError = document.body.textContent.includes('Invalid') || 
                           document.body.textContent.includes('Error') ||
                           document.body.textContent.includes('Wrong') ||
                           document.body.textContent.includes('incorrect');
            
            // Check if we're back on the game page
            const hasGameElements = !!document.querySelector('img[alt="Guess the player"]') ||
                                  !!document.querySelector('button.choice-button') ||
                                  document.body.textContent.includes("WHO'S THIS ACTIVE NFL PLAYER");
            
            return authInputsGone && !hasError && hasGameElements;
          },
          { timeout: 10000 }
        );
        console.log('[GUEST] ✓ Logged in successfully - auth modal closed and game visible');
      } catch (error) {
        console.error('[GUEST] Login failed - may need to create account or check credentials');
        throw error;
      }
      await takeScreenshot(guestPage, '02-logged-in', 'guest');
    }
    
    // === PHASE 2: Create and Join Room ===
    console.log('\n=== PHASE 2: Create and Join Room ===');
    
    // Host creates room
    console.log('[HOST] Switching to multiplayer mode...');
    const multiplayerClicked = await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const multiplayerBtn = buttons.find(btn => btn.textContent.includes('Multiplayer'));
      if (multiplayerBtn) {
        multiplayerBtn.click();
        return true;
      }
      return false;
    });
    
    if (!multiplayerClicked) {
      console.error('[HOST] Could not find Multiplayer button');
      await takeScreenshot(hostPage, 'error-no-multiplayer-button', 'host');
      throw new Error('Multiplayer button not found');
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    await takeScreenshot(hostPage, '03-multiplayer-mode', 'host');
    
    // Check if we need to sign in again for multiplayer
    const needsMultiplayerAuth = await hostPage.evaluate(() => {
      return document.body.textContent.includes('Sign in to play multiplayer');
    });
    
    if (needsMultiplayerAuth) {
      console.log('[HOST] Need to sign in again for multiplayer mode...');
      
      // Fill in credentials again
      const inputs = await hostPage.$$('input:not([type="hidden"])');
      if (inputs.length >= 2) {
        await inputs[0].click();
        await inputs[0].type('fresh', { delay: 100 });
        await new Promise(resolve => setTimeout(resolve, 500));
        await inputs[1].click();
        await inputs[1].type('test123', { delay: 100 });
      }
      
      // Click the multiplayer sign in button
      await new Promise(resolve => setTimeout(resolve, 500));
      await hostPage.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const signInBtn = buttons.find(btn => 
          btn.textContent.includes('Sign in to play multiplayer') ||
          btn.textContent.includes('Sign In')
        );
        if (signInBtn) signInBtn.click();
      });
      
      // Wait for auth to complete
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log('[HOST] Signed in for multiplayer');
      await takeScreenshot(hostPage, '03-multiplayer-signed-in', 'host');
    }
    
    // Wait for Processing... to finish
    console.log('[HOST] Waiting for multiplayer lobby to load...');
    await hostPage.waitForFunction(
      () => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return !buttons.some(btn => btn.textContent.includes('Processing')) &&
               buttons.some(btn => btn.textContent.includes('Host Game') || btn.textContent.includes('Create Room'));
      },
      { timeout: 15000 }
    );
    
    console.log('[HOST] Creating game room...');
    const hostGameClicked = await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const hostBtn = buttons.find(btn => btn.textContent.includes('Host Game') || btn.textContent.includes('Create Room'));
      if (hostBtn) {
        hostBtn.click();
        return true;
      }
      return false;
    });
    
    if (!hostGameClicked) {
      console.error('[HOST] Could not find Host Game button');
      
      // Log what buttons are available
      const availableButtons = await hostPage.evaluate(() => {
        return Array.from(document.querySelectorAll('button')).map(btn => btn.textContent.trim());
      });
      console.log('[HOST] Available buttons after multiplayer auth:', availableButtons);
      
      throw new Error('Host Game button not found');
    }
    
    // Wait for Ready button to appear
    try {
      await hostPage.waitForFunction(
        () => {
          const buttons = Array.from(document.querySelectorAll('button'));
          return buttons.some(btn => btn.textContent.includes('Ready'));
        },
        { timeout: 10000 }
      );
      console.log('[HOST] ✓ Room created, in waiting room');
    } catch (error) {
      console.error('[HOST] Failed to reach waiting room');
      await takeScreenshot(hostPage, 'error-waiting-room', 'host');
      throw error;
    }
    
    await takeScreenshot(hostPage, '03-waiting-room', 'host');
    
    // CRITICAL: Wait for player count to update in the database
    console.log('[HOST] Waiting for player count to update...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Guest joins room
    console.log('[GUEST] Switching to multiplayer mode...');
    await guestPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const multiplayerBtn = buttons.find(btn => btn.textContent.includes('Multiplayer'));
      if (multiplayerBtn) multiplayerBtn.click();
    });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if guest needs to sign in again for multiplayer
    const guestNeedsMultiplayerAuth = await guestPage.evaluate(() => {
      return document.body.textContent.includes('Sign in to play multiplayer');
    });
    
    if (guestNeedsMultiplayerAuth) {
      console.log('[GUEST] Need to sign in again for multiplayer mode...');
      
      // Fill in credentials again
      const inputs = await guestPage.$$('input:not([type="hidden"])');
      if (inputs.length >= 2) {
        await inputs[0].click();
        await inputs[0].type('fresh2', { delay: 100 });
        await new Promise(resolve => setTimeout(resolve, 500));
        await inputs[1].click();
        await inputs[1].type('test123', { delay: 100 });
      }
      
      // Click the multiplayer sign in button
      await new Promise(resolve => setTimeout(resolve, 500));
      await guestPage.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const signInBtn = buttons.find(btn => 
          btn.textContent.includes('Sign in to play multiplayer') ||
          btn.textContent.includes('Sign In')
        );
        if (signInBtn) signInBtn.click();
      });
      
      // Wait for auth to complete
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log('[GUEST] Signed in for multiplayer');
    }
    
    // Wait for room list to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('[GUEST] Looking for room to join...');
    
    // First refresh the room list
    const refreshed = await guestPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const refreshBtn = buttons.find(btn => btn.textContent.includes('Refresh List'));
      if (refreshBtn) {
        console.log('[Browser] Clicking Refresh List button');
        refreshBtn.click();
        return true;
      }
      return false;
    });
    
    if (refreshed) {
      console.log('[GUEST] Refreshed room list');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Step 1: Find and click on the room to select it
    console.log('[GUEST] Step 1: Looking for room to click and select...');
    
    const roomClicked = await guestPage.evaluate(() => {
      // Find the room element containing "fresh's Game"
      const roomElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent;
        const hasRoomInfo = text.includes("fresh's Game") && 
                          text.includes("Host: fresh") && 
                          text.includes("Players:");
        
        // Make sure it's a clickable element, not just a container
        const isClickable = el.tagName === 'DIV' || el.tagName === 'ARTICLE' || 
                          el.tagName === 'SECTION' || el.tagName === 'LI' || 
                          el.tagName === 'BUTTON' || el.tagName === 'A';
        
        // Check if it's not a large container
        const isReasonableSize = !Array.from(el.children).some(child => 
          child.textContent.includes("fresh's Game") && 
          child.textContent.includes("Host: fresh")
        );
        
        return hasRoomInfo && isClickable && isReasonableSize;
      });
      
      console.log('[Browser] Found', roomElements.length, 'potential room elements');
      
      if (roomElements.length > 0) {
        const roomEl = roomElements[0];
        console.log('[Browser] Clicking room element to select it:', roomEl.tagName, roomEl.className);
        roomEl.click();
        return true;
      }
      
      console.log('[Browser] Could not find room element to click');
      return false;
    });
    
    if (!roomClicked) {
      console.error('[GUEST] Could not find room to select');
      await takeScreenshot(guestPage, 'error-no-room-to-click', 'guest');
      throw new Error('Could not find room element to click');
    }
    
    // Wait for room details to appear
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('[GUEST] Room clicked, waiting for details panel...');
    
    // Step 2: Now look for the "Join This Room" button
    console.log('[GUEST] Step 2: Looking for Join This Room button...');
    
    const joined = await guestPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const joinBtn = buttons.find(btn => 
        btn.textContent.includes('Join This Room') || 
        btn.textContent.includes('Join Room') ||
        btn.textContent.includes('Join')
      );
      
      if (joinBtn) {
        console.log('[Browser] Found Join button:', joinBtn.textContent);
        // Check if button is disabled
        if (joinBtn.disabled) {
          console.log('[Browser] Join button is disabled');
          return false;
        }
        joinBtn.click();
        return true;
      }
      
      console.log('[Browser] No Join button found. Available buttons:', buttons.map(b => b.textContent.trim()));
      return false;
    });
    
    if (!joined) {
      console.error('[GUEST] Could not join room');
      await takeScreenshot(guestPage, 'error-no-join-method', 'guest');
      
      // Get more debug info
      const pageInfo = await guestPage.evaluate(() => {
        return {
          url: window.location.href,
          bodyText: document.body.innerText.substring(0, 1000),
          hasRoomVisible: document.body.textContent.includes("fresh's Game")
        };
      });
      console.log('[GUEST] Page info:', pageInfo);
      
      throw new Error('Could not find way to join room');
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Wait for Ready button to appear for guest
    try {
      await guestPage.waitForFunction(
        () => {
          const buttons = Array.from(document.querySelectorAll('button'));
          return buttons.some(btn => btn.textContent.includes('Ready'));
        },
        { timeout: 10000 }
      );
      console.log('[GUEST] ✓ Joined room successfully');
    } catch (error) {
      console.error('[GUEST] Failed to join room - timeout waiting for Ready button');
      
      // Get current page state for debugging
      const pageState = await guestPage.evaluate(() => {
        return {
          buttons: Array.from(document.querySelectorAll('button')).map(btn => btn.textContent.trim()),
          hasError: document.body.textContent.includes('Error') || document.body.textContent.includes('Failed'),
          visibleText: document.body.innerText.substring(0, 500)
        };
      });
      console.log('[GUEST] Page state after join attempt:', JSON.stringify(pageState, null, 2));
      await takeScreenshot(guestPage, 'error-join-failed', 'guest');
      throw error;
    }
    
    await takeScreenshot(guestPage, '03-waiting-room', 'guest');
    
    // Verify both players see 2 players in the room
    console.log('\n[VERIFICATION] Checking player counts...');
    
    const hostPlayerCount = await hostPage.evaluate(() => {
      const bodyText = document.body.innerText;
      return bodyText.match(/Players \((\d+)\/\d+\)/)?.[1] || '0';
    });
    
    const guestPlayerCount = await guestPage.evaluate(() => {
      const bodyText = document.body.innerText;
      return bodyText.match(/Players \((\d+)\/\d+\)/)?.[1] || '0';
    });
    
    console.log(`[HOST] Sees ${hostPlayerCount} player(s)`);
    console.log(`[GUEST] Sees ${guestPlayerCount} player(s)`);
    
    if (hostPlayerCount !== '2' || guestPlayerCount !== '2') {
      console.log('[SYNC] Player counts not synced, waiting...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Recheck
      const hostRecheck = await hostPage.evaluate(() => {
        const bodyText = document.body.innerText;
        return bodyText.match(/Players \((\d+)\/\d+\)/)?.[1] || '0';
      });
      const guestRecheck = await guestPage.evaluate(() => {
        const bodyText = document.body.innerText;
        return bodyText.match(/Players \((\d+)\/\d+\)/)?.[1] || '0';
      });
      
      console.log(`[HOST] After wait sees ${hostRecheck} player(s)`);
      console.log(`[GUEST] After wait sees ${guestRecheck} player(s)`);
      
      if (hostRecheck !== '2') {
        // Try refreshing the room list for the host
        console.log('[HOST] Attempting to refresh room state...');
        const refreshed = await hostPage.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const refreshBtn = buttons.find(btn => btn.textContent?.includes('Refresh Players'));
          if (refreshBtn) {
            console.log('[Client] Found Refresh Players button, clicking it');
            refreshBtn.click();
            return true;
          }
          console.log('[Client] No Refresh Players button found');
          return false;
        });
        
        if (refreshed) {
          await new Promise(resolve => setTimeout(resolve, 2000));
          const afterRefresh = await hostPage.evaluate(() => {
            const bodyText = document.body.innerText;
            return bodyText.match(/Players \((\d+)\/\d+\)/)?.[1] || '0';
          });
          console.log(`[HOST] After refresh sees ${afterRefresh} player(s)`);
          
          if (afterRefresh !== '2') {
            throw new Error(`Host still only sees ${afterRefresh} player(s) after refresh, expected 2`);
          }
        } else {
          throw new Error(`Host only sees ${hostRecheck} player(s), expected 2, and no refresh button available`);
        }
      }
    }
    
    // Both players mark ready
    console.log('\n[HOST] Marking ready...');
    await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const readyBtn = buttons.find(btn => btn.textContent.includes('Ready'));
      if (readyBtn) readyBtn.click();
    });
    await new Promise(resolve => setTimeout(resolve, 500));
    
    console.log('[GUEST] Marking ready...');
    await guestPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const readyBtn = buttons.find(btn => btn.textContent.includes('Ready'));
      if (readyBtn) readyBtn.click();
    });
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await takeScreenshot(hostPage, '04-both-ready', 'host');
    await takeScreenshot(guestPage, '04-both-ready', 'guest');
    
    // Debug: Check room state
    const roomState = await hostPage.evaluate(() => {
      const roomElements = document.querySelectorAll('.text-sm, .text-base, p');
      const playerElements = document.querySelectorAll('[class*="player"], [class*="Player"]');
      return {
        textContent: Array.from(roomElements).map(el => el.textContent?.trim()).filter(Boolean),
        playerCount: playerElements.length,
        buttons: Array.from(document.querySelectorAll('button')).map(btn => btn.textContent?.trim())
      };
    });
    console.log('[HOST] Room state:', JSON.stringify(roomState, null, 2));
    
    // Wait a bit to ensure both players are registered
    console.log('[HOST] Waiting for room state to stabilize...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Wait for Start Game button to appear (indicates 2+ players ready)
    console.log('[HOST] Waiting for Start Game button to appear...');
    try {
      await hostPage.waitForFunction(
        () => {
          const buttons = Array.from(document.querySelectorAll('button'));
          return buttons.some(btn => btn.textContent?.includes('Start Game'));
        },
        { timeout: 10000 }
      );
      console.log('[HOST] ✓ Start Game button is now available');
    } catch (error) {
      console.error('[HOST] Start Game button never appeared');
      
      // Debug: Check current state
      const debugInfo = await hostPage.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button')).map(btn => btn.textContent?.trim());
        const playerDivs = document.querySelectorAll('[class*="flex"][class*="items-center"][class*="gap"]');
        const players = Array.from(playerDivs).map(div => {
          const text = div.textContent?.trim();
          return text && text.length < 100 ? text : null;
        }).filter(Boolean);
        
        return {
          buttons,
          players,
          bodyText: document.body.innerText.substring(0, 500)
        };
      });
      console.log('[HOST] Debug info:', JSON.stringify(debugInfo, null, 2));
      throw new Error('Start Game button never became available');
    }
    
    // === PHASE 3: Start Game and Verify Both Players Transition ===
    console.log('\n=== PHASE 3: Start Game and Verify Transition ===');
    
    // Host starts game
    console.log('[HOST] Starting game...');
    const startGameClicked = await hostPage.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const startBtn = buttons.find(btn => btn.textContent.includes('Start Game'));
      if (startBtn) {
        console.log('[Client] Found Start Game button, clicking it');
        startBtn.click();
        return true;
      }
      console.log('[Client] Start Game button not found');
      return false;
    });
    
    if (!startGameClicked) {
      console.error('[HOST] Could not find Start Game button');
      const availableButtons = await hostPage.evaluate(() => {
        return Array.from(document.querySelectorAll('button')).map(btn => btn.textContent.trim());
      });
      console.log('[HOST] Available buttons:', availableButtons);
      throw new Error('Start Game button not found');
    }
    
    // Wait a moment for the Edge Function to process
    console.log('[HOST] Waiting for Edge Function to process...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check for any error messages
    const errorMessage = await hostPage.evaluate(() => {
      const errorElements = document.querySelectorAll('[role="alert"], .text-red-500, .text-red-600, .error');
      for (const el of errorElements) {
        if (el.textContent && el.textContent.trim()) {
          return el.textContent.trim();
        }
      }
      return null;
    });
    
    if (errorMessage) {
      console.error('[HOST] Error message found:', errorMessage);
    }
    
    // CRITICAL: Verify both players transition to active game
    console.log('\n[VERIFICATION] Checking game state transition...');
    
    // Wait a bit for realtime updates to propagate
    console.log('[VERIFICATION] Waiting for realtime updates to propagate...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Host should see game UI
    try {
      // Check current state first
      const preCheckState = await hostPage.evaluate(() => {
        return {
          hasGameImage: !!document.querySelector('img[alt="Guess the player"]'),
          hasChoiceButtons: !!document.querySelector('button.choice-button, button[class*="choice"]'),
          bodyText: document.body.textContent.substring(0, 200)
        };
      });
      console.log('[HOST] Pre-check state:', preCheckState);
      
      await waitForSelectorWithLogging(
        hostPage, 
        'img[alt="Guess the player"]', 
        'HOST', 
        'game player image',
        30000 // Increased timeout to 30 seconds
      );
      console.log('[HOST] ✓ Successfully transitioned to active game!');
      await takeScreenshot(hostPage, '05-game-active', 'host');
    } catch (error) {
      console.error('[HOST] ✗ Failed to transition to active game');
      
      // Take error screenshot and check final state
      await takeScreenshot(hostPage, 'HOST-error-final-debug', 'host');
      const finalState = await hostPage.evaluate(() => {
        return {
          hasGameImage: !!document.querySelector('img[alt="Guess the player"]'),
          hasPlaceholderImage: !!document.querySelector('img[src*="placeholder"], img[src*="placehold"]'),
          allImages: Array.from(document.querySelectorAll('img')).map(img => ({
            src: img.src,
            alt: img.alt
          })),
          errorMessages: Array.from(document.querySelectorAll('*')).filter(el => 
            el.textContent.includes('Error') || el.textContent.includes('error')
          ).map(el => el.textContent).slice(0, 3)
        };
      });
      console.log('[HOST] Final debug state:', JSON.stringify(finalState, null, 2));
      
      throw new Error('Host failed to transition to active game');
    }
    
    // CRITICAL: Guest should also see game UI (not stuck on waiting screen)
    try {
      await waitForSelectorWithLogging(
        guestPage, 
        'img[alt="Guess the player"]', 
        'GUEST', 
        'game player image',
        30000 // Increased timeout to 30 seconds
      );
      console.log('[GUEST] ✓ Successfully transitioned to active game!');
      await takeScreenshot(guestPage, '05-game-active', 'guest');
    } catch (error) {
      console.error('[GUEST] ✗ Failed to transition to active game - STUCK ON WAITING SCREEN');
      
      // Log current page state for debugging
      const pageContent = await guestPage.content();
      if (pageContent.includes('Waiting for game to start')) {
        console.error('[GUEST] Page still shows "Waiting for game to start..."');
      } else if (pageContent.includes('Waiting for game data')) {
        console.error('[GUEST] Page shows "Waiting for game data..."');
      }
      
      throw new Error('Guest failed to transition to active game - multiplayer sync issue');
    }
    
    // === PHASE 4: Test Round Advancement ===
    console.log('\n=== PHASE 4: Test Round Advancement ===');
    
    // Get current round number
    const getRoundNumber = async (page) => {
      return await page.evaluate(() => {
        // Look for round number in various possible locations
        const selectors = [
          'h3:contains("Round")',
          '[class*="round"]:contains("Round")',
          'div:contains("Round"):not(:has(*))'
        ];
        
        for (const selector of selectors) {
          const elements = Array.from(document.querySelectorAll('*')).filter(el => 
            el.textContent.includes('Round') && 
            el.textContent.match(/Round\s+\d+/)
          );
          
          if (elements.length > 0) {
            const match = elements[0].textContent.match(/Round\s+(\d+)/);
            return match ? parseInt(match[1]) : null;
          }
        }
        
        return null;
      });
    };
    
    const initialRound = await getRoundNumber(hostPage);
    console.log(`[TEST] Starting at Round ${initialRound}`);
    
    // Test Case 1: Both players answer quickly (should advance in 2 + 3 = 5 seconds)
    console.log('\n[TEST CASE 1] Both players answer quickly...');
    const startTime = Date.now();
    
    // Both players select first answer choice
    // Wait for answer buttons to be clickable
    await hostPage.waitForSelector('button[class*="choice"], button[data-choice], .choice-button', { visible: true });
    await guestPage.waitForSelector('button[class*="choice"], button[data-choice], .choice-button', { visible: true });
    
    // Click first available answer button
    await hostPage.evaluate(() => {
      const buttons = document.querySelectorAll('button[class*="choice"], button[data-choice], .choice-button');
      if (buttons.length > 0) buttons[0].click();
    });
    console.log('[HOST] Selected answer');
    
    await guestPage.evaluate(() => {
      const buttons = document.querySelectorAll('button[class*="choice"], button[data-choice], .choice-button');
      if (buttons.length > 0) buttons[0].click();
    });
    console.log('[GUEST] Selected answer');
    
    // Wait for round advancement
    console.log('[TEST] Waiting for round advancement (should be ~5 seconds)...');
    
    // Poll for round change
    let roundAdvanced = false;
    let advanceTime = 0;
    const pollInterval = 500; // Check every 500ms
    
    for (let i = 0; i < ROUND_ADVANCE_TIMEOUT_MS / pollInterval; i++) {
      await new Promise(resolve => setTimeout(resolve, pollInterval));
      const currentRound = await getRoundNumber(hostPage);
      
      if (currentRound && currentRound > initialRound) {
        advanceTime = Date.now() - startTime;
        roundAdvanced = true;
        console.log(`[TEST] ✓ Round advanced! Time: ${advanceTime}ms (${(advanceTime/1000).toFixed(1)}s)`);
        break;
      }
    }
    
    if (!roundAdvanced) {
      console.error('[TEST] ✗ Round did not advance within timeout');
      throw new Error('Round advancement failed');
    }
    
    // Verify timing is correct (should be around 5 seconds)
    if (advanceTime < 4000 || advanceTime > 6000) {
      console.warn(`[TEST] ⚠️ Round advancement timing off: ${(advanceTime/1000).toFixed(1)}s (expected ~5s)`);
    } else {
      console.log('[TEST] ✓ Round advancement timing correct');
    }
    
    await takeScreenshot(hostPage, '06-round-advanced', 'host');
    await takeScreenshot(guestPage, '06-round-advanced', 'guest');
    
    // Test Case 2: One player doesn't answer (should advance at 7 seconds)
    console.log('\n[TEST CASE 2] Only one player answers...');
    const round2StartTime = Date.now();
    const round2Initial = await getRoundNumber(hostPage);
    
    // Only host answers
    await hostPage.waitForSelector('button[class*="choice"], button[data-choice], .choice-button', { visible: true });
    await hostPage.evaluate(() => {
      const buttons = document.querySelectorAll('button[class*="choice"], button[data-choice], .choice-button');
      if (buttons.length > 0) buttons[0].click();
    });
    console.log('[HOST] Selected answer');
    console.log('[GUEST] Not answering (testing 7-second timeout)');
    
    // Wait for round advancement (should be at 7 seconds)
    let round2Advanced = false;
    let advance2Time = 0;
    
    for (let i = 0; i < ROUND_ADVANCE_TIMEOUT_MS / pollInterval; i++) {
      await new Promise(resolve => setTimeout(resolve, pollInterval));
      const currentRound = await getRoundNumber(hostPage);
      
      if (currentRound && currentRound > round2Initial) {
        advance2Time = Date.now() - round2StartTime;
        round2Advanced = true;
        console.log(`[TEST] ✓ Round advanced! Time: ${advance2Time}ms (${(advance2Time/1000).toFixed(1)}s)`);
        break;
      }
    }
    
    if (!round2Advanced) {
      console.error('[TEST] ✗ Round did not advance within timeout');
      throw new Error('Round advancement failed for timeout case');
    }
    
    // Verify timing is correct (should be around 7 seconds)
    if (advance2Time < 6500 || advance2Time > 7500) {
      console.warn(`[TEST] ⚠️ Round advancement timing off: ${(advance2Time/1000).toFixed(1)}s (expected ~7s)`);
    } else {
      console.log('[TEST] ✓ Round advancement timing correct for timeout case');
    }
    
    await takeScreenshot(hostPage, '07-timeout-advanced', 'host');
    await takeScreenshot(guestPage, '07-timeout-advanced', 'guest');
    
    console.log('\n=== TEST COMPLETED SUCCESSFULLY ===');
    console.log('✓ Both players transitioned to active game');
    console.log('✓ Round advancement works with all players answering');
    console.log('✓ Round advancement works with timeout');
    testPassed = true;
    
  } catch (error) {
    console.error('\n=== TEST FAILED ===');
    console.error(error);
    
    // Take error screenshots
    await takeScreenshot(hostPage, 'error-final-state', 'host').catch(() => {});
    await takeScreenshot(guestPage, 'error-final-state', 'guest').catch(() => {});
    
  } finally {
    // Keep browsers open for 5 seconds to observe final state
    console.log('\nKeeping browsers open for observation...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Close browsers
    await browserHost.close();
    await browserGuest.close();
    
    // Report result
    if (testPassed) {
      console.log('\n✅ MULTIPLAYER GAME FLOW TEST PASSED');
      process.exit(0);
    } else {
      console.log('\n❌ MULTIPLAYER GAME FLOW TEST FAILED');
      process.exit(1);
    }
  }
}

// Run the test
testMultiplayerGameFlow().catch(error => {
  console.error('Test execution error:', error);
  process.exit(1);
});