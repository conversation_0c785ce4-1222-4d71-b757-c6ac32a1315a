export type GameModeType = 'normal' | 'timed';

export interface PlayerData {
  id: number; // Or string if your IDs are different
  team_name: string;
  player_name: string;
  local_image_path: string | null; // Path relative to /public
  jersey_number: string | number | null; // Can be string like '-' or number
  position: string | null;
  height: string | null;
  weight: string | number | null;
  age_or_dob: string | number | null; // Keeping flexible based on scraped data
  experience: string | number | null; // Could be 'R' or number
  college: string | null;
  // Add any other fields from your JSON if needed
}

export interface PlayerChoice {
  name: string;
  isCorrect: boolean;
}

export interface PlayerQuestion {
  correctPlayer: PlayerData;
  choices: PlayerChoice[];
  imageUrl: string; // Derived absolute path for image component
}

export interface RecentAnswer {
  player: PlayerData;
  isCorrect: boolean;
  timestamp: number;
  // chosenAnswer?: string; // Optional field for ChoiceButton styling
} 