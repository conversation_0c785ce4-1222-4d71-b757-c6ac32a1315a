# Deploy Edge Function using Supabase Management API
# This bypasses CLI and Docker requirements entirely

Write-Host "Deploying via Supabase Management API..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"
$functionName = "submit-answer-handler"

# First, get your access token
Write-Host "`nSTEP 1: Getting access token" -ForegroundColor Yellow
Write-Host "Please go to: https://supabase.com/dashboard/account/tokens" -ForegroundColor Cyan
Write-Host "Create a new access token and paste it below:" -ForegroundColor Yellow
$accessToken = Read-Host -Prompt "Access Token" -AsSecureString
$accessTokenPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($accessToken))

if (-not $accessTokenPlain) {
    Write-Host "No token provided. Exiting." -ForegroundColor Red
    exit 1
}

Write-Host "`nSTEP 2: Reading function code" -ForegroundColor Yellow
$functionPath = "functions\$functionName\index.ts"
if (-not (Test-Path $functionPath)) {
    Write-Host "Function file not found at: $functionPath" -ForegroundColor Red
    exit 1
}

$functionCode = Get-Content $functionPath -Raw

Write-Host "`nSTEP 3: Deploying function" -ForegroundColor Yellow

# Create the API request
$headers = @{
    "Authorization" = "Bearer $accessTokenPlain"
    "Content-Type" = "application/json"
}

$body = @{
    name = $functionName
    slug = $functionName
    verify_jwt = $false
    import_map = @{
        imports = @{
            "https://deno.land/x/cors@v1.2.2/mod.ts" = "https://deno.land/x/cors@v1.2.2/mod.ts"
        }
    }
    entrypoint_path = "index.ts"
    import_map_path = $null
    body = $functionCode
} | ConvertTo-Json -Depth 10

$uri = "https://api.supabase.com/v1/projects/$projectRef/functions/$functionName"

try {
    Write-Host "Sending deployment request..." -ForegroundColor Cyan
    $response = Invoke-RestMethod -Uri $uri -Method Put -Headers $headers -Body $body
    
    Write-Host "`nFunction deployed successfully!" -ForegroundColor Green
    Write-Host "Function URL: https://$projectRef.supabase.co/functions/v1/$functionName" -ForegroundColor Blue
    
} catch {
    Write-Host "`nDeployment failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Yellow
    }
}

# Clear the token from memory
$accessTokenPlain = $null