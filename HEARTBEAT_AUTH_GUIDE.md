# Heartbeat Authentication Guide

## Overview
This guide explains how to properly handle authentication for the heartbeat system to avoid 401 Unauthorized errors.

## Common Causes of 401 Errors

1. **No Active Session**: User is not authenticated or session has expired
2. **Race Conditions**: Heartbeat sent before authentication completes
3. **Token Expiry**: Session token expires during gameplay
4. **Missing Headers**: Authorization header not included in request

## Solution Implementation

### 1. Use the Enhanced Heartbeat Hook

```typescript
import { useHeartbeat } from '@/hooks/useHeartbeat';

function GameRoom({ roomId }) {
  const { user } = useAuth();
  
  const { sendHeartbeat, stopHeartbeat } = useHeartbeat({
    roomId,
    interval: 15000, // 15 seconds
    onSessionExpired: () => {
      // Handle session expiry
      router.push('/login');
    },
    onError: (error) => {
      console.error('Heartbeat error:', error);
    }
  });

  // Hook automatically manages heartbeat lifecycle
}
```

### 2. Key Features of the Solution

#### Authentication State Checking
- Verifies user is authenticated before sending heartbeat
- Waits for auth loading to complete
- Validates session exists and is valid

#### Token Refresh
- Automatically refreshes tokens approaching expiry (5-minute buffer)
- Prevents 401 errors from expired tokens during gameplay

#### Error Handling
- Specific handling for 401 errors
- Stops heartbeat on authentication failures
- Implements retry logic for network errors (max 3 retries)

#### Race Condition Prevention
- Initial 1-second delay before first heartbeat
- Ensures authentication state is ready
- Skips heartbeat when conditions aren't met

### 3. Testing Authentication Scenarios

Run the comprehensive test suite:

```bash
cd web-app
npm test -- heartbeat
```

Key test scenarios covered:
- No authentication
- Loading authentication state
- Valid session
- Expired tokens
- Token refresh
- 401 error handling
- Network errors and retries
- Component lifecycle

### 4. Edge Function Requirements

The heartbeat-handler edge function expects:
- `Authorization: Bearer <token>` header
- Valid Supabase session token
- User must be authenticated via `supabase.auth.getUser()`

### 5. Migration Guide

If you're experiencing 401 errors, update your code:

**Before:**
```typescript
// Direct heartbeat call without auth checks
setInterval(async () => {
  await supabase.functions.invoke('heartbeat-handler', {
    body: { roomId, action: 'ping' }
  });
}, 15000);
```

**After:**
```typescript
// Use the hook with proper auth handling
const { sendHeartbeat } = useHeartbeat({
  roomId,
  onSessionExpired: handleSessionExpiry
});
```

## Debugging 401 Errors

1. Check browser console for `[HEARTBEAT]` logs
2. Verify authentication state in Redux DevTools
3. Check Network tab for Authorization headers
4. Confirm session validity in Application > Storage > Local Storage

## Best Practices

1. Always use the `useHeartbeat` hook instead of direct calls
2. Handle `onSessionExpired` callback appropriately
3. Monitor retry counts in production
4. Implement proper error boundaries
5. Test with expired sessions during development