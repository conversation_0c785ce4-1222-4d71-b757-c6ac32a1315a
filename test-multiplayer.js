const puppeteer = require('puppeteer');

const BASE_URL = 'http://localhost:3001';
const WAIT_TIMEOUT = 30000;

// Test user credentials
const USER1 = {
  email: '<EMAIL>',
  password: 'fresh123',
  username: 'fresh'
};

const USER2 = {
  email: '<EMAIL>', 
  password: 'fresh2123',
  username: 'fresh2'
};

// Helper function to wait and log
async function waitAndLog(ms, message) {
  console.log(`⏳ ${message} (${ms}ms)...`);
  await new Promise(resolve => setTimeout(resolve, ms));
}

// Helper to take screenshots
async function takeScreenshot(page, name, playerName) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  await page.screenshot({ 
    path: `./screenshots/${playerName}-${name}-${timestamp}.png`,
    fullPage: true 
  });
  console.log(`📸 Screenshot saved: ${playerName}-${name}`);
}

// Login helper
async function loginUser(page, user) {
  console.log(`\n🔐 Logging in as ${user.username}...`);
  
  // Click login button
  await page.waitForSelector('button:has-text("Login")', { timeout: WAIT_TIMEOUT });
  await page.click('button:has-text("Login")');
  
  // Fill login form
  await page.waitForSelector('input[type="email"]', { timeout: WAIT_TIMEOUT });
  await page.type('input[type="email"]', user.email);
  await page.type('input[type="password"]', user.password);
  
  // Submit
  await page.click('button[type="submit"]');
  
  // Wait for login to complete
  await page.waitForFunction(
    () => !document.querySelector('[role="dialog"]'),
    { timeout: WAIT_TIMEOUT }
  );
  
  console.log(`✅ Logged in as ${user.username}`);
}

// Main test function
async function testMultiplayerFlow() {
  console.log('🚀 Starting multiplayer game flow test...\n');
  
  // Create screenshots directory
  const fs = require('fs');
  if (!fs.existsSync('./screenshots')) {
    fs.mkdirSync('./screenshots');
  }
  
  // Launch browsers
  const browser1 = await puppeteer.launch({
    headless: false,
    args: ['--window-size=1200,900', '--window-position=0,0']
  });
  
  const browser2 = await puppeteer.launch({
    headless: false,
    args: ['--window-size=1200,900', '--window-position=1210,0']
  });
  
  const page1 = await browser1.newPage();
  const page2 = await browser2.newPage();
  
  // Set viewport
  await page1.setViewport({ width: 1200, height: 900 });
  await page2.setViewport({ width: 1200, height: 900 });
  
  // Enable console logging
  page1.on('console', msg => console.log(`[Player1 Console] ${msg.text()}`));
  page2.on('console', msg => console.log(`[Player2 Console] ${msg.text()}`));
  
  try {
    // Navigate to app
    console.log('📍 Navigating to app...');
    await Promise.all([
      page1.goto(BASE_URL, { waitUntil: 'networkidle2' }),
      page2.goto(BASE_URL, { waitUntil: 'networkidle2' })
    ]);
    
    // Login both users
    await loginUser(page1, USER1);
    await loginUser(page2, USER2);
    
    await waitAndLog(2000, 'Waiting after login');
    
    // Player 1 (Host) creates room
    console.log('\n🏠 Player 1 creating room...');
    await page1.waitForSelector('button:has-text("Multiplayer")', { timeout: WAIT_TIMEOUT });
    await page1.click('button:has-text("Multiplayer")');
    
    await page1.waitForSelector('button:has-text("Create Room")', { timeout: WAIT_TIMEOUT });
    await page1.click('button:has-text("Create Room")');
    
    // Wait for room code
    await page1.waitForSelector('[class*="text-4xl"][class*="font-bold"]', { timeout: WAIT_TIMEOUT });
    const roomCode = await page1.$eval('[class*="text-4xl"][class*="font-bold"]', el => el.textContent);
    console.log(`✅ Room created with code: ${roomCode}`);
    
    await takeScreenshot(page1, 'room-created', 'player1');
    
    // Player 2 joins room
    console.log(`\n👥 Player 2 joining room ${roomCode}...`);
    await page2.waitForSelector('button:has-text("Multiplayer")', { timeout: WAIT_TIMEOUT });
    await page2.click('button:has-text("Multiplayer")');
    
    await page2.waitForSelector('input[placeholder*="room code"]', { timeout: WAIT_TIMEOUT });
    await page2.type('input[placeholder*="room code"]', roomCode);
    
    await page2.waitForSelector('button:has-text("Join Room")', { timeout: WAIT_TIMEOUT });
    await page2.click('button:has-text("Join Room")');
    
    // Wait for both players to see each other
    await waitAndLog(3000, 'Waiting for players to connect');
    
    // Check if both players are in the room
    const player1Count = await page1.$eval('text=/Players \\(\\d+\\/\\d+\\)/', el => el.textContent);
    const player2Count = await page2.$eval('text=/Players \\(\\d+\\/\\d+\\)/', el => el.textContent);
    
    console.log(`\n📊 Player counts:`);
    console.log(`   Player 1 sees: ${player1Count}`);
    console.log(`   Player 2 sees: ${player2Count}`);
    
    await takeScreenshot(page1, 'both-in-room', 'player1');
    await takeScreenshot(page2, 'both-in-room', 'player2');
    
    // Host starts game
    console.log('\n🎮 Host starting game...');
    
    // Wait for start button to be enabled
    await page1.waitForSelector('button:has-text("Start Game"):not([disabled])', { timeout: WAIT_TIMEOUT });
    await page1.click('button:has-text("Start Game")');
    
    console.log('⏳ Waiting for game to start...');
    
    // Wait for game to start for both players
    await Promise.all([
      page1.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT }),
      page2.waitForSelector('img[alt*="player"]', { timeout: WAIT_TIMEOUT })
    ]).catch(async (err) => {
      console.error('❌ Failed to start game for both players:', err.message);
      await takeScreenshot(page1, 'game-start-failed', 'player1');
      await takeScreenshot(page2, 'game-start-failed', 'player2');
      throw err;
    });
    
    console.log('✅ Game started for both players!');
    
    await takeScreenshot(page1, 'game-started', 'player1');
    await takeScreenshot(page2, 'game-started', 'player2');
    
    // Test multiple rounds
    console.log('\n🔄 Testing game rounds...');
    
    for (let round = 1; round <= 10; round++) {
      console.log(`\n📍 Round ${round}:`);
      
      // Check if both players see the same question
      const player1HasImage = await page1.$('img[alt*="player"]') !== null;
      const player2HasImage = await page2.$('img[alt*="player"]') !== null;
      
      console.log(`   Player 1 sees image: ${player1HasImage}`);
      console.log(`   Player 2 sees image: ${player2HasImage}`);
      
      if (!player1HasImage || !player2HasImage) {
        console.error('❌ One or both players cannot see the game!');
        await takeScreenshot(page1, `round-${round}-error`, 'player1');
        await takeScreenshot(page2, `round-${round}-error`, 'player2');
        break;
      }
      
      // Both players answer
      const answerButtons1 = await page1.$$('button[class*="choice-button"]');
      const answerButtons2 = await page2.$$('button[class*="choice-button"]');
      
      if (answerButtons1.length > 0 && answerButtons2.length > 0) {
        // Player 1 answers immediately
        await answerButtons1[0].click();
        console.log('   Player 1 answered');
        
        // Player 2 answers after 1 second
        await waitAndLog(1000, 'Player 2 thinking');
        await answerButtons2[1].click();
        console.log('   Player 2 answered');
        
        // Wait for round transition (should be 2s + 3s = 5s based on rules)
        console.log('   Waiting for round advance...');
        const startTime = Date.now();
        
        // Wait for next round
        await page1.waitForFunction(
          (currentRound) => {
            const roundText = document.querySelector('text=/Round \\d+/');
            return roundText && !roundText.textContent.includes(`Round ${currentRound}`);
          },
          { timeout: 10000 },
          round
        ).catch(() => console.log('   Round advance timeout'));
        
        const transitionTime = Date.now() - startTime;
        console.log(`   Round advanced in ${transitionTime}ms`);
      } else {
        console.error('   ❌ No answer buttons found!');
        break;
      }
      
      await waitAndLog(1000, 'Preparing for next round');
    }
    
    console.log('\n✅ Test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    await takeScreenshot(page1, 'error', 'player1');
    await takeScreenshot(page2, 'error', 'player2');
  } finally {
    console.log('\n🧹 Cleaning up...');
    await waitAndLog(5000, 'Final wait before closing');
    await browser1.close();
    await browser2.close();
  }
}

// Run the test
testMultiplayerFlow().catch(console.error);