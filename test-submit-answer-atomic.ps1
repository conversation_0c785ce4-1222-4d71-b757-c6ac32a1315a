# Test Submit Answer Atomic Operation
# Tests the race condition fix in submit-answer-handler

$baseUrl = "https://xmyxuvuimebjltnaamox.supabase.co"
$anonKey = $env:SUPABASE_ANON_KEY
$authToken = $env:SUPABASE_AUTH_TOKEN

Write-Host "[TEST] Submit Answer Atomic Operation Test" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

if (-not $anonKey -or -not $authToken) {
    Write-Host "[ERROR] Missing environment variables" -ForegroundColor Red
    exit 1
}

# Get user ID from token
$tokenParts = $authToken.Split('.')
$payload = $tokenParts[1]
while ($payload.Length % 4 -ne 0) { $payload += "=" }
$tokenData = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($payload)) | ConvertFrom-Json
$userId = $tokenData.sub

Write-Host "[INFO] User ID: $userId" -ForegroundColor Green

$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $authToken"
    "apikey" = $anonKey
    "Prefer" = "return=representation"
}

Write-Host "[SCENARIO] Test Scenario:" -ForegroundColor Yellow
Write-Host "1. Create test room and set to active with a question" -ForegroundColor White
Write-Host "2. Add player to room" -ForegroundColor White
Write-Host "3. Submit answer that should ATOMICALLY advance to next question" -ForegroundColor White
Write-Host "4. Verify the atomic operation worked" -ForegroundColor White
Write-Host ""

# Step 1: Create room
Write-Host "[ROOM] Step 1: Creating test room..." -ForegroundColor Green
$roomData = @{
    host_id = $userId
    title = "Submit Answer Atomic Test $(Get-Random)"
    status = "waiting"
    max_players = 1
} | ConvertTo-Json

try {
    $roomResponse = Invoke-RestMethod -Uri "$baseUrl/rest/v1/game_rooms" `
        -Method POST `
        -Headers $headers `
        -Body $roomData
    
    $roomId = $roomResponse.id
    Write-Host "[OK] Room created: $roomId" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to create room: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Add player to room
Write-Host "[PLAYERS] Step 2: Adding player to room..." -ForegroundColor Green
$playerData = @{
    room_id = $roomId
    user_id = $userId
    is_ready = $true
} | ConvertTo-Json

try {
    $playerResponse = Invoke-RestMethod -Uri "$baseUrl/rest/v1/game_players" `
        -Method POST `
        -Headers $headers `
        -Body $playerData
    
    Write-Host "[OK] Player added to room" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to add player: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Manually set room to active with a test question
Write-Host "[SETUP] Step 3: Setting up active game with test question..." -ForegroundColor Green

$testQuestion = @{
    questionId = "test-question-$(Get-Random)"
    correctPlayerId = 123
    imageUrl = "/players_images/test-player.jpg"
    choices = @(
        @{ name = "Test Player A"; isCorrect = $true }
        @{ name = "Test Player B"; isCorrect = $false }
        @{ name = "Test Player C"; isCorrect = $false }
        @{ name = "Test Player D"; isCorrect = $false }
    )
    correctChoiceName = "Test Player A"
}

$activeRoomData = @{
    status = "active"
    current_question_data = $testQuestion
    current_round_number = 1
    current_round_answers = @()
    player_scores = @{ $userId = 0 }
    game_start_timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    original_player_ids = @($userId)
} | ConvertTo-Json -Depth 10

try {
    $updateResponse = Invoke-RestMethod -Uri "$baseUrl/rest/v1/game_rooms?id=eq.$roomId" `
        -Method PATCH `
        -Headers $headers `
        -Body $activeRoomData
    
    Write-Host "[OK] Room set to active with test question" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to activate room: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Submit answer to test atomic operation
Write-Host "[ATOMIC] Step 4: Testing atomic submit answer operation..." -ForegroundColor Green
Write-Host "[CRITICAL] This tests the race condition fix where submit+advance happens atomically" -ForegroundColor Magenta

$answerData = @{
    roomId = $roomId
    choiceName = "Test Player A"  # Correct answer
} | ConvertTo-Json

try {
    Write-Host "[CALL] Calling submit-answer-handler..." -ForegroundColor Cyan
    $submitResponse = Invoke-RestMethod -Uri "$baseUrl/functions/v1/submit-answer-handler" `
        -Method POST `
        -Headers $headers `
        -Body $answerData
    
    Write-Host "[RESPONSE] Submit Answer Response:" -ForegroundColor Cyan
    Write-Host "   Message: $($submitResponse.message)" -ForegroundColor White
    Write-Host "   Game Advanced: $($submitResponse.gameAdvanced)" -ForegroundColor White
    Write-Host "   Total Answers: $($submitResponse.totalAnswers)" -ForegroundColor White
    
    if ($submitResponse.gameAdvanced) {
        Write-Host "[SUCCESS] ATOMIC OPERATION SUCCESSFUL!" -ForegroundColor Green
        Write-Host "   Next Question ID: $($submitResponse.nextQuestionId)" -ForegroundColor White
        Write-Host "   Next Round: $($submitResponse.nextRoundNumber)" -ForegroundColor White
        Write-Host "   [OK] Race condition fix working - answer submitted AND game advanced atomically" -ForegroundColor Green
    } else {
        Write-Host "[INFO] Game did not advance (might be expected for this test setup)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "[ERROR] Submit answer failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorContent = $reader.ReadToEnd()
            Write-Host "[DEBUG] Error details: $errorContent" -ForegroundColor Red
        } catch {
            Write-Host "[DEBUG] Could not read error details" -ForegroundColor Yellow
        }
    }
    exit 1
}

# Step 5: Verify final state
Write-Host "[VERIFY] Step 5: Verifying final room state..." -ForegroundColor Green
try {
    $finalRoom = Invoke-RestMethod -Uri "$baseUrl/rest/v1/game_rooms?id=eq.$roomId&select=*" `
        -Method GET `
        -Headers $headers
    
    $room = $finalRoom[0]
    Write-Host "[STATUS] Final Room State:" -ForegroundColor Cyan
    Write-Host "   Round Number: $($room.current_round_number)" -ForegroundColor White
    Write-Host "   Question ID: $($room.current_question_data.questionId)" -ForegroundColor White
    Write-Host "   Answers Count: $($room.current_round_answers.Count)" -ForegroundColor White
    
    Write-Host ""
    Write-Host "[COMPLETE] Atomic Submit Answer Test Complete!" -ForegroundColor Cyan
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "This test verifies that the submit-answer-handler correctly handles" -ForegroundColor White
    Write-Host "the race condition by atomically submitting answers AND advancing" -ForegroundColor White
    Write-Host "the game to the next question in a single operation." -ForegroundColor White
    
} catch {
    Write-Host "[ERROR] Failed to verify final state: $($_.Exception.Message)" -ForegroundColor Red
} 