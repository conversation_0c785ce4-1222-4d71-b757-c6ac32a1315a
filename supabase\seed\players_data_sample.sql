-- Sample NFL player data for testing
-- This creates enough players for the game to work

INSERT INTO public.players_data (player_name, team_name, local_image_path, jersey_number, position, height, weight, age_or_dob, experience, college) VALUES
('<PERSON>', 'Kansas City Chiefs', 'players_images/mahomes.jpg', '15', 'QB', '6-2', 225, 28, 7, 'Texas Tech'),
('<PERSON>', 'Buffalo Bills', 'players_images/allen.jpg', '17', 'QB', '6-5', 237, 28, 6, 'Wyoming'),
('<PERSON>', 'Baltimore Ravens', 'players_images/jackson.jpg', '8', 'QB', '6-2', 212, 27, 6, 'Louisville'),
('<PERSON>', 'Cincinnati Bengals', 'players_images/burrow.jpg', '9', 'QB', '6-4', 221, 27, 4, '<PERSON>'),
('<PERSON>', 'Minnesota Vikings', 'players_images/jefferson.jpg', '18', '<PERSON>', '6-1', 195, 25, 4, '<PERSON>'),
('<PERSON><PERSON>''<PERSON><PERSON>', 'Cincinnati Bengals', 'players_images/chase.jpg', '1', '<PERSON>', '6-0', 201, 24, 3, '<PERSON>'),
('<PERSON>reek <PERSON>', 'Miami Dolphins', 'players_images/hill.jpg', '10', '<PERSON>', '5-10', 185, 30, 8, '<PERSON> <PERSON>'),
('<PERSON>e<PERSON>ee <PERSON>', '<PERSON> <PERSON>', 'players_images/lamb.jpg', '88', 'WR', '6-2', 198, 25, 4, '<PERSON>'),
('<PERSON> <PERSON>sa', '<PERSON> <PERSON> 49ers', 'players_images/bosa.jpg', '97', 'DE', '6-4', 266, 26, 5, 'Ohio State'),
('T.J. Watt', 'Pittsburgh Steelers', 'players_images/watt.jpg', '90', 'OLB', '6-4', 252, 29, 7, 'Wisconsin'),
('Micah Parsons', 'Dallas Cowboys', 'players_images/parsons.jpg', '11', 'LB', '6-3', 245, 25, 3, 'Penn State'),
('Myles Garrett', 'Cleveland Browns', 'players_images/garrett.jpg', '95', 'DE', '6-4', 272, 28, 7, 'Texas A&M'),
('Christian McCaffrey', 'San Francisco 49ers', 'players_images/mccaffrey.jpg', '23', 'RB', '5-11', 205, 28, 7, 'Stanford'),
('Derrick Henry', 'Tennessee Titans', 'players_images/henry.jpg', '22', 'RB', '6-3', 247, 30, 8, 'Alabama'),
('Nick Chubb', 'Cleveland Browns', 'players_images/chubb.jpg', '24', 'RB', '5-11', 227, 28, 6, 'Georgia'),
('Travis Kelce', 'Kansas City Chiefs', 'players_images/kelce.jpg', '87', 'TE', '6-5', 250, 34, 11, 'Cincinnati'),
('George Kittle', 'San Francisco 49ers', 'players_images/kittle.jpg', '85', 'TE', '6-4', 250, 30, 7, 'Iowa'),
('Mark Andrews', 'Baltimore Ravens', 'players_images/andrews.jpg', '89', 'TE', '6-5', 256, 28, 6, 'Oklahoma'),
('Sauce Gardner', 'New York Jets', 'players_images/gardner.jpg', '1', 'CB', '6-3', 190, 23, 2, 'Cincinnati'),
('Jalen Ramsey', 'Miami Dolphins', 'players_images/ramsey.jpg', '5', 'CB', '6-1', 194, 29, 8, 'Florida State');

-- Note: In a real deployment, you would need to:
-- 1. Upload actual player images to your storage bucket
-- 2. Update local_image_path to match your storage structure
-- 3. Add more players for variety
-- 4. Ensure all data is accurate and up-to-date