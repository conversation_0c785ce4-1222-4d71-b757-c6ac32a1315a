-- Fix RLS policy for game_players to resolve realtime subscription issues
-- This migration addresses the infinite recursion problem in the current RLS policy
-- by using a helper function with SECURITY DEFINER to break the recursion

-- Create a helper function to check if a user is in the same room as a player record
CREATE OR REPLACE FUNCTION public.user_can_see_game_player(p_room_id uuid, p_user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Check if the current user is in the same room as the player record
    -- This function runs with admin permissions and bypasses R<PERSON> temporarily
    RETURN EXISTS (
        SELECT 1 FROM public.game_players 
        WHERE room_id = p_room_id AND user_id = p_user_id
    );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.user_can_see_game_player(uuid, uuid) TO authenticated;

-- Drop the problematic policy that causes infinite recursion
DROP POLICY IF EXISTS "Allow players to read their own records and others in their room" ON public.game_players;

-- Create a new policy using the helper function to avoid recursion
CREATE POLICY "Allow players to read their own records and others in their room" ON public.game_players
    FOR SELECT TO authenticated
    USING (
        user_id = (select auth.uid()) OR 
        public.user_can_see_game_player(room_id, (select auth.uid()))
    );

-- Ensure the policy is enabled
ALTER TABLE public.game_players ENABLE ROW LEVEL SECURITY;

-- Verify that the realtime publication includes game_players
-- This should already be done by previous migrations, but we'll ensure it
DO $$
BEGIN
    -- Add game_players to realtime publication if not already added
    IF NOT EXISTS (
        SELECT 1 FROM pg_publication_tables 
        WHERE pubname = 'supabase_realtime' 
        AND schemaname = 'public' 
        AND tablename = 'game_players'
    ) THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE public.game_players;
    END IF;
END $$;
