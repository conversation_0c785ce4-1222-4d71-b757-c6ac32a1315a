# Comprehensive Host Re-entry Test Script
# Tests the critical host re-entry bug fix for Recognition-Combine

Write-Host "Starting Comprehensive Host Re-entry Test" -ForegroundColor Green
Write-Host "Testing the critical fix for host unable to re-enter active games" -ForegroundColor Yellow
Write-Host ""

# Test Configuration
$baseUrl = "http://localhost:3000"
$testDuration = 180 # 3 minutes
$startTime = Get-Date

function Write-TestResult {
    param(
        [string]$Test,
        [bool]$Passed,
        [string]$Details = ""
    )
    
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    $color = if ($Passed) { "Green" } else { "Red" }
    
    Write-Host "$status - $Test" -ForegroundColor $color
    if ($Details) {
        Write-Host "    $Details" -ForegroundColor Gray
    }
    Write-Host ""
}

function Test-HostReentryWorkflow {
    Write-Host "Testing Host Re-entry Workflow" -ForegroundColor Cyan
    Write-Host ""
    
    # Test 1: Application loads successfully
    try {
        $response = Invoke-WebRequest -Uri $baseUrl -TimeoutSec 10 -UseBasicParsing
        $appLoads = $response.StatusCode -eq 200
        Write-TestResult "Application loads successfully" $appLoads "Status: $($response.StatusCode)"
    }
    catch {
        Write-TestResult "Application loads successfully" $false "Error: $($_.Exception.Message)"
        return $false
    }
    
    # Test 2: Check for critical host re-entry function
    Write-Host "Checking for handleJoinRoom implementation..." -ForegroundColor Yellow
    
    $pageContent = Get-Content "web-app/src/app/page.tsx" -Raw
    
    $hasHostReentryDetection = $pageContent -match "isHostAttemptingActiveGameReentry"
    Write-TestResult "Host re-entry detection logic present" $hasHostReentryDetection
    
    $hasImmediateHandling = $pageContent -match "IMMEDIATE HOST RE-ENTRY detected"
    Write-TestResult "Immediate host re-entry handling present" $hasImmediateHandling
    
    $hasEarlyReturn = $pageContent -match "return; // CRITICAL: Exit early"
    Write-TestResult "Early return to prevent fall-through present" $hasEarlyReturn
    
    $hasLoadingState = $pageContent -match "isJoiningOrRejoiningRoom"
    Write-TestResult "Loading state management present" $hasLoadingState
    
    # Test 3: Check for proper state variables
    Write-Host "Checking state variable declarations..." -ForegroundColor Yellow
    
    $stateVariables = @(
        "isJoiningOrRejoiningRoom",
        "currentRoomGameData", 
        "playersInRoom",
        "isLeavingRoom",
        "activeRoomId"
    )
    
    foreach ($variable in $stateVariables) {
        $hasVariable = $pageContent -match "useState.*$variable" -or $pageContent -match "\[$variable, set"
        Write-TestResult "State variable '$variable' declared" $hasVariable
    }
    
    # Test 4: Check for UI loading feedback
    $hasLoadingButtons = $pageContent -match 'disabled=\{isJoiningOrRejoiningRoom\}'
    Write-TestResult "UI loading feedback implemented" $hasLoadingButtons
    
    $hasLoadingText = $pageContent -match '"Re-entering\.\.\."'
    Write-TestResult "Loading button text implemented" $hasLoadingText
    
    # Test 5: Check for proper error handling
    $hasErrorHandling = $pageContent -match "setIsJoiningOrRejoiningRoom\(false\)"
    Write-TestResult "Error state cleanup implemented" $hasErrorHandling
    
    return $true
}

function Test-CodeStructure {
    Write-Host "Testing Code Structure and Logic Flow" -ForegroundColor Cyan
    Write-Host ""
    
    $pageContent = Get-Content "web-app/src/app/page.tsx" -Raw
    
    # Test logical flow order
    $hostReentryIndex = $pageContent.IndexOf("isHostAttemptingActiveGameReentry")
    $tryBlockIndex = $pageContent.IndexOf("try {", $hostReentryIndex)
    
    if ($hostReentryIndex -gt 0 -and $tryBlockIndex -gt $hostReentryIndex) {
        # Find the if block that handles host re-entry
        $ifBlockPattern = "if \(isHostAttemptingActiveGameReentry\)"
        $matches = [regex]::Matches($pageContent, $ifBlockPattern)
        
        if ($matches.Count -gt 0) {
            $ifBlockIndex = $matches[0].Index
            $logicBeforeTry = $ifBlockIndex -lt $tryBlockIndex
            Write-TestResult "Host re-entry logic positioned before try block" $logicBeforeTry "Prevents fall-through to NEW JOIN logic"
        }
        else {
            Write-TestResult "Host re-entry logic positioned before try block" $false "Could not find if block"
        }
    }
    else {
        Write-TestResult "Host re-entry logic positioned before try block" $false "Logic flow issue detected"
    }
    
    # Test for comprehensive logging
    $hasComprehensiveLogging = $pageContent -match "\[Client\] IMMEDIATE HOST RE-ENTRY detected" -and
                              $pageContent -match "\[Client\] Host re-entry: Client active state set" -and
                              $pageContent -match "\[Client\] Host re-entry completed successfully"
    
    Write-TestResult "Comprehensive logging implemented" $hasComprehensiveLogging
    
    # Test for state preservation logic
    $hasStatePreservation = $pageContent -match "player_scores" -and 
                           $pageContent -match "Host's score should be preserved"
    Write-TestResult "Score preservation logic documented" $hasStatePreservation
}

function Test-EdgeCases {
    Write-Host "Testing Edge Cases and Error Conditions" -ForegroundColor Cyan
    Write-Host ""
    
    $pageContent = Get-Content "web-app/src/app/page.tsx" -Raw
    
    # Test for multiple attempt prevention
    $hasMultipleAttemptPrevention = $pageContent -match "Join attempt already in progress"
    Write-TestResult "Multiple simultaneous attempt prevention" $hasMultipleAttemptPrevention
    
    # Test for user authentication check
    $hasAuthCheck = $pageContent -match "You must be logged in to join"
    Write-TestResult "User authentication validation" $hasAuthCheck
    
    # Test for room validation
    $hasRoomValidation = $pageContent -match "Room details not found"
    Write-TestResult "Room validation implemented" $hasRoomValidation
    
    # Test for proper cleanup on errors
    $cleanupCount = ([regex]::Matches($pageContent, "setIsJoiningOrRejoiningRoom\(false\)")).Count
    $hasProperCleanup = $cleanupCount -ge 5  # Should be called in multiple error conditions
    Write-TestResult "Proper loading state cleanup on errors" $hasProperCleanup "Found $cleanupCount cleanup calls"
}

function Test-RealtimeSubscriptionFix {
    Write-Host "Testing Realtime Subscription Management" -ForegroundColor Cyan
    Write-Host ""
    
    $pageContent = Get-Content "web-app/src/app/page.tsx" -Raw
    
    # Test for captured room ID pattern
    $hasCapturedRoomId = $pageContent -match "capturedActiveRoomId.*activeRoomId"
    Write-TestResult "Realtime subscription cleanup uses captured room ID" $hasCapturedRoomId
    
    # Test for proper channel removal
    $hasChannelRemoval = $pageContent -match "removeChannel.*gamePlayersChannel"
    Write-TestResult "Proper channel removal implemented" $hasChannelRemoval
    
    # Test for error handling in cleanup
    $hasCleanupErrorHandling = $pageContent -match "\.catch.*err.*Error removing channel"
    Write-TestResult "Channel cleanup error handling" $hasCleanupErrorHandling
}

# Main Test Execution
Write-Host "Recognition-Combine Host Re-entry Fix Validation" -ForegroundColor Magenta
Write-Host "=================================================" -ForegroundColor Magenta
Write-Host ""

$testResults = @()

# Run all test suites
$testResults += Test-HostReentryWorkflow
Test-CodeStructure
Test-EdgeCases
Test-RealtimeSubscriptionFix

Write-Host ""
Write-Host "TEST SUMMARY" -ForegroundColor Magenta
Write-Host "===============" -ForegroundColor Magenta

$elapsed = ((Get-Date) - $startTime).TotalSeconds
Write-Host "Test Duration: $($elapsed.ToString('F1')) seconds" -ForegroundColor Gray
Write-Host ""

# Verify files exist
$criticalFiles = @(
    "web-app/src/app/page.tsx",
    "HOST_REENTRY_CRITICAL_FIX_SUMMARY.md"
)

Write-Host "File Verification:" -ForegroundColor Yellow
foreach ($file in $criticalFiles) {
    $exists = Test-Path $file
    $status = if ($exists) { "EXISTS" } else { "MISSING" }
    Write-Host "  $status $file" -ForegroundColor $(if ($exists) { "Green" } else { "Red" })
}
Write-Host ""

# Final recommendations
Write-Host "NEXT STEPS FOR MANUAL TESTING:" -ForegroundColor Green
Write-Host "1. Start the application: cd web-app; npm run dev" -ForegroundColor White
Write-Host "2. Create a multiplayer game as Host" -ForegroundColor White  
Write-Host "3. Start the game with other players" -ForegroundColor White
Write-Host "4. Host clicks 'Leave Game'" -ForegroundColor White
Write-Host "5. Host should see active game in lobby" -ForegroundColor White
Write-Host "6. Host clicks on game -> should see 'Re-enter Your Game' button (blue)" -ForegroundColor White
Write-Host "7. Host clicks 'Re-enter Your Game' -> should work immediately!" -ForegroundColor White
Write-Host ""

Write-Host "EXPECTED SUCCESS LOGS:" -ForegroundColor Green
Write-Host "[Client] IMMEDIATE HOST RE-ENTRY detected" -ForegroundColor Gray
Write-Host "[Client] Host re-entry: Client active state set" -ForegroundColor Gray  
Write-Host "[Client] Host re-entry completed successfully" -ForegroundColor Gray
Write-Host ""

Write-Host "Host Re-entry Fix Validation Complete!" -ForegroundColor Green 