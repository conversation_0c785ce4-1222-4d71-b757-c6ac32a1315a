-- Fix players_data table access for edge functions
-- This migration ensures the players_data table can be accessed by edge functions

-- First, ensure RLS is enabled on players_data table
ALTER TABLE public.players_data ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows anyone (including service role) to read player data
-- This is safe because player data is public game content
CREATE POLICY "Allow public read access to players_data" ON public.players_data
    FOR SELECT
    TO public
    USING (true);

-- Create indexes if they don't exist for better query performance
CREATE INDEX IF NOT EXISTS idx_players_data_has_image ON public.players_data(local_image_path) WHERE local_image_path IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_players_data_team_name ON public.players_data(team_name);

-- Add comment to track this migration
COMMENT ON TABLE public.players_data IS 'NFL player data for the game - public read access enabled';