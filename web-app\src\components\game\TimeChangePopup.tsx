import { motion, AnimatePresence } from 'framer-motion';

interface Props {
  timeChange: number | null; // e.g., 1 for +1s, -1 for -1s
  trigger: number;
  scoreChanged?: boolean; // New prop: true if score also changed
}

export function TimeChangePopup({ timeChange, trigger, scoreChanged }: Props) {
  if (!timeChange || timeChange === 0) return null;
  
  const isPositive = timeChange > 0;
  const text = isPositive ? `+${timeChange}s` : `${timeChange}s`;
  const colorClass = isPositive ? "text-green-400" : "text-red-400";

  // Base vertical position - similar to ScorePopup
  const initialYOffset = -23;
  const animationTravelY = -60;

  // If score also changed, push this popup further down to appear below the score popup
  const additionalOffsetY = scoreChanged && timeChange > 0 ? 35 : 0;
  
  return (
    <AnimatePresence>
      {trigger > 0 && (
        <motion.div
          key={`time-${trigger}`}
          initial={{ opacity: 1, y: initialYOffset + additionalOffsetY, scale: 0.7 }}
          animate={{ opacity: 0, y: initialYOffset + additionalOffsetY + animationTravelY, scale: 1.2 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1.2, ease: 'easeOut' }}
          className="absolute left-1/2 top-1/3 -translate-x-1/2 z-40 pointer-events-none"
        >
          <span 
            className={`text-3xl md:text-4xl font-bold ${colorClass}`}
            style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}
          >
            {text}
          </span>
        </motion.div>
      )}
    </AnimatePresence>
  );
} 