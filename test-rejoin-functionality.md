# Test Guide: Image Fix and Rejoin Functionality

## Overview
This guide tests two key improvements:
1. **Image Fix**: Valid image paths in `start-game-handler` using `atlanta-falcons/nick-kubitz.jpg`
2. **Rejoin Logic**: Non-hosts can rejoin active games after clicking "Leave Game"

## Prerequisites
- Local Supabase instance running (`supabase start` completed)
- Next.js dev server running (`npm run dev` in web-app directory)
- Clean database (delete all rows from `game_players` and `game_rooms` tables)

## Test Scenario

### Setup (Two Browser Windows/Profiles)
- **Browser A**: Regular browser window at `http://localhost:3000`
- **Browser B**: Incognito/different profile at `http://localhost:3000`

### Phase 1: Game Creation and Start

#### User A (Host):
1. Sign in as User A
2. Switch to Multiplayer Mode
3. Create a new game (should auto-join)
4. Wait in lobby

#### User B (Non-Host):
1. Sign in as User B  
2. Switch to Multiplayer Mode
3. Find User A's game and join it

#### Both Users:
1. Both click "Ready Up"

#### User A (Host):
1. Click "Start Game"

### Phase 2: Verification Points

#### ✅ Image Loading Test:
- **VERIFY**: Question image loads correctly (shows `nick-kubitz.jpg`)
- **CHECK**: Browser console for NO 400/404 image errors
- **EXPECTED LOG**: `[EF:start-game-handler] Generated new sample question: { ... imageUrl: '/players_images/atlanta-falcons/nick-kubitz.jpg' ... }`

#### ✅ Player Scores Panel:
- **VERIFY**: Left panel shows both User A and User B with 0 points
- **VERIFY**: Both players appear as connected

### Phase 3: Leave and Rejoin Test

#### User B (Non-Host) Leaves:
1. Click "Leave Game" button
2. **VERIFY**: User B returns to lobby list
3. **CHECK LOGS**: `[EF:leave-room-handler] Non-host ... leaving ACTIVE room ... Marking as disconnected.`
4. **CHECK DATABASE**: In `game_players` table, User B's record should have `is_connected: false` (NOT deleted)

#### User B (Non-Host) Rejoins:
1. From lobby, find the same active game (hosted by User A)
2. Click to view details, then click join/rejoin button
3. **VERIFY**: User B successfully rejoins and sees current active question
4. **CHECK LOGS**: 
   - `[Client] User ... attempting to REJOIN active room ...`
   - `[Client] User ... successfully REJOINED room ...`
5. **CHECK DATABASE**: User B's record should now have `is_connected: true`
6. **VERIFY**: User A's Player Scores panel updates to show User B as connected again

### Phase 4: Host Leave Test (Optional)

#### User A (Host) Leaves:
1. User A clicks "Leave Game"
2. **CHECK LOGS**: `[EF:leave-room-handler]` should execute deletion logic for host
3. **VERIFY**: Behavior depends on remaining players and room status

## Expected Log Messages

### start-game-handler:
```
[EF:start-game-handler] generateNewQuestionForRoom called.
[EF:start-game-handler] Generated new sample question: { ... imageUrl: '/players_images/atlanta-falcons/nick-kubitz.jpg' ... }
[EF:start-game-handler] Game started successfully in room ... Image: /players_images/atlanta-falcons/nick-kubitz.jpg
```

### leave-room-handler (Non-Host):
```
[EF:leave-room-handler] Non-host ... leaving ACTIVE room ... Marking as disconnected.
[EF:leave-room-handler] Non-host ... marked as disconnected in room ...
```

### Client (Rejoin):
```
[Client] User <UserB_ID> is already in game_players table for room <RoomID> (STANDARD REJOIN). Connected: false
[Client] User <UserB_ID> attempting to REJOIN active room <RoomID>.
[Client] User <UserB_ID> successfully REJOINED room <RoomID>.
```

## Success Criteria

✅ **Image Loading**: No 400/404 errors, valid image displays  
✅ **Non-Host Leave**: Player marked as disconnected, not deleted  
✅ **Non-Host Rejoin**: Player can successfully rejoin active game  
✅ **Database Integrity**: Correct `is_connected` status updates  
✅ **UI Updates**: Player scores panel reflects connection status  

## Troubleshooting

### Image Still Not Loading:
- Check file exists: `web-app/public/players_images/atlanta-falcons/nick-kubitz.jpg`
- Verify browser network tab for actual request URL
- Check console for exact error message

### Rejoin Not Working:
- Verify `game_players` record exists with `is_connected: false`
- Check client logs for rejoin attempt messages
- Ensure room status is still 'active'

### Database Issues:
- Clear all data from `game_players` and `game_rooms` tables
- Restart Supabase local instance
- Check Edge Function logs in Supabase Studio

## Database Cleanup (If Needed)
```sql
-- Run in Supabase Studio SQL Editor
DELETE FROM game_players;
DELETE FROM game_rooms;
``` 