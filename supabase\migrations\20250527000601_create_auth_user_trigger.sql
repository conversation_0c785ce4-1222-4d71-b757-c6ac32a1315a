-- supabase/migrations/20250527000601_create_auth_user_trigger.sql

-- Ensure the function exists (it should from 20250519012600_remote_schema.sql)
-- but it's good practice to make migrations idempotent if possible.
-- The function definition itself:
-- CREATE OR REPLACE FUNCTION public.handle_new_user()
--  RETURNS trigger
--  LANGUAGE plpgsql
--  SECURITY DEFINER
--  SET search_path TO 'public'
-- AS $function$
-- BEGIN
--   INSERT INTO public.profiles (id, username)
--   VALUES (NEW.id, NEW.raw_user_meta_data->>'username'); 
--   RETURN NEW;
-- END;
-- $function$;

-- Now, create the trigger if it doesn't already exist
-- This ensures it only gets created once.
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'on_auth_user_created'
    ) THEN
        CREATE TRIGGER on_auth_user_created
          AFTER INSERT ON auth.users
          FOR EACH ROW
          EXECUTE FUNCTION public.handle_new_user();
        
        RAISE NOTICE 'Trigger on_auth_user_created created on auth.users.';
    ELSE
        RAISE NOTICE 'Trigger on_auth_user_created already exists on auth.users.';
    END IF;
END $$;

-- Optional: Grant usage on the sequence if your profile table's primary key uses one and RLS is tight
-- (Not usually needed for this trigger if 'id' is just a UUID passed from auth.users)
