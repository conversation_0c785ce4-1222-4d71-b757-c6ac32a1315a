$uri = "http://127.0.0.1:54321/functions/v1/login-handler"

Write-Host "=== Testing OPTIONS Request ==="
try {
    $response = Invoke-WebRequest -Uri $uri -Method OPTIONS -UseBasicParsing
    Write-Host "Status: $($response.StatusCode) $($response.StatusDescription)"
    Write-Host "Headers:"
    $response.Headers.GetEnumerator() | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value -join ', ')"
    }
    Write-Host "Body: $($response.Content)"
} catch {
    Write-Host "OPTIONS Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)"
        Write-Host "Headers:"
        try {
            $_.Exception.Response.Headers.GetEnumerator() | ForEach-Object {
                Write-Host "  $($_.Key): $($_.Value -join ', ')"
            }
        } catch {
            Write-Host "  Could not read headers"
        }
    }
}

Write-Host ""
Write-Host "=== Testing POST Request ==="
$body = '{"identifier":"jkim","password":"wrongpassword"}'
try {
    $response = Invoke-WebRequest -Uri $uri -Method POST -Body $body -ContentType "application/json" -UseBasicParsing
    Write-Host "Status: $($response.StatusCode) $($response.StatusDescription)"
    Write-Host "Headers:"
    $response.Headers.GetEnumerator() | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value -join ', ')"
    }
    Write-Host "Body: $($response.Content)"
} catch {
    Write-Host "POST Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)"
        Write-Host "Headers:"
        try {
            $_.Exception.Response.Headers.GetEnumerator() | ForEach-Object {
                Write-Host "  $($_.Key): $($_.Value -join ', ')"
            }
        } catch {
            Write-Host "  Could not read headers"
        }
        
        # Try to read response body
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Body: $responseBody"
        } catch {
            Write-Host "Could not read response body"
        }
    }
} 