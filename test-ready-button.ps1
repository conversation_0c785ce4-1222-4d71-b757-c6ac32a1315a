# Test script for Ready Up button functionality
# This script will help verify that the Ready Up button works correctly with the new implementation

Write-Host "=== Ready Up Button Test Script ===" -ForegroundColor Green
Write-Host "This script will guide you through testing the Ready Up button functionality" -ForegroundColor Yellow
Write-Host ""

Write-Host "Test Steps:" -ForegroundColor Cyan
Write-Host "1. Open the web application in two browser windows/tabs" -ForegroundColor White
Write-Host "2. Sign in with different accounts in each window" -ForegroundColor White
Write-Host "3. Create a multiplayer room in one window" -ForegroundColor White
Write-Host "4. Join the room from the second window" -ForegroundColor White
Write-Host "5. Click the Ready Up button multiple times rapidly in both windows" -ForegroundColor White
Write-Host "6. Check the browser console for logging messages" -ForegroundColor White
Write-Host ""

Write-Host "Expected Behavior:" -ForegroundColor Cyan
Write-Host "- Button should show 'Processing...' when clicked" -ForegroundColor White
Write-Host "- Multiple rapid clicks should be ignored (only one DB update per click)" -ForegroundColor White
Write-Host "- Consol<PERSON> should show comprehensive logging" -ForegroundColor White
Write-Host "- Ready state should update immediately (optimistic UI)" -ForegroundColor White
Write-Host "- Realtime should confirm the update" -ForegroundColor White
Write-Host ""

Write-Host "Console Logs to Look For:" -ForegroundColor Cyan
Write-Host "- '[RoomView] handleToggleReady called - Starting comprehensive logging'" -ForegroundColor White
Write-Host "- '[RoomView] Set isSubmittingReady to true - blocking further submissions'" -ForegroundColor White
Write-Host "- '[RoomView] Already submitting ready state, skipping this call'" -ForegroundColor White
Write-Host "- '[RoomView] Database update successful'" -ForegroundColor White
Write-Host "- '[Realtime] game_players change received'" -ForegroundColor White
Write-Host ""

Write-Host "Starting the web application..." -ForegroundColor Green

# Change to web-app directory and start the development server
Set-Location "web-app"

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Start the development server
Write-Host "Starting Next.js development server..." -ForegroundColor Green
npm run dev 