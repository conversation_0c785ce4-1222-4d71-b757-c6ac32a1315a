async function testEdgeFunction() {
  const SUPABASE_URL = 'https://xmyxuvuimebjltnaamox.supabase.co';
  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY';

  // Test with a dummy auth token to see what error we get
  const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXVzZXItaWQiLCJpYXQiOjE3MzYyODA0MjYsImV4cCI6MTczNjI4NDAyNn0.test-signature';
  
  console.log('Testing Edge Function directly...\n');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/start-game-handler`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${testToken}`,
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        roomId: 'test-room-id'
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('\nResponse body:');
    
    try {
      const data = JSON.parse(responseText);
      console.log(JSON.stringify(data, null, 2));
    } catch {
      console.log(responseText);
    }
    
  } catch (error) {
    console.error('Fetch error:', error);
  }
}

testEdgeFunction();