# Multiplayer round advance timing test

Write-Host "MULTIPLAYER ROUND ADVANCE TIMING TEST" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Round Advance Rules:" -ForegroundColor Yellow
Write-Host "1. 7-second hard cap from round start" -ForegroundColor White
Write-Host "2. All players answer + 3s transition (if < 7s total)" -ForegroundColor White
Write-Host "3. Whichever happens FIRST" -ForegroundColor White
Write-Host ""
Write-Host "Opening test environment..." -ForegroundColor Green

# Create timing test HTML page
$testHtml = @"
<!DOCTYPE html>
<html>
<head>
    <title>Multiplayer Timing Test Helper</title>
    <style>
        body { font-family: Arial; padding: 20px; background: #1a1a1a; color: white; }
        .timer { font-size: 48px; font-weight: bold; color: #00ff00; }
        .scenario { margin: 20px 0; padding: 15px; border: 2px solid #444; }
        .expected { color: #ffff00; }
        .instructions { color: #00ffff; }
        button { padding: 10px 20px; font-size: 16px; margin: 5px; cursor: pointer; }
        .log { background: #000; padding: 10px; margin: 10px 0; font-family: monospace; height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Multiplayer Round Advance Test</h1>
    <div class="timer" id="timer">0.0s</div>
    <button onclick="startRound()">Start Round Timer</button>
    <button onclick="logEvent('Player 1 answered')">P1 Answer</button>
    <button onclick="logEvent('Player 2 answered')">P2 Answer</button>
    <button onclick="logEvent('Round advanced')">Round Advanced</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div class="log" id="log"></div>
    
    <div class="scenario">
        <h3>Scenario A: Both answer at 1s</h3>
        <div class="instructions">Both players click answer at 1.0s mark</div>
        <div class="expected">Expected: Round advances at 4.0s (1+3)</div>
    </div>
    
    <div class="scenario">
        <h3>Scenario B: Both answer at 2s</h3>
        <div class="instructions">Both players click answer at 2.0s mark</div>
        <div class="expected">Expected: Round advances at 5.0s (2+3)</div>
    </div>
    
    <div class="scenario">
        <h3>Scenario C: Both answer at 5s</h3>
        <div class="instructions">Both players click answer at 5.0s mark</div>
        <div class="expected">Expected: Round advances at 7.0s (hard cap, NOT 8s)</div>
    </div>
    
    <div class="scenario">
        <h3>Scenario D: One answers at 1s, other doesn't</h3>
        <div class="instructions">Only Player 1 answers at 1.0s</div>
        <div class="expected">Expected: Round advances at 7.0s (hard cap)</div>
    </div>
    
    <div class="scenario">
        <h3>Scenario E: Staggered - P1 at 1s, P2 at 3s</h3>
        <div class="instructions">P1 at 1.0s, P2 at 3.0s</div>
        <div class="expected">Expected: Round advances at 6.0s (3+3)</div>
    </div>
    
    <script>
        let startTime = null;
        let timerInterval = null;
        
        function startRound() {
            startTime = Date.now();
            clearInterval(timerInterval);
            document.getElementById('log').innerHTML = '<div style="color: #00ff00;">ROUND STARTED</div>';
            
            timerInterval = setInterval(() => {
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
                document.getElementById('timer').textContent = elapsed + 's';
                
                // Highlight 7s cap
                if (elapsed >= 7.0) {
                    document.getElementById('timer').style.color = '#ff0000';
                }
            }, 100);
        }
        
        function logEvent(event) {
            if (!startTime) {
                alert('Start the round timer first!');
                return;
            }
            const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
            const log = document.getElementById('log');
            log.innerHTML += '<div>' + elapsed + 's: ' + event + '</div>';
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('timer').textContent = '0.0s';
            document.getElementById('timer').style.color = '#00ff00';
            clearInterval(timerInterval);
            startTime = null;
        }
    </script>
</body>
</html>
"@

# Save test helper
$testPath = "$env:TEMP\multiplayer-timing-test.html"
$testHtml | Out-File -FilePath $testPath -Encoding UTF8

# Open test helper
Start-Process $testPath

# Open game in two windows
Start-Sleep -Seconds 1
Start-Process "https://recognition-combine.vercel.app/"
Start-Sleep -Seconds 2
Start-Process "https://recognition-combine.vercel.app/"

Write-Host ""
Write-Host "TEST SETUP COMPLETE!" -ForegroundColor Green
Write-Host ""
Write-Host "Instructions:" -ForegroundColor Yellow
Write-Host "1. Use the timing helper window to track exact timings" -ForegroundColor White
Write-Host "2. Sign in to both game windows with different accounts" -ForegroundColor White
Write-Host "3. Create/join the same room" -ForegroundColor White
Write-Host "4. When ready to test:" -ForegroundColor White
Write-Host "   - Click 'Start Round Timer' when the game round begins" -ForegroundColor Cyan
Write-Host "   - Click 'P1/P2 Answer' when each player submits" -ForegroundColor Cyan
Write-Host "   - Click 'Round Advanced' when you see the next question" -ForegroundColor Cyan
Write-Host "5. Compare actual timings with expected timings" -ForegroundColor White
Write-Host ""
Write-Host "Press F12 in game windows to see console logs" -ForegroundColor Yellow