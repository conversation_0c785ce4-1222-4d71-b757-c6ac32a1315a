import { render, screen, waitFor, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createClient } from '@supabase/supabase-js';
// Import the exported component, not the default export
const HomePageContent = vi.fn(() => null);
import { AuthProvider } from '../providers/AuthProvider';

// Mock Supabase
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn()
}));

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: vi.fn((key) => {
      if (key === 'roomId') return 'test-room-id';
      if (key === 'gameType') return 'multiplayer';
      return null;
    })
  }),
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn()
  })
}));

describe('Heartbeat Authentication Tests', () => {
  let mockSupabaseClient: any;
  let heartbeatInterval: NodeJS.Timeout | null = null;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Create mock Supabase client
    mockSupabaseClient = {
      auth: {
        getSession: vi.fn(),
        getUser: vi.fn(),
        onAuthStateChange: vi.fn((callback) => {
          // Simulate auth state change subscription
          return {
            data: { subscription: { unsubscribe: vi.fn() } }
          };
        }),
        signOut: vi.fn()
      },
      functions: {
        invoke: vi.fn()
      },
      from: vi.fn(() => ({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn(),
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        delete: vi.fn().mockReturnThis(),
        on: vi.fn().mockReturnThis(),
        subscribe: vi.fn(() => ({
          unsubscribe: vi.fn()
        }))
      })),
      channel: vi.fn(() => ({
        on: vi.fn().mockReturnThis(),
        subscribe: vi.fn(() => 'subscribed'),
        unsubscribe: vi.fn()
      }))
    };

    (createClient as any).mockReturnValue(mockSupabaseClient);
  });

  afterEach(() => {
    vi.useRealTimers();
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
  });

  it('should not send heartbeat when user is not authenticated', async () => {
    // Mock no session
    mockSupabaseClient.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null
    });

    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null
    });

    render(
      <AuthProvider>
        <HomePageContent />
      </AuthProvider>
    );

    // Fast forward time to trigger heartbeat
    act(() => {
      vi.advanceTimersByTime(16000); // Heartbeat runs every 15 seconds
    });

    await waitFor(() => {
      // Heartbeat should not be invoked when not authenticated
      expect(mockSupabaseClient.functions.invoke).not.toHaveBeenCalledWith(
        'heartbeat-handler',
        expect.any(Object)
      );
    });
  });

  it('should send heartbeat with valid authentication', async () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>'
    };

    const mockSession = {
      access_token: 'valid-token',
      user: mockUser
    };

    // Mock authenticated session
    mockSupabaseClient.auth.getSession.mockResolvedValue({
      data: { session: mockSession },
      error: null
    });

    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null
    });

    // Mock successful heartbeat response
    mockSupabaseClient.functions.invoke.mockResolvedValue({
      data: { success: true },
      error: null
    });

    // Mock room data
    mockSupabaseClient.from().select().eq().single.mockResolvedValue({
      data: {
        id: 'test-room-id',
        host_id: mockUser.id,
        status: 'waiting',
        players: [{ id: mockUser.id, name: 'Test User' }]
      },
      error: null
    });

    render(
      <AuthProvider>
        <HomePageContent />
      </AuthProvider>
    );

    // Wait for component to mount and initialize
    await waitFor(() => {
      expect(mockSupabaseClient.auth.getSession).toHaveBeenCalled();
    });

    // Fast forward to trigger heartbeat
    act(() => {
      vi.advanceTimersByTime(16000);
    });

    await waitFor(() => {
      expect(mockSupabaseClient.functions.invoke).toHaveBeenCalledWith(
        'heartbeat-handler',
        {
          body: { roomId: 'test-room-id', action: 'ping' }
        }
      );
    });
  });

  it('should handle 401 error gracefully and attempt session refresh', async () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>'
    };

    const mockSession = {
      access_token: 'expired-token',
      user: mockUser
    };

    // Mock authenticated session initially
    mockSupabaseClient.auth.getSession.mockResolvedValue({
      data: { session: mockSession },
      error: null
    });

    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null
    });

    // Mock 401 error for heartbeat
    const error401 = new Error('Edge Function returned a non-2xx status code');
    (error401 as any).status = 401;
    
    mockSupabaseClient.functions.invoke.mockResolvedValue({
      data: null,
      error: error401
    });

    // Mock room data
    mockSupabaseClient.from().select().eq().single.mockResolvedValue({
      data: {
        id: 'test-room-id',
        host_id: mockUser.id,
        status: 'waiting',
        players: [{ id: mockUser.id, name: 'Test User' }]
      },
      error: null
    });

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(
      <AuthProvider>
        <HomePageContent />
      </AuthProvider>
    );

    // Fast forward to trigger heartbeat
    act(() => {
      vi.advanceTimersByTime(16000);
    });

    await waitFor(() => {
      expect(mockSupabaseClient.functions.invoke).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[HEARTBEAT] Error invoking heartbeat-handler:'),
        expect.any(Error)
      );
    });

    consoleSpy.mockRestore();
  });

  it('should delay heartbeat until authentication is ready', async () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>'
    };

    const mockSession = {
      access_token: 'valid-token',
      user: mockUser
    };

    // Initially return no session
    mockSupabaseClient.auth.getSession.mockResolvedValueOnce({
      data: { session: null },
      error: null
    });

    // Then return valid session after delay
    mockSupabaseClient.auth.getSession.mockResolvedValueOnce({
      data: { session: mockSession },
      error: null
    });

    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null
    });

    mockSupabaseClient.functions.invoke.mockResolvedValue({
      data: { success: true },
      error: null
    });

    render(
      <AuthProvider>
        <HomePageContent />
      </AuthProvider>
    );

    // First heartbeat attempt - should not invoke due to no session
    act(() => {
      vi.advanceTimersByTime(16000);
    });

    expect(mockSupabaseClient.functions.invoke).not.toHaveBeenCalled();

    // Simulate auth state change
    const authStateCallback = mockSupabaseClient.auth.onAuthStateChange.mock.calls[0][0];
    act(() => {
      authStateCallback('SIGNED_IN', mockSession);
    });

    // Next heartbeat should work
    act(() => {
      vi.advanceTimersByTime(15000);
    });

    await waitFor(() => {
      expect(mockSupabaseClient.functions.invoke).toHaveBeenCalledWith(
        'heartbeat-handler',
        expect.any(Object)
      );
    });
  });

  it('should stop heartbeat when user leaves room', async () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>'
    };

    const mockSession = {
      access_token: 'valid-token',
      user: mockUser
    };

    mockSupabaseClient.auth.getSession.mockResolvedValue({
      data: { session: mockSession },
      error: null
    });

    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null
    });

    mockSupabaseClient.functions.invoke.mockResolvedValue({
      data: { success: true },
      error: null
    });

    const { unmount } = render(
      <AuthProvider>
        <HomePageContent />
      </AuthProvider>
    );

    // Trigger first heartbeat
    act(() => {
      vi.advanceTimersByTime(16000);
    });

    const firstCallCount = mockSupabaseClient.functions.invoke.mock.calls.length;

    // Unmount component (simulate leaving room)
    unmount();

    // Advance time to see if heartbeat still runs
    act(() => {
      vi.advanceTimersByTime(30000);
    });

    // No new heartbeat calls should be made
    expect(mockSupabaseClient.functions.invoke).toHaveBeenCalledTimes(firstCallCount);
  });
});