# Test script to debug answer submission state timing issue

Write-Host "Answer Submission State Debug Test" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""
Write-Host "This test will help debug why the answer submission state reverts." -ForegroundColor Yellow
Write-Host ""
Write-Host "MANUAL TESTING STEPS:" -ForegroundColor Cyan
Write-Host "1. Open the app in your browser at http://localhost:3001"
Write-Host "2. Open Browser DevTools (F12) and go to Console tab"
Write-Host "3. Start a multiplayer game"
Write-Host "4. When a question appears, run this command in the console:"
Write-Host ""
Write-Host "   // Debug timer to track state changes" -ForegroundColor White
Write-Host '   let startTime = Date.now();' -ForegroundColor White
Write-Host '   let checkState = () => {' -ForegroundColor White
Write-Host '     const elapsed = Date.now() - startTime;' -ForegroundColor White
Write-Host '     const buttons = document.querySelectorAll(".grid.grid-cols-2 button");' -ForegroundColor White
Write-Host '     const msg = document.querySelector("p:has-text(\"Answer submitted\")");' -ForegroundColor White
Write-Host '     console.log(`[STATE CHECK @ ${elapsed}ms]`, {' -ForegroundColor White
Write-Host '       hasSubmittedMsg: !!msg,' -ForegroundColor White
Write-Host '       msgText: msg?.textContent,' -ForegroundColor White
Write-Host '       buttonsDisabled: Array.from(buttons).map(b => b.disabled),' -ForegroundColor White
Write-Host '       buttonClasses: Array.from(buttons).map(b => b.className.includes("pointer-events-none"))' -ForegroundColor White
Write-Host '     });' -ForegroundColor White
Write-Host '   };' -ForegroundColor White
Write-Host '   ' -ForegroundColor White
Write-Host '   // Set up interval checks' -ForegroundColor White
Write-Host '   [0, 50, 100, 230, 500, 720, 1000, 1500, 2000, 3000].forEach(delay => {' -ForegroundColor White
Write-Host '     setTimeout(checkState, delay);' -ForegroundColor White
Write-Host '   });' -ForegroundColor White
Write-Host ""
Write-Host "5. Click an answer button"
Write-Host "6. Watch the console output to see when state changes"
Write-Host "7. Look for [SUBMISSION_STATE] logs to track state changes"
Write-Host ""
Write-Host "Press any key to open browser..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Open browser
Start-Process "http://localhost:3001"