# Test deployment using curl with proper Supabase API

Write-Host "Testing Supabase Edge Function deployment..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"
$functionName = "start-game-handler"

# Get service role key from .env file
$envPath = Join-Path $PSScriptRoot ".env"
$serviceRoleKey = ""

if (Test-Path $envPath) {
    Get-Content $envPath | ForEach-Object {
        if ($_ -match '^SUPABASE_SERVICE_ROLE_KEY=(.+)$') {
            $serviceRoleKey = $matches[1].Trim()
        }
    }
}

if (-not $serviceRoleKey) {
    Write-Host "ERROR: Could not find SUPABASE_SERVICE_ROLE_KEY in .env file" -ForegroundColor Red
    exit 1
}

Write-Host "Service role key found" -ForegroundColor Green

# Test the edge function endpoint first
Write-Host "`nTesting edge function endpoint..." -ForegroundColor Yellow
$testUrl = "https://$projectRef.supabase.co/functions/v1/$functionName"

try {
    $testResponse = Invoke-WebRequest -Uri $testUrl -Method OPTIONS -Headers @{
        "apikey" = $serviceRoleKey
    }
    Write-Host "Edge function endpoint is accessible: $($testResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Edge function test failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host @"

To deploy the edge function:

1. Using Supabase Dashboard (RECOMMENDED):
   - Go to: https://app.supabase.com/project/$projectRef/functions
   - Click on '$functionName' or create new function
   - Copy contents from: $PSScriptRoot\functions\$functionName\index.ts
   - Paste and deploy

2. Using Supabase CLI from WSL/Linux:
   supabase functions deploy $functionName --project-ref $projectRef

3. The edge function code has been updated with the fix for the PlayerQuestion structure.
   Once deployed, the multiplayer game start should work correctly.

"@ -ForegroundColor Cyan

# Open the dashboard in default browser
$dashboardUrl = "https://app.supabase.com/project/$projectRef/functions"
Write-Host "Opening Supabase Dashboard in browser..." -ForegroundColor Yellow
Start-Process $dashboardUrl

# Also open the function file
$functionPath = Join-Path $PSScriptRoot "functions\$functionName\index.ts"
Write-Host "Opening function file in notepad..." -ForegroundColor Yellow
notepad $functionPath