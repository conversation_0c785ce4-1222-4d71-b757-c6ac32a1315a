#!/usr/bin/env node

/**
 * CSS Output Verification Script
 * 
 * This script verifies that the Next.js build process has generated
 * valid CSS files and that they contain substantial content.
 * 
 * Run after: npm run build
 * Purpose: Catch CSS generation failures before deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying CSS output...');

// Configuration
const CSS_MIN_SIZE_BYTES = 1000; // Minimum expected CSS file size (1KB)
const CRITICAL_CSS_CLASSES = [
  'jumbotron-title',
  'game-loading-container', 
  'loading-text',
  'football-spinner'
];

// Paths
const projectRoot = path.join(__dirname, '..');
const nextBuildDir = path.join(projectRoot, '.next');
const staticCssDir = path.join(nextBuildDir, 'static', 'css');

// Check if .next directory exists
if (!fs.existsSync(nextBuildDir)) {
  console.error('🔴 CRITICAL: .next directory not found!');
  console.error('   Run "npm run build" first to generate build output.');
  process.exit(1);
}

// Check if CSS directory exists
if (!fs.existsSync(staticCssDir)) {
  console.error('🔴 CRITICAL: CSS output directory not found!');
  console.error(`   Expected: ${staticCssDir}`);
  console.error('   This indicates CSS generation failed during build.');
  process.exit(1);
}

// Get all CSS files
let cssFiles;
try {
  cssFiles = fs.readdirSync(staticCssDir).filter(f => f.endsWith('.css'));
} catch (err) {
  console.error('🔴 CRITICAL: Cannot read CSS directory!', err.message);
  process.exit(1);
}

if (cssFiles.length === 0) {
  console.error('🔴 CRITICAL: No CSS files found in output directory!');
  console.error(`   Directory checked: ${staticCssDir}`);
  console.error('   This indicates CSS generation completely failed.');
  process.exit(1);
}

console.log(`📁 Found ${cssFiles.length} CSS file(s): ${cssFiles.join(', ')}`);

// Check each CSS file
let hasValidMainCss = false;
let totalCssSize = 0;

for (const cssFile of cssFiles) {
  const cssPath = path.join(staticCssDir, cssFile);
  const stats = fs.statSync(cssPath);
  totalCssSize += stats.size;
  
  console.log(`📄 ${cssFile}: ${stats.size} bytes`);
  
  if (stats.size < CSS_MIN_SIZE_BYTES) {
    console.warn(`⚠️  WARNING: ${cssFile} is suspiciously small (${stats.size} bytes)`);
    console.warn('   This may indicate CSS generation issues.');
  } else {
    hasValidMainCss = true;
  }
}

if (!hasValidMainCss) {
  console.error('🔴 CRITICAL: No CSS files meet minimum size requirements!');
  console.error(`   All CSS files are smaller than ${CSS_MIN_SIZE_BYTES} bytes.`);
  console.error('   This strongly suggests CSS generation failed.');
  process.exit(1);
}

// Check for critical CSS classes in the largest file
const largestCssFile = cssFiles.reduce((largest, current) => {
  const currentPath = path.join(staticCssDir, current);
  const largestPath = path.join(staticCssDir, largest);
  return fs.statSync(currentPath).size > fs.statSync(largestPath).size ? current : largest;
});

const largestCssPath = path.join(staticCssDir, largestCssFile);
const cssContent = fs.readFileSync(largestCssPath, 'utf8');

console.log(`🔍 Checking critical CSS classes in ${largestCssFile}...`);

const missingClasses = [];
for (const className of CRITICAL_CSS_CLASSES) {
  if (!cssContent.includes(className)) {
    missingClasses.push(className);
  }
}

if (missingClasses.length > 0) {
  console.warn('⚠️  WARNING: Some critical CSS classes are missing:');
  missingClasses.forEach(cls => console.warn(`   - .${cls}`));
  console.warn('   This may indicate Tailwind purging or CSS processing issues.');
  console.warn('   Verify that these classes are used in components.');
} else {
  console.log('✅ All critical CSS classes found.');
}

// Final summary
console.log('\n📊 CSS Verification Summary:');
console.log(`   Total CSS files: ${cssFiles.length}`);
console.log(`   Total CSS size: ${Math.round(totalCssSize / 1024 * 100) / 100} KB`);
console.log(`   Largest file: ${largestCssFile} (${Math.round(fs.statSync(largestCssPath).size / 1024 * 100) / 100} KB)`);

if (missingClasses.length === 0 && hasValidMainCss) {
  console.log('🟢 CSS output verification PASSED!');
  process.exit(0);
} else {
  console.log('🟡 CSS output verification completed with WARNINGS.');
  console.log('   Build can proceed, but review warnings above.');
  process.exit(0);
} 