# Puppeteer Testing - Comprehensive Documentation

## Overview
This document provides comprehensive information about setting up and running Puppeteer tests for the Recognition Combine multiplayer game flow, including cross-platform browser detection and troubleshooting.

## Key Findings

### 1. Browser Path Issues (RESOLVED)
**Problem**: The original test hardcoded a Linux path `/usr/bin/chromium-browser` which fails on different environments.

**Solution**: Implemented cross-platform browser detection:
```javascript
// Detect environment and set appropriate browser path
let executablePath;
const platform = os.platform();

// Check if we're in WSL
const isWSL = platform === 'linux' && os.release().toLowerCase().includes('microsoft');

if (isWSL) {
    // In WSL, use the system chromium-browser
    executablePath = '/usr/bin/chromium-browser';
    console.log('Detected WSL environment, using system chromium-browser');
} else if (platform === 'win32') {
    // On Windows, check common Chrome/Edge paths
    const possiblePaths = [
        'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
    ];
    // ... check paths
}
```

### 2. UI Join Flow Issue (UNRESOLVED)
**Problem**: The multiplayer room join flow appears to have changed. The test cannot find a "Join" button for available rooms.

**Observations**:
- Room is visible: "fresh's Game"
- Shows "Players: 0/4" even after host joins
- Available buttons: "Sign Out", "Single-player Mode", "Multiplayer Mode", "Global Top 5 ❯", "Refresh List", "Host Game"
- NO "Join" button appears in the UI

**Attempted Solutions**:
1. Added wait time for player count to update
2. Clicked "Refresh List" button
3. Attempted to click on room element directly
4. Simplified join logic to match working tests

**Current Status**: The test successfully:
- ✅ Launches browsers cross-platform
- ✅ Authenticates both players
- ✅ Creates room with host
- ❌ Guest cannot join room (no Join button found)

### 3. Authentication Flow
The test handles multi-step authentication well:
1. Initial login to the app
2. Secondary login required for multiplayer mode
3. Both players authenticate successfully

## Test Script Location
- Main test: `/mnt/c/Projects/recognition-combine/test-multiplayer-game-flow.js`
- Screenshots saved to: `test-screenshots-game-flow/`

## Running the Test

### Prerequisites
1. Ensure dev server is running: `npm run dev`
2. For WSL: Install chromium-browser: `sudo apt install chromium-browser`
3. For Windows: Ensure Chrome or Edge is installed

### Command
```bash
cd /mnt/c/Projects/recognition-combine
node test-multiplayer-game-flow.js
```

## Known Issues (RESOLVED)

### 1. Missing Join Button ✅ RESOLVED
**Solution**: Room joining requires a two-step process:
1. Click on a room to select it (shows details panel)
2. Click "Join This Room" button in the details panel

The UI incorrectly states "double-click to join" but uses click-then-join pattern.

### 2. Player Count Display ✅ UNDERSTOOD
Rooms show "Players: 0/4" for guests due to RLS (Row Level Security) policies:
- Guests cannot see `game_players` records for rooms they haven't joined
- This is expected behavior for security reasons
- Player count updates correctly after joining

## New Issues Discovered

### 1. Game Start Failure
After successfully joining and marking ready:
- Host clicks "Start Game" button
- Edge Function `start-game-handler` is called
- Game fails to transition to active state
- No player image appears

This requires investigating the Edge Function and game state management.

## Test Results Summary

### ✅ Successful Steps:
1. Cross-platform browser detection works
2. Both players authenticate successfully
3. Host creates room
4. Guest finds and selects room
5. "Join This Room" button appears and works
6. Guest joins room successfully
7. Both players mark ready

### ❌ Remaining Issue:
- Game doesn't start when host clicks "Start Game"
- Appears to be an Edge Function or state management issue

## Recommendations

1. **Fix UI Instructions**: Update "double-click to join" text to accurately describe the click-then-join process

2. **Investigate Game Start Issue**: 
   - Check Edge Function `start-game-handler` logs
   - Verify game state transitions
   - Test if single-player games start correctly

3. **Consider UI Improvements**:
   - Add actual double-click functionality as the UI suggests
   - Show accurate player counts in lobby (if security allows)
   - Make room cards more obviously clickable

## Environment-Specific Notes

### WSL (Windows Subsystem for Linux)
- Uses system-installed chromium-browser at `/usr/bin/chromium-browser`
- Requires `--no-sandbox` flag for proper operation
- May have performance differences compared to native Linux

### Windows
- Automatically detects Chrome or Edge installations
- Searches common installation paths
- Falls back to Puppeteer bundled browser if none found

### macOS/Linux
- Uses Puppeteer bundled browser by default
- Can specify custom browser path if needed

## Debug Information
The test includes extensive logging:
- Browser console messages via `setupConsoleLogging()`
- Screenshot capture at each major step
- Detailed error messages with page state
- Room information before join attempts

## Next Steps
1. Investigate why Join buttons don't appear in the room list
2. Check if the application's room joining mechanism has changed
3. Consider updating the test to match the current UI patterns
4. Add more robust element selection strategies