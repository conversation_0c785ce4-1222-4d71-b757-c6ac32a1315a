// Check Edge Function logs via Supabase Management API
const https = require('https');
const fs = require('fs');
const path = require('path');

// Read the access token
const tokenPath = path.join(process.env.USERPROFILE || process.env.HOME, '.supabase', 'access-token');
let token = '';

try {
  token = fs.readFileSync(tokenPath, 'utf8').trim();
} catch (err) {
  console.error('Could not read access token. Run "npx supabase login" first.');
  process.exit(1);
}

const projectRef = 'xmyxuvuimebjltnaamox';
const functionName = 'start-game-handler';

console.log('Fetching Edge Function logs...\n');

// Get logs from the last 10 minutes
const since = new Date(Date.now() - 10 * 60 * 1000).toISOString();
const apiUrl = `https://api.supabase.com/v1/projects/${projectRef}/functions/${functionName}/logs?since=${since}`;

const options = {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Accept': 'application/json'
  }
};

https.get(apiUrl, options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const logs = JSON.parse(data);
      
      if (logs.error) {
        console.error('Error fetching logs:', logs.error);
        return;
      }
      
      if (!logs.data || logs.data.length === 0) {
        console.log('No recent logs found.');
        console.log('\nNote: Logs might take a few seconds to appear after function execution.');
        return;
      }
      
      console.log(`Found ${logs.data.length} log entries:\n`);
      
      logs.data.forEach((log, index) => {
        console.log(`--- Log Entry ${index + 1} ---`);
        console.log(`Time: ${log.timestamp}`);
        console.log(`Level: ${log.level || 'info'}`);
        console.log(`Message: ${log.message}`);
        if (log.metadata) {
          console.log(`Metadata: ${JSON.stringify(log.metadata, null, 2)}`);
        }
        console.log('');
      });
      
    } catch (err) {
      console.error('Failed to parse response:', err);
      console.log('Raw response:', data);
    }
  });
}).on('error', (err) => {
  console.error('Request failed:', err);
});