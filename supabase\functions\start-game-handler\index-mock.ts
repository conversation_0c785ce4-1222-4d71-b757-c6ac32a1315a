// Mock version of start-game-handler for testing when players_data is empty
// This generates fake questions to test the multiplayer flow

import { serve } from "https://deno.land/std@0.177.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { corsHeaders } from "../_shared/cors.ts"

interface PlayerChoice { 
  name: string; 
  isCorrect: boolean; 
}

interface PlayerQuestion {
  questionId: string;
  correctPlayerId: number;
  imageUrl: string;
  choices: PlayerChoice[];
  correctChoiceName: string;
}

// Mock player data for testing
const MOCK_PLAYERS = [
  { id: 1, name: "<PERSON>", team: "Chiefs" },
  { id: 2, name: "<PERSON>", team: "<PERSON>" },
  { id: 3, name: "<PERSON>", team: "Ravens" },
  { id: 4, name: "<PERSON>", team: "Bengals" },
];

async function generateMockQuestion(): Promise<PlayerQuestion> {
  // Pick a random correct player
  const correctIndex = Math.floor(Math.random() * MOCK_PLAYERS.length);
  const correctPlayer = MOCK_PLAYERS[correctIndex];
  
  // Create choices
  const choices: PlayerChoice[] = MOCK_PLAYERS.map(player => ({
    name: player.name,
    isCorrect: player.id === correctPlayer.id
  }));
  
  // Shuffle choices
  for (let i = choices.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [choices[i], choices[j]] = [choices[j], choices[i]];
  }
  
  return {
    questionId: `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    correctPlayerId: correctPlayer.id,
    imageUrl: `https://placehold.co/400x600/333/fff?text=${encodeURIComponent(correctPlayer.name)}`,
    choices,
    correctChoiceName: correctPlayer.name
  };
}

serve(async (req) => {
  console.log('[EDGE_START_GAME_MOCK] Request received:', req.method, req.url);
  
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    const authHeader = req.headers.get('Authorization');
    
    if (!supabaseUrl || !supabaseServiceRoleKey) {
      throw new Error('Missing environment variables');
    }

    const token = authHeader?.replace('Bearer ', '');
    if (!token) {
      throw new Error('No auth token provided');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
    
    // Verify user
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    if (userError || !user) {
      throw new Error('User not authenticated');
    }

    const { roomId } = await req.json();
    if (!roomId) {
      throw new Error('Room ID is required');
    }

    console.log(`[EDGE_START_GAME_MOCK] Starting game for room ${roomId} by user ${user.id}`);

    // Fetch room data
    const { data: roomData, error: roomError } = await supabase
      .from('game_rooms')
      .select('*, game_players(user_id, is_ready)')
      .eq('id', roomId)
      .single();

    if (roomError || !roomData) {
      throw new Error('Room not found');
    }

    // Verify host
    if (roomData.host_id !== user.id) {
      return new Response(JSON.stringify({ 
        error: 'Only the host can start the game' 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 403,
      });
    }

    // Verify status
    if (roomData.status !== 'waiting') {
      return new Response(JSON.stringify({ 
        error: 'Game already started or finished',
        currentStatus: roomData.status
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 409,
      });
    }

    // Generate mock question
    const firstQuestion = await generateMockQuestion();
    console.log('[EDGE_START_GAME_MOCK] Generated mock question:', firstQuestion);

    // Update room to active
    const now = new Date();
    const roundEndsAt = new Date(now.getTime() + 30000).toISOString();
    
    const updateData = {
      status: 'active',
      game_start_timestamp: now.toISOString(),
      question_started_at: now.toISOString(),
      current_round_number: 1,
      current_question_data: firstQuestion,
      player_scores: {},
      current_round_answers: [],
      current_round_ends_at: roundEndsAt,
      transition_deadline: new Date(now.getTime() + 7000).toISOString(),
      last_activity_timestamp: now.toISOString(),
      original_player_ids: roomData.game_players.map((p: any) => p.user_id),
      timer_type: 'round',
      timer_started_at: now.toISOString(),
      timer_duration_seconds: 7.0,
      first_answer_at: null,
      all_answers_window_seconds: 2.0
    };

    const { error: updateError } = await supabase
      .from('game_rooms')
      .update(updateData)
      .eq('id', roomId)
      .eq('status', 'waiting');

    if (updateError) {
      throw new Error(`Failed to start game: ${updateError.message}`);
    }

    console.log('[EDGE_START_GAME_MOCK] Game started successfully');

    return new Response(JSON.stringify({
      success: true,
      firstQuestion,
      gameStartTime: now.toISOString(),
      roundEndsAt,
      roundNumber: 1
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('[EDGE_START_GAME_MOCK] Error:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'Internal server error' 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});