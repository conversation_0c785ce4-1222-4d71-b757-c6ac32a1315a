/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["lightningcss"],
  // Updated option name per Next.js 15
  skipTrailingSlashRedirect: true,
  generateBuildId: () => 'build',
  webpack: (config) => {
    return config;
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://xmyxuvuimebjltnaamox.supabase.co; object-src 'none';"
          }
        ]
      }
    ];
  }
};

export default nextConfig;