import { useEffect, useRef, useCallback } from 'react';
import { useAuth } from '../providers/AuthProvider';
import supabase from '../lib/supabaseClient';

interface UseHeartbeatOptions {
  roomId: string | null;
  interval?: number;
  onError?: (error: Error) => void;
  onSessionExpired?: () => void;
}

export function useHeartbeat({
  roomId,
  interval = 15000,
  onError,
  onSessionExpired
}: UseHeartbeatOptions) {
  const { user, loading: authLoading } = useAuth();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);
  const maxRetries = 3;

  const sendHeartbeat = useCallback(async () => {
    if (!roomId || !user || authLoading) {
      console.log('[HEARTBEAT] Skipping - conditions not met:', {
        roomId: !!roomId,
        user: !!user,
        authLoading
      });
      return;
    }

    try {
      // Check session validity before sending heartbeat
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session) {
        console.error('[HEARTBEAT] No valid session found');
        onSessionExpired?.();
        return;
      }

      // Check if token is close to expiry (within 5 minutes)
      const expiresAt = session.expires_at;
      const now = Math.floor(Date.now() / 1000);
      const bufferTime = 300; // 5 minutes
      
      if (expiresAt && (expiresAt - now) < bufferTime) {
        console.log('[HEARTBEAT] Token expiring soon, refreshing session');
        const { error: refreshError } = await supabase.auth.refreshSession();
        if (refreshError) {
          console.error('[HEARTBEAT] Failed to refresh session:', refreshError);
          onSessionExpired?.();
          return;
        }
      }

      console.log('[HEARTBEAT] Sending for room', roomId);
      const { error } = await supabase.functions.invoke('heartbeat-handler', {
        body: { roomId, action: 'ping' }
      });

      if (error) {
        console.error('[HEARTBEAT] Error:', error);
        
        // Handle specific error cases
        if ((error as any).status === 401) {
          console.error('[HEARTBEAT] Authentication error - session may have expired');
          onSessionExpired?.();
          
          // Stop heartbeat on auth errors
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          return;
        }

        // Increment retry count for other errors
        retryCountRef.current++;
        
        if (retryCountRef.current >= maxRetries) {
          console.error('[HEARTBEAT] Max retries reached, stopping heartbeat');
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          onError?.(error as Error);
        }
      } else {
        // Reset retry count on success
        retryCountRef.current = 0;
        console.log('[HEARTBEAT] Success');
      }
    } catch (error) {
      console.error('[HEARTBEAT] Unexpected error:', error);
      onError?.(error as Error);
    }
  }, [roomId, user, authLoading, onError, onSessionExpired]);

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Don't start heartbeat if conditions aren't met
    if (!roomId || !user || authLoading) {
      return;
    }

    // Send initial heartbeat after a short delay to ensure auth is ready
    const initialTimeout = setTimeout(() => {
      sendHeartbeat();
    }, 1000);

    // Set up recurring heartbeat
    intervalRef.current = setInterval(sendHeartbeat, interval);

    // Cleanup
    return () => {
      clearTimeout(initialTimeout);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [roomId, user, authLoading, interval, sendHeartbeat]);

  // Return control functions
  return {
    sendHeartbeat,
    stopHeartbeat: useCallback(() => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }, [])
  };
}