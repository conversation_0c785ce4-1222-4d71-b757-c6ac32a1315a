# Simple loop to call transition-monitor for testing
param(
    [string]$SupabaseUrl = "https://xmyxuvuimebjltnaamox.supabase.co",
    [string]$AnonKey = $env:SUPABASE_ANON_KEY,
    [int]$IntervalSeconds = 1
)

if (-not $AnonKey) {
    Write-Host "Error: SUPABASE_ANON_KEY environment variable not set" -ForegroundColor Red
    Write-Host "Set it using: `$env:SUPABASE_ANON_KEY = 'your-anon-key-here'" -ForegroundColor Yellow
    exit 1
}

Write-Host "Starting transition monitor loop..." -ForegroundColor Green
Write-Host "Calling transition-monitor every $IntervalSeconds second(s)" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop" -ForegroundColor Yellow
Write-Host ""

$headers = @{
    "Authorization" = "Bearer $AnonKey"
    "Content-Type" = "application/json"
}

$url = "$SupabaseUrl/functions/v1/transition-monitor"
$callCount = 0

while ($true) {
    try {
        $callCount++
        Write-Host "[$callCount] Calling transition-monitor... " -NoNewline
        
        $response = Invoke-RestMethod -Uri $url -Method POST -Headers $headers -Body "{}"
        
        if ($response.gamesChecked -gt 0 -or $response.transitionsProcessed -gt 0) {
            Write-Host "✓ Checked: $($response.gamesChecked), Processed: $($response.transitionsProcessed)" -ForegroundColor Green
        } else {
            Write-Host "✓ No active games" -ForegroundColor DarkGray
        }
    }
    catch {
        Write-Host "✗ Error: $_" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds $IntervalSeconds
}