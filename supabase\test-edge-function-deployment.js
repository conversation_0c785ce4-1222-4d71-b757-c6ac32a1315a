// Test Edge Function deployment
const https = require('https');

const projectRef = 'xmyxuvuimebjltnaamox';
const functionName = 'start-game-handler';
const functionUrl = `https://${projectRef}.supabase.co/functions/v1/${functionName}`;

// Read service role key from .env file
const fs = require('fs');
const path = require('path');
const envPath = path.join(__dirname, '.env');
const envContent = fs.readFileSync(envPath, 'utf8');
const serviceRoleKey = envContent.match(/SUPABASE_SERVICE_ROLE_KEY=(.+)/)[1].replace(/"/g, '');

console.log('Testing Edge Function deployment...');
console.log(`URL: ${functionUrl}`);
console.log('');

// Test payload
const testPayload = JSON.stringify({
  roomCode: 'TEST123',
  hostPlayerId: 'test-host-id'
});

const options = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${serviceRoleKey}`,
    'Content-Length': Buffer.byteLength(testPayload)
  }
};

const req = https.request(functionUrl, options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  console.log(`Status Message: ${res.statusMessage}`);
  console.log('Headers:', JSON.stringify(res.headers, null, 2));
  console.log('');

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('Response Body:');
    try {
      const parsed = JSON.parse(data);
      console.log(JSON.stringify(parsed, null, 2));
      
      if (res.statusCode === 200 || res.statusCode === 201) {
        console.log('\n✅ Edge Function is deployed and working!');
      } else if (res.statusCode === 400) {
        console.log('\n✅ Edge Function is deployed! (Returned expected error for test payload)');
      } else if (res.statusCode === 500) {
        console.log('\n⚠️  Edge Function is deployed but encountering errors');
        console.log('This might be due to database permissions or other runtime issues');
      }
    } catch (e) {
      console.log(data);
      if (res.statusCode === 404) {
        console.log('\n❌ Edge Function not found - deployment may have failed');
      }
    }
  });
});

req.on('error', (error) => {
  console.error('Request Error:', error);
  console.log('\n❌ Could not reach Edge Function - check deployment');
});

req.write(testPayload);
req.end();

console.log('Sending test request...\n');