import { assertEquals, assertExists } from "https://deno.land/std@0.177.0/testing/asserts.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.0";

const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY") || "";
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

Deno.test("heartbeat-handler: should reject requests without Authorization header", async () => {
  const response = await fetch(`${supabaseUrl}/functions/v1/heartbeat-handler`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      roomId: "test-room-id",
      action: "ping",
    }),
  });

  assertEquals(response.status, 401);
  const data = await response.json();
  assertExists(data.error);
  assertEquals(data.error.includes("User not authenticated"), true);
});

Deno.test("heartbeat-handler: should reject requests with invalid token", async () => {
  const response = await fetch(`${supabaseUrl}/functions/v1/heartbeat-handler`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer invalid-token",
    },
    body: JSON.stringify({
      roomId: "test-room-id",
      action: "ping",
    }),
  });

  assertEquals(response.status, 401);
  const data = await response.json();
  assertExists(data.error);
});

Deno.test("heartbeat-handler: should accept valid authenticated requests", async () => {
  // Create a test user
  const adminClient = createClient(supabaseUrl, supabaseServiceKey);
  const { data: authData, error: authError } = await adminClient.auth.admin.createUser({
    email: `test-${Date.now()}@example.com`,
    password: "test-password-123",
    email_confirm: true,
  });

  if (authError) {
    throw new Error(`Failed to create test user: ${authError.message}`);
  }

  const testUserId = authData.user.id;

  try {
    // Create a test room
    const { data: roomData, error: roomError } = await adminClient
      .from("game_rooms")
      .insert({
        host_id: testUserId,
        status: "waiting",
        settings: {
          maxPlayers: 4,
          questionCount: 10,
          timeLimit: 30,
        },
      })
      .select()
      .single();

    if (roomError) {
      throw new Error(`Failed to create test room: ${roomError.message}`);
    }

    // Add player to room
    const { error: playerError } = await adminClient
      .from("game_players")
      .insert({
        room_id: roomData.id,
        player_id: testUserId,
        player_name: "Test User",
        is_host: true,
        is_ready: true,
        is_connected: true,
      });

    if (playerError) {
      throw new Error(`Failed to add player to room: ${playerError.message}`);
    }

    // Get user token
    const userClient = createClient(supabaseUrl, supabaseAnonKey);
    const { data: sessionData, error: sessionError } = await userClient.auth.signInWithPassword({
      email: authData.user.email!,
      password: "test-password-123",
    });

    if (sessionError) {
      throw new Error(`Failed to sign in: ${sessionError.message}`);
    }

    // Make authenticated heartbeat request
    const response = await fetch(`${supabaseUrl}/functions/v1/heartbeat-handler`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${sessionData.session!.access_token}`,
      },
      body: JSON.stringify({
        roomId: roomData.id,
        action: "ping",
      }),
    });

    assertEquals(response.status, 200);
    const data = await response.json();
    assertEquals(data.success, true);
    assertEquals(data.action, "pong");

    // Verify last_seen_at was updated
    const { data: playerData } = await adminClient
      .from("game_players")
      .select("last_seen_at")
      .eq("room_id", roomData.id)
      .eq("player_id", testUserId)
      .single();

    assertExists(playerData?.last_seen_at);

    // Cleanup
    await adminClient.from("game_players").delete().eq("room_id", roomData.id);
    await adminClient.from("game_rooms").delete().eq("id", roomData.id);
    await adminClient.auth.admin.deleteUser(testUserId);
  } catch (error) {
    // Cleanup on error
    await adminClient.auth.admin.deleteUser(testUserId).catch(() => {});
    throw error;
  }
});

Deno.test("heartbeat-handler: should handle OPTIONS requests for CORS", async () => {
  const response = await fetch(`${supabaseUrl}/functions/v1/heartbeat-handler`, {
    method: "OPTIONS",
    headers: {
      "Origin": "http://localhost:3000",
      "Access-Control-Request-Method": "POST",
      "Access-Control-Request-Headers": "authorization,content-type",
    },
  });

  assertEquals(response.status, 200);
  assertEquals(response.headers.get("Access-Control-Allow-Origin"), "*");
  assertExists(response.headers.get("Access-Control-Allow-Headers"));
  assertExists(response.headers.get("Access-Control-Allow-Methods"));
});

Deno.test("heartbeat-handler: should handle missing roomId", async () => {
  // Create authenticated user
  const userClient = createClient(supabaseUrl, supabaseAnonKey);
  const { data: authData } = await userClient.auth.signUp({
    email: `test-${Date.now()}@example.com`,
    password: "test-password-123",
  });

  if (!authData.session) {
    throw new Error("Failed to create session");
  }

  const response = await fetch(`${supabaseUrl}/functions/v1/heartbeat-handler`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${authData.session.access_token}`,
    },
    body: JSON.stringify({
      action: "ping",
      // roomId is missing
    }),
  });

  assertEquals(response.status, 400);
  const data = await response.json();
  assertExists(data.error);
  assertEquals(data.error.includes("Missing roomId"), true);

  // Cleanup
  const adminClient = createClient(supabaseUrl, supabaseServiceKey);
  await adminClient.auth.admin.deleteUser(authData.user!.id);
});

Deno.test("heartbeat-handler: should handle non-existent room", async () => {
  // Create authenticated user
  const userClient = createClient(supabaseUrl, supabaseAnonKey);
  const { data: authData } = await userClient.auth.signUp({
    email: `test-${Date.now()}@example.com`,
    password: "test-password-123",
  });

  if (!authData.session) {
    throw new Error("Failed to create session");
  }

  const response = await fetch(`${supabaseUrl}/functions/v1/heartbeat-handler`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${authData.session.access_token}`,
    },
    body: JSON.stringify({
      roomId: "00000000-0000-0000-0000-000000000000", // Non-existent room
      action: "ping",
    }),
  });

  assertEquals(response.status, 404);
  const data = await response.json();
  assertExists(data.error);
  assertEquals(data.error.includes("Room not found"), true);

  // Cleanup
  const adminClient = createClient(supabaseUrl, supabaseServiceKey);
  await adminClient.auth.admin.deleteUser(authData.user!.id);
});