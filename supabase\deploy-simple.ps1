Write-Host "Deploying start-game-handler..." -ForegroundColor Green

Set-Location "C:\Projects\recognition-combine\supabase"

try {
    "1" | supabase functions deploy start-game-handler
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: start-game-handler deployed!" -ForegroundColor Green
    } else {
        Write-Host "FAILED: Deployment failed" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Deployment process completed." -ForegroundColor Cyan 