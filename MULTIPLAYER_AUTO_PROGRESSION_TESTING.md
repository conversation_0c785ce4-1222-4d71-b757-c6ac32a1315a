# Multiplayer Auto-Progression Testing Guide

## Feature Overview
The multiplayer game now automatically progresses to the next question 2.3 seconds after all players have submitted their answers. This eliminates the need for the host to manually click "Next Question" for every round.

## Implementation Details

### Key Changes
1. Added auto-progression logic in `web-app/src/app/page.tsx` (lines 2628-2679)
2. Only the host triggers auto-progression to avoid duplicate calls
3. 2.3 second delay gives players time to see results
4. Timer is cancelled if host manually advances before it expires

### Code Location
- **Main Logic**: `web-app/src/app/page.tsx` - useEffect starting at line 2628
- **Tests**: 
  - Unit tests: `web-app/src/tests/multiplayer-next-question.test.ts`
  - Integration tests: `web-app/src/tests/multiplayer-next-question-integration.test.ts`

## Manual Testing Steps

### Basic Auto-Progression Test
1. Start the development server: `npm run dev`
2. Open 3 browser tabs (use incognito/private windows for separate sessions)
3. In Tab 1 (Host):
   - Click "Multiplayer Mode"
   - Enter name and create room
   - Note the room code
4. In Tabs 2 & 3 (Players):
   - Click "Multiplayer Mode"
   - Enter names and join with room code
5. All players click "Ready"
6. Host clicks "Start Game"
7. When question appears, all players submit different answers
8. **Expected**: After 2.3 seconds, the game automatically advances to the next question

### Tab Focus/Blur Test
1. Follow steps 1-7 above
2. After all players submit answers, immediately alt-tab away from the host's tab
3. Wait 3 seconds
4. Alt-tab back to the host's tab
5. **Expected**: The next question should have loaded automatically

### Manual Override Test
1. Follow steps 1-7 above
2. After all players submit answers, host immediately clicks "Next Question" (within 2.3 seconds)
3. **Expected**: Game advances immediately, no double-progression occurs

### Partial Submission Test
1. Set up 3-player game as above
2. Only 2 players submit answers
3. Wait 5+ seconds
4. **Expected**: No auto-progression occurs; waiting for all players

### Console Logging
Open browser DevTools to see auto-progression logs:
```
[AUTO_PROGRESSION] Checking conditions: {...}
[AUTO_PROGRESSION] All players submitted! Starting 2.3 second timer for auto-progression...
[AUTO_PROGRESSION] Timer expired, auto-advancing to next question...
```

## Automated Testing

### Run Unit Tests
```bash
cd web-app
npm test -- multiplayer-next-question.test.ts
```

### Run Integration Tests (requires Playwright)
```bash
cd web-app
npx playwright test multiplayer-next-question-integration.test.ts
```

### Run PowerShell Test Script
```powershell
.\test-multiplayer-auto-progression.ps1
```

## Troubleshooting

### Issue: Auto-progression not working
1. Check console for `[AUTO_PROGRESSION]` logs
2. Verify all players have submitted (check `answersForCurrentQuestion.length`)
3. Ensure you're testing as the host (only host triggers auto-progression)

### Issue: Double progression
1. Check if `isAdvancingToNextQuestion` flag is properly set
2. Verify timer cleanup in useEffect return function

### Issue: Tab switching breaks progression
1. Check visibility change handler isn't interfering
2. Verify `allPlayersSubmitted` check in visibility handler (line 2604)

## Performance Considerations
- Timer is only active when all conditions are met
- Cleanup prevents memory leaks
- Only host processes auto-progression logic
- No impact on single-player modes