Write-Host "=== Testing HOST-Specific Realtime Logging ===" -ForegroundColor Cyan
Write-Host ""

Write-Host "This script will help you test the enhanced Realtime logging with HOST-specific prefixes." -ForegroundColor Green
Write-Host ""

Write-Host "Expected logging patterns to look for:" -ForegroundColor Yellow
Write-Host "- '[Realtime HOST] game_players change received:' when you are the host" -ForegroundColor White
Write-Host "- '[Realtime] game_players change received:' when you are NOT the host" -ForegroundColor White
Write-Host "- '[Realtime HOST] New player joined room - Host perspective:' for host-specific join events" -ForegroundColor White
Write-Host "- '[Realtime HOST] Player state changed - Host perspective:' for host-specific ready state changes" -ForegroundColor White
Write-Host "- '[Realtime HOST] Player left room - Host perspective:' for host-specific departure events" -ForegroundColor White
Write-Host ""

Write-Host "Testing steps:" -ForegroundColor Yellow
Write-Host "1. Open browser console (F12 -> Console tab)" -ForegroundColor White
Write-Host "2. Create a room (you will be the host)" -ForegroundColor White
Write-Host "3. Look for '[Realtime HOST]' prefixed logs" -ForegroundColor White
Write-Host "4. Have another user join your room" -ForegroundColor White
Write-Host "5. Toggle ready states and observe the detailed host perspective logs" -ForegroundColor White
Write-Host "6. Have the other user leave and observe departure logs" -ForegroundColor White
Write-Host "7. Join someone else's room (you will NOT be host)" -ForegroundColor White
Write-Host "8. Look for '[Realtime]' prefixed logs (without HOST)" -ForegroundColor White
Write-Host ""

Write-Host "Key features to verify:" -ForegroundColor Yellow
Write-Host "- Host logs include additional context like 'allPlayersReady', 'totalPlayers'" -ForegroundColor White
Write-Host "- Host logs show perspective-specific information for game management" -ForegroundColor White
Write-Host "- Non-host logs are standard without the extra host-specific details" -ForegroundColor White
Write-Host "- All logs include isCurrentUserHost, currentUserId, and roomHostId for debugging" -ForegroundColor White
Write-Host ""

Write-Host "Starting the web application..." -ForegroundColor Green

# Change to web-app directory and start the development server
Set-Location "web-app"

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Start the development server
Write-Host "Starting Next.js development server..." -ForegroundColor Green
npm run dev 