﻿curl -X POST https://xmyxuvuimebjltnaamox.supabase.co/functions/v1/deploy \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "start-game-handler",
    "code": "// Follow this setup guide to integrate the Deno language server with your editor:\n// https://deno.land/manual/getting_started/setup_your_environment\n// This enables autocomplete, go to definition, etc.\n\n// Setup type definitions for built-in Supabase Runtime APIs\nimport \"jsr:@supabase/functions-js/edge-runtime.d.ts\"\n\nimport { serve } from 'https://deno.land/std@0.177.0/http/server.ts'\nimport { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'\nimport { corsHeaders } from '../_shared/cors.ts'\n\n// --- Helper: Simulate fetching all player data (adapt to your actual playerData source) ---\ninterface PlayerData { \n  id: number; \n  team_name: string;\n  player_name: string; \n  local_image_path: string | null; // This will be the path relative to /public/players_images/\n  jersey_number: string | number | null;\n  position: string | null;\n  height: string | null;\n  weight: string | number | null;\n  age_or_dob: string | number | null;\n  experience: string | number | null;\n  college: string | null;\n}\n\ninterface PlayerChoice { \n  name: string; \n  isCorrect: boolean; \n}\n\n// Match the frontend's expected structure for multiplayer\ninterface PlayerQuestion {\n  questionId: string;\n  correctPlayerId: number;\n  imageUrl: string;\n  choices: PlayerChoice[];\n  correctChoiceName: string;\n}\n\nasync function generateNewQuestionForRoom(supabaseAdmin: SupabaseClient, roomId: string): Promise<PlayerQuestion | null> {\n  console.log('[EDGE_START_GAME] generateNewQuestionForRoom called for room:', roomId);\n  \n  try {\n    // Query real player data from the players_data table\n    console.log('[EDGE_START_GAME] Fetching players from players_data table...');\n    const { data: playersData, error: fetchError } = await supabaseAdmin\n      .from('players_data')\n      .select('id, team_name, player_name, local_image_path, jersey_number, position, height, weight, age_or_dob, experience, college')\n      .not('local_image_path', 'is', null)\n      .limit(100); // Get a reasonable sample of players with images\n\n    if (fetchError) {\n      console.error('[EDGE_START_GAME] Error fetching players from database:', {\n        message: fetchError.message,\n        code: fetchError.code,\n        details: fetchError.details,\n        hint: fetchError.hint,\n        // Log full error object for debugging\n        fullError: JSON.stringify(fetchError)\n      });\n      // Fallback to sample data if database query fails\n      const samplePlayers: PlayerData[] = [\n        { id: 1001, player_name: 'Sample Player Alpha', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '99', position: 'QB', height: '6-2', weight: 210, age_or_dob: 25, experience: '3', college: 'Sample U' },\n        { id: 1002, player_name: 'Sample Player Bravo', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '88', position: 'WR', height: '6-0', weight: 195, age_or_dob: 23, experience: 'R', college: 'Sample State' },\n        { id: 1003, player_name: 'Sample Player Charlie', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '77', position: 'LB', height: '6-3', weight: 245, age_or_dob: 28, experience: '6', college: 'Sample Tech' },\n        { id: 1004, player_name: 'Sample Player Delta', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '22', position: 'RB', height: '5-11', weight: 215, age_or_dob: 24, experience: '2', college: 'Sample College' },\n        { id: 1005, player_name: 'Sample Player Echo', team_name: 'Atlanta Falcons', local_image_path: 'atlanta-falcons/nick-kubitz.jpg', jersey_number: '55', position: 'DE', height: '6-5', weight: 275, age_or_dob: 26, experience: '4', college: 'Sample University' },\n      ];\n      console.log('[EDGE_START_GAME] Using fallback sample players due to database error');\n      const shuffledPlayers = [...samplePlayers].sort(() => 0.5 - Math.random());\n      const correctPlayer = shuffledPlayers[0];\n      const distractors = shuffledPlayers.slice(1, 4);\n      \n      const choices: PlayerChoice[] = [\n        { name: correctPlayer.player_name, isCorrect: true },\n        ...distractors.map(p => ({ name: p.player_name, isCorrect: false }))\n      ].sort(() => 0.5 - Math.random());\n\n      return {\n        questionId: crypto.randomUUID(),\n        correctPlayerId: correctPlayer.id,\n        imageUrl: `/players_images/${correctPlayer.local_image_path}`,\n        choices: choices,\n        correctChoiceName: correctPlayer.player_name\n      };\n    }\n\n    if (!playersData || playersData.length < 4) {\n      console.error(`[EDGE_START_GAME] Not enough players in database. Found: ${playersData?.length || 0}`);\n      return null;\n    }\n\n    console.log(`[EDGE_START_GAME] Successfully fetched ${playersData.length} players from database`);\n\n    // Shuffle players to pick a correct answer and distractors\n    console.log('[EDGE_START_GAME] Shuffling players and selecting correct answer...');\n    const shuffledPlayers = [...playersData].sort(() => 0.5 - Math.random());\n    const correctPlayer = shuffledPlayers[0];\n    const distractors = shuffledPlayers.slice(1, 4); // Need 3 distractors\n\n    console.log(`[EDGE_START_GAME] Correct player selected: ${correctPlayer.player_name} (ID: ${correctPlayer.id})`);\n    console.log(`[EDGE_START_GAME] Distractors selected: ${distractors.map(p => p.player_name).join(', ')}`);\n\n    if (distractors.length < 3) {\n      console.error('[EDGE_START_GAME] Could not get enough distractors from players');\n      return null;\n    }\n\n    const choices: PlayerChoice[] = [\n      { name: correctPlayer.player_name, isCorrect: true },\n      ...distractors.map(p => ({ name: p.player_name, isCorrect: false }))\n    ].sort(() => 0.5 - Math.random()); // Shuffle choices\n\n    console.log(`[EDGE_START_GAME] Choices created and shuffled: ${choices.map(c => `${c.name}(${c.isCorrect ? 'CORRECT' : 'wrong'})`).join(', ')}`);\n\n    const question: PlayerQuestion = {\n      questionId: crypto.randomUUID(),\n      correctPlayerId: correctPlayer.id,\n      // Construct the full image URL for the client\n      imageUrl: `/players_images/${correctPlayer.local_image_path}`,\n      choices: choices,\n      correctChoiceName: correctPlayer.player_name // Store the correct choice name\n    };\n    \n    console.log('[EDGE_START_GAME] Generated question successfully:', {\n      questionId: question.questionId,\n      correctPlayerId: question.correctPlayerId,\n      imageUrl: question.imageUrl,\n      correctChoiceName: question.correctChoiceName,\n      totalChoices: question.choices.length\n    });\n    return question;\n  } catch (error) {\n    console.error('[EDGE_START_GAME] Unexpected error in generateNewQuestionForRoom:', error);\n    return null;\n  }\n}\n\nconsole.log('[EDGE_FN_LOAD] start-game-handler function script loaded.'); // Log when script itself is loaded\n\ninterface StartGameRequestBody {\n  roomId?: string;\n}\n\nserve(async (req: Request) => {\n  console.log(`[EDGE_START_GAME] Request received. Method: ${req.method}, URL: ${req.url}`);\n  \n  if (req.method === 'OPTIONS') {\n    console.log('[EDGE_START_GAME] OPTIONS request, responding with CORS.');\n    return new Response('ok', { headers: corsHeaders })\n  }\n\n  try {\n    console.log('[EDGE_START_GAME] Processing POST request for start-game-handler');\n    \n    // Environment variable checks\n    const supabaseUrl = Deno.env.get('SUPABASE_URL')\n    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')\n    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')\n\n    if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceRoleKey) {\n      console.error('[EDGE_START_GAME] CRITICAL: Missing environment variables');\n      console.error('[EDGE_START_GAME] SUPABASE_URL:', supabaseUrl ? 'Present' : 'MISSING');\n      console.error('[EDGE_START_GAME] SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Present' : 'MISSING');\n      console.error('[EDGE_START_GAME] SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceRoleKey ? 'Present' : 'MISSING');\n      throw new Error('Server configuration error: Missing Supabase credentials.')\n    }\n    console.log('[EDGE_START_GAME] Environment variables loaded successfully');\n    \n    // Parse request body\n    console.log('[EDGE_START_GAME] Parsing request body...');\n    const requestBody: StartGameRequestBody = await req.json()\n    console.log('[EDGE_START_GAME] Parsed request body:', requestBody);\n    const { roomId } = requestBody;\n\n    if (!roomId) {\n      console.error('[EDGE_START_GAME] Missing roomId in request body');\n      throw new Error('Room ID is required.')\n    }\n    console.log(`[EDGE_START_GAME] Processing start game for room ID: ${roomId}`);\n    \n    // Authentication\n    const authHeader = req.headers.get('Authorization')\n    if (!authHeader) {\n      console.error('[EDGE_START_GAME] Missing Authorization header');\n      throw new Error('User not authenticated: Missing Authorization header.')\n    }\n    console.log('[EDGE_START_GAME] Authorization header present, verifying user...');\n\n    const userClient = createClient(supabaseUrl, supabaseAnonKey, {\n      global: { headers: { Authorization: authHeader } },\n    })\n    console.log('[EDGE_START_GAME] User client created, getting user...');\n    \n    const { data: { user }, error: userError } = await userClient.auth.getUser()\n    if (userError) {\n      console.error('[EDGE_START_GAME] User authentication error:', userError);\n      throw new Error('User not authenticated: ' + userError.message)\n    }\n    if (!user) {\n      console.error('[EDGE_START_GAME] No user found in session');\n      throw new Error('User not authenticated or not found.')\n    }\n    console.log(`[EDGE_START_GAME] User authenticated successfully. User ID: ${user.id}`);\n    const requesterId = user.id\n\n    // Create admin client\n    console.log('[EDGE_START_GAME] Creating admin client...');\n    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)\n    console.log('[EDGE_START_GAME] Admin client created successfully');\n\n    console.log(`[EDGE_START_GAME] Processing start game request for room ${roomId} by user ${requesterId}`)\n\n    // CRITICAL: Fetch current room state first for optimistic locking validation\n    console.log(`[EDGE_START_GAME] [OPTIMISTIC_LOCK_SETUP] Fetching current room state for validation and race condition detection...`);\n    const { data: roomData, error: roomFetchError } = await supabaseAdmin\n      .from('game_rooms')\n      .select('*, game_players(user_id, is_ready)') // Fetch nested players\n      .eq('id', roomId)\n      .single()\n\n    if (roomFetchError) {\n      console.error(`[EDGE_START_GAME] Database error fetching room ${roomId}:`, roomFetchError);\n      throw new Error('Database error: ' + roomFetchError.message)\n    }\n    if (!roomData) {\n      console.error(`[EDGE_START_GAME] Room ${roomId} not found in database`);\n      throw new Error('Room not found.')\n    }\n    console.log(`[EDGE_START_GAME] [ROOM_STATE_SNAPSHOT] Current room state:`, {\n      id: roomData.id,\n      host_id: roomData.host_id,\n      status: roomData.status,\n      playersCount: roomData.game_players?.length || 0,\n      created_at: roomData.created_at,\n      game_start_timestamp: roomData.game_start_timestamp,\n      last_activity_timestamp: roomData.last_activity_timestamp\n    });\n\n    // 1. Verify caller is the host\n    console.log(`[EDGE_START_GAME] Verifying host permissions. Room host: ${roomData.host_id}, Requester: ${requesterId}`);\n    if (roomData.host_id !== requesterId) {\n      console.error(`[EDGE_START_GAME] Permission denied. User ${requesterId} is not host of room ${roomId} (host: ${roomData.host_id})`);\n      return new Response(JSON.stringify({ \n        error: 'Only the host can start the game.',\n        details: `User ${requesterId} is not the host of room ${roomId}. Host is: ${roomData.host_id}`\n      }), {\n        headers: { ...corsHeaders, 'Content-Type': 'application/json' },\n        status: 403, // 403 Forbidden for permission issues\n      });\n    }\n    console.log('[EDGE_START_GAME] Host verification passed');\n\n    // 2. CRITICAL: Verify game is in 'waiting' status with enhanced race condition detection\n    console.log(`[EDGE_START_GAME] [STATUS_VALIDATION] Checking room status. Current status: ${roomData.status}`);\n    if (roomData.status !== 'waiting') {\n      const errorMessage = `Invalid room status. Expected: 'waiting', Actual: '${roomData.status}'. Game may have already started or is finished.`;\n      console.error(`[EDGE_START_GAME] ${errorMessage}`);\n      \n      // Enhanced 409 response with comprehensive race condition analysis\n      const timeElapsedSinceCreation = roomData.created_at ? Date.now() - new Date(roomData.created_at).getTime() : null;\n      const gameStartTime = roomData.game_start_timestamp ? new Date(roomData.game_start_timestamp).toISOString() : null;\n      const timeSinceGameStart = gameStartTime ? Date.now() - new Date(gameStartTime).getTime() : null;\n      \n      console.error(`[EDGE_START_GAME] [RACE_CONDITION_ANALYSIS] Room status conflict analysis:`, {\n        roomId,\n        currentStatus: roomData.status,\n        expectedStatus: 'waiting',\n        timeElapsedSinceCreation: timeElapsedSinceCreation ? `${Math.round(timeElapsedSinceCreation / 1000)}s` : 'unknown',\n        gameStartTimestamp: gameStartTime,\n        timeSinceGameStart: timeSinceGameStart ? `${Math.round(timeSinceGameStart / 1000)}s ago` : 'N/A',\n        requesterId,\n        hostId: roomData.host_id,\n        timestamp: new Date().toISOString(),\n        isRecentStart: timeSinceGameStart ? timeSinceGameStart < 30000 : false // Within last 30 seconds\n      });\n      \n      // Determine conflict type for better client handling\n      let conflictType = 'INVALID_STATUS';\n      let suggestion = 'Room is in an invalid state for starting a game.';\n      \n      if (roomData.status === 'active') {\n        conflictType = timeSinceGameStart && timeSinceGameStart < 30000 ? 'RACE_CONDITION_ALREADY_STARTED' : 'GAME_ALREADY_ACTIVE';\n        suggestion = timeSinceGameStart && timeSinceGameStart < 30000 \n          ? 'The game has already been started, possibly by a concurrent request.' \n          : 'The game was started earlier and is currently active.';\n      }\n      \n      return new Response(JSON.stringify({ \n        error: 'Game has already started or cannot be started in current state.',\n        details: errorMessage,\n        currentStatus: roomData.status,\n        gameStartTime: gameStartTime,\n        conflictType: conflictType,\n        suggestion: suggestion,\n        timestamp: new Date().toISOString()\n      }), {\n        headers: { ...corsHeaders, 'Content-Type': 'application/json' },\n        status: 409, // 409 Conflict is appropriate for \"already started\"\n      });\n    }\n    console.log('[EDGE_START_GAME] Room status verification passed');\n\n    // 3. Verify player conditions (readiness, minimum count)\n    const playersInRoom = roomData.game_players || [];\n    console.log(`[EDGE_START_GAME] [PLAYER_VALIDATION] Checking player readiness. Players in room: ${playersInRoom.length}`);\n    console.log(`[EDGE_START_GAME] Player details:`, playersInRoom.map(p => ({ user_id: p.user_id, is_ready: p.is_ready })));\n    \n    const allPlayersReady = playersInRoom.length > 0 && playersInRoom.every((p: any) => p.is_ready);\n    const minPlayersMet = playersInRoom.length >= 2; // Changed back to 2 for production\n    const readyPlayerCount = playersInRoom.filter((p: any) => p.is_ready).length;\n    const notReadyPlayerCount = playersInRoom.length - readyPlayerCount;\n\n    console.log(`[EDGE_START_GAME] Player readiness analysis:`, {\n      totalPlayers: playersInRoom.length,\n      readyPlayers: readyPlayerCount,\n      notReadyPlayers: notReadyPlayerCount,\n      allPlayersReady,\n      minPlayersMet,\n      requiredMinimum: 2\n    });\n\n    if (!allPlayersReady) {\n      console.error('[EDGE_START_GAME] Not all players are ready');\n      return new Response(JSON.stringify({ \n        error: 'Not all players are ready.',\n        details: `${notReadyPlayerCount} out of ${playersInRoom.length} players are not ready.`,\n        readyCount: readyPlayerCount,\n        totalCount: playersInRoom.length\n      }), {\n        headers: { ...corsHeaders, 'Content-Type': 'application/json' },\n        status: 400, // 400 Bad Request for validation failures\n      });\n    }\n    if (!minPlayersMet) {\n      console.error('[EDGE_START_GAME] Minimum players not met');\n      return new Response(JSON.stringify({ \n        error: 'Minimum players not met.',\n        details: `Need at least 2 players, but only ${playersInRoom.length} found.`,\n        currentCount: playersInRoom.length,\n        requiredCount: 2\n      }), {\n        headers: { ...corsHeaders, 'Content-Type': 'application/json' },\n        status: 400, // 400 Bad Request for validation failures\n      });\n    }\n    console.log('[EDGE_START_GAME] All player requirements satisfied');\n\n    // 4. Generate the first question\n    console.log('[EDGE_START_GAME] Generating first question...');\n    const firstQuestion = await generateNewQuestionForRoom(supabaseAdmin, roomId);\n    if (!firstQuestion) {\n      console.error(`[EDGE_START_GAME] Failed to generate first question for room ${roomId}`);\n      throw new Error('Failed to generate the first question for the game.');\n    }\n    console.log(`[EDGE_START_GAME] First question generated successfully:`, {\n      correctPlayerName: firstQuestion.correctPlayer.player_name,\n      correctPlayerId: firstQuestion.correctPlayer.id,\n      imageUrl: firstQuestion.imageUrl,\n      choicesCount: firstQuestion.choices.length\n    });\n\n    // 5. CRITICAL: Prepare original_player_ids for rejoin logic\n    console.log('[EDGE_START_GAME] [ORIGINAL_PLAYERS] Preparing original_player_ids for rejoin logic...');\n    const allOriginalPlayerIds = playersInRoom.map((p: any) => p.user_id);\n    console.log('[EDGE_START_GAME] [ORIGINAL_PLAYERS] Current players at game start:', JSON.stringify(allOriginalPlayerIds));\n    console.log('[EDGE_START_GAME] [ORIGINAL_PLAYERS] Player details:', playersInRoom.map((p: any) => ({ \n      user_id: p.user_id, \n      is_ready: p.is_ready \n    })));\n\n    // CRITICAL: Validate allOriginalPlayerIds is a proper array\n    if (!Array.isArray(allOriginalPlayerIds)) {\n      console.error('[EDGE_START_GAME] CRITICAL ERROR: allOriginalPlayerIds is not an array:', allOriginalPlayerIds);\n      throw new Error('Invalid player data: Unable to extract player IDs for game start');\n    }\n    \n    if (allOriginalPlayerIds.length === 0) {\n      console.error('[EDGE_START_GAME] CRITICAL ERROR: No player IDs found for game start');\n      throw new Error('Invalid game state: No players found to start game');\n    }\n    \n    // Validate all player IDs are strings\n    const invalidPlayerIds = allOriginalPlayerIds.filter(id => typeof id !== 'string' || !id.trim());\n    if (invalidPlayerIds.length > 0) {\n      console.error('[EDGE_START_GAME] CRITICAL ERROR: Invalid player IDs found:', invalidPlayerIds);\n      throw new Error('Invalid player data: Some player IDs are not valid strings');\n    }\n    \n    console.log('[EDGE_START_GAME] [VALIDATION_SUCCESS] allOriginalPlayerIds validated successfully:', {\n      count: allOriginalPlayerIds.length,\n      playerIds: allOriginalPlayerIds\n    });\n\n    // 6. CRITICAL: Atomic update with optimistic locking\n    console.log('[EDGE_START_GAME] [ATOMIC_UPDATE] Preparing game start update...');\n    const roundDurationSeconds = 30; \n    const now = new Date();\n    const roundEndsAt = new Date(now.getTime() + roundDurationSeconds * 1000).toISOString();\n\n    // CRITICAL: Validate the first question data before using it\n    if (!firstQuestion || !firstQuestion.correctPlayer || !firstQuestion.imageUrl || !Array.isArray(firstQuestion.choices)) {\n      console.error('[EDGE_START_GAME] CRITICAL ERROR: Invalid first question data:', firstQuestion);\n      throw new Error('Invalid question data: Generated question is incomplete or malformed');\n    }\n    \n    console.log('[EDGE_START_GAME] [QUESTION_VALIDATION] First question validated successfully:', {\n      correctPlayerName: firstQuestion.correctPlayer.player_name,\n      correctPlayerId: firstQuestion.correctPlayer.id,\n      imageUrl: firstQuestion.imageUrl,\n      choicesCount: firstQuestion.choices.length\n    });\n\n    const updateData = {\n      status: 'active' as const,\n      game_start_timestamp: now.toISOString(),\n      question_started_at: now.toISOString(), // Track when this question starts\n      current_round_number: 1,\n      current_question_data: firstQuestion as any,\n      player_scores: {} as Record<string, number>, \n      current_round_answers: [] as any[], \n      current_round_ends_at: roundEndsAt,\n      transition_deadline: new Date(now.getTime() + 7000).toISOString(), // 7-second hard cap for first question\n      last_activity_timestamp: now.toISOString(),\n      original_player_ids: allOriginalPlayerIds, // CRITICAL: Set this for rejoin logic!\n      // Timer state for visual countdown\n      timer_type: 'round' as const,\n      timer_started_at: now.toISOString(),\n      timer_duration_seconds: 7.0,\n      // Initialize answer timing fields\n      first_answer_at: null,\n      all_answers_window_seconds: 2.0\n    };\n    \n    // CRITICAL: Validate the update data structure before attempting database update\n    console.log('[EDGE_START_GAME] [UPDATE_DATA_VALIDATION] Validating update data structure...');\n    \n    // Validate required fields are present and correct types\n    const validationErrors: string[] = [];\n    \n    if (updateData.status !== 'active') validationErrors.push('status must be \"active\"');\n    if (!updateData.game_start_timestamp || typeof updateData.game_start_timestamp !== 'string') validationErrors.push('game_start_timestamp must be a valid ISO string');\n    if (typeof updateData.current_round_number !== 'number' || updateData.current_round_number < 1) validationErrors.push('current_round_number must be a positive number');\n    if (!updateData.current_question_data) validationErrors.push('current_question_data cannot be null/undefined');\n    if (typeof updateData.player_scores !== 'object' || updateData.player_scores === null) validationErrors.push('player_scores must be an object');\n    if (!Array.isArray(updateData.current_round_answers)) validationErrors.push('current_round_answers must be an array');\n    if (!updateData.current_round_ends_at || typeof updateData.current_round_ends_at !== 'string') validationErrors.push('current_round_ends_at must be a valid ISO string');\n    if (!updateData.last_activity_timestamp || typeof updateData.last_activity_timestamp !== 'string') validationErrors.push('last_activity_timestamp must be a valid ISO string');\n    if (!Array.isArray(updateData.original_player_ids) || updateData.original_player_ids.length === 0) validationErrors.push('original_player_ids must be a non-empty array');\n    \n    if (validationErrors.length > 0) {\n      console.error('[EDGE_START_GAME] CRITICAL ERROR: Update data validation failed:', {\n        errors: validationErrors,\n        updateData: JSON.stringify(updateData, null, 2)\n      });\n      throw new Error(`Invalid update data: ${validationErrors.join(', ')}`);\n    }\n    \n    console.log('[EDGE_START_GAME] [UPDATE_DATA_VALIDATION] Update data validated successfully');\n    console.log('[EDGE_START_GAME] [ATOMIC_UPDATE] Update data prepared:', {\n      status: updateData.status,\n      game_start_timestamp: updateData.game_start_timestamp,\n      current_round_number: updateData.current_round_number,\n      current_question_data: `[Question with ${firstQuestion.choices.length} choices]`, // Abbreviated for logging\n      player_scores: JSON.stringify(updateData.player_scores),\n      current_round_answers_length: updateData.current_round_answers.length,\n      current_round_ends_at: updateData.current_round_ends_at,\n      last_activity_timestamp: updateData.last_activity_timestamp,\n      original_player_ids: JSON.stringify(updateData.original_player_ids) // Log the critical field\n    });\n\n    // CRITICAL: Use optimistic locking to prevent race conditions - only update if status is still 'waiting'\n    console.log(`[EDGE_START_GAME] [OPTIMISTIC_LOCK] Attempting atomic update - ensuring room ${roomId} status is still 'waiting'...`);\n    \n    try {\n      const { data: updateResult, error: updateRoomError } = await supabaseAdmin\n        .from('game_rooms')\n        .update(updateData)\n        .eq('id', roomId)\n        .eq('status', 'waiting') // CRITICAL: Only update if status is still 'waiting'\n        .select('id, status, game_start_timestamp, current_round_number, original_player_ids') // Return updated data to verify\n\n      if (updateRoomError) {\n        console.error(`[EDGE_START_GAME] Database error updating room ${roomId}:`, {\n          error: updateRoomError,\n          message: updateRoomError.message,\n          code: updateRoomError.code,\n          details: updateRoomError.details,\n          hint: updateRoomError.hint\n        });\n        throw new Error(`Failed to start game - database update error: ${updateRoomError.message}`)\n      }\n\n      // CRITICAL: Check if the update actually affected any rows (optimistic lock check)\n      if (!updateResult || updateResult.length === 0) {\n        console.error(`[EDGE_START_GAME] [OPTIMISTIC_LOCK_FAILED] Room ${roomId} was not updated - status may have changed concurrently`);\n        \n        // Fetch current room state to understand what happened\n        const { data: currentRoomState } = await supabaseAdmin\n          .from('game_rooms')\n          .select('id, status, game_start_timestamp, host_id, last_activity_timestamp')\n          .eq('id', roomId)\n          .single();\n          \n        console.error(`[EDGE_START_GAME] [CONCURRENT_MODIFICATION] Current room state after failed update:`, currentRoomState);\n        \n        // Enhanced conflict response for better client handling\n        const gameStartTime = currentRoomState?.game_start_timestamp ? new Date(currentRoomState.game_start_timestamp).toISOString() : null;\n        const timeSinceStart = gameStartTime ? Date.now() - new Date(gameStartTime).getTime() : null;\n        \n        return new Response(JSON.stringify({ \n          error: 'Game has already started or cannot be started in current state.',\n          details: `Room status changed during update attempt. Current status: ${currentRoomState?.status || 'unknown'}`,\n          currentStatus: currentRoomState?.status,\n          gameStartTime: gameStartTime,\n          conflictType: 'CONCURRENT_START_ATTEMPT',\n          suggestion: 'Another request may have started the game simultaneously.',\n          timeSinceStart: timeSinceStart ? `${Math.round(timeSinceStart / 1000)}s ago` : 'unknown',\n          timestamp: new Date().toISOString()\n        }), {\n          headers: { ...corsHeaders, 'Content-Type': 'application/json' },\n          status: 409, // 409 Conflict for concurrent modification\n        });\n      }\n\n      console.log(`[EDGE_START_GAME] [OPTIMISTIC_LOCK_SUCCESS] Room ${roomId} successfully updated:`, updateResult[0]);\n      console.log(`[EDGE_START_GAME] [ORIGINAL_PLAYERS_VERIFICATION] original_player_ids set to:`, JSON.stringify(updateResult[0]?.original_player_ids));\n      console.log(`[EDGE_START_GAME] SUCCESS: Game started successfully in room ${roomId}. Image: ${firstQuestion.imageUrl}`)\n      \n      // Return success with comprehensive data\n      return new Response(JSON.stringify({ \n        message: 'Game started successfully!', \n        firstQuestion,\n        gameStartTime: now.toISOString(),\n        roomId: roomId,\n        roundEndsAt: roundEndsAt,\n        roundNumber: 1\n      }), {\n        headers: { ...corsHeaders, 'Content-Type': 'application/json' },\n        status: 200,\n      })\n      \n    } catch (dbError) {\n      console.error('[EDGE_START_GAME] CRITICAL DATABASE ERROR during room update:', {\n        error: dbError,\n        message: dbError instanceof Error ? dbError.message : String(dbError),\n        stack: dbError instanceof Error ? dbError.stack : undefined,\n        roomId,\n        updateData: JSON.stringify(updateData, null, 2)\n      });\n      throw new Error(`Critical database error during game start: ${dbError instanceof Error ? dbError.message : String(dbError)}`);\n    }\n\n  } catch (error) {\n    console.error('[EDGE_START_GAME] UNHANDLED EXCEPTION in start-game-handler:', error);\n    if (error instanceof Error) {\n      console.error('[EDGE_START_GAME] Exception Name:', error.name);\n      console.error('[EDGE_START_GAME] Exception Message:', error.message);\n      console.error('[EDGE_START_GAME] Exception Stack:', error.stack);\n    }\n    \n    // Determine appropriate status code based on error type/message\n    let statusCode = 500; // Default to internal server error\n    let errorMessage = error instanceof Error ? error.message : 'Internal Server Error';\n    \n    if (errorMessage.includes('Room ID is required') || errorMessage.includes('Missing roomId')) {\n      statusCode = 400; // Bad Request for missing required fields\n    } else if (errorMessage.includes('not authenticated') || errorMessage.includes('Missing Authorization')) {\n      statusCode = 401; // Unauthorized for authentication issues\n    } else if (errorMessage.includes('Room not found')) {\n      statusCode = 404; // Not Found for missing resources\n    } else if (errorMessage.includes('Database error')) {\n      statusCode = 500; // Keep as 500 for actual database issues\n      errorMessage = 'Database error occurred while processing request';\n    } else if (errorMessage.includes('Server configuration error')) {\n      statusCode = 503; // Service Unavailable for configuration issues\n      errorMessage = 'Service temporarily unavailable due to configuration issues';\n    }\n    \n    console.error(`[EDGE_START_GAME] Returning ${statusCode} status with message: ${errorMessage}`);\n    \n    return new Response(JSON.stringify({ \n      error: errorMessage, \n      details: error instanceof Error ? error.message : String(error),\n      timestamp: new Date().toISOString()\n    }), {\n      headers: { ...corsHeaders, 'Content-Type': 'application/json' },\n      status: statusCode,\n    })\n  }\n})\n\n/* To invoke locally:\n\n  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)\n  2. Make an HTTP request:\n\n  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/start-game-handler' \\n    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \\n    --header 'Content-Type: application/json' \\n    --data '{\"name\":\"Functions\"}'\n\n*/\n"
  }'
