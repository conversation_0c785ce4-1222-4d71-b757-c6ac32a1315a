import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useMemo, useState } from 'react';

interface Props {
  /** Every time this number increments (> 0), a new animation plays. */
  trigger: number | null;
  /** Score increase amount (e.g., 10, 15, 25) */
  scoreIncrease: number;
  /** Bonus level for this answer (0 = no bonus, 1 = BQ1, 2 = BQ2, 3+ = BQ3) */
  bonusLevel: number;
}

interface FootballCfg {
  initScale: number;
  initX: number;
  angle: number;
  distance: number;
  duration: number;
  delay: number;
  spin: number;
  stretch: number;
}

const MAX_FOOTBALLS = 5;

export function MultiplayerScoreAnimation({ trigger, scoreIncrease, bonusLevel }: Props) {
  const [seed, setSeed] = useState(0);
  
  useEffect(() => {
    if (trigger) setSeed(trigger);
  }, [trigger]);

  // Calculate number of footballs based on bonus level (similar to streak in single player)
  const footballCount = Math.min(Math.max(bonusLevel, 1), MAX_FOOTBALLS);

  const footballConfigs = useMemo((): FootballCfg[] => {
    if (!trigger) return [];

    return Array.from({ length: footballCount }, (_, i): FootballCfg => {
      const initScale = 1.75 + Math.random() * 0.375; // 2.5x larger scale (half of previous 5x)
      const initX = (Math.random() - 0.5) * 75; // 2.5x wider spread (half of previous 5x)

      const angle = -Math.PI / 2 + (Math.random() - 0.5) * (Math.PI / 6); // ±15°
      const distance = 200 + Math.random() * 100; // 2.5x longer distance (half of previous 5x)

      return {
        initScale,
        initX,
        angle,
        distance,
        duration: 1.0 + Math.random() * 0.4, // Adjusted duration for smaller movement
        delay: i * 0.06, // Slightly shorter delay between footballs
        spin: (Math.random() - 0.5) * 120, // Reduced spin for smaller scale
        stretch: 1.2 + Math.random() * 0.15, // Reduced stretch
      };
    });
  }, [footballCount, trigger]);

  if (!trigger || scoreIncrease <= 0) return null;

  // Determine score display text and color based on bonus level
  const getScoreDisplay = () => {
    if (bonusLevel === 0) {
      return { text: `+${scoreIncrease}`, color: 'text-yellow-300' };
    } else if (bonusLevel === 1) {
      return { text: `+${scoreIncrease} BQ1!`, color: 'text-green-300' };
    } else if (bonusLevel === 2) {
      return { text: `+${scoreIncrease} BQ2!`, color: 'text-blue-300' };
    } else {
      return { text: `+${scoreIncrease} BQ3!`, color: 'text-purple-300' };
    }
  };

  const scoreDisplay = getScoreDisplay();

  return (
    <div className="absolute inset-0 pointer-events-none z-50 no-debug-box">
      {/* Score Popup - 2.5x larger and positioned outside container */}
      <AnimatePresence>
        <motion.div
          key={`score-${trigger}`}
          initial={{ opacity: 1, y: -25, scale: 1.75 }} // 2.5x larger initial scale (half of previous)
          animate={{ opacity: 0, y: -100, scale: 2.75 }} // 2.5x larger final scale and movement (half of previous)
          exit={{ opacity: 0 }}
          transition={{ duration: 1.2, ease: 'easeOut' }} // Adjusted duration for smaller movement
          className="absolute left-1/2 top-0 -translate-x-1/2"
        >
          <span
            className={`${scoreDisplay.color} text-2xl font-bold whitespace-nowrap`} // 2.5x larger text (half of previous text-4xl)
            style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.9)' }}
          >
            {scoreDisplay.text}
          </span>
        </motion.div>
      </AnimatePresence>

      {/* Football Animation - 2.5x larger with spring-like upward motion */}
      <AnimatePresence>
        <div className="absolute left-1/2 top-0 -translate-x-1/2 no-debug-box">
          {footballConfigs.map((cfg, i) => {
            const targetX = cfg.initX + Math.cos(cfg.angle) * cfg.distance * 0.5; // Horizontal spread
            // Ensure upward movement - make targetY negative (upward) and more pronounced
            const targetY = -Math.abs(Math.sin(cfg.angle) * cfg.distance); // Force upward movement

            return (
              <motion.span
                key={`football-${seed}-${i}`}
                initial={{
                  opacity: 1,
                  scale: cfg.initScale,
                  x: cfg.initX,
                  y: -10, // Start slightly above the container
                  rotate: 0,
                }}
                animate={{
                  opacity: [1, 1, 0],
                  scale: [cfg.initScale, cfg.stretch, cfg.stretch * 0.95],
                  x: [cfg.initX, targetX],
                  // Spring-like upward motion: start at -10, bounce up to targetY * 0.4 (less upward), then kick up to targetY (full upward)
                  y: [-10, targetY * 0.4, targetY],
                  rotate: [0, cfg.spin],
                }}
                transition={{
                  duration: cfg.duration,
                  delay: cfg.delay,
                  ease: [0.25, 0.46, 0.45, 0.94], // Spring-like easing curve
                  opacity: {
                    duration: cfg.duration * 0.35,
                    delay: cfg.delay + cfg.duration * 0.65,
                  },
                  y: {
                    duration: cfg.duration,
                    delay: cfg.delay,
                    ease: [0.68, -0.55, 0.265, 1.55], // Bouncy spring easing for Y movement - this creates the "kicked up" effect
                  },
                }}
                exit={{ opacity: 0 }}
                className="text-4xl select-none" // 2.5x larger emoji (half of previous text-8xl)
                style={{
                  textShadow: "2px 2px 4px rgba(0,0,0,0.8)", // Adjusted shadow for smaller emoji
                  willChange: "transform, opacity",
                  transformOrigin: "center",
                  transform: "translateZ(0)",
                }}
              >
                🏈
              </motion.span>
            );
          })}
        </div>
      </AnimatePresence>
    </div>
  );
}
