import React from 'react';

interface FootballLoaderProps {
  message: string;
}

export const FootballLoader: React.FC<FootballLoaderProps> = ({ message }) => {
  return (
    <div className="game-loading-container">
      <p className="loading-text">{message}</p>
      <div className="football-spinner">
        <span>🏈</span>
        <span>🏈</span>
        <span>🏈</span>
        <span>🏈</span>
        <span>🏈</span>
      </div>
      <p className="loading-subtext">Just a moment...</p>
    </div>
  );
}; 