const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000';
const HOST_CREDENTIALS = { username: 'fresh', password: 'test123' };
const GUEST_CREDENTIALS = { username: 'fresh2', password: 'test123' };
const SCREENSHOT_DIR = path.join(__dirname, 'test-screenshots-join-only');

// Helper to take screenshots
async function takeScreenshot(page, name, prefix) {
  try {
    await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    const filename = `${prefix}-${name}.png`;
    const filepath = path.join(SCREENSHOT_DIR, filename);
    await page.screenshot({ path: filepath, fullPage: true });
    console.log(`[${prefix}] Screenshot saved: ${filename}`);
  } catch (error) {
    console.error(`[${prefix}] Screenshot failed:`, error.message);
  }
}

// Main test
async function testMultiplayerJoinOnly() {
  console.log('=== MULTIPLAYER JOIN TEST ===\n');
  console.log('This test verifies the join flow works correctly');
  console.log('It stops before starting the game to avoid Edge Function issues');
  console.log('---\n');
  
  const browserOptions = {
    headless: true,
    defaultViewport: { width: 1200, height: 900 },
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    executablePath: '/usr/bin/chromium-browser'
  };

  let hostBrowser, guestBrowser;
  let hostPage, guestPage;

  try {
    // Launch browsers
    console.log('Launching browsers...');
    hostBrowser = await puppeteer.launch(browserOptions);
    guestBrowser = await puppeteer.launch(browserOptions);
    
    hostPage = await hostBrowser.newPage();
    guestPage = await guestBrowser.newPage();
    
    // Navigate
    await Promise.all([
      hostPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' }),
      guestPage.goto(BASE_URL, { waitUntil: 'domcontentloaded' })
    ]);
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // === Quick Authentication ===
    console.log('\nAuthenticating users...');
    
    async function authenticate(page, creds, name) {
      // Click Multiplayer Mode
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Fill credentials
      await page.type('input[type="email"], input[type="text"]:not([type="password"])', creds.username);
      await page.type('input[type="password"]', creds.password);
      await page.keyboard.press('Enter');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Click Multiplayer Mode again
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button'))
          .find(b => b.textContent === 'Multiplayer Mode');
        if (btn) btn.click();
      });
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log(`${name} authenticated`);
    }
    
    await authenticate(hostPage, HOST_CREDENTIALS, 'HOST');
    await authenticate(guestPage, GUEST_CREDENTIALS, 'GUEST');
    
    // === Host Creates Room ===
    console.log('\nHost creating room...');
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Host Game');
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verify host is in room
    const hostRoomInfo = await hostPage.evaluate(() => {
      const roomTitle = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.includes("Room:"))?.textContent;
      const playerCount = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.match(/Players.*\(\d+\/\d+\)/))?.textContent;
      const buttons = Array.from(document.querySelectorAll('button')).map(b => b.textContent);
      return { roomTitle, playerCount, buttons };
    });
    
    console.log('Host room info:', hostRoomInfo);
    await takeScreenshot(hostPage, '01-host-room-created', 'host');
    
    // === Guest Joins Using Room List ===
    console.log('\nGuest viewing room list...');
    
    // Refresh room list
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent.includes('Refresh'));
      if (btn) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check available rooms
    const roomList = await guestPage.evaluate(() => {
      const roomDivs = Array.from(document.querySelectorAll('div[class*="bg-slate-800"]'));
      return roomDivs.map(div => ({
        text: div.textContent,
        hasHost: div.textContent.includes('fresh'),
        hasPlayers: div.textContent.includes('Players:')
      }));
    });
    
    console.log(`Found ${roomList.length} rooms in lobby`);
    await takeScreenshot(guestPage, '02-guest-room-list', 'guest');
    
    // Click on room to view details
    console.log('Guest clicking room to view details...');
    await guestPage.evaluate(() => {
      const roomDivs = Array.from(document.querySelectorAll('div[class*="bg-slate-800"]'));
      const freshRoom = roomDivs.find(div => 
        div.textContent.includes('fresh') && 
        div.textContent.includes('Players:')
      );
      if (freshRoom) freshRoom.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    await takeScreenshot(guestPage, '03-guest-room-detail', 'guest');
    
    // Click Join This Room
    console.log('Guest clicking Join This Room...');
    const joinResult = await guestPage.evaluate(() => {
      const joinBtn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Join This Room' || b.textContent.includes('Join This Room'));
      
      if (joinBtn && !joinBtn.disabled) {
        joinBtn.click();
        return { clicked: true, buttonText: joinBtn.textContent };
      }
      return { clicked: false, buttons: Array.from(document.querySelectorAll('button')).map(b => b.textContent) };
    });
    
    console.log('Join result:', joinResult);
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verify guest joined
    const guestJoinedInfo = await guestPage.evaluate(() => {
      const inRoom = document.body.textContent.includes('Leave Game') || 
                     document.body.textContent.includes('Ready');
      const playerInfo = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.match(/Players.*\(\d+\/\d+\)/))?.textContent;
      const buttons = Array.from(document.querySelectorAll('button')).map(b => b.textContent);
      
      return { inRoom, playerInfo, buttons };
    });
    
    console.log('Guest joined info:', guestJoinedInfo);
    await takeScreenshot(guestPage, '04-guest-joined', 'guest');
    
    // Check host's view after guest joined
    const hostUpdatedInfo = await hostPage.evaluate(() => {
      const playerCount = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.match(/Players.*\(\d+\/\d+\)/))?.textContent;
      const playerList = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('(Host)') || el.textContent.includes('(You)'))
        .map(el => el.textContent.trim());
      
      return { playerCount, playerList };
    });
    
    console.log('Host updated info:', hostUpdatedInfo);
    await takeScreenshot(hostPage, '05-host-with-guest', 'host');
    
    // === Both Players Ready Up ===
    console.log('\nTesting ready up flow...');
    
    // Host ready
    await hostPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Ready Up' || b.textContent === 'Ready');
      if (btn && !btn.disabled) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Guest ready
    await guestPage.evaluate(() => {
      const btn = Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Ready Up' || b.textContent === 'Ready');
      if (btn && !btn.disabled) btn.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check final state
    const finalHostState = await hostPage.evaluate(() => {
      const playerCount = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent.match(/Players.*\(\d+\/\d+\)/))?.textContent;
      const readyPlayers = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('✓'))
        .length;
      const hasStartButton = !!Array.from(document.querySelectorAll('button'))
        .find(b => b.textContent === 'Start Game' && !b.disabled);
      
      return { playerCount, readyPlayers, hasStartButton };
    });
    
    const finalGuestState = await guestPage.evaluate(() => {
      const readyPlayers = Array.from(document.querySelectorAll('*'))
        .filter(el => el.textContent.includes('✓'))
        .length;
      const waitingForHost = document.body.textContent.includes('Waiting for host');
      
      return { readyPlayers, waitingForHost };
    });
    
    console.log('\nFinal states:');
    console.log('Host:', finalHostState);
    console.log('Guest:', finalGuestState);
    
    await takeScreenshot(hostPage, '06-final-ready', 'host');
    await takeScreenshot(guestPage, '06-final-ready', 'guest');
    
    // === Summary ===
    console.log('\n=== TEST SUMMARY ===');
    
    const testResults = {
      hostCreatedRoom: !!hostRoomInfo.roomTitle,
      guestSawRoomInList: roomList.length > 0 && roomList.some(r => r.hasHost),
      guestJoinedRoom: guestJoinedInfo.inRoom,
      playerCountCorrect: hostUpdatedInfo.playerCount && hostUpdatedInfo.playerCount.includes('2/'),
      bothPlayersReady: finalHostState.readyPlayers >= 2,
      startButtonAvailable: finalHostState.hasStartButton
    };
    
    console.log('\nTest Results:');
    Object.entries(testResults).forEach(([test, passed]) => {
      console.log(`${passed ? '✓' : '✗'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    });
    
    const allPassed = Object.values(testResults).every(r => r);
    
    if (allPassed) {
      console.log('\n✅ ALL TESTS PASSED!');
      console.log('\nThe multiplayer join flow is working correctly.');
      console.log('The issue with starting the game is due to missing player data in the database.');
      console.log('\nTo fix the Edge Function error, you need to:');
      console.log('1. Populate the players_data table with NFL player data');
      console.log('2. Ensure players have local_image_path values');
      console.log('3. Have at least 4 different players with images');
    } else {
      console.log('\n❌ SOME TESTS FAILED');
      console.log('The join flow has issues that need to be fixed.');
    }
    
  } catch (error) {
    console.error('\n❌ Test error:', error.message);
    await takeScreenshot(hostPage, 'error-host', 'host');
    await takeScreenshot(guestPage, 'error-guest', 'guest');
    throw error;
    
  } finally {
    if (hostBrowser) await hostBrowser.close();
    if (guestBrowser) await guestBrowser.close();
  }
}

// Run test
testMultiplayerJoinOnly()
  .then(() => {
    console.log('\nTest completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\nTest failed:', error.message);
    process.exit(1);
  });