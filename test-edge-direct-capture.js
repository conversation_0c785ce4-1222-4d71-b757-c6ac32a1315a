// Direct test to capture Edge Function error details
const puppeteer = require('puppeteer');

async function captureEdgeFunctionError() {
  console.log('=== Capturing Edge Function Error Details ===\n');
  
  const browser = await puppeteer.launch({
    headless: false,
    executablePath: process.platform === 'linux' ? '/usr/bin/chromium-browser' : undefined,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    devtools: true // Open devtools to see network tab
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable detailed console logging
    page.on('console', msg => {
      console.log('[Browser Console]', msg.text());
    });
    
    // Capture network failures
    page.on('requestfailed', request => {
      console.log('[Request Failed]', request.url(), request.failure());
    });
    
    // Intercept and log the Edge Function response
    page.on('response', async response => {
      if (response.url().includes('start-game-handler')) {
        console.log('\n=== EDGE FUNCTION RESPONSE ===');
        console.log('URL:', response.url());
        console.log('Status:', response.status(), response.statusText());
        console.log('Headers:', response.headers());
        
        try {
          const text = await response.text();
          console.log('\nResponse Body:', text);
          
          try {
            const json = JSON.parse(text);
            console.log('\nParsed JSON:', JSON.stringify(json, null, 2));
          } catch (e) {
            console.log('(Response is not valid JSON)');
          }
        } catch (e) {
          console.log('Could not read response body:', e.message);
        }
        
        console.log('=== END EDGE FUNCTION RESPONSE ===\n');
      }
    });
    
    // Navigate to the app
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    console.log('Please manually:');
    console.log('1. Click "Multiplayer Mode"');
    console.log('2. Login with fresh/test123');
    console.log('3. Click "Multiplayer Mode" again if needed');
    console.log('4. Create a new room');
    console.log('5. Mark yourself as ready');
    console.log('6. Click "Start Game"');
    console.log('\nWatch for the Edge Function response above.\n');
    console.log('The browser will stay open for 2 minutes...\n');
    
    // Wait for manual testing
    await new Promise(resolve => setTimeout(resolve, 120000));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await browser.close();
  }
}

captureEdgeFunctionError();