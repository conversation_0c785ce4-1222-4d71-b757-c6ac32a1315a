import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

Deno.serve(async (req) => {
  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    const TIMEOUT_SECONDS = 60; // A player is considered disconnected after 60 seconds of silence
    const threshold = new Date(Date.now() - TIMEOUT_SECONDS * 1000).toISOString();

    // Find all connected players who haven't sent a heartbeat recently
    const { data: stalePlayers, error } = await supabase
      .from('game_players')
      .select('id, room_id, user_id')
      .eq('is_connected', true)
      .lt('last_seen_at', threshold);

    if (error) throw error;

    if (!stalePlayers || stalePlayers.length === 0) {
      return new Response("No disconnected players to update.", { status: 200 });
    }

    const stalePlayerIds = stalePlayers.map(p => p.id);
    console.log(`[Janitor] Found ${stalePlayers.length} stale players to mark as disconnected.`);

    // Mark these players as disconnected
    const { error: updateError } = await supabase
      .from('game_players')
      .update({ is_connected: false })
      .in('id', stalePlayerIds);

    if (updateError) throw updateError;
    
    // Optional: Add logic here to check if a HOST disconnected. If so,
    // you could update the `game_rooms` status to 'abandoned'.

    return new Response(JSON.stringify({ cleaned_players: stalePlayerIds }), { status: 200 });

  } catch (error) {
    console.error('Error in player-disconnect-janitor:', error);
    return new Response(JSON.stringify({ error: error.message }), { status: 500 });
  }
});
