# Test submit-answer-handler Edge Function

Write-Host "Testing submit-answer-handler Edge Function..." -ForegroundColor Cyan

$projectUrl = "https://xmyxuvuimebjltnaamox.supabase.co"
$functionUrl = "$projectUrl/functions/v1/submit-answer-handler"

# Test 1: OPTIONS request (CORS preflight)
Write-Host "`nTest 1: OPTIONS request (CORS check)" -ForegroundColor Yellow
$optionsResponse = Invoke-WebRequest -Uri $functionUrl -Method OPTIONS -UseBasicParsing
Write-Host "Status: $($optionsResponse.StatusCode)" -ForegroundColor Green
Write-Host "Headers: $($optionsResponse.Headers | ConvertTo-Json)" -ForegroundColor Gray

# Test 2: POST without auth (should fail with 401)
Write-Host "`nTest 2: POST without auth (should fail)" -ForegroundColor Yellow
try {
    $body = @{
        roomId = "test-room"
        choiceName = "Test Player"
    } | ConvertTo-Json

    $response = Invoke-WebRequest -Uri $functionUrl -Method POST -Body $body -ContentType "application/json" -UseBasicParsing
    Write-Host "Unexpected success: $($response.StatusCode)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode.value__ -eq 401) {
        Write-Host "✓ Correctly rejected with 401 Unauthorized" -ForegroundColor Green
    } else {
        Write-Host "Failed with: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 3: POST with invalid body (should fail with 400)
Write-Host "`nTest 3: POST with invalid body" -ForegroundColor Yellow
try {
    # Using anon key for basic auth
    $anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY"
    
    $headers = @{
        "Authorization" = "Bearer $anonKey"
        "Content-Type" = "application/json"
        "apikey" = $anonKey
    }
    
    $body = @{
        # Missing required fields
        foo = "bar"
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri $functionUrl -Method POST -Body $body -Headers $headers
    Write-Host "Unexpected success" -ForegroundColor Red
} catch {
    $errorResponse = $_.ErrorDetails.Message | ConvertFrom-Json
    if ($errorResponse.error -like "*required*") {
        Write-Host "✓ Correctly rejected with: $($errorResponse.error)" -ForegroundColor Green
    } else {
        Write-Host "Failed with: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Details: $($_.ErrorDetails.Message)" -ForegroundColor Gray
    }
}

Write-Host "`n✅ Edge Function is deployed and responding correctly!" -ForegroundColor Green
Write-Host "Function URL: $functionUrl" -ForegroundColor Blue