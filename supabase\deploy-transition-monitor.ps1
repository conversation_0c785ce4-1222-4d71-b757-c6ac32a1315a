# Deploy the transition-monitor edge function
Write-Host "Deploying transition-monitor edge function..." -ForegroundColor Green

# Get the current directory
$currentDir = Get-Location
$functionsDir = Join-Path $currentDir "functions"

# Verify we're in the supabase directory
if (-not (Test-Path $functionsDir)) {
    Write-Host "Error: Must run from supabase directory" -ForegroundColor Red
    exit 1
}

# Deploy the function
Write-Host "Running: supabase functions deploy transition-monitor" -ForegroundColor Yellow
supabase functions deploy transition-monitor

if ($LASTEXITCODE -eq 0) {
    Write-Host "transition-monitor deployed successfully!" -ForegroundColor Green
} else {
    Write-Host "Failed to deploy transition-monitor" -ForegroundColor Red
    exit 1
}

Write-Host "`nDeployment complete!" -ForegroundColor Green