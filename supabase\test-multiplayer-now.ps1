# Test multiplayer game NOW

Write-Host "TIME TO TEST MULTIPLAYER!" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Instructions:" -ForegroundColor Yellow
Write-Host "1. Opening game in 2 browser windows..." -ForegroundColor White
Write-Host "2. Sign in with different accounts in each window" -ForegroundColor White
Write-Host "3. Create/join the same room" -ForegroundColor White
Write-Host "4. Start the game and watch for:" -ForegroundColor White
Write-Host "   - Both players can submit answers" -ForegroundColor Green
Write-Host "   - 3-second transition timer after both answer" -ForegroundColor Green
Write-Host "   - Game advances to next question" -ForegroundColor Green
Write-Host "   - Scores update correctly" -ForegroundColor Green
Write-Host "   - Bonus levels (BQ1/BQ2/BQ3) on streaks" -ForegroundColor Green
Write-Host ""

# Open game in two windows
Start-Process "https://recognition-combine.vercel.app/"
Start-Sleep -Seconds 2
Start-Process "https://recognition-combine.vercel.app/"

Write-Host "Browser windows opened!" -ForegroundColor Green
Write-Host ""
Write-Host "While testing, press F12 to open console and watch for:" -ForegroundColor Yellow
Write-Host "- 'Answer submitted successfully!' messages" -ForegroundColor White
Write-Host "- 'Final answer submitted! Showing results for 3 seconds...' when both answer" -ForegroundColor White
Write-Host "- Any error messages in red" -ForegroundColor White