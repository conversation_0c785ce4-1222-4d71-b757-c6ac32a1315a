const puppeteer = require('puppeteer');

// Test configuration
const TEST_CONFIG = {
  gameUrl: 'https://recognition-combine.vercel.app/',
  player1: {
    email: 'fresh',
    password: 'test123',
    name: 'Player 1'
  },
  player2: {
    email: 'fresh2', 
    password: 'test123',
    name: 'Player 2'
  },
  headless: false, // Set to true for CI/CD
  slowMo: 50, // Slow down actions for visibility
  viewport: { width: 1200, height: 800 }
};

// Test scenarios with expected timings
const TEST_SCENARIOS = [
  {
    name: 'FAST - Both at 1s',
    player1AnswerTime: 1000,
    player2AnswerTime: 1000,
    expectedAdvanceTime: 4000, // 1 + 3
    tolerance: 500
  },
  {
    name: 'MEDIUM - Both at 2s',
    player1AnswerTime: 2000,
    player2AnswerTime: 2000,
    expectedAdvanceTime: 5000, // 2 + 3
    tolerance: 500
  },
  {
    name: 'LATE - Both at 5s',
    player1AnswerTime: 5000,
    player2AnswerTime: 5000,
    expectedAdvanceTime: 7000, // Cap kicks in, not 8
    tolerance: 500
  },
  {
    name: 'SOLO - Only P1 answers',
    player1AnswerTime: 1000,
    player2AnswerTime: null, // No answer
    expectedAdvanceTime: 7000, // Cap
    tolerance: 500
  },
  {
    name: 'STAGGERED - P1 at 1s, P2 at 3s',
    player1AnswerTime: 1000,
    player2AnswerTime: 3000,
    expectedAdvanceTime: 6000, // 3 + 3
    tolerance: 500
  }
];

class MultiplayerTest {
  constructor() {
    this.browser1 = null;
    this.browser2 = null;
    this.page1 = null;
    this.page2 = null;
    this.results = [];
  }

  async setup() {
    console.log('🚀 Setting up browsers...');
    
    // Launch two browser instances
    this.browser1 = await puppeteer.launch({
      headless: TEST_CONFIG.headless,
      slowMo: TEST_CONFIG.slowMo,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      defaultViewport: TEST_CONFIG.viewport
    });
    
    this.browser2 = await puppeteer.launch({
      headless: TEST_CONFIG.headless,
      slowMo: TEST_CONFIG.slowMo,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      defaultViewport: TEST_CONFIG.viewport
    });

    this.page1 = await this.browser1.newPage();
    this.page2 = await this.browser2.newPage();

    // Enable console logging from pages
    this.page1.on('console', msg => console.log('P1:', msg.text()));
    this.page2.on('console', msg => console.log('P2:', msg.text()));

    // Navigate to game
    await Promise.all([
      this.page1.goto(TEST_CONFIG.gameUrl, { waitUntil: 'networkidle2' }),
      this.page2.goto(TEST_CONFIG.gameUrl, { waitUntil: 'networkidle2' })
    ]);

    console.log('✅ Browsers ready');
  }

  async signIn(page, player) {
    console.log(`🔐 Signing in ${player.name}...`);
    
    // Click sign in button
    await page.waitForSelector('button:has-text("Sign In"), button:has-text("Sign in"), [data-testid="sign-in"]');
    await page.click('button:has-text("Sign In"), button:has-text("Sign in"), [data-testid="sign-in"]');
    
    // Wait for auth form
    await page.waitForSelector('input[type="email"]', { visible: true });
    
    // Fill credentials
    await page.type('input[type="email"]', player.email);
    await page.type('input[type="password"]', player.password);
    
    // Submit
    await page.click('button[type="submit"]');
    
    // Wait for auth to complete
    await page.waitForFunction(() => {
      const signInBtn = document.querySelector('button:has-text("Sign In")');
      return !signInBtn || signInBtn.style.display === 'none' || !document.body.contains(signInBtn);
    }, { timeout: 10000 });
    
    console.log(`✅ ${player.name} signed in`);
  }

  async createRoom() {
    console.log('🏠 Creating room...');
    
    // Player 1 creates room
    await this.page1.click('button:has-text("Multiplayer")');
    await this.page1.waitForSelector('button:has-text("Create Room")', { visible: true });
    await this.page1.click('button:has-text("Create Room")');
    
    // Get room code
    await this.page1.waitForSelector('[data-testid="room-code"], .room-code, div:has-text("Room Code")', { visible: true });
    
    const roomCode = await this.page1.evaluate(() => {
      // Try multiple selectors to find room code
      const selectors = [
        '[data-testid="room-code"]',
        '.room-code',
        'div:has-text("Room Code")',
        'span:has-text(/^[A-Z0-9]{6}$/)'
      ];
      
      for (const selector of selectors) {
        const elem = document.querySelector(selector);
        if (elem) {
          const match = elem.textContent.match(/[A-Z0-9]{6}/);
          if (match) return match[0];
        }
      }
      
      // Fallback: search all text for 6-char code
      const allText = document.body.innerText;
      const codeMatch = allText.match(/\b[A-Z0-9]{6}\b/);
      return codeMatch ? codeMatch[0] : null;
    });
    
    console.log(`✅ Room created: ${roomCode}`);
    return roomCode;
  }

  async joinRoom(roomCode) {
    console.log(`🚪 Player 2 joining room ${roomCode}...`);
    
    // Player 2 joins room
    await this.page2.click('button:has-text("Multiplayer")');
    await this.page2.waitForSelector('input[placeholder*="room code"], input[placeholder*="Room code"], input[type="text"]', { visible: true });
    
    const inputSelector = await this.page2.evaluate(() => {
      const inputs = document.querySelectorAll('input');
      for (const input of inputs) {
        if (input.placeholder?.toLowerCase().includes('room') || 
            input.placeholder?.toLowerCase().includes('code')) {
          return inputs.indexOf(input);
        }
      }
      return 0;
    });
    
    await this.page2.type(`input:nth-of-type(${inputSelector + 1})`, roomCode);
    await this.page2.click('button:has-text("Join Room"), button:has-text("Join")');
    
    // Wait for both players to be in room
    await this.page1.waitForFunction(() => {
      const playerElements = document.querySelectorAll('[data-testid*="player"], .player-name, div:has-text("Player")');
      return playerElements.length >= 2;
    }, { timeout: 10000 });
    
    console.log('✅ Both players in room');
  }

  async startGame() {
    console.log('🎮 Starting game...');
    
    // Player 1 (host) starts game
    await this.page1.click('button:has-text("Start Game"), button:has-text("Start")');
    
    // Wait for countdown to finish and game to start
    await Promise.all([
      this.page1.waitForFunction(() => {
        const score = document.querySelector('.score, [data-testid="score"], div:has-text("Score")');
        const question = document.querySelector('h2, .question-text');
        return score && question;
      }, { timeout: 15000 }),
      this.page2.waitForFunction(() => {
        const score = document.querySelector('.score, [data-testid="score"], div:has-text("Score")');
        const question = document.querySelector('h2, .question-text');
        return score && question;
      }, { timeout: 15000 })
    ]);
    
    console.log('✅ Game started');
  }

  async runScenario(scenario) {
    console.log(`\n🧪 Testing: ${scenario.name}`);
    
    // Inject timing measurement
    const measurementCode = `
      window.roundTiming = {
        start: Date.now(),
        player1Answer: null,
        player2Answer: null,
        roundAdvanced: null,
        questionText: document.querySelector('h2')?.textContent || ''
      };
      
      // Monitor for round advance
      const observer = new MutationObserver(() => {
        const newQuestion = document.querySelector('h2')?.textContent || '';
        if (newQuestion && newQuestion !== window.roundTiming.questionText) {
          window.roundTiming.roundAdvanced = Date.now();
          window.roundTiming.advanceTime = window.roundTiming.roundAdvanced - window.roundTiming.start;
          observer.disconnect();
        }
      });
      
      observer.observe(document.body, { childList: true, subtree: true });
    `;
    
    await Promise.all([
      this.page1.evaluate(measurementCode),
      this.page2.evaluate(measurementCode)
    ]);
    
    // Schedule answer submissions
    const answerPromises = [];
    
    if (scenario.player1AnswerTime !== null) {
      answerPromises.push(
        this.submitAnswerAtTime(this.page1, scenario.player1AnswerTime, 'player1')
      );
    }
    
    if (scenario.player2AnswerTime !== null) {
      answerPromises.push(
        this.submitAnswerAtTime(this.page2, scenario.player2AnswerTime, 'player2')
      );
    }
    
    // Wait for answers to be submitted
    await Promise.all(answerPromises);
    
    // Wait for round to advance (max 10 seconds)
    const advanceTime = await this.waitForRoundAdvance(this.page1, 10000);
    
    // Analyze results
    const result = {
      scenario: scenario.name,
      expected: scenario.expectedAdvanceTime,
      actual: advanceTime,
      difference: Math.abs(advanceTime - scenario.expectedAdvanceTime),
      passed: Math.abs(advanceTime - scenario.expectedAdvanceTime) <= scenario.tolerance
    };
    
    this.results.push(result);
    
    console.log(`⏱️  Expected: ${scenario.expectedAdvanceTime}ms`);
    console.log(`⏱️  Actual: ${advanceTime}ms`);
    console.log(`${result.passed ? '✅ PASS' : '❌ FAIL'} (difference: ${result.difference}ms)`);
    
    // Wait a bit before next scenario
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  async submitAnswerAtTime(page, targetTime, playerName) {
    const startTime = Date.now();
    
    // Wait until target time
    await new Promise(resolve => setTimeout(resolve, targetTime));
    
    // Click first answer choice
    await page.click('.choice-button:first-child, button[data-choice]:first-child, button[class*="choice"]:first-child');
    
    // Record actual submission time
    await page.evaluate((player) => {
      window.roundTiming[`${player}Answer`] = Date.now();
    }, playerName);
    
    const actualTime = Date.now() - startTime;
    console.log(`  📝 ${playerName} answered at ${actualTime}ms`);
  }

  async waitForRoundAdvance(page, timeout) {
    const startTime = Date.now();
    
    await page.waitForFunction(() => {
      return window.roundTiming && window.roundTiming.roundAdvanced;
    }, { timeout });
    
    const timing = await page.evaluate(() => window.roundTiming);
    return timing.advanceTime;
  }

  async printResults() {
    console.log('\n\n📊 TEST RESULTS SUMMARY');
    console.log('========================');
    
    let passed = 0;
    let failed = 0;
    
    this.results.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      const diff = result.passed ? '' : ` (off by ${result.difference}ms)`;
      
      console.log(`\n${status} ${result.scenario}`);
      console.log(`   Expected: ${result.expected}ms`);
      console.log(`   Actual: ${result.actual}ms${diff}`);
      
      if (result.passed) passed++;
      else failed++;
    });
    
    console.log('\n------------------------');
    console.log(`Total: ${passed} passed, ${failed} failed`);
    console.log(failed === 0 ? '\n🎉 All tests passed!' : '\n⚠️  Some tests failed');
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    if (this.browser1) await this.browser1.close();
    if (this.browser2) await this.browser2.close();
  }
}

// Main test runner
async function runTests() {
  console.log('🎯 Multiplayer Round Advance Automated Test Suite');
  console.log('================================================\n');
  
  const test = new MultiplayerTest();
  
  try {
    // Setup
    await test.setup();
    
    // Sign in both players
    await test.signIn(test.page1, TEST_CONFIG.player1);
    await test.signIn(test.page2, TEST_CONFIG.player2);
    
    // Create and join room
    const roomCode = await test.createRoom();
    await test.joinRoom(roomCode);
    
    // Start game
    await test.startGame();
    
    // Run all test scenarios
    for (const scenario of TEST_SCENARIOS) {
      await test.runScenario(scenario);
    }
    
    // Print results
    await test.printResults();
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await test.cleanup();
  }
}

// Run tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { MultiplayerTest, runTests };