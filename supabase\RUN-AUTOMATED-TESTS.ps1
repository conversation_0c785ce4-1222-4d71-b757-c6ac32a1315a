# MAIN AUTOMATED TEST RUNNER

Clear-Host

Write-Host "====================================================================" -ForegroundColor Cyan
Write-Host "           AUTOMATED MULTIPLAYER ROUND ADVANCE TESTS                " -ForegroundColor Cyan  
Write-Host "====================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This will automatically test all round advance timing scenarios:" -ForegroundColor Yellow
Write-Host "- Both at 1s -> 4s     - Both at 5s -> 7s cap" -ForegroundColor White
Write-Host "- Both at 2s -> 5s     - One player -> 7s" -ForegroundColor White
Write-Host "                       - Staggered -> 6s" -ForegroundColor White
Write-Host ""

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor White

$hasNode = $false
try {
    $nodeVer = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK: Node.js $nodeVer" -ForegroundColor Green
        $hasNode = $true
    }
} catch {
    Write-Host "X: Node.js not found" -ForegroundColor Red
}

if (-not $hasNode) {
    $nodeCheck = Get-Command node -ErrorAction SilentlyContinue
    if ($nodeCheck) {
        $hasNode = $true
        Write-Host "OK: Node.js found" -ForegroundColor Green
    } else {
        Write-Host "X: Node.js not found" -ForegroundColor Red
    }
}

$hasPuppeteer = Test-Path "node_modules/puppeteer"
if ($hasPuppeteer) {
    Write-Host "OK: Puppeteer installed" -ForegroundColor Green
} else {
    Write-Host "X: Puppeteer not installed" -ForegroundColor Red
}

$hasTestScript = Test-Path "test-multiplayer-automated.js"
if ($hasTestScript) {
    Write-Host "OK: Test script found" -ForegroundColor Green
} else {
    Write-Host "X: Test script not found" -ForegroundColor Red
}

# Install if needed
if (-not $hasNode) {
    Write-Host ""
    Write-Host "ERROR: Node.js is required!" -ForegroundColor Red
    Write-Host "Download from: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

if (-not $hasPuppeteer) {
    Write-Host ""
    Write-Host "Installing Puppeteer..." -ForegroundColor Yellow
    npm install puppeteer
    Write-Host "OK: Puppeteer installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "TEST ACCOUNT SETUP" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host ""
Write-Host "The automated test needs two test accounts." -ForegroundColor Yellow
Write-Host ""
Write-Host "Choose an option:" -ForegroundColor White
Write-Host "1. I already have test accounts" -ForegroundColor Green
Write-Host "2. Help me create test accounts" -ForegroundColor Blue
Write-Host "3. Run simple version (manual sign-in)" -ForegroundColor Yellow
Write-Host ""

$choice = Read-Host "Enter choice (1-3)"

if ($choice -eq "1") {
    Write-Host ""
    Write-Host "Great! Make sure test-multiplayer-automated.js has correct credentials." -ForegroundColor Green
    Write-Host "Default credentials expected:" -ForegroundColor Gray
    Write-Host "  Player 1: <EMAIL>" -ForegroundColor Gray
    Write-Host "  Player 2: <EMAIL>" -ForegroundColor Gray
    Write-Host ""
    $ready = Read-Host "Are credentials updated? (y/n)"
    if ($ready -eq 'y') {
        Write-Host ""
        Write-Host "Starting automated tests..." -ForegroundColor Green
        node test-multiplayer-automated.js
    }
} elseif ($choice -eq "2") {
    Write-Host ""
    Write-Host "Running account creation helper..." -ForegroundColor Blue
    if (Test-Path "create-test-accounts.ps1") {
        & ".\create-test-accounts.ps1"
    } else {
        Write-Host "Account creation script not found" -ForegroundColor Red
    }
} elseif ($choice -eq "3") {
    Write-Host ""
    Write-Host "Running simple version (manual sign-in)..." -ForegroundColor Yellow
    if (Test-Path "test-multiplayer-simple.js") {
        node test-multiplayer-simple.js
    } else {
        Write-Host "Simple test not found. Running full version..." -ForegroundColor Yellow
        node test-multiplayer-automated.js
    }
} else {
    Write-Host "Invalid choice. Exiting." -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = Read-Host