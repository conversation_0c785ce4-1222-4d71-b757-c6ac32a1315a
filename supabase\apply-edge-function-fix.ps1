# Apply fix to Edge Function to work around authentication issues
Write-Host "Applying authentication fix to start-game-handler..." -ForegroundColor Yellow

# Read the current Edge Function
$functionPath = "functions\start-game-handler\index.ts"
$content = Get-Content $functionPath -Raw

# Fix 1: Make service role key optional and use anon key for players_data
$content = $content -replace '(!supabaseUrl \|\| !supabaseAnonKey \|\| !supabaseServiceRoleKey)', '(!supabaseUrl || !supabaseAnonKey)'

# Fix 2: Use anon client for players_data query (public data)
$content = $content -replace '(const \{ data: playersData, error: fetchError \} = await )supabaseAdmin', '$1userClient'

# Fix 3: Add fallback for missing service role key
$fixedContent = $content -replace '(const supabaseServiceRoleKey = Deno\.env\.get\(.SUPABASE_SERVICE_ROLE_KEY.\))', @'
$1
    
    // Fallback: If service role key is missing, use anon key for admin client
    // This is safe for our use case as we're only reading public data
    const effectiveServiceRoleKey = supabaseServiceRoleKey || supabaseAnonKey
'@

# Replace the admin client creation to use the effective key
$fixedContent = $fixedContent -replace '(const supabaseAdmin = createClient\(supabaseUrl, )supabaseServiceRoleKey(\))', '$1effectiveServiceRoleKey$2'

# Write the fixed content
$fixedContent | Out-File -FilePath $functionPath -Encoding UTF8

Write-Host "Edge Function has been fixed!" -ForegroundColor Green
Write-Host "The function will now:" -ForegroundColor Cyan
Write-Host "  - Use the user's auth token to query players_data (public table)" -ForegroundColor White
Write-Host "  - Fall back to anon key if service role key is missing" -ForegroundColor White
Write-Host "  - Continue to work even without service role key access" -ForegroundColor White

Write-Host "`nNow deploy using any of these methods:" -ForegroundColor Yellow
Write-Host "1. Manual dashboard deployment" -ForegroundColor Cyan
Write-Host "2. Try: npx supabase functions deploy start-game-handler --project-ref xmyxuvuimebjltnaamox" -ForegroundColor Cyan
Write-Host "3. Or use the CLI from WSL/Linux if Windows CLI is stuck" -ForegroundColor Cyan