const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://ktlncsmzllxmtykzrlcc.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0bG5jc216bGx4bXR5a3pybGNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzAzMjAxNDIsImV4cCI6MjA0NTg5NjE0Mn0.o9W-NODzqE8gzZaWGP6lPmUJpdzf54aBMRdZdiqxiSI';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugStartGame() {
  console.log('Starting debug of start-game-handler...\n');

  try {
    // Step 1: Authenticate as test user
    console.log('1. Authenticating as test user fresh...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (authError) {
      console.error('Authentication failed:', authError);
      return;
    }

    console.log('✓ Authentication successful');
    console.log('User ID:', authData.user.id);
    console.log('Access Token:', authData.session.access_token.substring(0, 20) + '...\n');

    // Step 2: Find a waiting room hosted by this user
    console.log('2. Looking for waiting rooms hosted by fresh...');
    const { data: rooms, error: roomsError } = await supabase
      .from('game_rooms')
      .select('*')
      .eq('host_id', authData.user.id)
      .eq('status', 'waiting')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roomsError) {
      console.error('Failed to fetch rooms:', roomsError);
      return;
    }

    if (!rooms || rooms.length === 0) {
      console.log('No waiting rooms found. Creating one...');
      
      // Create a new room
      const { data: newRoom, error: createError } = await supabase
        .from('game_rooms')
        .insert({
          host_id: authData.user.id,
          status: 'waiting',
          max_players: 5,
          game_type: 'multiplayer',
          room_code: Math.random().toString(36).substring(2, 8).toUpperCase()
        })
        .select()
        .single();

      if (createError) {
        console.error('Failed to create room:', createError);
        return;
      }

      console.log('✓ Created new room:', newRoom.room_code);
      rooms[0] = newRoom;
    } else {
      console.log('✓ Found existing room:', rooms[0].room_code);
    }

    const room = rooms[0];
    console.log('\nRoom details:');
    console.log('- Room ID:', room.id);
    console.log('- Room Code:', room.room_code);
    console.log('- Status:', room.status);
    console.log('- Created at:', room.created_at);

    // Step 3: Call start-game-handler edge function
    console.log('\n3. Calling start-game-handler edge function...');
    console.log('Request body:', JSON.stringify({ roomId: room.id }, null, 2));

    const response = await fetch(`${supabaseUrl}/functions/v1/start-game-handler`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.session.access_token}`,
      },
      body: JSON.stringify({ roomId: room.id })
    });

    console.log('\n4. Response details:');
    console.log('- Status:', response.status, response.statusText);
    console.log('- Headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('\n- Raw response body:');
    console.log(responseText);

    // Try to parse as JSON
    try {
      const responseJson = JSON.parse(responseText);
      console.log('\n- Parsed JSON response:');
      console.log(JSON.stringify(responseJson, null, 2));

      // Check if it's an error response
      if (responseJson.error) {
        console.log('\n⚠️  Error response received:');
        console.log('- Error:', responseJson.error);
        console.log('- Details:', responseJson.details);
      } else if (responseJson.success === false) {
        console.log('\n⚠️  Unsuccessful response:');
        console.log('- Message:', responseJson.message);
      } else {
        console.log('\n✓ Success response received');
        console.log('- Success:', responseJson.success);
        console.log('- Message:', responseJson.message);
        if (responseJson.gameState) {
          console.log('- Game state included:', Object.keys(responseJson.gameState));
        }
      }
    } catch (parseError) {
      console.log('\n⚠️  Response is not valid JSON');
    }

    // Step 5: Check if room status was updated
    console.log('\n5. Checking if room status was updated...');
    const { data: updatedRoom, error: checkError } = await supabase
      .from('game_rooms')
      .select('*')
      .eq('id', room.id)
      .single();

    if (checkError) {
      console.error('Failed to check room status:', checkError);
    } else {
      console.log('Room status after function call:', updatedRoom.status);
      console.log('Game state:', updatedRoom.game_state ? 'Present' : 'Not present');
      if (updatedRoom.game_state) {
        console.log('Game state keys:', Object.keys(updatedRoom.game_state));
      }
    }

  } catch (error) {
    console.error('\nUnexpected error:', error);
  }

  console.log('\nDebug complete!');
  process.exit(0);
}

// Run the debug
debugStartGame();