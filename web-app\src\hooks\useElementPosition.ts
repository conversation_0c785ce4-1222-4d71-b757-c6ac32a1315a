import { useCallback } from 'react';

export interface ElementPosition {
  x: number;
  y: number;
}

/**
 * Hook to get the absolute viewport position of an element
 * Returns a function that can be called with a DOM element to get its center position
 */
export function useElementPosition() {
  const getElementPosition = useCallback((element: HTMLElement | null): ElementPosition | null => {
    if (!element) return null;

    const rect = element.getBoundingClientRect();
    
    // Calculate center position of the element relative to the viewport
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    return {
      x: centerX,
      y: centerY,
    };
  }, []);

  return { getElementPosition };
}
