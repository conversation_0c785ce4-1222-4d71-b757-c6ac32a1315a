import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PlayerData {
  id: number;
  team_name: string;
  player_name: string;
  local_image_path: string | null;
  jersey_number: string | number | null;
  position: string | null;
  height: string | null;
  weight: string | number | null;
  age_or_dob: string | number | null;
  experience: string | number | null;
  college: string | null;
}

interface PlayerChoice {
  name: string;
  isCorrect: boolean;
}

interface PlayerQuestion {
  questionId: string;
  correctPlayerId: number;
  imageUrl: string;
  choices: PlayerChoice[];
  correctChoiceName: string;
}

// Helper to shuffle array
function shuffleArray<T>(array: T[]): T[] {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}

// Load player data
async function loadPlayerData(supabaseAdmin: any): Promise<PlayerData[]> {
  try {
    const { data: playersData, error: fetchError } = await supabaseAdmin
      .from('players_data')
      .select('*')
      .not('local_image_path', 'is', null)
      .limit(500);

    if (fetchError || !playersData || playersData.length === 0) {
      // Fallback data
      return [
        { id: 1, team_name: 'Arizona Cardinals', player_name: 'Isaiah Adams', local_image_path: 'arizona-cardinals/isaiah-adams.jpg', jersey_number: '74', position: 'OL', height: '6-4', weight: '315', age_or_dob: '24', experience: '2', college: 'Illinois' },
        { id: 2, team_name: 'Arizona Cardinals', player_name: 'Andre Baccellia', local_image_path: 'arizona-cardinals/andre-baccellia.jpg', jersey_number: '82', position: 'WR', height: '5-10', weight: '175', age_or_dob: '28', experience: '2', college: 'Washington' },
        { id: 3, team_name: 'Arizona Cardinals', player_name: 'Budda Baker', local_image_path: 'arizona-cardinals/budda-baker.jpg', jersey_number: '3', position: 'S', height: '5-10', weight: '195', age_or_dob: '29', experience: '9', college: 'Washington' },
        { id: 4, team_name: 'Arizona Cardinals', player_name: 'Kelvin Beachum', local_image_path: 'arizona-cardinals/kelvin-beachum.jpg', jersey_number: '68', position: 'OL', height: '6-3', weight: '308', age_or_dob: '35', experience: '14', college: 'Southern Methodist' }
      ];
    }

    return playersData;
  } catch (error) {
    console.error('[TRANSITION_MONITOR] Error loading player data:', error);
    return [];
  }
}

// Generate a question
function generateQuestion(allPlayers: PlayerData[], excludeIds: Set<number> = new Set(), allowLoop: boolean = true): PlayerQuestion | null {
  if (!allPlayers || allPlayers.length < 4) {
    return null;
  }

  let availablePlayers = allPlayers.filter(p => p.id != null && !excludeIds.has(p.id));
  
  // If no available players and looping is allowed, reset and use all players
  if (availablePlayers.length < 1 && allowLoop) {
    console.log('[TRANSITION_MONITOR] All players exhausted, looping back to start');
    availablePlayers = allPlayers.filter(p => p.id != null);
  }
  
  if (availablePlayers.length < 1) {
    return null;
  }

  const correctPlayerIndex = Math.floor(Math.random() * availablePlayers.length);
  const correctPlayer = availablePlayers[correctPlayerIndex];

  const distractors: PlayerData[] = [];
  const potentialDistractors = allPlayers.filter(p => p.id !== correctPlayer.id);
  shuffleArray(potentialDistractors);

  for (const p of potentialDistractors) {
    if (distractors.length < 3) {
      distractors.push(p);
    } else {
      break;
    }
  }

  if (distractors.length < 3) {
    return null;
  }

  const choices: PlayerChoice[] = [
    { name: correctPlayer.player_name, isCorrect: true },
    ...distractors.map(p => ({ name: p.player_name, isCorrect: false }))
  ];

  const shuffledChoices = shuffleArray(choices);
  const imageUrl = correctPlayer.local_image_path
    ? `/players_images/${correctPlayer.local_image_path}`
    : '/images/placeholder.jpg';

  return {
    questionId: crypto.randomUUID(),
    correctPlayerId: correctPlayer.id,
    imageUrl: imageUrl,
    choices: shuffledChoices,
    correctChoiceName: correctPlayer.player_name
  };
}

console.log('[TRANSITION_MONITOR] Function loaded');

serve(async (req: Request) => {
  console.log(`[TRANSITION_MONITOR] Request received. Method: ${req.method}`);
  
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)

    // Check if this is a request for a specific room
    let specificRoomId: string | null = null;
    if (req.method === 'POST') {
      try {
        const body = await req.json();
        specificRoomId = body.specificRoomId || null;
      } catch {
        // No body or invalid JSON, process all rooms
      }
    }

    console.log('[TRANSITION_MONITOR] Checking for games that need transitions...', 
      specificRoomId ? `Specific room: ${specificRoomId}` : 'All rooms');

    // Get active games (specific room or all)
    let query = supabaseAdmin
      .from('game_rooms')
      .select('*, game_players(user_id)')
      .eq('status', 'active')
      .not('current_question_data', 'is', null);
    
    if (specificRoomId) {
      query = query.eq('id', specificRoomId);
    }

    const { data: activeGames, error: fetchError } = await query;

    if (fetchError) {
      console.error('[TRANSITION_MONITOR] Error fetching active games:', fetchError);
      throw fetchError;
    }

    if (!activeGames || activeGames.length === 0) {
      console.log('[TRANSITION_MONITOR] No active games to monitor');
      return new Response(JSON.stringify({ 
        message: 'No active games to monitor',
        processed: 0 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      });
    }

    console.log(`[TRANSITION_MONITOR] Found ${activeGames.length} active games to check`);

    // Load player data once for all games
    const allPlayers = await loadPlayerData(supabaseAdmin);
    const now = Date.now();
    let transitionsProcessed = 0;

    // Process each active game
    for (const game of activeGames) {
      try {
        const roomId = game.id;
        const currentQuestion = game.current_question_data;
        const questionStartedAt = game.question_started_at;
        const currentAnswers = game.current_round_answers || [];
        const playersInRoom = game.game_players || [];
        const transitionDeadline = game.transition_deadline;

        if (!currentQuestion || !questionStartedAt) {
          console.log(`[TRANSITION_MONITOR] Skipping room ${roomId} - no current question`);
          continue;
        }

        // Calculate timings
        const questionStartTime = new Date(questionStartedAt).getTime();
        const timeSinceStart = now - questionStartTime;
        const allPlayersAnswered = currentAnswers.filter((a: any) => 
          a.questionId === currentQuestion.questionId
        ).length === playersInRoom.length;

        // Determine if we should transition
        let shouldTransition = false;
        let transitionReason = '';

        // Check transition deadline (server-controlled timing)
        if (transitionDeadline) {
          const deadlineTime = new Date(transitionDeadline).getTime();
          if (now >= deadlineTime) {
            shouldTransition = true;
            transitionReason = 'transition deadline reached';
          }
        } else {
          // Fallback: Check hard cap (7 seconds)
          if (timeSinceStart >= 7000) {
            shouldTransition = true;
            transitionReason = '7-second hard cap';
          }
          // Or if all players answered and 3 seconds passed
          else if (allPlayersAnswered && timeSinceStart >= 3000) {
            shouldTransition = true;
            transitionReason = 'all players answered + 3 seconds';
          }
        }

        if (!shouldTransition) {
          console.log(`[TRANSITION_MONITOR] Room ${roomId} not ready for transition`, {
            timeSinceStart: Math.round(timeSinceStart / 1000) + 's',
            allPlayersAnswered,
            transitionDeadline
          });
          continue;
        }

        console.log(`[TRANSITION_MONITOR] Transitioning room ${roomId} - ${transitionReason}`);

        // Generate next question
        const nextQuestion = generateQuestion(allPlayers);
        if (!nextQuestion) {
          console.error(`[TRANSITION_MONITOR] Failed to generate next question for room ${roomId}`);
          continue;
        }

        // Update the room with new question
        const newRoundNumber = (game.current_round_number || 1) + 1;
        const nowTime = new Date();
        const { error: updateError } = await supabaseAdmin
          .from('game_rooms')
          .update({
            current_question_data: nextQuestion,
            current_round_answers: [],
            current_round_number: newRoundNumber,
            question_started_at: nowTime.toISOString(),
            transition_deadline: new Date(nowTime.getTime() + 7000).toISOString(), // Reset 7-second hard cap
            transition_until: null, // Clear old transition field if it exists
            next_question_data: null, // Clear pre-generated question if it exists
            first_answer_at: null, // Reset first answer timestamp for new round
            last_activity_timestamp: nowTime.toISOString(),
            // Timer state for visual countdown
            timer_type: 'round' as const,
            timer_started_at: nowTime.toISOString(),
            timer_duration_seconds: 7.0
          })
          .eq('id', roomId)
          .eq('status', 'active')
          .eq('current_question_data->questionId', currentQuestion.questionId); // Prevent race conditions

        if (updateError) {
          console.error(`[TRANSITION_MONITOR] Failed to update room ${roomId}:`, updateError);
        } else {
          console.log(`[TRANSITION_MONITOR] Successfully transitioned room ${roomId} to round ${newRoundNumber}`);
          transitionsProcessed++;
        }
      } catch (gameError) {
        console.error(`[TRANSITION_MONITOR] Error processing game ${game.id}:`, gameError);
      }
    }

    return new Response(JSON.stringify({ 
      message: 'Transition monitor completed',
      gamesChecked: activeGames.length,
      transitionsProcessed 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('[TRANSITION_MONITOR] Unexpected error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})