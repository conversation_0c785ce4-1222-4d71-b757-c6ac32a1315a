// Mock edge function response for testing
const mockStartGameResponse = {
  message: 'Game started successfully!',
  firstQuestion: {
    questionId: 'mock-q-' + Date.now(),
    correctPlayerId: 1001,
    imageUrl: '/players_images/arizona-cardinals/budda-baker.jpg',
    choices: [
      { name: '<PERSON><PERSON>', isCorrect: true },
      { name: '<PERSON>', isCorrect: false },
      { name: '<PERSON>', isCorrect: false },
      { name: '<PERSON><PERSON><PERSON><PERSON>', isCorrect: false }
    ],
    correctChoiceName: '<PERSON><PERSON> Baker'
  },
  gameStartTime: new Date().toISOString(),
  roundEndsAt: new Date(Date.now() + 30000).toISOString(),
  roundNumber: 1
};

console.log('Mock start game response:', JSON.stringify(mockStartGameResponse, null, 2));