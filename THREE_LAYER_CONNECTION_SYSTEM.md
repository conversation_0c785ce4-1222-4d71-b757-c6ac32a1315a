# Three-Layer Connection Management System

This document outlines the implementation of the "functionally apparent, UI invisible" connection management system as specified.

## Overview

The system implements three layers of connection management:

1. **Layer 1: The Proactive Client** - Prevents actions when the user is known to be offline
2. **Layer 2: The Authoritative Server** - Detects and handles players who have silently disconnected  
3. **Layer 3: The Reactive UI** - The interface becomes "functionally obvious" by disabling elements and visually graying out stale data

## Layer 1: The Proactive Client

### Implementation
- **File**: `web-app/src/hooks/useOnlineStatus.ts`
- **Purpose**: Tracks browser online/offline status using `navigator.onLine`
- **Integration**: Used in `HomePageContent` component to disable network-dependent buttons

### Key Features
- Listens to `online` and `offline` events
- Automatically disables buttons when offline:
  - Create Room button
  - Ready Up button  
  - Start Game button
- Provides tooltip feedback: "You are offline. Please check your connection."

### Usage
```typescript
const isOnline = useOnlineStatus();

<Button 
  disabled={!isOnline || otherConditions}
  title={!isOnline ? "You are offline. Please check your connection." : ""}
>
```

## Layer 2: The Authoritative Server

### Heartbeat System
- **Location**: Room subscription useEffect in `web-app/src/app/page.tsx`
- **Frequency**: Every 20 seconds
- **Condition**: Only when tab is visible (`document.visibilityState === 'visible'`)

```typescript
heartbeatInterval = setInterval(async () => {
  if (document.visibilityState === 'visible') {
    await supabase
      .from('game_players')
      .update({ last_seen_at: new Date().toISOString() })
      .match({ user_id: user.id, room_id: capturedActiveRoomId });
  }
}, 20000);
```

### Player Disconnect Janitor
- **File**: `supabase/functions/player-disconnect-janitor/index.ts`
- **Schedule**: Every minute (configured in `supabase/config.toml`)
- **Timeout**: 60 seconds of silence = disconnected
- **Action**: Marks stale players as `is_connected: false`

### Database Schema
The system uses existing columns in `game_players` table:
- `is_connected` (boolean, default: true)
- `last_seen_at` (timestamp, default: null)
- Indexes for efficient queries (already exist)

## Layer 3: The Reactive UI

### Disconnected Player Display
Players marked as disconnected are visually distinct:
- **Opacity**: 50% 
- **Filter**: Grayscale
- **Icon**: 🔌 disconnection indicator
- **Tooltip**: Shows player name + "is disconnected"

```typescript
className={cn(
  "text-xs p-1.5 rounded mb-1 flex justify-between items-center",
  "bg-slate-700/50 hover:bg-slate-700/70 transition-colors",
  !player.is_connected && "opacity-50 grayscale"
)}
title={!player.is_connected ? `${player.profile?.username || 'Player'} is disconnected` : ''}
```

### DisconnectedOverlay Component
- **File**: `web-app/src/components/ui/DisconnectedOverlay.tsx`
- **Trigger**: When current user is marked as `is_connected: false`
- **Action**: Full-screen overlay with "Rejoin Game" button
- **Design**: Clean, centered modal with connection lost icon

### Functionally Apparent Logic
```typescript
// Check if current user is marked as disconnected
const self = playersInRoom.find(p => p.user_id === user?.id);

// Show overlay if disconnected
if (self && !self.is_connected && selectedOverallGameType === 'multiplayer' && multiplayerPanelState === 'in_room') {
  return <DisconnectedOverlay onReconnect={handleRejoinRoom} />;
}
```

## Configuration

### Cron Jobs (supabase/config.toml)
```toml
[functions.player-disconnect-janitor]
enabled = true
verify_jwt = false
schedule = "* * * * *"  # Every minute
entrypoint = "./functions/player-disconnect-janitor/index.ts"
```

### CSS Styling (web-app/src/app/globals.css)
```css
.player-item.offline {
  opacity: 0.5;
  font-style: italic;
  filter: grayscale(80%);
}
```

## Key Benefits

1. **No Explicit Error Messages**: Users never see "You are disconnected" banners
2. **Functionally Obvious**: The app *behaves* as if you're disconnected
3. **Seamless Recovery**: One-click rejoin when connection is restored
4. **Real-time Updates**: Other players see disconnections immediately
5. **Robust Detection**: Server-side janitor catches silent disconnections

## Files Modified/Created

### New Files
- `web-app/src/hooks/useOnlineStatus.ts`
- `web-app/src/components/ui/DisconnectedOverlay.tsx`
- `supabase/functions/player-disconnect-janitor/index.ts`
- `supabase/deploy-player-disconnect-janitor.ps1`

### Modified Files
- `web-app/src/app/page.tsx` - Added heartbeat system, offline button logic, DisconnectedOverlay
- `web-app/src/app/globals.css` - Added disconnected player styling
- `supabase/config.toml` - Added cron job configuration

## Testing

To test the system:

1. **Layer 1**: Disconnect WiFi and observe buttons become disabled
2. **Layer 2**: Close laptop/tab for >60 seconds, reopen to see janitor marked you as disconnected
3. **Layer 3**: When marked as disconnected, observe the DisconnectedOverlay appears

The system provides a seamless, "functionally apparent" experience where connection issues are handled gracefully without explicit error messaging.
