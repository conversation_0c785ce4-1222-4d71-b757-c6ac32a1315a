# Deploy without any workdir context

Write-Host "Bypassing workdir context entirely..." -ForegroundColor Cyan

$projectRef = "xmyxuvuimebjltnaamox"

# Create a temporary directory outside the project
$tempDir = "$env:TEMP\supabase-deploy-$(Get-Random)"
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

Write-Host "Using temp directory: $tempDir" -ForegroundColor Yellow

# Copy the function to temp directory
$functionPath = "functions\submit-answer-handler"
$tempFunctionPath = "$tempDir\submit-answer-handler"

Write-Host "Copying function files..." -ForegroundColor Yellow
Copy-Item -Path $functionPath -Destination $tempFunctionPath -Recurse -Force

# Change to temp directory
Push-Location $tempDir

Write-Host "Deploying from clean environment..." -ForegroundColor Cyan

# Deploy from clean environment
npx supabase functions deploy submit-answer-handler --project-ref $projectRef --no-verify-jwt

# Return to original directory
Pop-Location

# Clean up
Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "`nDeployment completed!" -ForegroundColor Green