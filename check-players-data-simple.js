// This test assumes the dev server is running and will use the same Supabase instance

const puppeteer = require('puppeteer');

async function checkPlayersData() {
  console.log('Checking players_data table via browser console...\n');
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    executablePath: '/usr/bin/chromium-browser'
  });
  
  try {
    const page = await browser.newPage();
    
    // Navigate to the app
    await page.goto('http://localhost:3000', { waitUntil: 'domcontentloaded' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Execute Supabase query in browser context
    const result = await page.evaluate(async () => {
      // Access Supabase from window if available
      if (typeof window !== 'undefined' && window.__NEXT_DATA__) {
        try {
          // Try to get Supabase URL and key from the page
          const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
          
          // These should be available in the browser environment
          const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 
            (document.querySelector('meta[name="supabase-url"]')?.content);
          const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 
            (document.querySelector('meta[name="supabase-anon-key"]')?.content);
          
          if (!supabaseUrl || !supabaseAnonKey) {
            return { error: 'Could not find Supabase credentials' };
          }
          
          const supabase = createClient(supabaseUrl, supabaseAnonKey);
          
          // Check players_data
          const { count, error } = await supabase
            .from('players_data')
            .select('*', { count: 'exact', head: true })
            .not('local_image_path', 'is', null);
          
          if (error) {
            return { error: error.message };
          }
          
          return { count };
        } catch (e) {
          return { error: e.message };
        }
      }
      return { error: 'Unable to access Supabase in browser context' };
    });
    
    console.log('Result:', result);
    
    if (result.error) {
      console.error('Error:', result.error);
      console.log('\nThis might mean:');
      console.log('1. The players_data table is empty');
      console.log('2. The table has no players with images');
      console.log('3. There\'s a permissions issue');
    } else if (result.count !== undefined) {
      console.log(`\nPlayers with images in database: ${result.count}`);
      if (result.count < 4) {
        console.warn('\n⚠️  WARNING: Not enough players with images!');
        console.warn('The game needs at least 4 players with images to work.');
        console.warn('\nThis is why the start-game-handler is failing with a 500 error.');
      }
    }
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

checkPlayersData();