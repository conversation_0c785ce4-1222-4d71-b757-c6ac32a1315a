﻿-- Add transition_until and next_question_data columns to game_rooms table if they don't exist
-- These columns support the 3-second transition period between questions

DO $$ 
BEGIN
    -- Add transition_until column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'transition_until') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN transition_until timestamptz DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.transition_until IS 'Timestamp indicating when the transition period ends and the next question should be shown. NULL when not in transition.';
    END IF;

    -- Add next_question_data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'next_question_data') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN next_question_data jsonb DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.next_question_data IS 'The next question data that will become active after the transition period ends.';
    END IF;
END$$;
