<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Answer State Debug Monitor</title>
    <style>
        body {
            font-family: monospace;
            background: #1a1a1a;
            color: #fff;
            padding: 20px;
        }
        .monitor {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .state-ok { color: #4f4; }
        .state-bad { color: #f44; }
        .timestamp { color: #88f; }
        .log-entry { margin: 5px 0; padding: 5px; background: #333; }
        button { padding: 10px 20px; margin: 5px; }
        #status { font-size: 24px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Answer Submission State Monitor</h1>
    
    <div class="monitor">
        <h2>Instructions:</h2>
        <ol>
            <li>Open your game in another tab/window</li>
            <li>Start a multiplayer game</li>
            <li>Open browser DevTools Console (F12)</li>
            <li>Click "Start Monitoring" below</li>
            <li>Submit an answer in the game</li>
            <li>Watch the state changes below</li>
        </ol>
    </div>

    <button onclick="startMonitoring()">Start Monitoring</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="status" class="monitor">
        Status: <span id="statusText">Not monitoring</span>
    </div>
    
    <div id="log" class="monitor">
        <h3>State Change Log:</h3>
    </div>

    <script>
        let monitoring = false;
        let logEntries = [];
        let checkInterval;

        function startMonitoring() {
            monitoring = true;
            document.getElementById('statusText').textContent = 'Monitoring...';
            document.getElementById('statusText').className = 'state-ok';
            
            // Inject monitoring code into console
            console.log('%c=== COPY AND PASTE THIS INTO YOUR GAME CONSOLE ===', 'color: yellow; font-size: 16px');
            console.log(`
// State Monitor Injector
window.stateMonitor = {
    logs: [],
    startTime: Date.now(),
    
    log: function(event, data) {
        const entry = {
            time: Date.now() - this.startTime,
            event: event,
            data: data,
            timestamp: new Date().toISOString()
        };
        this.logs.push(entry);
        console.log('[STATE_MONITOR]', entry);
        
        // Send to parent if in iframe
        if (window.parent !== window) {
            window.parent.postMessage({
                type: 'STATE_UPDATE',
                entry: entry
            }, '*');
        }
    },
    
    checkState: function() {
        const buttons = document.querySelectorAll('.grid.grid-cols-2 button');
        const msg = document.querySelector('p:has-text("Answer submitted")');
        const firstButton = buttons[0];
        
        return {
            hasSubmittedMsg: !!msg,
            msgVisible: msg ? window.getComputedStyle(msg).display !== 'none' : false,
            buttonsCount: buttons.length,
            buttonsDisabled: Array.from(buttons).map(b => b.disabled),
            firstButtonClasses: firstButton ? firstButton.className : 'no button',
            hasPointerEventsNone: firstButton ? firstButton.className.includes('pointer-events-none') : false
        };
    }
};

// Override console.log to capture SUBMISSION_STATE logs
const originalLog = console.log;
console.log = function(...args) {
    if (args[0] && typeof args[0] === 'string' && args[0].includes('SUBMISSION_STATE')) {
        window.stateMonitor.log('CONSOLE_LOG', args);
    }
    originalLog.apply(console, args);
};

// Monitor state changes
setInterval(() => {
    const state = window.stateMonitor.checkState();
    window.stateMonitor.log('STATE_CHECK', state);
}, 250);

console.log('%cState monitor installed!', 'color: green; font-size: 14px');
            `);
            console.log('%c================================', 'color: yellow; font-size: 16px');
            
            // Start checking for updates
            checkInterval = setInterval(updateDisplay, 500);
        }
        
        function updateDisplay() {
            // In a real implementation, we'd receive messages from the game window
            // For now, just show instructions
            if (logEntries.length === 0 && monitoring) {
                addLogEntry({
                    time: 0,
                    event: 'WAITING',
                    data: 'Paste the code above into your game console...'
                });
            }
        }
        
        function addLogEntry(entry) {
            const logDiv = document.getElementById('log');
            const entryDiv = document.createElement('div');
            entryDiv.className = 'log-entry';
            
            const stateClass = entry.data.hasSubmittedMsg && !entry.data.buttonsDisabled?.includes(false) 
                ? 'state-ok' 
                : 'state-bad';
            
            entryDiv.innerHTML = `
                <span class="timestamp">[${entry.time}ms]</span> 
                <strong>${entry.event}</strong>: 
                <span class="${stateClass}">${JSON.stringify(entry.data)}</span>
            `;
            
            logDiv.appendChild(entryDiv);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '<h3>State Change Log:</h3>';
            logEntries = [];
        }
        
        // Listen for messages from game window
        window.addEventListener('message', (event) => {
            if (event.data.type === 'STATE_UPDATE') {
                addLogEntry(event.data.entry);
                logEntries.push(event.data.entry);
            }
        });
    </script>
</body>
</html>