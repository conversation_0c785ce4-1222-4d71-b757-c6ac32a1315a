// Check and process game transition if needed
// Called by clients when they notice transition_deadline has passed

import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface CheckTransitionRequest {
  roomId: string;
}

console.log('[EDGE_FN_LOAD] check-game-transition function script loaded.');

serve(async (req: Request) => {
  console.log(`[CHECK_TRANSITION] Request received. Method: ${req.method}`);
  
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    
    if (!supabaseUrl || !supabaseServiceRoleKey) {
      throw new Error('Missing environment variables')
    }
    
    const { roomId }: CheckTransitionRequest = await req.json()
    
    if (!roomId) {
      throw new Error('Room ID is required')
    }
    
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)
    
    // Fetch room and check if transition is needed
    const { data: room, error: fetchError } = await supabaseAdmin
      .from('game_rooms')
      .select('*')
      .eq('id', roomId)
      .eq('status', 'active')
      .single()
    
    if (fetchError || !room) {
      console.log(`[CHECK_TRANSITION] Room ${roomId} not found or not active`);
      return new Response(JSON.stringify({ 
        message: 'Room not found or not active',
        transitioned: false 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }
    
    // Check if transition is needed
    const now = Date.now();
    const transitionDeadline = room.transition_deadline ? new Date(room.transition_deadline).getTime() : null;
    
    if (!transitionDeadline || now < transitionDeadline) {
      console.log(`[CHECK_TRANSITION] Room ${roomId} not ready for transition yet`);
      return new Response(JSON.stringify({ 
        message: 'Not time for transition yet',
        transitioned: false,
        timeRemaining: transitionDeadline ? transitionDeadline - now : null
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }
    
    console.log(`[CHECK_TRANSITION] Room ${roomId} needs transition, processing...`);
    
    // Call the transition-monitor function to handle the actual transition
    const { data, error } = await supabaseAdmin.functions.invoke('transition-monitor', {
      body: { specificRoomId: roomId }
    })
    
    if (error) {
      console.error(`[CHECK_TRANSITION] Error calling transition-monitor:`, error);
      throw error
    }
    
    return new Response(JSON.stringify({ 
      message: 'Transition processed',
      transitioned: true,
      details: data
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
    
  } catch (error) {
    console.error('[CHECK_TRANSITION] Error:', error);
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})