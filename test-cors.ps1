$uri = "http://127.0.0.1:54321/functions/v1/login-handler"
$headers = @{
    "Content-Type" = "application/json"
    "Origin" = "http://localhost:3000"
}
$body = @{
    identifier = "jkim"
    password = "wrongpassword"
} | ConvertTo-Json

Write-Host "Testing POST request to login-handler..."
Write-Host "URI: $uri"
Write-Host "Body: $body"
Write-Host ""

try {
    $response = Invoke-WebRequest -Uri $uri -Method POST -Headers $headers -Body $body -UseBasicParsing
    Write-Host "Response Status: $($response.StatusCode)"
    Write-Host "Response Headers:"
    $response.Headers | Format-Table -AutoSize
    Write-Host "Response Body:"
    $response.Content
} catch {
    Write-Host "Error occurred:"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
    
    if ($_.Exception.Response) {
        Write-Host "Response Headers:"
        $_.Exception.Response.Headers | Format-Table -AutoSize
        
        # Try to read the response body
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body:"
            Write-Host $responseBody
        } catch {
            Write-Host "Could not read response body: $($_.Exception.Message)"
        }
    }
} 