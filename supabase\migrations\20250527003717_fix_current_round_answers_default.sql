-- Change default for current_round_answers to be an empty array instead of empty object
-- This fixes the frontend error: "current_round_answers is defined but not an array: {}"

-- Step 1: Change the default value for new rows
ALTER TABLE public.game_rooms
ALTER COLUMN current_round_answers SET DEFAULT '[]'::jsonb;

-- Step 2: Update existing rows that have the empty object default to empty array
-- This is safe since we're converting {} to [] which maintains the expected data structure
UPDATE public.game_rooms
SET current_round_answers = '[]'::jsonb
WHERE current_round_answers = '{}'::jsonb;

-- Log the changes for debugging
-- Note: PostgreSQL doesn't have console.log, but we can use RAISE NOTICE for logging
DO $$
BEGIN
    RAISE NOTICE 'Migration completed: current_round_answers default changed from {} to [] and existing empty objects updated';
END $$;
