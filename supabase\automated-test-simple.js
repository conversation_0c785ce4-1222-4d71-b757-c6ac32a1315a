/**
 * Simplified Automated Multiplayer Test
 * Tests multiplayer round advance timing automatically
 */

const puppeteer = require('puppeteer');

const TEST_URL = 'https://recognition-combine.vercel.app/';
const PLAYER1 = { email: 'fresh', password: 'test123' };
const PLAYER2 = { email: 'fresh2', password: 'test123' };

// Test scenarios
const TEST_SCENARIOS = [
  { name: 'Both at 1s → Round at 4s', p1: 1000, p2: 1000, expected: 4000 },
  { name: 'Both at 2s → Round at 5s', p1: 2000, p2: 2000, expected: 5000 },
  { name: 'Both at 5s → Round at 7s', p1: 5000, p2: 5000, expected: 7000 },
  { name: 'Only P1 → Round at 7s', p1: 1000, p2: null, expected: 7000 },
  { name: 'P1 at 1s, P2 at 3s → Round at 6s', p1: 1000, p2: 3000, expected: 6000 }
];

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function runAutomatedTest() {
  console.log('🎯 Fully Automated Multiplayer Round Advance Test');
  console.log('================================================\n');
  
  let browser1, browser2, page1, page2;
  
  try {
    // Launch browsers
    console.log('🚀 Launching browsers...');
    browser1 = await puppeteer.launch({ 
      headless: false,
      args: ['--window-size=1280,800', '--window-position=0,0']
    });
    browser2 = await puppeteer.launch({ 
      headless: false,
      args: ['--window-size=1280,800', '--window-position=640,0']
    });
    
    page1 = await browser1.newPage();
    page2 = await browser2.newPage();
    
    // Navigate to game
    await Promise.all([
      page1.goto(TEST_URL, { waitUntil: 'networkidle2' }),
      page2.goto(TEST_URL, { waitUntil: 'networkidle2' })
    ]);
    
    console.log('✅ Browsers ready');
    
    // Inject monitoring code
    const monitoringCode = `
      window.testData = {
        gameStartTime: null,
        roundChanges: [],
        currentRound: 0
      };
      
      // Monitor for game start and round changes
      let lastQuestion = null;
      setInterval(() => {
        const questionEl = document.querySelector('h2');
        const question = questionEl?.textContent || '';
        
        if (question && question.includes('?') && question !== lastQuestion) {
          const now = Date.now();
          
          if (!window.testData.gameStartTime) {
            window.testData.gameStartTime = now;
            console.log('🎮 Game started at:', new Date(now).toISOString());
          } else {
            const elapsed = now - window.testData.gameStartTime;
            window.testData.roundChanges.push({
              round: window.testData.currentRound++,
              time: elapsed,
              question: question
            });
            console.log('📍 Round', window.testData.currentRound, 'at', elapsed + 'ms');
          }
          
          lastQuestion = question;
        }
      }, 100);
      
      // Helper to submit answer at specific time
      window.submitAt = function(targetMs) {
        if (!window.testData.gameStartTime) {
          console.error('Game not started');
          return;
        }
        
        const elapsed = Date.now() - window.testData.gameStartTime;
        const delay = targetMs - elapsed;
        
        const submitAnswer = () => {
          const buttons = Array.from(document.querySelectorAll('button')).filter(b =>
            b.textContent && b.textContent.match(/^[A-Za-z\\s]+$/) && !b.textContent.includes('Ready')
          );
          if (buttons.length >= 4) {
            const button = buttons[Math.floor(Math.random() * 4)];
            console.log('Submitting:', button.textContent);
            button.click();
          }
        };
        
        if (delay > 0) {
          setTimeout(submitAnswer, delay);
        } else {
          submitAnswer();
        }
      };
    `;
    
    await Promise.all([
      page1.evaluate(monitoringCode),
      page2.evaluate(monitoringCode)
    ]);
    
    console.log('✅ Monitoring code injected\n');
    
    // Sign in both players
    console.log('🔐 Signing in players...');
    
    // Player 1 sign in
    await page1.click('button:has-text("Sign In")');
    await delay(1000);
    await page1.fill('input[type="email"]', PLAYER1.email);
    await page1.fill('input[type="password"]', PLAYER1.password);
    await page1.click('button[type="submit"]');
    
    // Player 2 sign in
    await page2.click('button:has-text("Sign In")');
    await delay(1000);
    await page2.fill('input[type="email"]', PLAYER2.email);
    await page2.fill('input[type="password"]', PLAYER2.password);
    await page2.click('button[type="submit"]');
    
    await delay(3000);
    console.log('✅ Both players signed in\n');
    
    // Create room
    console.log('🏠 Creating room...');
    await page1.click('button:has-text("Create Room")');
    await delay(2000);
    
    // Get room code
    const roomCode = await page1.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*'));
      const codeEl = elements.find(el => el.textContent?.match(/^[A-Z0-9]{6}$/));
      return codeEl?.textContent;
    });
    
    console.log(`✅ Room created: ${roomCode}\n`);
    
    // Join room
    console.log('🏠 Player 2 joining room...');
    await page2.click('button:has-text("Join Room")');
    await delay(1000);
    await page2.fill('input[placeholder*="room code" i]', roomCode);
    await page2.click('button[type="submit"]');
    await delay(2000);
    console.log('✅ Player 2 joined\n');
    
    // Ready up
    console.log('🎮 Players getting ready...');
    await Promise.all([
      page1.click('button:has-text("Ready")'),
      page2.click('button:has-text("Ready")')
    ]);
    await delay(2000);
    
    // Start game
    console.log('🎮 Starting game...');
    await page1.click('button:has-text("Start Game")');
    await delay(3000);
    console.log('✅ Game started!\n');
    
    // Run test scenarios
    console.log('📊 Running test scenarios...');
    console.log('========================\n');
    
    const results = [];
    
    for (let i = 0; i < TEST_SCENARIOS.length; i++) {
      const scenario = TEST_SCENARIOS[i];
      console.log(`Test ${i + 1}: ${scenario.name}`);
      
      // Schedule submissions
      if (scenario.p1 !== null) {
        await page1.evaluate(`window.submitAt(${scenario.p1})`);
      }
      if (scenario.p2 !== null) {
        await page2.evaluate(`window.submitAt(${scenario.p2})`);
      }
      
      // Wait for round to complete
      await delay(8000);
      
      // Get timing from player 1
      const roundTiming = await page1.evaluate((roundIndex) => {
        const changes = window.testData.roundChanges || [];
        return changes[roundIndex];
      }, i);
      
      if (roundTiming) {
        const actualTime = roundTiming.time;
        const difference = Math.abs(actualTime - scenario.expected);
        const passed = difference <= 500; // 500ms tolerance
        
        console.log(`Expected: ${scenario.expected}ms, Actual: ${actualTime}ms`);
        console.log(`Difference: ${difference}ms - ${passed ? '✅ PASS' : '❌ FAIL'}\n`);
        
        results.push({
          scenario: scenario.name,
          expected: scenario.expected,
          actual: actualTime,
          difference,
          passed
        });
      } else {
        console.log('❌ No round change detected\n');
        results.push({
          scenario: scenario.name,
          expected: scenario.expected,
          actual: null,
          passed: false
        });
      }
    }
    
    // Summary
    console.log('\n📈 Test Summary');
    console.log('==============');
    
    const passed = results.filter(r => r.passed).length;
    console.log(`\nPassed: ${passed}/${results.length} tests\n`);
    
    results.forEach((r, i) => {
      console.log(`${i + 1}. ${r.scenario}: ${r.passed ? '✅' : '❌'}`);
    });
    
    // Keep browsers open for inspection
    console.log('\n✅ Test complete! Browsers will remain open for inspection.');
    console.log('Press Ctrl+C to exit.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Clean up on error
    if (browser1) await browser1.close();
    if (browser2) await browser2.close();
    
    process.exit(1);
  }
}

// Run the test
runAutomatedTest().catch(console.error);