# Automated Multiplayer Round Advance Tests

## Quick Start

```powershell
./RUN-AUTOMATED-TESTS.ps1
```

This will guide you through everything!

## What Gets Tested

The automated test suite verifies all round advance timing scenarios:

| Scenario | Player Actions | Expected Result | Rule Applied |
|----------|---------------|-----------------|--------------|
| **FAST** | Both answer at 1s | Round at 4.0s | 1s + 3s transition |
| **MEDIUM** | Both answer at 2s | Round at 5.0s | 2s + 3s transition |
| **LATE** | Both answer at 5s | Round at 7.0s | 7s cap (not 8s) |
| **SOLO** | Only P1 answers | Round at 7.0s | 7s cap |
| **STAGGER** | P1 at 1s, P2 at 3s | Round at 6.0s | 3s + 3s transition |

## Test Files Overview

### 1. **RUN-AUTOMATED-TESTS.ps1** (Start Here!)
- Main entry point
- Checks prerequisites
- Guides through setup
- Runs appropriate test

### 2. **test-multiplayer-automated.js**
- Full automated test
- Controls 2 browsers
- Signs in automatically
- Runs all scenarios
- Measures exact timings
- Reports pass/fail

### 3. **test-multiplayer-simple.js**
- Simplified version
- Prompts for credentials
- Manual game setup
- Automated timing tests

### 4. **create-test-accounts.ps1**
- Helps create test accounts
- Provides SQL scripts
- Opens Supabase dashboard

## Prerequisites

1. **Node.js** - Required for Puppeteer
2. **Test Accounts** - Two accounts in Supabase Auth
3. **Updated Credentials** - In test scripts

## Running Tests

### Option 1: Fully Automated (Recommended)
```bash
# Ensure test accounts exist and update credentials in test-multiplayer-automated.js
node test-multiplayer-automated.js
```

### Option 2: Semi-Automated
```bash
# Prompts for credentials, requires manual game setup
node test-multiplayer-simple.js
```

### Option 3: Guided Setup
```powershell
# Interactive setup and test runner
./RUN-AUTOMATED-TESTS.ps1
```

## Test Output Example

```
🎯 Multiplayer Round Advance Automated Test Suite
================================================

🚀 Setting up browsers...
✅ Browsers ready
🔐 Signing in Player 1...
✅ Player 1 signed in
🔐 Signing in Player 2...
✅ Player 2 signed in
🏠 Creating room...
✅ Room created: ABC123
🚪 Player 2 joining room ABC123...
✅ Both players in room
🎮 Starting game...
✅ Game started

🧪 Testing: FAST - Both at 1s
  📝 player1 answered at 1052ms
  📝 player2 answered at 1048ms
⏱️  Expected: 4000ms
⏱️  Actual: 4023ms
✅ PASS (difference: 23ms)

[... more test results ...]

📊 TEST RESULTS SUMMARY
========================
✅ PASS FAST - Both at 1s
   Expected: 4000ms
   Actual: 4023ms

Total: 5 passed, 0 failed

🎉 All tests passed!
```

## Troubleshooting

### "Chrome not found"
- Puppeteer will download Chrome automatically
- If issues persist, install Chrome manually

### "Authentication failed"
- Ensure test accounts exist in Supabase
- Check email/password are correct
- Verify accounts are confirmed

### "Room creation failed"
- Check if multiplayer is enabled
- Verify Edge Functions are deployed
- Check Supabase Realtime is working

### "Timing off by >500ms"
- Check network latency
- Ensure no other apps using bandwidth
- Run tests multiple times

## Understanding Results

- **PASS**: Timing within 500ms tolerance
- **FAIL**: Timing exceeds tolerance
- Results show expected vs actual times
- All scenarios should pass for proper implementation

## Manual Verification

After automated tests, you can manually verify by:
1. Opening two game windows
2. Using F12 console monitoring
3. Testing edge cases
4. Checking visual transitions