import { useEffect, useRef, useState } from 'react';

interface QueuedAnimation {
  id: string;
  type: 'enhanced' | 'fallback';
  trigger: number;
  scoreIncrease?: number;
  bonusLevel?: number;
  originPosition: { x: number; y: number } | null;
  timestamp: number;
}

/**
 * Hook to manage animation queue for when tab visibility changes
 * Ensures animations play even if triggered while tab was hidden
 */
export function useAnimationQueue() {
  const [isVisible, setIsVisible] = useState(!document.hidden);
  const animationQueueRef = useRef<QueuedAnimation[]>([]);
  const [pendingAnimations, setPendingAnimations] = useState<QueuedAnimation[]>([]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const visible = !document.hidden;
      console.log('[AnimationQueue] Visibility changed:', { 
        visible, 
        queuedCount: animationQueueRef.current.length 
      });
      
      setIsVisible(visible);

      // When tab becomes visible, process queued animations
      if (visible && animationQueueRef.current.length > 0) {
        console.log('[AnimationQueue] Tab visible, processing queued animations:', 
          animationQueueRef.current.length);
        
        // Move queued animations to pending state
        setPendingAnimations([...animationQueueRef.current]);
        
        // Clear the queue
        animationQueueRef.current = [];
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Check initial visibility
    handleVisibilityChange();

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const queueAnimation = (animation: Omit<QueuedAnimation, 'timestamp'>) => {
    if (!isVisible) {
      console.log('[AnimationQueue] Tab hidden, queueing animation:', animation.id);
      animationQueueRef.current.push({
        ...animation,
        timestamp: Date.now()
      });
    }
  };

  const clearPendingAnimations = () => {
    setPendingAnimations([]);
  };

  return {
    isVisible,
    queueAnimation,
    pendingAnimations,
    clearPendingAnimations
  };
}