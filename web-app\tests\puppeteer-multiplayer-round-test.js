const puppeteer = require('puppeteer');

// Test configuration
const TEST_URL = 'http://localhost:3000';
const TIMEOUT = 60000; // 60 seconds for long operations
const SHORT_WAIT = 2000;
const TRANSITION_WAIT = 4000;

// Test credentials (you'll need to set these)
const HOST_EMAIL = process.env.TEST_HOST_EMAIL || '<EMAIL>';
const HOST_PASSWORD = process.env.TEST_HOST_PASSWORD || 'testpass123';
const PLAYER_EMAIL = process.env.TEST_PLAYER_EMAIL || '<EMAIL>';
const PLAYER_PASSWORD = process.env.TEST_PLAYER_PASSWORD || 'testpass123';

class MultiplayerRoundTest {
  constructor() {
    this.hostBrowser = null;
    this.playerBrowser = null;
    this.hostPage = null;
    this.playerPage = null;
    this.roomCode = '';
    this.testResults = {
      roomCreation: false,
      playerJoin: false,
      gameStart: false,
      roundsCompleted: 0,
      totalRounds: 10,
      errors: [],
      timestamps: {}
    };
  }

  async initialize() {
    console.log('🚀 Initializing Puppeteer browsers...');
    
    // Launch browsers with specific args for stability
    const browserOptions = {
      headless: false, // Set to true for CI
      executablePath: '/snap/bin/chromium', // Use system Chromium
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ],
      defaultViewport: { width: 1280, height: 720 }
    };

    this.hostBrowser = await puppeteer.launch(browserOptions);
    this.playerBrowser = await puppeteer.launch(browserOptions);
    
    this.hostPage = await this.hostBrowser.newPage();
    this.playerPage = await this.playerBrowser.newPage();
    
    // Enable console logging
    this.hostPage.on('console', msg => {
      if (msg.type() === 'error') console.log('[HOST ERROR]:', msg.text());
    });
    
    this.playerPage.on('console', msg => {
      if (msg.type() === 'error') console.log('[PLAYER ERROR]:', msg.text());
    });
    
    console.log('✅ Browsers initialized');
  }

  async cleanup() {
    console.log('🧹 Cleaning up browsers...');
    if (this.hostBrowser) await this.hostBrowser.close();
    if (this.playerBrowser) await this.playerBrowser.close();
  }

  async waitForSelector(page, selector, options = {}) {
    try {
      await page.waitForSelector(selector, { timeout: TIMEOUT, ...options });
      return true;
    } catch (error) {
      console.error(`Failed to find selector: ${selector}`, error.message);
      this.testResults.errors.push(`Selector not found: ${selector}`);
      return false;
    }
  }

  async clickButton(page, buttonText, pageName = 'Page') {
    try {
      await page.evaluate((text) => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const button = buttons.find(btn => btn.textContent.includes(text));
        if (button) {
          button.click();
          return true;
        }
        throw new Error(`Button with text "${text}" not found`);
      }, buttonText);
      console.log(`[${pageName}] ✓ Clicked: "${buttonText}"`);
      return true;
    } catch (error) {
      console.error(`[${pageName}] Failed to click: "${buttonText}"`, error.message);
      this.testResults.errors.push(`${pageName}: Failed to click "${buttonText}"`);
      return false;
    }
  }

  async loginUser(page, email, password, userName) {
    console.log(`\n🔐 Logging in ${userName}...`);
    
    // Click Multiplayer Mode to trigger auth modal
    await this.clickButton(page, 'Multiplayer Mode', userName);
    await page.waitForTimeout(SHORT_WAIT);
    
    // Fill login form
    await page.type('input[type="email"], input[placeholder*="Username"], input[placeholder*="Email"]', email);
    await page.type('input[type="password"]', password);
    
    // Submit login
    await this.clickButton(page, 'Sign in', userName);
    
    // Wait for auth to complete
    await page.waitForTimeout(TRANSITION_WAIT);
    
    // Check if logged in by looking for multiplayer lobby
    const isLoggedIn = await page.evaluate(() => {
      return document.body.textContent.includes('Create a New Game') || 
             document.body.textContent.includes('Active Games');
    });
    
    if (isLoggedIn) {
      console.log(`[${userName}] ✅ Login successful`);
      return true;
    } else {
      console.log(`[${userName}] ❌ Login failed`);
      return false;
    }
  }

  async createRoom() {
    console.log('\n📝 Step 1: Creating room as host...');
    this.testResults.timestamps.roomCreationStart = Date.now();
    
    // Navigate to the app
    await this.hostPage.goto(TEST_URL);
    await this.hostPage.waitForLoadState('networkidle');
    
    // Login host
    if (!await this.loginUser(this.hostPage, HOST_EMAIL, HOST_PASSWORD, 'HOST')) {
      throw new Error('Host login failed');
    }
    
    // Create room
    await this.clickButton(this.hostPage, 'Create a New Game', 'HOST');
    await this.hostPage.waitForTimeout(SHORT_WAIT);
    
    // Get room code
    this.roomCode = await this.hostPage.evaluate(() => {
      const roomHeader = document.querySelector('h2');
      if (roomHeader && roomHeader.textContent.includes('Room:')) {
        const match = roomHeader.textContent.match(/Room: (.*)/);
        return match ? match[1].trim() : '';
      }
      return '';
    });
    
    if (this.roomCode) {
      console.log(`[HOST] ✅ Room created: ${this.roomCode}`);
      this.testResults.roomCreation = true;
      this.testResults.timestamps.roomCreationEnd = Date.now();
      await this.hostPage.screenshot({ path: 'test-screenshots/01-room-created.png' });
      return true;
    } else {
      throw new Error('Failed to create room');
    }
  }

  async joinRoom() {
    console.log('\n👥 Step 2: Player joining room...');
    this.testResults.timestamps.playerJoinStart = Date.now();
    
    // Navigate to the app
    await this.playerPage.goto(TEST_URL);
    await this.playerPage.waitForLoadState('networkidle');
    
    // Login player
    if (!await this.loginUser(this.playerPage, PLAYER_EMAIL, PLAYER_PASSWORD, 'PLAYER')) {
      throw new Error('Player login failed');
    }
    
    // Look for the room in active games
    await this.playerPage.waitForTimeout(SHORT_WAIT);
    
    // Check if room is visible
    const roomVisible = await this.playerPage.evaluate((code) => {
      return document.body.textContent.includes(code);
    }, this.roomCode);
    
    if (!roomVisible) {
      throw new Error(`Room ${this.roomCode} not visible to player`);
    }
    
    // Click join button
    await this.clickButton(this.playerPage, 'Join This Room', 'PLAYER');
    await this.playerPage.waitForTimeout(SHORT_WAIT);
    
    // Verify joined
    const joined = await this.playerPage.evaluate((code) => {
      const roomHeader = document.querySelector('h2');
      return roomHeader && roomHeader.textContent.includes(`Room: ${code}`);
    }, this.roomCode);
    
    if (joined) {
      console.log('[PLAYER] ✅ Successfully joined room');
      this.testResults.playerJoin = true;
      this.testResults.timestamps.playerJoinEnd = Date.now();
      await this.playerPage.screenshot({ path: 'test-screenshots/02-player-joined.png' });
      return true;
    } else {
      throw new Error('Failed to join room');
    }
  }

  async readyUpAndStart() {
    console.log('\n🎮 Step 3: Ready up and start game...');
    this.testResults.timestamps.gameStartBegin = Date.now();
    
    // Player ready up
    await this.clickButton(this.playerPage, 'Ready Up', 'PLAYER');
    await this.playerPage.waitForTimeout(1000);
    
    // Host ready up
    await this.clickButton(this.hostPage, 'Ready Up', 'HOST');
    await this.hostPage.waitForTimeout(1000);
    
    // Wait for Start Game button to be enabled
    const startEnabled = await this.hostPage.waitForFunction(
      () => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const startBtn = buttons.find(b => b.textContent.includes('Start Game'));
        return startBtn && !startBtn.disabled;
      },
      { timeout: 10000 }
    );
    
    if (!startEnabled) {
      throw new Error('Start Game button never became enabled');
    }
    
    // Start the game
    await this.clickButton(this.hostPage, 'Start Game', 'HOST');
    
    // Wait for countdown
    const countdownStarted = await Promise.all([
      this.hostPage.waitForFunction(() => document.body.textContent.includes('Get Ready!'), { timeout: 10000 }),
      this.playerPage.waitForFunction(() => document.body.textContent.includes('Get Ready!'), { timeout: 10000 })
    ]);
    
    console.log('[GAME] ✅ Countdown started on both screens');
    await this.hostPage.screenshot({ path: 'test-screenshots/03-countdown.png' });
    
    // Wait for game to actually start (player image appears)
    await Promise.all([
      this.hostPage.waitForSelector('img[alt*="player"]', { timeout: 15000 }),
      this.playerPage.waitForSelector('img[alt*="player"]', { timeout: 15000 })
    ]);
    
    console.log('[GAME] ✅ Game started - first question loaded');
    this.testResults.gameStart = true;
    this.testResults.timestamps.gameStartEnd = Date.now();
    return true;
  }

  async playRound(roundNumber) {
    console.log(`\n🎯 Playing round ${roundNumber}...`);
    const roundStart = Date.now();
    
    try {
      // Ensure player images are loaded
      await Promise.all([
        this.hostPage.waitForSelector('img[alt*="player"]', { timeout: 10000 }),
        this.playerPage.waitForSelector('img[alt*="player"]', { timeout: 10000 })
      ]);
      
      // Get the player name from image alt text (for logging)
      const playerName = await this.hostPage.evaluate(() => {
        const img = document.querySelector('img[alt*="player"]');
        return img ? img.alt : 'Unknown';
      });
      console.log(`[ROUND ${roundNumber}] Player shown: ${playerName}`);
      
      // Get choices for both players
      const [hostChoices, playerChoices] = await Promise.all([
        this.hostPage.$$eval('button.bg-slate-700', buttons => buttons.map(b => b.textContent)),
        this.playerPage.$$eval('button.bg-slate-700', buttons => buttons.map(b => b.textContent))
      ]);
      
      console.log(`[ROUND ${roundNumber}] Host sees ${hostChoices.length} choices`);
      console.log(`[ROUND ${roundNumber}] Player sees ${playerChoices.length} choices`);
      
      if (hostChoices.length < 4 || playerChoices.length < 4) {
        console.warn(`[ROUND ${roundNumber}] ⚠️  Expected 4 choices, got ${hostChoices.length} for host, ${playerChoices.length} for player`);
      }
      
      // Submit answers (different choices for variety)
      const hostChoiceIndex = roundNumber % 4; // Rotate through choices
      const playerChoiceIndex = (roundNumber + 2) % 4; // Different pattern
      
      await Promise.all([
        this.hostPage.evaluate((index) => {
          const buttons = document.querySelectorAll('button.bg-slate-700');
          if (buttons[index]) buttons[index].click();
        }, hostChoiceIndex),
        this.playerPage.evaluate((index) => {
          const buttons = document.querySelectorAll('button.bg-slate-700');
          if (buttons[index]) buttons[index].click();
        }, playerChoiceIndex)
      ]);
      
      console.log(`[ROUND ${roundNumber}] ✓ Both players submitted answers`);
      
      // Wait for transition
      await this.hostPage.waitForTimeout(TRANSITION_WAIT);
      
      // Check if game ended
      const gameEnded = await this.hostPage.evaluate(() => {
        return !!document.querySelector('button:has-text("Play Again")') ||
               document.body.textContent.includes('Final Scores');
      });
      
      const roundDuration = Date.now() - roundStart;
      console.log(`[ROUND ${roundNumber}] ✅ Completed in ${roundDuration}ms`);
      
      this.testResults.roundsCompleted = roundNumber;
      
      // Take screenshot every 3 rounds
      if (roundNumber % 3 === 0) {
        await this.hostPage.screenshot({ path: `test-screenshots/round-${roundNumber}.png` });
      }
      
      return !gameEnded;
    } catch (error) {
      console.error(`[ROUND ${roundNumber}] ❌ Error:`, error.message);
      this.testResults.errors.push(`Round ${roundNumber}: ${error.message}`);
      return false;
    }
  }

  async playFullGame() {
    console.log('\n🎮 Step 4: Playing full game (10 rounds)...');
    this.testResults.timestamps.gameplayStart = Date.now();
    
    for (let round = 1; round <= this.testResults.totalRounds; round++) {
      const continueGame = await this.playRound(round);
      if (!continueGame) {
        console.log(`[GAME] Game ended after round ${round}`);
        break;
      }
    }
    
    this.testResults.timestamps.gameplayEnd = Date.now();
    const totalGameTime = (this.testResults.timestamps.gameplayEnd - this.testResults.timestamps.gameplayStart) / 1000;
    console.log(`[GAME] ✅ Completed ${this.testResults.roundsCompleted} rounds in ${totalGameTime.toFixed(1)}s`);
  }

  async verifyGameCompletion() {
    console.log('\n🏁 Step 5: Verifying game completion...');
    
    // Wait for game to fully end
    await Promise.all([
      this.hostPage.waitForSelector('button:has-text("Play Again")', { timeout: 15000 }),
      this.playerPage.waitForSelector('button:has-text("Play Again")', { timeout: 15000 })
    ]);
    
    // Get final scores
    const hostScores = await this.hostPage.evaluate(() => {
      const scoreElements = Array.from(document.querySelectorAll('*'));
      const scores = {};
      scoreElements.forEach(el => {
        const text = el.textContent || '';
        if (text.includes('points') || text.includes('Points')) {
          const match = text.match(/(\w+).*?(\d+)\s*points?/i);
          if (match) {
            scores[match[1]] = parseInt(match[2]);
          }
        }
      });
      return scores;
    });
    
    console.log('[GAME] Final scores:', hostScores);
    
    await this.hostPage.screenshot({ path: 'test-screenshots/game-complete.png' });
    
    // Test rematch
    console.log('\n🔄 Testing rematch functionality...');
    await this.clickButton(this.hostPage, 'Play Again', 'HOST');
    await this.clickButton(this.playerPage, 'Play Again', 'PLAYER');
    
    // Wait for ready buttons to reappear
    const rematchReady = await Promise.all([
      this.hostPage.waitForSelector('button:has-text("Ready Up")', { timeout: 10000 }),
      this.playerPage.waitForSelector('button:has-text("Ready Up")', { timeout: 10000 })
    ]);
    
    if (rematchReady) {
      console.log('[REMATCH] ✅ Successfully returned to lobby');
    }
    
    return true;
  }

  async testEdgeCases() {
    console.log('\n🔧 Step 6: Testing edge cases...');
    
    // Test 1: Player disconnect mid-game
    console.log('\n[EDGE CASE] Testing player disconnect...');
    
    // Start a new game quickly
    await this.clickButton(this.playerPage, 'Ready Up', 'PLAYER');
    await this.clickButton(this.hostPage, 'Ready Up', 'HOST');
    await this.hostPage.waitForTimeout(1000);
    await this.clickButton(this.hostPage, 'Start Game', 'HOST');
    
    // Wait for game to start
    await this.hostPage.waitForSelector('img[alt*="player"]', { timeout: 15000 });
    
    // Close player page to simulate disconnect
    await this.playerPage.close();
    console.log('[EDGE CASE] Player disconnected');
    
    // Wait for host to detect disconnect
    await this.hostPage.waitForTimeout(5000);
    
    // Check if host shows player as disconnected
    const disconnectDetected = await this.hostPage.evaluate(() => {
      return document.body.textContent.includes('disconnected') ||
             document.body.textContent.includes('offline') ||
             !!document.querySelector('.bg-red-900');
    });
    
    console.log(`[EDGE CASE] Disconnect detected by host: ${disconnectDetected}`);
    
    // Reopen player page and try to rejoin
    this.playerPage = await this.playerBrowser.newPage();
    await this.playerPage.goto(TEST_URL);
    
    // Login and look for rejoin option
    await this.loginUser(this.playerPage, PLAYER_EMAIL, PLAYER_PASSWORD, 'PLAYER');
    
    const rejoinAvailable = await this.playerPage.evaluate((code) => {
      return document.body.textContent.includes('Rejoin') || 
             document.body.textContent.includes(code);
    }, this.roomCode);
    
    console.log(`[EDGE CASE] Rejoin option available: ${rejoinAvailable}`);
    
    return true;
  }

  async runFullTest() {
    console.log('🏁 Starting Comprehensive Multiplayer Round Advancement Test');
    console.log('=' .repeat(60));
    
    try {
      await this.initialize();
      await this.createRoom();
      await this.joinRoom();
      await this.readyUpAndStart();
      await this.playFullGame();
      await this.verifyGameCompletion();
      await this.testEdgeCases();
      
      // Generate test report
      console.log('\n📊 TEST RESULTS');
      console.log('=' .repeat(60));
      console.log(`Room Creation: ${this.testResults.roomCreation ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Player Join: ${this.testResults.playerJoin ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Game Start: ${this.testResults.gameStart ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Rounds Completed: ${this.testResults.roundsCompleted}/${this.testResults.totalRounds}`);
      console.log(`Errors: ${this.testResults.errors.length}`);
      
      if (this.testResults.errors.length > 0) {
        console.log('\nErrors encountered:');
        this.testResults.errors.forEach(err => console.log(`  - ${err}`));
      }
      
      // Timing analysis
      console.log('\n⏱️  TIMING ANALYSIS');
      console.log('=' .repeat(60));
      const timings = this.testResults.timestamps;
      if (timings.roomCreationEnd && timings.roomCreationStart) {
        console.log(`Room Creation: ${(timings.roomCreationEnd - timings.roomCreationStart) / 1000}s`);
      }
      if (timings.playerJoinEnd && timings.playerJoinStart) {
        console.log(`Player Join: ${(timings.playerJoinEnd - timings.playerJoinStart) / 1000}s`);
      }
      if (timings.gameStartEnd && timings.gameStartBegin) {
        console.log(`Game Start: ${(timings.gameStartEnd - timings.gameStartBegin) / 1000}s`);
      }
      if (timings.gameplayEnd && timings.gameplayStart) {
        const avgRoundTime = (timings.gameplayEnd - timings.gameplayStart) / 1000 / this.testResults.roundsCompleted;
        console.log(`Average Round Time: ${avgRoundTime.toFixed(1)}s`);
      }
      
    } catch (error) {
      console.error('\n❌ TEST FAILED:', error.message);
      console.error(error.stack);
      
      // Take error screenshots
      if (this.hostPage) {
        await this.hostPage.screenshot({ path: 'test-screenshots/error-host.png' }).catch(() => {});
      }
      if (this.playerPage) {
        await this.playerPage.screenshot({ path: 'test-screenshots/error-player.png' }).catch(() => {});
      }
    } finally {
      await this.cleanup();
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new MultiplayerRoundTest();
  test.runFullTest().then(() => {
    console.log('\n✅ Test execution completed');
    process.exit(0);
  }).catch(error => {
    console.error('\n❌ Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = MultiplayerRoundTest;