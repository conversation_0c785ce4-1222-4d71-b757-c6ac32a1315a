# Simplified Edge Function deployment script
# This script deploys Edge Functions using minimal parameters

param(
    [Parameter(Mandatory=$true)]
    [string]$FunctionName
)

Write-Host "`nDeploying Edge Function: $FunctionName" -ForegroundColor Cyan

# Change to supabase directory
$supabaseDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $supabaseDir

Write-Host "Working directory: $(Get-Location)" -ForegroundColor Gray

# Method 1: Try with explicit working directory
Write-Host "`nMethod 1: Deploying with explicit working directory..." -ForegroundColor Yellow
$env:SUPABASE_WORKDIR = $supabaseDir
npx supabase@latest functions deploy $FunctionName --project-ref xmyxuvuimebjltnaamox --no-verify-jwt

if ($LASTEXITCODE -ne 0) {
    Write-Host "`nMethod 1 failed. Trying Method 2..." -ForegroundColor Yellow
    
    # Method 2: Deploy from parent directory
    Set-Location ..
    Write-Host "Changed to parent directory: $(Get-Location)" -ForegroundColor Gray
    npx supabase@latest functions deploy $FunctionName --project-ref xmyxuvuimebjltnaamox --no-verify-jwt --workdir supabase
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "`nMethod 2 failed. Trying Method 3..." -ForegroundColor Yellow
        
        # Method 3: Use supabase link and deploy
        Set-Location $supabaseDir
        npx supabase@latest link --project-ref xmyxuvuimebjltnaamox
        npx supabase@latest functions deploy $FunctionName --no-verify-jwt
    }
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nDeployment successful!" -ForegroundColor Green
    Write-Host "Edge Function '$FunctionName' has been deployed." -ForegroundColor Cyan
} else {
    Write-Host "`nAll deployment methods failed." -ForegroundColor Red
    Write-Host "Please check the error messages above." -ForegroundColor Yellow
}

Write-Host "`nTo test multiplayer functionality: node test-multiplayer-game-flow.js" -ForegroundColor Cyan