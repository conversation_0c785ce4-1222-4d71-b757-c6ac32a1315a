// Event-based version - the last answer triggers the transition directly
// This is a modified version that advances the game immediately when appropriate

import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

// ... [Include all the same type definitions and helper functions from the original]

serve(async (req: Request) => {
  // ... [Include all the same authentication and validation logic]
  
  try {
    // ... [Include all the validation up to line 405]
    
    // CRITICAL FIX: Check if this is the final answer and advance game if so
    const allPlayersHaveAnswered = updatedAnswers.length === playersInRoom.length;
    console.log(`[EDGE_SUBMIT_ANSWER] Answer count check: ${updatedAnswers.length}/${playersInRoom.length} players answered. Final answer: ${allPlayersHaveAnswered}`);

    if (allPlayersHaveAnswered) {
      // All players have answered - advance after 3 seconds
      console.log('[EDGE_SUBMIT_ANSWER] This is the final answer. Will advance in 3 seconds...');
      
      // First, update with the answer and set transition flag
      const transitionDeadline = new Date(Date.now() + 3000).toISOString();
      
      await supabaseAdmin
        .from('game_rooms')
        .update({
          current_round_answers: updatedAnswers,
          player_scores: updatedScores,
          player_bonus_levels: updatedBonusLevels,
          transition_until: transitionDeadline, // Use existing field for backward compatibility
          last_activity_timestamp: new Date().toISOString()
        })
        .eq('id', roomId)
        .eq('status', 'active')
      
      // Schedule the actual transition using Deno's setTimeout
      setTimeout(async () => {
        console.log(`[EDGE_SUBMIT_ANSWER] Executing scheduled transition for room ${roomId}`);
        
        try {
          // Generate next question
          const playerData = await loadPlayerData();
          const askedPlayerIds = new Set<number>();
          // ... [Add question generation logic]
          
          const nextQuestion = generateQuestion(playerData, askedPlayerIds);
          
          if (nextQuestion) {
            // Advance to next question
            await supabaseAdmin
              .from('game_rooms')
              .update({
                current_round_number: roomData.current_round_number + 1,
                current_question_data: nextQuestion,
                current_round_answers: [],
                question_started_at: new Date().toISOString(),
                transition_until: null,
                last_activity_timestamp: new Date().toISOString()
              })
              .eq('id', roomId)
              .eq('status', 'active')
            
            console.log(`[EDGE_SUBMIT_ANSWER] Game ${roomId} advanced to round ${roomData.current_round_number + 1}`);
          }
        } catch (error) {
          console.error(`[EDGE_SUBMIT_ANSWER] Error during scheduled transition:`, error);
        }
      }, 3000);
      
      // Return immediate response
      return new Response(JSON.stringify({ 
        message: 'Final answer submitted! Next question in 3 seconds...',
        answer: newAnswer,
        isCorrect: validChoice.isCorrect,
        newScore: updatedScores[userId] || 0,
        totalAnswers: updatedAnswers.length,
        inTransition: true,
        transitionDeadline,
        timestamp: new Date().toISOString()
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }
    
    // ... [Rest of the non-final answer logic]
  } catch (error) {
    // ... [Error handling]
  }
})