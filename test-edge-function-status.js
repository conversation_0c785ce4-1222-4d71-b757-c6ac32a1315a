const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://xmyxuvuimebjltnaamox.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testEdgeFunction() {
  console.log('=== Testing Edge Function Status ===\n');
  
  try {
    // First sign in to get a valid token
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (authError) {
      console.error('Auth error:', authError);
      
      // Try to create the user if it doesn't exist
      console.log('\nTrying to create user...');
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: {
            username: 'fresh'
          }
        }
      });
      
      if (signUpError) {
        console.error('Sign up error:', signUpError);
        return;
      }
      
      console.log('User created successfully');
    } else {
      console.log('✓ Authenticated successfully');
    }
    
    // Now test the edge function with a minimal request
    console.log('\nTesting edge function...');
    const { data, error } = await supabase.functions.invoke('start-game-handler', {
      body: { roomId: 'test-room-123' }
    });
    
    if (error) {
      console.error('Edge function error:', error);
      console.error('Error details:', {
        message: error.message,
        context: error.context,
        status: error.status
      });
      
      // If it's a validation error, that's actually good - it means the function is deployed
      if (error.message && (error.message.includes('Room not found') || 
                           error.message.includes('not authenticated'))) {
        console.log('\n✓ Edge function is deployed and responding (validation error is expected)');
      } else {
        console.log('\n✗ Edge function returned an unexpected error');
      }
    } else {
      console.log('Edge function response:', data);
    }
    
  } catch (err) {
    console.error('Exception:', err);
  }
}

testEdgeFunction();