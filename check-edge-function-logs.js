const puppeteer = require('puppeteer');
const os = require('os');
const fs = require('fs').promises;

async function checkEdgeFunctionLogs() {
  console.log('=== Checking Edge Function Logs ===');
  
  // Detect environment
  const platform = os.platform();
  const isWSL = platform === 'linux' && os.release().toLowerCase().includes('microsoft');
  
  let executablePath;
  if (isWSL) {
    executablePath = '/usr/bin/chromium-browser';
  }
  
  const browser = await puppeteer.launch({
    headless: true,
    ...(executablePath && { executablePath }),
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Capture all console logs
    const logs = [];
    page.on('console', msg => {
      const text = msg.text();
      logs.push({
        type: msg.type(),
        text: text,
        timestamp: new Date().toISOString()
      });
      
      // Log edge function related messages
      if (text.includes('EDGE_START_GAME') || 
          text.includes('start-game-handler') ||
          text.includes('Start Game') ||
          text.includes('firstQuestion') ||
          text.includes('current_question_data')) {
        console.log(`[Browser ${msg.type()}] ${text}`);
      }
    });
    
    // Navigate to the app
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Login
    console.log('\n1. Logging in...');
    await page.click('button:has-text("Login")');
    await page.waitForTimeout(1000);
    
    await page.type('input[type="text"]', 'fresh');
    await page.type('input[type="password"]', 'test123');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);
    
    // Go to multiplayer
    console.log('2. Going to multiplayer...');
    await page.click('button:has-text("Multiplayer")');
    await page.waitForTimeout(2000);
    
    // Re-auth for multiplayer
    const needsAuth = await page.$('input[type="text"]');
    if (needsAuth) {
      await page.type('input[type="text"]', 'fresh');
      await page.type('input[type="password"]', 'test123');
      await page.click('button[type="submit"]');
      await page.waitForTimeout(2000);
    }
    
    // Create room
    console.log('3. Creating room...');
    await page.click('button:has-text("Host Game")');
    await page.waitForTimeout(2000);
    
    // Mark ready
    console.log('4. Marking ready...');
    await page.click('button:has-text("Ready")');
    await page.waitForTimeout(1000);
    
    // Start game and capture network requests
    console.log('5. Starting game and monitoring network...\n');
    
    // Set up request interception
    await page.setRequestInterception(true);
    
    page.on('request', request => {
      if (request.url().includes('start-game-handler')) {
        console.log('\n[NETWORK REQUEST] start-game-handler');
        console.log('URL:', request.url());
        console.log('Method:', request.method());
        console.log('Headers:', request.headers());
        if (request.method() === 'POST') {
          console.log('Body:', request.postData());
        }
      }
      request.continue();
    });
    
    page.on('response', response => {
      if (response.url().includes('start-game-handler')) {
        console.log('\n[NETWORK RESPONSE] start-game-handler');
        console.log('Status:', response.status());
        console.log('Headers:', response.headers());
        response.text().then(body => {
          console.log('Body:', body);
          try {
            const parsed = JSON.parse(body);
            console.log('\nParsed response:');
            console.log(JSON.stringify(parsed, null, 2));
          } catch (e) {
            // Not JSON
          }
        }).catch(e => {
          console.log('Could not read body:', e.message);
        });
      }
    });
    
    // Click start game
    await page.click('button:has-text("Start Game")');
    
    // Wait a bit to capture responses
    await page.waitForTimeout(5000);
    
    // Check current page state
    console.log('\n6. Checking page state...');
    const pageContent = await page.content();
    
    if (pageContent.includes('Waiting for game to start')) {
      console.log('⚠️  Page still shows "Waiting for game to start"');
    } else if (pageContent.includes('Game Starting')) {
      console.log('⚠️  Page shows "Game Starting..." (loading state)');
    } else if (pageContent.includes('Player to identify')) {
      console.log('✓ Game started successfully!');
    }
    
    // Save all logs
    const logFile = 'edge-function-debug-logs.json';
    await fs.writeFile(logFile, JSON.stringify(logs, null, 2));
    console.log(`\nAll logs saved to: ${logFile}`);
    
  } finally {
    await browser.close();
  }
}

checkEdgeFunctionLogs().catch(console.error);