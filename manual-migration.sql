-- Manual Migration Script for Multiplayer Transition Feature
-- Run this in Supabase SQL Editor: https://supabase.com/dashboard/project/xmyxuvuimebjltnaamox/editor

-- First, check what columns already exist
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
    AND table_name = 'game_rooms'
    AND column_name IN ('player_bonus_levels', 'transition_until', 'next_question_data')
ORDER BY column_name;

-- Add missing columns with existence checks
DO $$ 
BEGIN
    -- Add player_bonus_levels column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'player_bonus_levels') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN player_bonus_levels jsonb NOT NULL DEFAULT '{}'::jsonb;
        
        COMMENT ON COLUMN public.game_rooms.player_bonus_levels IS 'Tracks consecutive correct answer bonuses for each player';
        RAISE NOTICE 'Added player_bonus_levels column';
    ELSE
        RAISE NOTICE 'player_bonus_levels column already exists';
    END IF;

    -- Add transition_until column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'transition_until') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN transition_until timestamptz DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.transition_until IS 'Timestamp indicating when the transition period ends and the next question should be shown. NULL when not in transition.';
        RAISE NOTICE 'Added transition_until column';
    ELSE
        RAISE NOTICE 'transition_until column already exists';
    END IF;

    -- Add next_question_data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'next_question_data') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN next_question_data jsonb DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.next_question_data IS 'The next question data that will become active after the transition period ends.';
        RAISE NOTICE 'Added next_question_data column';
    ELSE
        RAISE NOTICE 'next_question_data column already exists';
    END IF;
END$$;

-- Create index on transition_until for efficient queries if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE schemaname = 'public' 
                   AND tablename = 'game_rooms' 
                   AND indexname = 'idx_game_rooms_transition_until') THEN
        CREATE INDEX idx_game_rooms_transition_until ON public.game_rooms(transition_until) 
        WHERE transition_until IS NOT NULL;
        RAISE NOTICE 'Added index on transition_until';
    ELSE
        RAISE NOTICE 'Index on transition_until already exists';
    END IF;
END$$;

-- Verify the final state
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
    AND table_name = 'game_rooms'
    AND column_name IN ('player_bonus_levels', 'transition_until', 'next_question_data')
ORDER BY column_name;

-- Mark migrations as applied in the schema_migrations table
INSERT INTO supabase_migrations.schema_migrations (version, name, statements)
VALUES 
    ('20250128000000', 'add_player_bonus_levels', ARRAY['ALTER TABLE public.game_rooms ADD COLUMN player_bonus_levels jsonb NOT NULL DEFAULT ''{}''::jsonb']),
    ('20250129000000', 'add_transition_until_column', ARRAY['ALTER TABLE public.game_rooms ADD COLUMN transition_until timestamptz DEFAULT NULL', 'ALTER TABLE public.game_rooms ADD COLUMN next_question_data jsonb DEFAULT NULL'])
ON CONFLICT (version) DO NOTHING;