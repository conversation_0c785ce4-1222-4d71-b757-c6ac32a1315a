const puppeteer = require('puppeteer');

async function testPageLoad() {
  console.log('=== Testing Page Load ===');
  
  let browser;
  try {
    // Try to detect browser
    let executablePath;
    if (process.platform === 'linux') {
      const fs = require('fs').promises;
      // Check for WSL chromium
      try {
        await fs.access('/usr/bin/chromium-browser');
        executablePath = '/usr/bin/chromium-browser';
        console.log('Found chromium-browser');
      } catch (e) {
        console.log('No chromium-browser found, using bundled');
      }
    }
    
    browser = await puppeteer.launch({
      headless: true,
      executablePath,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => console.log('Browser console:', msg.text()));
    page.on('pageerror', err => console.log('Page error:', err.message));
    
    // Monitor network requests
    page.on('requestfailed', request => {
      console.log('Failed request:', request.url(), '-', request.failure().errorText);
    });
    
    console.log('Navigating to http://localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'domcontentloaded' });
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check page content
    const pageContent = await page.evaluate(() => {
      return {
        title: document.title,
        bodyText: document.body.innerText.substring(0, 200),
        hasButtons: document.querySelectorAll('button').length,
        buttonTexts: Array.from(document.querySelectorAll('button')).map(b => b.textContent.trim()),
        hasLoading: document.body.textContent.includes('Loading...')
      };
    });
    
    console.log('Page content:', JSON.stringify(pageContent, null, 2));
    
    // Take screenshot
    await page.screenshot({ path: 'test-page-load.png' });
    console.log('Screenshot saved as test-page-load.png');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    if (browser) await browser.close();
  }
}

testPageLoad();