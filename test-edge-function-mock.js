// Test if the Edge Function mock is working by checking its response structure
const https = require('https');

async function testMockEdgeFunction() {
  console.log('=== Testing Mock Edge Function Response ===\n');
  
  // Use a test room ID that doesn't need to exist for the mock
  const roomId = 'mock-test-room-' + Date.now();
  const data = JSON.stringify({ roomId });
  
  // Use a dummy JWT token (the mock might not validate it fully)
  const dummyToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXVzZXItaWQiLCJpYXQiOjE2MDk0NTkyMDAsImV4cCI6OTk5OTk5OTk5OX0.dummy';
  
  const options = {
    hostname: 'xmyxuvuimebjltnaamox.supabase.co',
    port: 443,
    path: '/functions/v1/start-game-handler',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'Authorization': `Bearer ${dummyToken}`,
      'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2ODMxNTAsImV4cCI6MjA2MjI1OTE1MH0.WC8u7cCNSV0LdVmoijHIEBlNblAyBGlFxsy2_mM7XZY'
    }
  };
  
  console.log('Testing Mock Edge Function at: https://' + options.hostname + options.path);
  console.log('With room ID:', roomId);
  console.log('Using dummy JWT for testing\n');
  
  const req = https.request(options, (res) => {
    console.log('Response status:', res.statusCode);
    console.log('Response headers:', JSON.stringify(res.headers, null, 2));
    
    let responseData = '';
    
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      console.log('\nResponse body:', responseData);
      
      try {
        const parsedData = JSON.parse(responseData);
        console.log('\nParsed response:', JSON.stringify(parsedData, null, 2));
        
        if (parsedData.error) {
          console.log('\n❌ Edge Function returned error:', parsedData.error);
          console.log('This might be expected if the mock is checking authentication');
        } else if (parsedData.success) {
          console.log('\n✅ Mock Edge Function is working!');
          console.log('Mock question generated:', parsedData.firstQuestion);
        }
      } catch (e) {
        console.log('\n❌ Failed to parse response as JSON');
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('\n❌ Failed to call Edge Function:', error);
  });
  
  req.write(data);
  req.end();
}

testMockEdgeFunction();