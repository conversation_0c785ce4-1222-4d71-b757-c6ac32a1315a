# PowerShell script to run migration and deploy edge function
Write-Host "`nDeployment Script for Multiplayer Transition Feature" -ForegroundColor Yellow
Write-Host "===================================================" -ForegroundColor Yellow

# Step 1: Run database migration
Write-Host "`nStep 1: Running database migration..." -ForegroundColor Cyan
Write-Host "This will add transition_until and next_question_data columns to game_rooms table" -ForegroundColor Gray

cd $PSScriptRoot
npx supabase db push --include-all

if ($LASTEXITCODE -ne 0) {
    Write-Host "Migration failed! Please check the error above." -ForegroundColor Red
    Write-Host "Make sure to enter the database password: 9ACv!PEKEN5`$@Mh" -ForegroundColor Yellow
    exit 1
}

Write-Host "Migration completed successfully!" -ForegroundColor Green

# Step 2: Deploy edge function
Write-Host "`nStep 2: Deploying submit-answer-handler edge function..." -ForegroundColor Cyan

cd supabase
.\deploy-submit-answer-handler.ps1

if ($LASTEXITCODE -ne 0) {
    Write-Host "Edge function deployment failed!" -ForegroundColor Red
    exit 1
}

Write-Host "`nDeployment complete! The 3-second transition delay is now active." -ForegroundColor Green
Write-Host "Players will see a countdown between questions in multiplayer games." -ForegroundColor Gray