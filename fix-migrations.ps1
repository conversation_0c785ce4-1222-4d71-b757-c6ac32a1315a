# Fix Migration Issues Script
# This script handles migration conflicts by checking column existence

Write-Host "Migration Fix Script" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan
Write-Host ""

$password = '9ACv!PEKEN5$@Mh'

# First, let's check the current database schema
Write-Host "Step 1: Checking current database schema..." -ForegroundColor Yellow
$checkColumnsQuery = @"
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
    AND table_name = 'game_rooms'
    AND column_name IN ('player_bonus_levels', 'transition_until', 'next_question_data')
ORDER BY column_name;
"@

$result = $checkColumnsQuery | supabase db query --password $password 2>&1
Write-Host "Current columns in game_rooms table:"
Write-Host $result
Write-Host ""

# Check which migrations have been applied
Write-Host "Step 2: Checking applied migrations..." -ForegroundColor Yellow
$appliedMigrations = supabase db remote list --password $password 2>&1
Write-Host "Applied migrations:"
Write-Host $appliedMigrations | Select-String -Pattern "20250128|20250129|20250617|20250625|20250629"
Write-Host ""

# Create a consolidated migration that checks for column existence
Write-Host "Step 3: Creating safe migration file..." -ForegroundColor Yellow
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$migrationFile = "supabase/migrations/${timestamp}_safe_add_missing_columns.sql"

$migrationContent = @"
-- Safe migration to add missing columns with existence checks
-- This migration will only add columns that don't already exist

DO `$`$ 
BEGIN
    -- Add player_bonus_levels column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'player_bonus_levels') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN player_bonus_levels jsonb NOT NULL DEFAULT '{}'::jsonb;
        
        COMMENT ON COLUMN public.game_rooms.player_bonus_levels IS 'Tracks consecutive correct answer bonuses for each player';
    END IF;

    -- Add transition_until column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'transition_until') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN transition_until timestamptz DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.transition_until IS 'Timestamp indicating when the transition period ends and the next question should be shown. NULL when not in transition.';
    END IF;

    -- Add next_question_data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'game_rooms' 
                   AND column_name = 'next_question_data') THEN
        ALTER TABLE public.game_rooms 
        ADD COLUMN next_question_data jsonb DEFAULT NULL;
        
        COMMENT ON COLUMN public.game_rooms.next_question_data IS 'The next question data that will become active after the transition period ends.';
    END IF;
END`$`$;

-- Create index on transition_until for efficient queries if it doesn't exist
DO `$`$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE schemaname = 'public' 
                   AND tablename = 'game_rooms' 
                   AND indexname = 'idx_game_rooms_transition_until') THEN
        CREATE INDEX idx_game_rooms_transition_until ON public.game_rooms(transition_until) 
        WHERE transition_until IS NOT NULL;
    END IF;
END`$`$;
"@

$migrationContent | Out-File -FilePath $migrationFile -Encoding UTF8
Write-Host "Created migration file: $migrationFile" -ForegroundColor Green
Write-Host ""

# Apply the safe migration
Write-Host "Step 4: Applying safe migration..." -ForegroundColor Yellow
$applyResult = supabase db push --password $password 2>&1
Write-Host $applyResult
Write-Host ""

# Verify the columns were added
Write-Host "Step 5: Verifying columns were added..." -ForegroundColor Yellow
$verifyResult = $checkColumnsQuery | supabase db query --password $password 2>&1
Write-Host "Final column state:"
Write-Host $verifyResult

Write-Host ""
Write-Host "Migration fix completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Deploy the edge functions using the individual PowerShell scripts"
Write-Host "2. Test the multiplayer transition feature"