# Robust Event-Based Transition System

## Overview

This implementation provides a clean, robust, and efficient system for handling automatic game transitions in multiplayer games. It uses an event-based approach where clients act as reminders while the server maintains full authority.

## Architecture

### Three-Layer System

1. **Primary: Event-Based Client Reminders**
   - Clients monitor `transition_deadline` with local timers
   - When deadline passes, client calls `check-game-transition`
   - Multiple clients provide natural redundancy
   - No polling, pure event-driven efficiency

2. **Secondary: Server Validation**
   - Server validates all transition requests
   - Checks actual time vs deadline
   - Prevents race conditions with question ID verification
   - Maintains complete authority over game state

3. **Tertiary: Safety Monitor (Optional)**
   - Catches edge case where all clients disconnect
   - Only processes truly abandoned games
   - Runs infrequently (every 30s recommended)
   - Minimal overhead - usually finds 0 games

## Key Components

### 1. Client-Side (page.tsx)
```typescript
// Enhanced with retry logic and stale deadline detection
- Monitors transition_deadline with useEffect
- Sets timer for deadline + 100ms buffer
- Retries up to 3 times on failure
- Skips stale deadlines (>10s in future)
```

### 2. Edge Functions

#### check-game-transition
- Called by clients when deadline reached
- Validates room exists and is active
- Checks if transition time has actually passed
- Invokes transition-monitor if valid

#### transition-monitor
- Handles actual game state transitions
- Generates next question
- Updates room with new round data
- Includes race condition protection

#### transition-safety-monitor (NEW)
- Optional safety net for abandoned games
- Only processes games with 0 connected players
- Deadline must be >5 seconds overdue
- Prevents permanent game stalls

### 3. Database Fields
- `transition_deadline`: When next transition should occur
- `question_started_at`: When current question began
- `current_question_data`: Current question being played
- `current_round_answers`: Answers for current round

## Deployment

```powershell
# Run the robust deployment script
.\deploy-robust-transitions.ps1
```

This deploys all three edge functions automatically.

## Optional: Schedule Safety Monitor

For maximum robustness, schedule the safety monitor to run periodically:

### Option 1: Supabase Cron (if available)
```sql
SELECT cron.schedule(
  'transition-safety-check',
  '*/30 * * * * *',  -- Every 30 seconds
  $$
  SELECT net.http_post(
    url := 'https://your-project.supabase.co/functions/v1/transition-safety-monitor',
    headers := jsonb_build_object(
      'Authorization', 'Bearer YOUR_ANON_KEY'
    )
  );
  $$
);
```

### Option 2: External Cron
```bash
# Add to crontab
* * * * * curl -X POST https://your-project.supabase.co/functions/v1/transition-safety-monitor \
  -H "Authorization: Bearer YOUR_ANON_KEY" && sleep 30 && \
  curl -X POST https://your-project.supabase.co/functions/v1/transition-safety-monitor \
  -H "Authorization: Bearer YOUR_ANON_KEY"
```

### Option 3: GitHub Actions
```yaml
name: Transition Safety Monitor
on:
  schedule:
    - cron: '*/1 * * * *'  # Every minute
jobs:
  check-transitions:
    runs-on: ubuntu-latest
    steps:
      - name: Call safety monitor
        run: |
          curl -X POST ${{ secrets.SUPABASE_URL }}/functions/v1/transition-safety-monitor \
            -H "Authorization: Bearer ${{ secrets.SUPABASE_ANON_KEY }}"
```

## Benefits

1. **Clean**: No complex polling or external dependencies
2. **Efficient**: Only activates when needed
3. **Robust**: Multiple layers of protection
4. **Scalable**: Works with any number of games
5. **Reliable**: Handles disconnections and edge cases

## Edge Cases Handled

1. **All clients disconnect**: Safety monitor catches after 5s
2. **Network failures**: Client retries up to 3 times
3. **Race conditions**: Server validates question IDs
4. **Stale data**: Client ignores old deadlines
5. **Multiple simultaneous calls**: Server handles gracefully

## Monitoring

Watch for these log patterns:

- `[TRANSITION_HELPER]` - Client-side timer events
- `[CHECK_TRANSITION]` - Server validation checks
- `[TRANSITION_MONITOR]` - Actual transitions
- `[TRANSITION_SAFETY]` - Safety monitor activity

## Performance

- **Normal operation**: Zero overhead between transitions
- **Transition time**: <100ms typical
- **Safety monitor**: <50ms when no abandoned games
- **Client retries**: Max 3 seconds additional delay

This system provides the best of both worlds: the efficiency of event-based transitions with the reliability of server-side validation and safety mechanisms.