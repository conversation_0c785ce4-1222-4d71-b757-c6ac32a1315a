# Quick Login Script to Get Auth Token for Testing
# This logs in and extracts the JWT token for use in tests

param(
    [Parameter(Mandatory=$true)]
    [string]$Email,
    
    [Parameter(Mandatory=$true)]
    [string]$Password
)

$baseUrl = "https://xmyxuvuimebjltnaamox.supabase.co"
$anonKey = $env:SUPABASE_ANON_KEY

if (-not $anonKey) {
    Write-Host "[ERROR] SUPABASE_ANON_KEY environment variable not set" -ForegroundColor Red
    exit 1
}

Write-Host "[LOGIN] Attempting to login and get auth token..." -ForegroundColor Cyan

$loginBody = @{
    identifier = $Email
    password = $Password
} | ConvertTo-Json

$headers = @{
    "Content-Type" = "application/json"
    "apikey" = $anonKey
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/functions/v1/login-handler" `
        -Method POST `
        -Headers $headers `
        -Body $loginBody
    
    $accessToken = $response.session.access_token
    
    Write-Host "[SUCCESS] Login successful!" -ForegroundColor Green
    Write-Host "[TOKEN] Access Token: $accessToken" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "[SETUP] To use this token, run:" -ForegroundColor Cyan
    Write-Host "`$env:SUPABASE_AUTH_TOKEN = `"$accessToken`"" -ForegroundColor White
    
    # Auto-set the environment variable
    $env:SUPABASE_AUTH_TOKEN = $accessToken
    Write-Host "[AUTO] Environment variable SUPABASE_AUTH_TOKEN has been set!" -ForegroundColor Green
    
} catch {
    Write-Host "[ERROR] Login failed: $($_.Exception.Message)" -ForegroundColor Red
    
    # Try to get more details about the error
    if ($_.Exception.Response) {
        Write-Host "[DEBUG] Response Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorContent = $reader.ReadToEnd()
            Write-Host "[DEBUG] Error Content: $errorContent" -ForegroundColor Yellow
            
            if ($errorContent) {
                $errorBody = $errorContent | ConvertFrom-Json
                Write-Host "[DETAILS] Error: $($errorBody.error_description)" -ForegroundColor Red
            }
        } catch {
            Write-Host "[DETAILS] Could not parse error response: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    exit 1
} 