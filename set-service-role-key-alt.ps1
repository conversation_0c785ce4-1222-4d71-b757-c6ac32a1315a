# Alternative method using environment variable
Write-Host "Setting service role key using environment variable method..." -ForegroundColor Yellow

$env:SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhteXh1dnVpbWViamx0bmFhbW94Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjY4MzE1MCwiZXhwIjoyMDYyMjU5MTUwfQ.YjQP5MvOznin_3B9xb_y9E0G_kECbmBc12QL9kH4gD4"

npx supabase secrets set --env-file .env --project-ref xmyxuvuimebjltnaamox

Write-Host "`nIf the above doesn't work, try the direct method below:" -ForegroundColor Yellow
Write-Host "npx supabase secrets set --project-ref xmyxuvuimebjltnaamox" -ForegroundColor Cyan
Write-Host "Then paste: SUPABASE_SERVICE_ROLE_KEY=<the-key-value>" -ForegroundColor Cyan